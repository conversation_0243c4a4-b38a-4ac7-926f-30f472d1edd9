<?php

namespace Tests\Feature\Reviews;

use App\Jobs\AssociateLeadWithReviewJob;
use App\Models\ConsumerReviews\Review;
use App\Models\ConsumerReviews\Reviewer;
use App\Models\Odin\Consumer;
use App\Repositories\Odin\ConsumerRepository;
use App\Repositories\ReviewRepository;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Mockery;
use Mockery\MockInterface;
use Tests\TestCase;

class AssociateLeadWithReviewJobTest extends TestCase
{
    use DatabaseTransactions;

    /**
     * Testing the job handle function
     *
     * @throws BindingResolutionException
     */
    public function test_job_handle(): void
    {
        $reviewerId = 3;
        $phone = '123-456-789';
        $email = '<EMAIL>';
        $reviewer = new Reviewer;
        $reviewer[Reviewer::FIELD_ID] = $reviewerId;
        $reviewer[Reviewer::FIELD_PHONE] = $phone;
        $reviewer[Reviewer::FIELD_EMAIL] = $email;

        // ID of related consumer
        $consumerRelationId = 10;
        $consumer = new Consumer;
        $consumer[Consumer::FIELD_ID] = $consumerRelationId;

        // Review mock
        $reviewMock = Mockery::mock(Review::class);
        $reviewMock->shouldReceive('getAttribute')->once()->andReturn($reviewer);

        // Create mock for ReviewRepository
        $this->instance(
            ReviewRepository::class,
            Mockery::mock(ReviewRepository::class, function (MockInterface $mock) use ($reviewer) {
                $mock->shouldReceive('associateReviewerWithConsumers')->once()->withArgs(function ($reviewerArg, $consumerArg) use ($reviewer) {
                    $this->assertEquals($reviewer[Reviewer::FIELD_ID], $reviewerArg[Reviewer::FIELD_ID]);

                    return true;
                })->andReturn(true);
            })
        );

        $this->instance(
            ConsumerRepository::class,
            Mockery::mock(ConsumerRepository::class, function (MockInterface $mock) use ($reviewer) {
                $mock->shouldReceive('getAssociatedConsumers')->once()->withArgs(function ($email, $phone) use ($reviewer) {
                    $this->assertEquals($reviewer[Reviewer::FIELD_EMAIL], $email);
                    $this->assertEquals($reviewer[Reviewer::FIELD_PHONE], $phone);

                    return true;
                })->andReturn(new Collection);
            })
        );

        // Create job instance with company parameter for constructor
        $job = app()->make(AssociateLeadWithReviewJob::class, ['reviews' => collect([$reviewMock])]);

        // Run handle with injected dependencies, tested by expected function calls in mocks
        $this->assertEquals(null, $job->handle(
            app()->make(ReviewRepository::class),
            app()->make(ConsumerRepository::class),
        ));
    }

    public function test_phone_normalization(): void
    {
        // All phone numbers with these digits should resolve to this pattern
        $expectedSqlPattern = '%123%456%7890%';

        // Each number is resolved to the local digits without country code, separated by SQL pattern match % characters
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('+1(123)456-7890'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('+1-************'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('+1 (123)456-7890'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('(+1) ************'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('1-************'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('1 1234567890'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('1234567890'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('************'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('************'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('(123)456-7890'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('************'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('123/456/7890'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('abc 123 abc 456 abc 7890 abc'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('123 4567890'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('(*************'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('+123 (123)456-7890'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('000 ************'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('+61 ************'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('(+000) (123)4567890'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql(' 123 4567890 '));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('phone: ************'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('+11234567890'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('11234567890'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('611234567890'));
        $this->assertEquals($expectedSqlPattern, normalizePhoneNumberSql('61 1234567890'));
    }

    public function test_phone_normalization_no_pattern(): void
    {
        $this->assertEquals('%1234%', normalizePhoneNumberSql('1234'));
        $this->assertEquals('%56789%', normalizePhoneNumberSql('56789'));
        $this->assertEquals('%34-45-1%', normalizePhoneNumberSql('34-45-1'));
        $this->assertEquals('%0%', normalizePhoneNumberSql('0'));
        $this->assertEquals('%abcd%', normalizePhoneNumberSql('abcd'));
    }
}
