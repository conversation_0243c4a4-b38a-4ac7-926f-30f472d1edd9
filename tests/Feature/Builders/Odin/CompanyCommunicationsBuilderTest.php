<?php

namespace Tests\Feature\Builders\Odin;

use App\Builders\Odin\CompanyCommunicationsBuilder;
use App\Models\Call;
use App\Models\Email;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyUser;
use App\Models\Phone;
use App\Models\Text;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CompanyCommunicationsBuilderTest extends TestCase
{
    use RefreshDatabase;

    protected CompanyCommunicationsBuilder $builder;

    protected function setUp(): void
    {
        parent::setUp();

        $this->builder = app(CompanyCommunicationsBuilder::class);
    }

    #[Test]
    public function it_runs_successfully_with_no_communications()
    {
        $this->assertEmpty($this->builder->get());
    }

    #[Test]
    public function it_retrieves_calls_by_company_location()
    {
        $user = User::factory()->createQuietly();

        $phone = Phone::factory()->create();

        $user->phones()->attach($phone);

        $company = Company::factory()->createQuietly();

        $companyLocation = CompanyLocation::factory()->for($company)->create();

        $call = Call::factory()->for($phone)->create([
            'relation_type' => 'company_location',
            'relation_id' => $companyLocation->id,
        ]);

        $communications = $this->builder->get();

        $this->assertCount(1, $communications);

        $this->assertEquals($company->name, $communications->first()->company_name);
        $this->assertEquals($company->id, $communications->first()->company_id);
        $this->assertEquals($user->id, $communications->first()->user_id);
        $this->assertEquals($user->name, $communications->first()->user_name);
        $this->assertEquals($call->created_at, $communications->first()->contact_date);
        $this->assertEquals('company_location_call', $communications->first()->contact_type);
        $this->assertEquals($companyLocation->name, $communications->first()->contact_name);
        $this->assertEquals(Call::class, $communications->first()->contact_model);
        $this->assertEquals($call->id, $communications->first()->contact_model_id);
        $this->assertEquals(CompanyLocation::class, $communications->first()->contact_dest_model);
        $this->assertEquals($companyLocation->id, $communications->first()->contact_dest_model_id);
        $this->assertEquals($call->other_number, $communications->first()->contact_dest);
        $this->assertEquals($call->external_reference, $communications->first()->contact_contents);
    }

    #[Test]
    public function it_retrieves_calls_by_company_user()
    {
        $user = User::factory()->createQuietly();

        $phone = Phone::factory()->create();

        $user->phones()->attach($phone);

        $company = Company::factory()->createQuietly();

        $companyUser = CompanyUser::factory()->for($company)->create();

        $call = Call::factory()->for($phone)->create([
            'relation_type' => 'company_user',
            'relation_id' => $companyUser->id,
        ]);

        $communications = $this->builder->get();

        $this->assertCount(1, $communications);

        $this->assertEquals($company->name, $communications->first()->company_name);
        $this->assertEquals($company->id, $communications->first()->company_id);
        $this->assertEquals($user->id, $communications->first()->user_id);
        $this->assertEquals($user->name, $communications->first()->user_name);
        $this->assertEquals($call->created_at, $communications->first()->contact_date);
        $this->assertEquals('company_user_call', $communications->first()->contact_type);
        $this->assertEquals(str($companyUser->first_name)->append(' ', $companyUser->last_name)->value(), $communications->first()->contact_name);
        $this->assertEquals(Call::class, $communications->first()->contact_model);
        $this->assertEquals($call->id, $communications->first()->contact_model_id);
        $this->assertEquals(CompanyUser::class, $communications->first()->contact_dest_model);
        $this->assertEquals($companyUser->id, $communications->first()->contact_dest_model_id);
        $this->assertEquals($call->other_number, $communications->first()->contact_dest);
        $this->assertEquals($call->external_reference, $communications->first()->contact_contents);
    }

    #[Test]
    public function it_retrieves_calls_by_company()
    {
        $user = User::factory()->createQuietly();

        $phone = Phone::factory()->create();

        $user->phones()->attach($phone);

        $company = Company::factory()->createQuietly();

        $call = Call::factory()->for($phone)->create([
            'relation_type' => 'company',
            'relation_id' => $company->id,
        ]);

        $communications = $this->builder->get();

        $this->assertCount(1, $communications);

        $this->assertEquals($company->name, $communications->first()->company_name);
        $this->assertEquals($company->id, $communications->first()->company_id);
        $this->assertEquals($user->id, $communications->first()->user_id);
        $this->assertEquals($user->name, $communications->first()->user_name);
        $this->assertEquals($call->created_at, $communications->first()->contact_date);
        $this->assertEquals('company_call', $communications->first()->contact_type);
        $this->assertEquals(str($company->name), $communications->first()->contact_name);
        $this->assertEquals(Call::class, $communications->first()->contact_model);
        $this->assertEquals($call->id, $communications->first()->contact_model_id);
        $this->assertEquals(Company::class, $communications->first()->contact_dest_model);
        $this->assertEquals($company->id, $communications->first()->contact_dest_model_id);
        $this->assertEquals($call->other_number, $communications->first()->contact_dest);
        $this->assertEquals($call->external_reference, $communications->first()->contact_contents);
    }

    #[Test]
    public function it_retrieves_untracked_calls_by_company_user()
    {
        $user = User::factory()->createQuietly();

        $phone = Phone::factory()->create();

        $user->phones()->attach($phone);

        $company = Company::factory()->createQuietly();

        $cellNumber = '5555555555';

        $companyUser = CompanyUser::factory()->for($company)->create([
            'formatted_cell_phone' => $cellNumber,
        ]);

        $call = Call::factory()->for($phone)->createQuietly([
            'relation_type' => 'phone',
            'relation_id' => null,
            'formatted_other_number' => $cellNumber,
        ]);

        $communications = $this->builder->get();

        $this->assertCount(1, $communications);

        $this->assertEquals($company->name, $communications->first()->company_name);
        $this->assertEquals($company->id, $communications->first()->company_id);
        $this->assertEquals($user->id, $communications->first()->user_id);
        $this->assertEquals($user->name, $communications->first()->user_name);
        $this->assertEquals($call->created_at, $communications->first()->contact_date);
        $this->assertEquals('phone_call', $communications->first()->contact_type);
        $this->assertEquals(str($companyUser->first_name)->append(' ', $companyUser->last_name)->value(), $communications->first()->contact_name);
        $this->assertEquals(Call::class, $communications->first()->contact_model);
        $this->assertEquals($call->id, $communications->first()->contact_model_id);
        $this->assertEquals(CompanyUser::class, $communications->first()->contact_dest_model);
        $this->assertEquals($companyUser->id, $communications->first()->contact_dest_model_id);
        $this->assertEquals($call->other_number, $communications->first()->contact_dest);
        $this->assertEquals($call->external_reference, $communications->first()->contact_contents);
    }

    #[Test]
    public function it_retrieves_emails()
    {
        $user = User::factory()->createQuietly();

        $company = Company::factory()->createQuietly();

        $companyUser = CompanyUser::factory()->for($company)->create();

        $email = Email::factory()
            ->for($user, 'fromUser')
            ->for($companyUser, 'toCompanyUser')
            ->create();

        $communications = $this->builder->get();

        $this->assertCount(1, $communications);

        $this->assertEquals($company->name, $communications->first()->company_name);
        $this->assertEquals($company->id, $communications->first()->company_id);
        $this->assertEquals($user->id, $communications->first()->user_id);
        $this->assertEquals($user->name, $communications->first()->user_name);
        $this->assertEquals($email->created_at, $communications->first()->contact_date);
        $this->assertEquals('email', $communications->first()->contact_type);
        $this->assertEquals(str($companyUser->first_name)->append(' ', $companyUser->last_name)->value(), $communications->first()->contact_name);
        $this->assertEquals(Email::class, $communications->first()->contact_model);
        $this->assertEquals($email->id, $communications->first()->contact_model_id);
        $this->assertEquals(CompanyUser::class, $communications->first()->contact_dest_model);
        $this->assertEquals($companyUser->id, $communications->first()->contact_dest_model_id);
        $this->assertEquals($email->to_address, $communications->first()->contact_dest);
        $this->assertEquals($email->body, $communications->first()->contact_contents);
    }

    #[Test]
    public function it_retrieves_texts()
    {
        $user = User::factory()->createQuietly();

        $phone = Phone::factory()->create();

        $user->phones()->attach($phone);

        $company = Company::factory()->createQuietly();

        $cellNumber = '5555555555';

        $companyUser = CompanyUser::factory()->for($company)->create([
            'cell_phone' => $cellNumber,
        ]);

        $text = Text::factory()->for($phone)->createQuietly([
            'other_number' => $cellNumber,
        ]);

        $communications = $this->builder->get();

        $this->assertCount(1, $communications);

        $this->assertEquals($company->name, $communications->first()->company_name);
        $this->assertEquals($company->id, $communications->first()->company_id);
        $this->assertEquals($user->id, $communications->first()->user_id);
        $this->assertEquals($user->name, $communications->first()->user_name);
        $this->assertEquals($text->created_at, $communications->first()->contact_date);
        $this->assertEquals('text', $communications->first()->contact_type);
        $this->assertEquals(str($companyUser->first_name)->append(' ', $companyUser->last_name)->value(), $communications->first()->contact_name);
        $this->assertEquals(Text::class, $communications->first()->contact_model);
        $this->assertEquals($text->id, $communications->first()->contact_model_id);
        $this->assertEquals(CompanyUser::class, $communications->first()->contact_dest_model);
        $this->assertEquals($companyUser->id, $communications->first()->contact_dest_model_id);
        $this->assertEquals($text->other_number, $communications->first()->contact_dest);
        $this->assertEquals($text->message_body, $communications->first()->contact_contents);
    }

    #[Test]
    public function it_sorts_communications_in_descending_order_by_default()
    {
        $user = User::factory()->createQuietly();

        $phone = Phone::factory()->create();

        $user->phones()->attach($phone);

        $company = Company::factory()->createQuietly();

        $cellNumber = '5555555555';

        $companyUser = CompanyUser::factory()->for($company)->create([
            'formatted_cell_phone' => $cellNumber,
        ]);

        $untrackedCall = Call::factory()->for($phone)->createQuietly([
            'relation_type' => 'phone',
            'relation_id' => null,
            'formatted_other_number' => $cellNumber,
        ]);

        $this->travel(1)->minute();

        $companyLocation = CompanyLocation::factory()->for($company)->create();

        $companyLocationCall = Call::factory()->for($phone)->create([
            'relation_type' => 'company_location',
            'relation_id' => $companyLocation->id,
        ]);

        $communications = $this->builder->get();

        $this->assertCount(2, $communications);

        $this->assertEquals($communications->first()->contact_type, 'company_location_call');
        $this->assertEquals($communications->first()->contact_dest_model, CompanyLocation::class);
        $this->assertEquals($communications->last()->contact_type, 'phone_call');
        $this->assertEquals($communications->last()->contact_dest_model, CompanyUser::class);
    }

    #[Test]
    public function it_can_sort_communications_in_ascending_order()
    {
        $user = User::factory()->createQuietly();

        $phone = Phone::factory()->create();

        $user->phones()->attach($phone);

        $company = Company::factory()->createQuietly();

        $cellNumber = '5555555555';

        $companyUser = CompanyUser::factory()->for($company)->create([
            'formatted_cell_phone' => $cellNumber,
        ]);

        $untrackedCall = Call::factory()->for($phone)->createQuietly([
            'relation_type' => 'phone',
            'relation_id' => null,
            'formatted_other_number' => $cellNumber,
        ]);

        $this->travel(1)->minute();

        $companyLocation = CompanyLocation::factory()->for($company)->create();

        $companyLocationCall = Call::factory()->for($phone)->create([
            'relation_type' => 'company_location',
            'relation_id' => $companyLocation->id,
        ]);

        $communications = $this->builder->ascending()->get();

        $this->assertCount(2, $communications);

        $this->assertEquals($communications->first()->contact_type, 'phone_call');
        $this->assertEquals($communications->first()->contact_dest_model, CompanyUser::class);
        $this->assertEquals($communications->last()->contact_type, 'company_location_call');
        $this->assertEquals($communications->last()->contact_dest_model, CompanyLocation::class);
    }

    #[Test]
    public function it_orders_communications_by_contact_date_by_default()
    {
        $user = User::factory()->createQuietly();

        $phone = Phone::factory()->create();

        $user->phones()->attach($phone);

        $company = Company::factory()->createQuietly();

        $cellNumber = '5555555555';

        $companyUser = CompanyUser::factory()->for($company)->create([
            'formatted_cell_phone' => $cellNumber,
        ]);

        $untrackedCall = Call::factory()->for($phone)->createQuietly([
            'relation_type' => 'phone',
            'relation_id' => null,
            'formatted_other_number' => $cellNumber,
        ]);

        $this->travel(1)->minute();

        $companyLocation = CompanyLocation::factory()->for($company)->create();

        $companyLocationCall = Call::factory()->for($phone)->create([
            'relation_type' => 'company_location',
            'relation_id' => $companyLocation->id,
        ]);

        $communications = $this->builder->get();

        $this->assertCount(2, $communications);

        $this->assertEquals($communications->first()->contact_type, 'company_location_call');
        $this->assertEquals($communications->first()->contact_dest_model, CompanyLocation::class);
        $this->assertEquals($communications->last()->contact_type, 'phone_call');
        $this->assertEquals($communications->last()->contact_dest_model, CompanyUser::class);
    }

    #[Test]
    public function it_can_order_communications_by_other_columns()
    {
        $user = User::factory()->createQuietly();

        $phone = Phone::factory()->create();

        $user->phones()->attach($phone);

        $company = Company::factory()->createQuietly();

        $cellNumber = '5555555555';

        $companyUser = CompanyUser::factory()->for($company)->create([
            'formatted_cell_phone' => $cellNumber,
        ]);

        $untrackedCall = Call::factory()->for($phone)->createQuietly([
            'relation_type' => 'phone',
            'relation_id' => null,
            'formatted_other_number' => $cellNumber,
        ]);

        $this->travel(1)->minute();

        $companyLocation = CompanyLocation::factory()->for($company)->create();

        $companyLocationCall = Call::factory()->for($phone)->create([
            'relation_type' => 'company_location',
            'relation_id' => $companyLocation->id,
        ]);

        $communications = $this->builder->orderByContactType()->get();

        $this->assertCount(2, $communications);

        $this->assertEquals($communications->first()->contact_type, 'phone_call');
        $this->assertEquals($communications->first()->contact_dest_model, CompanyUser::class);
        $this->assertEquals($communications->last()->contact_type, 'company_location_call');
        $this->assertEquals($communications->last()->contact_dest_model, CompanyLocation::class);

        $communications = $this->builder->orderByDestinationModel()->ascending()->get();

        $this->assertCount(2, $communications);

        $this->assertEquals($communications->first()->contact_type, 'company_location_call');
        $this->assertEquals($communications->first()->contact_dest_model, CompanyLocation::class);
        $this->assertEquals($communications->last()->contact_type, 'phone_call');
        $this->assertEquals($communications->last()->contact_dest_model, CompanyUser::class);
    }

    #[Test]
    public function it_can_filter_by_user()
    {
        $user = User::factory()->createQuietly();

        $phone = Phone::factory()->create();

        $user->phones()->attach($phone);

        $user2 = User::factory()->createQuietly();

        $phone2 = Phone::factory()->create();

        $user2->phones()->attach($phone2);

        $company = Company::factory()->createQuietly();

        $companyLocation = CompanyLocation::factory()->for($company)->create();

        $call = Call::factory()->for($phone)->create([
            'relation_type' => 'company_location',
            'relation_id' => $companyLocation->id,
        ]);

        $call2 = Call::factory()->for($phone2)->create([
            'relation_type' => 'company_location',
            'relation_id' => $companyLocation->id,
        ]);

        $this->assertCount(2, $this->builder->get());

        $communicationsForUser2 = $this->builder->forUser($user2)->get();

        $this->assertCount(1, $communicationsForUser2);

        $this->assertEquals($company->name, $communicationsForUser2->first()->company_name);
        $this->assertEquals($company->id, $communicationsForUser2->first()->company_id);
        $this->assertEquals($user2->id, $communicationsForUser2->first()->user_id);
        $this->assertEquals($user2->name, $communicationsForUser2->first()->user_name);
        $this->assertEquals($call2->created_at, $communicationsForUser2->first()->contact_date);
        $this->assertEquals('company_location_call', $communicationsForUser2->first()->contact_type);
        $this->assertEquals($companyLocation->name, $communicationsForUser2->first()->contact_name);
        $this->assertEquals(Call::class, $communicationsForUser2->first()->contact_model);
        $this->assertEquals($call2->id, $communicationsForUser2->first()->contact_model_id);
        $this->assertEquals(CompanyLocation::class, $communicationsForUser2->first()->contact_dest_model);
        $this->assertEquals($companyLocation->id, $communicationsForUser2->first()->contact_dest_model_id);
        $this->assertEquals($call2->other_number, $communicationsForUser2->first()->contact_dest);
        $this->assertEquals($call2->external_reference, $communicationsForUser2->first()->contact_contents);
    }

    #[Test]
    public function it_can_filter_by_company()
    {
        $user = User::factory()->createQuietly();

        $phone = Phone::factory()->create();

        $user->phones()->attach($phone);

        $company = Company::factory()->createQuietly();

        $companyLocation = CompanyLocation::factory()->for($company)->create();

        $company2 = Company::factory()->createQuietly();

        $companyLocation2 = CompanyLocation::factory()->for($company2)->create();

        $call = Call::factory()->for($phone)->create([
            'relation_type' => 'company_location',
            'relation_id' => $companyLocation->id,
        ]);

        $call2 = Call::factory()->for($phone)->create([
            'relation_type' => 'company_location',
            'relation_id' => $companyLocation2->id,
        ]);

        $this->assertCount(2, $this->builder->get());

        $communicationsForCompany2 = $this->builder->forCompany($company2)->get();

        $this->assertCount(1, $communicationsForCompany2);

        $this->assertEquals($company2->name, $communicationsForCompany2->first()->company_name);
        $this->assertEquals($company2->id, $communicationsForCompany2->first()->company_id);
        $this->assertEquals($user->id, $communicationsForCompany2->first()->user_id);
        $this->assertEquals($user->name, $communicationsForCompany2->first()->user_name);
        $this->assertEquals($call2->created_at, $communicationsForCompany2->first()->contact_date);
        $this->assertEquals('company_location_call', $communicationsForCompany2->first()->contact_type);
        $this->assertEquals($companyLocation2->name, $communicationsForCompany2->first()->contact_name);
        $this->assertEquals(Call::class, $communicationsForCompany2->first()->contact_model);
        $this->assertEquals($call2->id, $communicationsForCompany2->first()->contact_model_id);
        $this->assertEquals(CompanyLocation::class, $communicationsForCompany2->first()->contact_dest_model);
        $this->assertEquals($companyLocation2->id, $communicationsForCompany2->first()->contact_dest_model_id);
        $this->assertEquals($call2->other_number, $communicationsForCompany2->first()->contact_dest);
        $this->assertEquals($call2->external_reference, $communicationsForCompany2->first()->contact_contents);
    }

    #[Test]
    public function it_retrieves_the_first_communication_record()
    {
        $user = User::factory()->createQuietly();

        $phone = Phone::factory()->create();

        $user->phones()->attach($phone);

        $company = Company::factory()->createQuietly();

        $cellNumber = '5555555555';

        $companyUser = CompanyUser::factory()->for($company)->create([
            'formatted_cell_phone' => $cellNumber,
        ]);

        $untrackedCall = Call::factory()->for($phone)->createQuietly([
            'relation_type' => 'phone',
            'relation_id' => null,
            'formatted_other_number' => $cellNumber,
        ]);

        $this->travel(1)->minute();

        $companyLocation = CompanyLocation::factory()->for($company)->create();

        $companyLocationCall = Call::factory()->for($phone)->create([
            'relation_type' => 'company_location',
            'relation_id' => $companyLocation->id,
        ]);

        $communication = $this->builder->first();

        $this->assertEquals($communication->contact_type, 'company_location_call');
        $this->assertEquals($communication->contact_dest_model, CompanyLocation::class);
    }
}
