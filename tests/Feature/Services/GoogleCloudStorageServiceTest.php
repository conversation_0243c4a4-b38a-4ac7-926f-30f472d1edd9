<?php

namespace Tests\Feature\Services;

use App\Services\CloudStorage\GoogleCloudStorageService;
use Exception;
use Google\Cloud\Storage\Bucket;
use Google\Cloud\Storage\StorageObject;
use PHPUnit\Framework\Attributes\DataProvider;
use Psr\Http\Message\StreamInterface;
use Tests\TestCase;

class GoogleCloudStorageServiceTest extends TestCase
{
    private GoogleCloudStorageService $service;

    private string $bucketName;

    protected function setUp(): void
    {
        parent::setUp();

        if (blank(config('services.google.storage.project_id'))) {
            $this->markTestSkipped('Skipping due to missing Google Storage Credentials');
        }

        $this->service = app(GoogleCloudStorageService::class);
        $this->bucketName = config('services.google.storage.buckets.email_template_images'); // Using the email template images bucket just for testing purposes
    }

    /**
     * @throws Exception
     */
    public function test_check_bucket_set()
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Bucket not set');

        $this->service->checkBucketSet();
    }

    public function test_get_bucket()
    {
        $bucket = $this->service->getBucketByName($this->bucketName);

        $this->assertInstanceOf(Bucket::class, $bucket);
    }

    /**
     * @return string[][]
     */
    public static function objectDataProvider(): array
    {
        return [
            [
                'auto-test.txt',
                'test data string',
            ],
        ];
    }

    /**
     * @dataProvider objectDataProvider
     *
     * @Depends testGetBucket
     *
     * @throws Exception
     */
    #[DataProvider('objectDataProvider')]
    public function test_upload(string $filename, string $data)
    {
        $this->service->setCurrentBucket($this->bucketName);

        $object = $this->service->upload($filename, $data);

        $this->assertInstanceOf(StorageObject::class, $object);
        $this->assertTrue($object->exists());
        $this->assertEquals($filename, $object->name());
    }

    /**
     * @dataProvider objectDataProvider
     *
     * @Depends testUpload
     *
     * @throws Exception
     */
    #[DataProvider('objectDataProvider')]
    public function test_download_object(string $filename, string $data)
    {
        $this->service->setCurrentBucket($this->bucketName);

        $objectStreamInterface = $this->service->downloadObject($filename);

        $this->assertInstanceOf(StreamInterface::class, $objectStreamInterface);
        $this->assertEquals($data, $objectStreamInterface->getContents());
    }

    /**
     * @Depends testUpload
     *
     * @dataProvider objectDataProvider
     *
     * @throws Exception
     */
    #[DataProvider('objectDataProvider')]
    public function test_delete_object(string $filename)
    {
        $this->service->setCurrentBucket($this->bucketName);

        $this->service->deleteObject($filename);

        $this->assertFalse($this->service->getObject($filename)->exists());
    }

    /**
     * @return string[][]
     */
    public static function multipleObjectsDataProvider(): array
    {
        return [
            [
                [
                    'auto-test-1.txt' => [
                        'name' => 'auto-test-1.txt',
                        'data' => 'test data string 1',
                        'options' => [],
                    ],
                    'auto-test-2.txt' => [
                        'name' => 'auto-test-2.txt',
                        'data' => 'test data string 2',
                        'options' => [],
                    ],
                    'auto-test-3.txt' => [
                        'name' => 'auto-test-3.txt',
                        'data' => 'test data string 3',
                        'options' => [],
                    ],
                ],
            ],
        ];
    }

    /**
     * @throws Exception
     *
     * @dataProvider multipleObjectsDataProvider
     */
    #[DataProvider('multipleObjectsDataProvider')]
    public function test_upload_multiple(array $uploads)
    {
        $this->service->setCurrentBucket($this->bucketName);

        $responses = $this->service->uploadMultiple($uploads);

        foreach ($responses as $name => $response) {
            $this->assertEquals('fulfilled', $response['state']);
            $this->assertInstanceOf(StorageObject::class, $response['value']);
            $this->assertEquals($name, $response['value']->name());
        }
    }

    /**
     * @throws Exception
     *
     * @Depends testUploadMultiple
     *
     * @dataProvider multipleObjectsDataProvider
     */
    #[DataProvider('multipleObjectsDataProvider')]
    public function test_download_multiple_objects(array $objects)
    {
        $this->service->setCurrentBucket($this->bucketName);

        $responses = $this->service->downloadMultipleObjects(array_keys($objects));

        foreach ($responses as $name => $response) {
            $this->assertEquals('fulfilled', $response['state']);
            $this->assertInstanceOf(StreamInterface::class, $response['value']);
            $this->assertEquals($objects[$name]['data'], $response['value']->getContents());
        }
    }

    /**
     * @throws Exception
     *
     * @Depends testDownloadMultipleObjects
     *
     * @dataProvider multipleObjectsDataProvider
     */
    #[DataProvider('multipleObjectsDataProvider')]
    public function test_delete_multiple_objects(array $objects)
    {
        $this->service->setCurrentBucket($this->bucketName);

        $success = $this->service->deleteMultipleObjects(array_keys($objects));

        $this->assertTrue($success);

        foreach ($objects as $name => $object) {
            $this->assertFalse($this->service->getObject($name)->exists());
        }
    }
}
