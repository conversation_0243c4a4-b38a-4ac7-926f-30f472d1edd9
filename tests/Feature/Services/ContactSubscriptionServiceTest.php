<?php

namespace Tests\Feature\Services;

use App\Models\ContactSubscription;
use App\Services\PubSub\ContactSubscriptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ContactSubscriptionServiceTest extends TestCase
{
    use RefreshDatabase;

    private ContactSubscriptionService $service;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = app(ContactSubscriptionService::class);
    }

    public function test_update_or_create_contact_subscription()
    {
        /**
         * Update
         */
        $contactSubscription = ContactSubscription::factory()->create();

        $updatedValues = [
            ContactSubscription::FIELD_CONTACT_ID => 99,
            ContactSubscription::FIELD_CONTACT_TYPE => 'updated contact',
            ContactSubscription::FIELD_CONTACT_METHOD => 'updated method',
            ContactSubscription::FIELD_NOTIFICATION_TYPE => 'updated notification',
            ContactSubscription::FIELD_UNSUBSCRIBED => true,
        ];

        $result = $this->service->updateOrCreateContactSubscription(
            $contactSubscription->{ContactSubscription::FIELD_ID},
            $updatedValues[ContactSubscription::FIELD_CONTACT_ID],
            $updatedValues[ContactSubscription::FIELD_CONTACT_TYPE],
            $updatedValues[ContactSubscription::FIELD_CONTACT_METHOD],
            $updatedValues[ContactSubscription::FIELD_NOTIFICATION_TYPE],
            $updatedValues[ContactSubscription::FIELD_UNSUBSCRIBED]
        );

        $this->assertInstanceOf(ContactSubscription::class, $result);

        $contactSubscription->refresh();

        $this->assertEquals($updatedValues[ContactSubscription::FIELD_CONTACT_ID], $contactSubscription->{ContactSubscription::FIELD_CONTACT_ID});
        $this->assertEquals($updatedValues[ContactSubscription::FIELD_CONTACT_TYPE], $contactSubscription->{ContactSubscription::FIELD_CONTACT_TYPE});
        $this->assertEquals($updatedValues[ContactSubscription::FIELD_CONTACT_METHOD], $contactSubscription->{ContactSubscription::FIELD_CONTACT_METHOD});
        $this->assertEquals($updatedValues[ContactSubscription::FIELD_NOTIFICATION_TYPE], $contactSubscription->{ContactSubscription::FIELD_NOTIFICATION_TYPE});
        $this->assertGreaterThan(0, $contactSubscription->{ContactSubscription::FIELD_UNSUBSCRIBED});

        /**
         * Create
         */
        $newValues = [
            ContactSubscription::FIELD_CONTACT_ID => 123,
            ContactSubscription::FIELD_CONTACT_TYPE => 'new contact',
            ContactSubscription::FIELD_CONTACT_METHOD => 'new method',
            ContactSubscription::FIELD_NOTIFICATION_TYPE => 'new notification',
            ContactSubscription::FIELD_UNSUBSCRIBED => false,
        ];

        $result = $this->service->updateOrCreateContactSubscription(
            null,
            $newValues[ContactSubscription::FIELD_CONTACT_ID],
            $newValues[ContactSubscription::FIELD_CONTACT_TYPE],
            $newValues[ContactSubscription::FIELD_CONTACT_METHOD],
            $newValues[ContactSubscription::FIELD_NOTIFICATION_TYPE],
            $newValues[ContactSubscription::FIELD_UNSUBSCRIBED]
        );

        $this->assertInstanceOf(ContactSubscription::class, $result);

        $this->assertDatabaseHas(ContactSubscription::TABLE, $result->toArray());

        $this->assertGreaterThan(0, $result->{ContactSubscription::FIELD_ID});
        $this->assertEquals($newValues[ContactSubscription::FIELD_CONTACT_ID], $result->{ContactSubscription::FIELD_CONTACT_ID});
        $this->assertEquals($newValues[ContactSubscription::FIELD_CONTACT_TYPE], $result->{ContactSubscription::FIELD_CONTACT_TYPE});
        $this->assertEquals($newValues[ContactSubscription::FIELD_CONTACT_METHOD], $result->{ContactSubscription::FIELD_CONTACT_METHOD});
        $this->assertEquals($newValues[ContactSubscription::FIELD_NOTIFICATION_TYPE], $result->{ContactSubscription::FIELD_NOTIFICATION_TYPE});
        $this->assertEquals(0, $result->{ContactSubscription::FIELD_UNSUBSCRIBED});
    }

    public function test_get_contact_subscription_by_id()
    {
        $referenceContactSubscription = ContactSubscription::factory()->create();

        $contactSubscription = $this->service->getContactSubscriptionById($referenceContactSubscription->id);

        $this->assertInstanceOf(ContactSubscription::class, $contactSubscription);
        $this->assertEquals($referenceContactSubscription->{ContactSubscription::FIELD_ID}, $contactSubscription->{ContactSubscription::FIELD_ID});
        $this->assertEquals($referenceContactSubscription->{ContactSubscription::FIELD_CONTACT_ID}, $contactSubscription->{ContactSubscription::FIELD_CONTACT_ID});
        $this->assertEquals($referenceContactSubscription->{ContactSubscription::FIELD_CONTACT_TYPE}, $contactSubscription->{ContactSubscription::FIELD_CONTACT_TYPE});
        $this->assertEquals($referenceContactSubscription->{ContactSubscription::FIELD_CONTACT_METHOD}, $contactSubscription->{ContactSubscription::FIELD_CONTACT_METHOD});
        $this->assertEquals($referenceContactSubscription->{ContactSubscription::FIELD_NOTIFICATION_TYPE}, $contactSubscription->{ContactSubscription::FIELD_NOTIFICATION_TYPE});
        $this->assertEquals($referenceContactSubscription->{ContactSubscription::FIELD_UNSUBSCRIBED}, $contactSubscription->{ContactSubscription::FIELD_UNSUBSCRIBED});

        /**
         * Non-existent ID test
         */
        $contactSubscription = $this->service->getContactSubscriptionById(123);

        $this->assertNull($contactSubscription);
    }

    public function test_search_contact_subscriptions()
    {
        ContactSubscription::factory()->create([
            ContactSubscription::FIELD_ID => 1,
            ContactSubscription::FIELD_CONTACT_ID => 1,
            ContactSubscription::FIELD_CONTACT_TYPE => ContactSubscription::CONTACT_TYPE_CONTACT,
            ContactSubscription::FIELD_CONTACT_METHOD => ContactSubscription::CONTACT_METHOD_EMAIL,
            ContactSubscription::FIELD_NOTIFICATION_TYPE => ContactSubscription::NOTIFICATION_TYPE_SALESBAIT,
            ContactSubscription::FIELD_UNSUBSCRIBED => time(),
            ContactSubscription::FIELD_DELETED_AT => null,
        ]);

        ContactSubscription::factory()->create([
            ContactSubscription::FIELD_ID => 2,
            ContactSubscription::FIELD_CONTACT_ID => 2,
            ContactSubscription::FIELD_CONTACT_TYPE => ContactSubscription::CONTACT_TYPE_USER,
            ContactSubscription::FIELD_CONTACT_METHOD => ContactSubscription::CONTACT_METHOD_EMAIL,
            ContactSubscription::FIELD_NOTIFICATION_TYPE => ContactSubscription::NOTIFICATION_TYPE_SALESBAIT,
            ContactSubscription::FIELD_UNSUBSCRIBED => 0,
            ContactSubscription::FIELD_DELETED_AT => null,
        ]);

        ContactSubscription::factory()->create([
            ContactSubscription::FIELD_ID => 3,
            ContactSubscription::FIELD_CONTACT_ID => 3,
            ContactSubscription::FIELD_CONTACT_TYPE => ContactSubscription::CONTACT_TYPE_CONTACT,
            ContactSubscription::FIELD_CONTACT_METHOD => ContactSubscription::CONTACT_METHOD_SMS,
            ContactSubscription::FIELD_NOTIFICATION_TYPE => ContactSubscription::NOTIFICATION_TYPE_SALESBAIT,
            ContactSubscription::FIELD_UNSUBSCRIBED => 0,
            ContactSubscription::FIELD_DELETED_AT => null,
        ]);

        ContactSubscription::factory()->create([
            ContactSubscription::FIELD_ID => 4,
            ContactSubscription::FIELD_CONTACT_ID => 4,
            ContactSubscription::FIELD_CONTACT_TYPE => ContactSubscription::CONTACT_TYPE_USER,
            ContactSubscription::FIELD_CONTACT_METHOD => ContactSubscription::CONTACT_METHOD_SMS,
            ContactSubscription::FIELD_NOTIFICATION_TYPE => ContactSubscription::NOTIFICATION_TYPE_SALESBAIT,
            ContactSubscription::FIELD_UNSUBSCRIBED => time(),
            ContactSubscription::FIELD_DELETED_AT => null,
        ]);

        ContactSubscription::factory()->create([
            ContactSubscription::FIELD_ID => 5,
            ContactSubscription::FIELD_CONTACT_ID => 5,
            ContactSubscription::FIELD_CONTACT_TYPE => ContactSubscription::CONTACT_TYPE_CONTACT,
            ContactSubscription::FIELD_CONTACT_METHOD => ContactSubscription::CONTACT_METHOD_EMAIL,
            ContactSubscription::FIELD_NOTIFICATION_TYPE => ContactSubscription::NOTIFICATION_TYPE_SALESBAIT,
            ContactSubscription::FIELD_UNSUBSCRIBED => 0,
            ContactSubscription::FIELD_DELETED_AT => date('Y-m-d H:i:s'),
        ]);

        /**
         * Single record
         */
        $searchParams = [
            ContactSubscription::FIELD_CONTACT_ID => 1,
            ContactSubscription::FIELD_CONTACT_TYPE => ContactSubscription::CONTACT_TYPE_CONTACT,
            ContactSubscription::FIELD_CONTACT_METHOD => ContactSubscription::CONTACT_METHOD_EMAIL,
            ContactSubscription::FIELD_NOTIFICATION_TYPE => ContactSubscription::NOTIFICATION_TYPE_SALESBAIT,
            ContactSubscription::FIELD_UNSUBSCRIBED => true,
        ];

        $contactSubscriptionsRes = $this->service->searchContactSubscriptions(
            $searchParams[ContactSubscription::FIELD_CONTACT_ID],
            $searchParams[ContactSubscription::FIELD_CONTACT_TYPE],
            $searchParams[ContactSubscription::FIELD_CONTACT_METHOD],
            $searchParams[ContactSubscription::FIELD_NOTIFICATION_TYPE],
            $searchParams[ContactSubscription::FIELD_UNSUBSCRIBED]
        );

        $contactSubscription = $contactSubscriptionsRes->first();

        $this->assertEquals($searchParams[ContactSubscription::FIELD_CONTACT_ID], $contactSubscription->{ContactSubscription::FIELD_CONTACT_ID});
        $this->assertEquals($searchParams[ContactSubscription::FIELD_CONTACT_TYPE], $contactSubscription->{ContactSubscription::FIELD_CONTACT_TYPE});
        $this->assertEquals($searchParams[ContactSubscription::FIELD_CONTACT_METHOD], $contactSubscription->{ContactSubscription::FIELD_CONTACT_METHOD});
        $this->assertEquals($searchParams[ContactSubscription::FIELD_NOTIFICATION_TYPE], $contactSubscription->{ContactSubscription::FIELD_NOTIFICATION_TYPE});
        $this->assertGreaterThan(0, $contactSubscription->{ContactSubscription::FIELD_UNSUBSCRIBED});

        /**
         * Single record
         */
        $searchParams = [
            ContactSubscription::FIELD_CONTACT_ID => 2,
            ContactSubscription::FIELD_CONTACT_TYPE => ContactSubscription::CONTACT_TYPE_USER,
            ContactSubscription::FIELD_CONTACT_METHOD => ContactSubscription::CONTACT_METHOD_EMAIL,
            ContactSubscription::FIELD_NOTIFICATION_TYPE => ContactSubscription::NOTIFICATION_TYPE_SALESBAIT,
            ContactSubscription::FIELD_UNSUBSCRIBED => false,
        ];

        $contactSubscriptionsRes = $this->service->searchContactSubscriptions(
            $searchParams[ContactSubscription::FIELD_CONTACT_ID],
            $searchParams[ContactSubscription::FIELD_CONTACT_TYPE],
            $searchParams[ContactSubscription::FIELD_CONTACT_METHOD],
            $searchParams[ContactSubscription::FIELD_NOTIFICATION_TYPE],
            $searchParams[ContactSubscription::FIELD_UNSUBSCRIBED]
        );

        $contactSubscription = $contactSubscriptionsRes->first();

        $this->assertEquals($searchParams[ContactSubscription::FIELD_CONTACT_ID], $contactSubscription->{ContactSubscription::FIELD_CONTACT_ID});
        $this->assertEquals($searchParams[ContactSubscription::FIELD_CONTACT_TYPE], $contactSubscription->{ContactSubscription::FIELD_CONTACT_TYPE});
        $this->assertEquals($searchParams[ContactSubscription::FIELD_CONTACT_METHOD], $contactSubscription->{ContactSubscription::FIELD_CONTACT_METHOD});
        $this->assertEquals($searchParams[ContactSubscription::FIELD_NOTIFICATION_TYPE], $contactSubscription->{ContactSubscription::FIELD_NOTIFICATION_TYPE});
        $this->assertEquals(0, $contactSubscription->{ContactSubscription::FIELD_UNSUBSCRIBED});

        /**
         * Multiple records
         */
        $searchParams = [
            ContactSubscription::FIELD_CONTACT_ID => null,
            ContactSubscription::FIELD_CONTACT_TYPE => null,
            ContactSubscription::FIELD_CONTACT_METHOD => null,
            ContactSubscription::FIELD_NOTIFICATION_TYPE => null,
            ContactSubscription::FIELD_UNSUBSCRIBED => true,
        ];

        $contactSubscriptionsRes = $this->service->searchContactSubscriptions(
            $searchParams[ContactSubscription::FIELD_CONTACT_ID],
            $searchParams[ContactSubscription::FIELD_CONTACT_TYPE],
            $searchParams[ContactSubscription::FIELD_CONTACT_METHOD],
            $searchParams[ContactSubscription::FIELD_NOTIFICATION_TYPE],
            $searchParams[ContactSubscription::FIELD_UNSUBSCRIBED]
        );

        $this->assertGreaterThan(1, $contactSubscriptionsRes->count());

        foreach ($contactSubscriptionsRes as $contactSubscription) {
            $this->assertGreaterThan(0, $contactSubscription->{ContactSubscription::FIELD_UNSUBSCRIBED});
        }

        /**
         * Multiple records
         */
        $searchParams = [
            ContactSubscription::FIELD_CONTACT_ID => null,
            ContactSubscription::FIELD_CONTACT_TYPE => null,
            ContactSubscription::FIELD_CONTACT_METHOD => ContactSubscription::CONTACT_METHOD_EMAIL,
            ContactSubscription::FIELD_NOTIFICATION_TYPE => null,
            ContactSubscription::FIELD_UNSUBSCRIBED => null,
        ];

        $contactSubscriptionsRes = $this->service->searchContactSubscriptions(
            $searchParams[ContactSubscription::FIELD_CONTACT_ID],
            $searchParams[ContactSubscription::FIELD_CONTACT_TYPE],
            $searchParams[ContactSubscription::FIELD_CONTACT_METHOD],
            $searchParams[ContactSubscription::FIELD_NOTIFICATION_TYPE],
            $searchParams[ContactSubscription::FIELD_UNSUBSCRIBED]
        );

        $this->assertGreaterThan(1, $contactSubscriptionsRes->count());

        foreach ($contactSubscriptionsRes as $contactSubscription) {
            $this->assertEquals(ContactSubscription::CONTACT_METHOD_EMAIL, $contactSubscription->{ContactSubscription::FIELD_CONTACT_METHOD});
        }

        /**
         * Non existent record
         */
        $searchParams = [
            ContactSubscription::FIELD_CONTACT_ID => 123,
        ];

        $contactSubscriptionsRes = $this->service->searchContactSubscriptions(
            $searchParams[ContactSubscription::FIELD_CONTACT_ID]
        );

        $this->assertEquals(0, $contactSubscriptionsRes->count());
    }

    public function test_unsubscribe()
    {
        /**
         * Test existing contact
         */
        $contactSubscriptionId = ContactSubscription::factory()->create()->id;

        $unsubscribeRes = $this->service->unsubscribe($contactSubscriptionId);

        $this->assertTrue($unsubscribeRes);

        $contactSubscription = ContactSubscription::query()->where(ContactSubscription::FIELD_ID, $contactSubscriptionId)->first();

        $this->assertGreaterThan(0, $contactSubscription->{ContactSubscription::FIELD_UNSUBSCRIBED});

        /**
         * Test non-existent contact
         */
        $contactSubscriptionId = 123;

        $unsubscribeRes = $this->service->unsubscribe($contactSubscriptionId);

        $this->assertFalse($unsubscribeRes);
    }

    public function test_delete_contact_subscription()
    {
        /**
         * Existing contact subscription
         */
        $contactSubscriptionId = ContactSubscription::factory()->create()->id;

        $this->assertNotSoftDeleted(ContactSubscription::TABLE, [ContactSubscription::FIELD_ID => $contactSubscriptionId]);

        $deleteRes = $this->service->deleteContactSubscription($contactSubscriptionId);

        $this->assertTrue($deleteRes);
        $this->assertSoftDeleted(ContactSubscription::TABLE, [ContactSubscription::FIELD_ID => $contactSubscriptionId]);

        /**
         * Non-existent contact subscription
         */
        $contactSubscriptionId = 123;

        $this->assertDatabaseMissing(ContactSubscription::TABLE, [ContactSubscription::FIELD_ID => $contactSubscriptionId]);

        $deleteRes = $this->service->deleteContactSubscription($contactSubscriptionId);

        $this->assertFalse($deleteRes);
    }
}
