<?php

namespace Tests\Feature\Http\Controllers\API;

use App\Models\CompanyUserRelationship;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Models\Odin\ProductAssignment;
use App\Models\User;
use Database\Seeders\DirectLeadsProductSeeder;
use Database\Seeders\ProductsSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;
use Throwable;

class CompanySearchControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->seed(ProductsSeeder::class);
        $this->seed(DirectLeadsProductSeeder::class);
    }

    #[Test]
    public function search(): void
    {
        $this->actingAs(User::factory()->create())
            ->post(route('internal-api.v1.company-search.search'), [
                'filters' => [],
            ])
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'status',
                    'companies' => [
                        'data',
                        'links' => [
                            'first',
                            'last',
                            'prev',
                            'next',
                        ],
                        'meta' => [
                            'current_page',
                            'from',
                            'last_page',
                            'links' => [
                                '*' => [
                                    'url',
                                    'label',
                                    'active',
                                ],
                            ],
                            'path',
                            'per_page',
                            'to',
                            'total',
                        ],
                    ],
                ],
            ]);
    }

    #[Test, DataProvider('roleProvider')]
    public function search_with_role_filterables($role, $filter): void
    {
        $company = Company::factory()->createQuietly();

        Company::factory()->createQuietly();

        $user = User::factory()->create();

        $company->assign($user)->as($role);

        $data = [
            'filters' => [
                $filter => [$user->id, 'test'],
            ],
        ];

        $this->actingAs($user)
            ->post(route('internal-api.v1.company-search.search'), $data)
            ->assertOk()
            ->assertJsonCount(1, 'data.companies.data')
            ->assertJsonPath('data.companies.data.0.id', $company->id);
    }

    #[Test, DataProvider('roleProvider')]
    public function search_with_role_filterables_empty($role, $filter): void
    {
        $company = Company::factory()->createQuietly();

        Company::factory()->createQuietly();

        $user = User::factory()->create();

        $company->assign($user)->as($role);

        $data = [
            'filters' => [
                $filter => [],
            ],
        ];

        $this->actingAs($user)
            ->post(route('internal-api.v1.company-search.search'), $data)
            ->assertOk()
            ->assertJsonCount(2, 'data.companies.data');
    }

    #[Test, DataProvider('roleProvider')]
    public function search_with_role_filterables_single_numeric($role, $filter): void
    {
        $company = Company::factory()->createQuietly();

        Company::factory()->createQuietly();

        $user = User::factory()->create();

        $company->assign($user)->as($role);

        $data = [
            'filters' => [
                $filter => $user->id,
            ],
        ];

        $this->actingAs($user)
            ->post(route('internal-api.v1.company-search.search'), $data)
            ->assertOk()
            ->assertJsonCount(1, 'data.companies.data')
            ->assertJsonPath('data.companies.data.0.id', $company->id);
    }

    #[Test, DataProvider('roleProvider')]
    public function search_with_role_filterables_single_string($role, $filter): void
    {
        $company = Company::factory()->createQuietly();

        Company::factory()->createQuietly();

        $user = User::factory()->create();

        $company->assign($user)->as($role);

        $data = [
            'filters' => [
                $filter => 'test',
            ],
        ];

        $this->actingAs($user)
            ->post(route('internal-api.v1.company-search.search'), $data)
            ->assertOk()
            ->assertJsonCount(2, 'data.companies.data');
    }

    /**
     * @see \App\Services\Filterables\Company\CompanyAccountManagerFilterable
     * @see \App\Services\Filterables\Company\CompanyBusinessDevelopmentManagerFilterable
     * @see \App\Services\Filterables\Company\CompanySalesDevelopmentRepresentativeFilterable
     */
    public static function roleProvider(): array
    {
        return [
            'Account Manager' => [
                'account-manager',
                'company-account-manager',
            ],
            'Business Development Manager' => [
                'business-development-manager',
                'company-business-development-manager',
            ],
            'Sales Development Representative' => [
                'sales-development-representative',
                'company-sales-development-representative',
            ],
        ];
    }

    #[Test, DataProvider('qrTop500TrueProvider')]
    public function search_with_qr_top_500_companies_filter_on_true(mixed $option, int $count): void
    {
        Company::factory()->has(CompanyData::factory(state: [
            'payload' => [
                'qr_top_500_company' => 1,
            ],
        ]), 'data')->createQuietly();

        $this->actingAs(User::factory()->create())
            ->post(route('internal-api.v1.company-search.search'), [
                'filters' => [
                    'company-qr-top-500' => $option,
                ],
            ])
            ->assertOk()
            ->assertJsonCount($count, 'data.companies.data');
    }

    public static function qrTop500TrueProvider(): array
    {
        return [
            'true as bool' => [true, 1],
            'true as string' => ['true', 1],
            'true as int' => [1, 1],
            'false as bool' => [false, 0],
            'false as string' => ['false', 0],
            'false as int' => [0, 0],
        ];
    }

    #[Test, DataProvider('qrTop500FalseProvider')]
    public function search_with_qr_top_500_companies_filter_on_false(mixed $option, int $count): void
    {
        Company::factory()->has(CompanyData::factory(state: [
            'payload' => [
                'qr_top_500_company' => 0,
            ],
        ]), 'data')->createQuietly();

        $this->actingAs(User::factory()->create())
            ->post(route('internal-api.v1.company-search.search'), [
                'filters' => [
                    'company-qr-top-500' => $option,
                ],
            ])
            ->assertOk()
            ->assertJsonCount($count, 'data.companies.data');
    }

    public static function qrTop500FalseProvider(): array
    {
        return [
            'false as bool' => [false, 1],
            'false as string' => ['false', 1],
            'false as int' => [0, 1],
            'true as bool' => [true, 0],
            'true as string' => ['true', 0],
            'true as int' => [1, 0],
        ];
    }

    /**
     * @throws Throwable
     */
    #[Test]
    public function search_with_missing_role_assignment_filterables(): void
    {
        $this->createRoleAndAssignToCompany('account-manager');
        $this->createRoleAndAssignToCompany('business-development-manager');
        $this->createRoleAndAssignToCompany('sales-development-representative');
        $this->createRoleAndAssignToCompany('customer-success-manager');
        $this->createRoleAndAssignToCompany('onboarding-manager');

        $filteredInCompany = Company::factory()->createQuietly();

        $this->assertDatabaseCount('companies', 6);

        $this->actingAs(User::factory()->create())
            ->post(route('internal-api.v1.company-search.search'), [
                'filters' => [
                    'company-missing-role-assignments' => [
                        'account-manager',
                        'business-development-manager',
                        'sales-development-representative',
                        'customer-success-manager',
                        'onboarding-manager',
                    ],
                ],
            ])
            ->assertOk()
            ->assertJsonCount(1, 'data.companies.data')
            ->assertJsonPath('data.companies.data.0.id', $filteredInCompany->id);
    }

    /**
     * @throws Throwable
     */
    #[Test]
    public function search_with_missing_role_assignment_filterables_with_soft_deleted_user_relationship(): void
    {
        $this->createRoleAndAssignToCompany('account-manager');
        $this->createRoleAndAssignToCompany('business-development-manager');
        $this->createRoleAndAssignToCompany('sales-development-representative');
        $this->createRoleAndAssignToCompany('customer-success-manager');
        $this->createRoleAndAssignToCompany('onboarding-manager');

        $companyWithAccountManager = Company::whereHas('accountManager')->first();

        $companyWithAccountManager->unassign('account-manager');

        $filteredInCompany = Company::factory()->createQuietly();

        $this->assertDatabaseCount('companies', 6);

        $this->actingAs(User::factory()->create())
            ->post(route('internal-api.v1.company-search.search'), [
                'filters' => [
                    'company-missing-role-assignments' => [
                        'account-manager',
                        'business-development-manager',
                        'sales-development-representative',
                        'customer-success-manager',
                        'onboarding-manager',
                    ],
                ],
            ])
            ->assertOk()
            ->assertJsonCount(2, 'data.companies.data')
            ->assertJsonPath('data.companies.data.0.id', $companyWithAccountManager->id)
            ->assertJsonPath('data.companies.data.1.id', $filteredInCompany->id);
    }

    /**
     * @throws Throwable
     */
    #[Test]
    public function search_with_missing_role_assignment_filterables_with_invalid_option(): void
    {
        $companyWithInvalidOptionRole = $this->createRoleAndAssignToCompany('invalid-option');
        $this->createRoleAndAssignToCompany('business-development-manager');
        $this->createRoleAndAssignToCompany('sales-development-representative');
        $this->createRoleAndAssignToCompany('customer-success-manager');
        $this->createRoleAndAssignToCompany('onboarding-manager');

        $filteredInCompany = Company::factory()->createQuietly();

        $this->assertDatabaseCount('companies', 6);

        $this->actingAs(User::factory()->create())
            ->post(route('internal-api.v1.company-search.search'), [
                'filters' => [
                    'company-missing-role-assignments' => [
                        'invalid-option',
                        'business-development-manager',
                        'sales-development-representative',
                        'customer-success-manager',
                        'onboarding-manager',
                    ],
                ],
            ])
            ->assertOk()
            ->assertJsonCount(2, 'data.companies.data')
            ->assertJsonPath('data.companies.data.0.id', $companyWithInvalidOptionRole->id)
            ->assertJsonPath('data.companies.data.1.id', $filteredInCompany->id);
    }

    /**
     * @throws Throwable
     */
    private function createRoleAndAssignToCompany(string $roleName): Company
    {
        $role = Role::findOrCreate($roleName);

        $company = Company::factory()->createQuietly();

        $company->assign(User::factory()->create())->as($role->name);

        return $company;
    }

    /**
     * @throws Throwable
     */
    #[Test, DataProvider('first_lead_purchased_date_filterable')]
    public function search_with_first_lead_purchased_date_filterable(int $count, string $startDate, string $endDate): void
    {
        Company::factory()->has(ProductAssignment::factory(state: [
            'created_at' => Carbon::parse('2025-03-21'),
            'chargeable' => true,
            'delivered' => true,
        ]))->createQuietly();

        $this->actingAs(User::factory()->create())
            ->post(route('internal-api.v1.company-search.search'), [
                'filters' => [
                    'company-first-lead-purchased-date-range' => [
                        'from' => Carbon::parse($startDate),
                        'to' => Carbon::parse($endDate),
                    ],
                ],
            ])
            ->assertOk()
            ->assertJsonCount($count, 'data.companies.data');
    }

    public static function first_lead_purchased_date_filterable(): array
    {
        return [
            'returns 1 due to being within range' => [1, '2025-03-20', '2025-03-30'],
            'returns 0 due to being less than start date' => [0, '2025-03-22', '2025-03-30'],
            'returns 0 due to being more than end date' => [0, '2025-03-01', '2025-03-20'],
            'return 1 due to being same as start date' => [1, '2025-03-21', '2025-03-30'],
            'returns 1 due to being same as end date' => [1, '2025-03-10', '2025-03-21'],
        ];
    }

    /**
     * We can use this to find companies with recent first leads purchased that don't yet have an onboarding manager.
     */
    #[Test]
    public function search_with_first_lead_purchased_date_filterable_and_missing_onboarding_manager(): void
    {
        Company::factory()->has(ProductAssignment::factory(state: [
            'created_at' => Carbon::parse('2025-03-21'),
            'chargeable' => true,
            'delivered' => true,
        ]))->createQuietly(); // Will be filtered in

        Company::factory()->createQuietly(); // Will be filtered out due to not having any first lead purchased

        Company::factory()->has(ProductAssignment::factory(state: [
            'created_at' => Carbon::parse('2025-03-21'),
            'chargeable' => false,
            'delivered' => true,
        ]))->createQuietly(); // Will be filtered out due to not having a first lead purchased that's chargeable

        Company::factory()->has(ProductAssignment::factory(state: [
            'created_at' => Carbon::parse('2025-03-21'),
            'chargeable' => true,
            'delivered' => false,
        ]))->createQuietly(); // Will be filtered out due to not having a first lead purchased that's delivered

        Company::factory()->has(ProductAssignment::factory(state: [
            'created_at' => Carbon::parse('2021-06-07'),
            'chargeable' => true,
            'delivered' => true,
        ]))->createQuietly(); // Will be filtered out due to first lead purchased being way in the past

        Company::factory()
            ->has(ProductAssignment::factory(state: [
                'created_at' => Carbon::parse('2025-03-21'),
                'chargeable' => true,
                'delivered' => true,
            ]))
            ->has(CompanyUserRelationship::factory(state: [
                'role_id' => Role::findOrCreate('onboarding-manager')->id,
            ]), 'userRelationships')
            ->createQuietly(); // Will be filtered out due to having an onboarding manager

        $this->actingAs(User::factory()->create())
            ->post(route('internal-api.v1.company-search.search'), [
                'filters' => [
                    'company-missing-role-assignments' => [
                        'onboarding-manager',
                    ],
                    'company-first-lead-purchased-date-range' => [
                        'from' => Carbon::parse('2025-03-21'),
                        'to' => Carbon::parse('2025-03-30'),
                    ],
                ],
            ])
            ->assertOk()
            ->assertJsonCount(1, 'data.companies.data');
    }
}
