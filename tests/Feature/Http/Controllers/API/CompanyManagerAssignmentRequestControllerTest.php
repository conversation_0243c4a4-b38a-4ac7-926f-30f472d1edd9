<?php

namespace Tests\Feature\Http\Controllers\API;

use App\Models\CompanyManagerAssignmentRequest;
use App\Models\CompanyUserRelationship;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class CompanyManagerAssignmentRequestControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    public static function routes(): array
    {
        return [
            'get-pending-requests' => [
                'get', fn () => route('internal-api.v2.company-manager-assignment-requests.get-pending-requests'),
            ],
            'get-request-history' => ['get', fn () => route('internal-api.v2.company-manager-assignment-requests.get-request-history')],
            'approve' => ['patch', fn () => route('internal-api.v2.company-manager-assignment-requests.approve', 1)],
            'deny' => ['patch', fn () => route('internal-api.v2.company-manager-assignment-requests.deny', 1)],
            'get-my-requests' => [
                'get', fn () => route('internal-api.v2.company-manager-assignment-requests.get-my-requests'),
            ],
            'request-assignment' => [
                'post', fn () => route('internal-api.v2.company-manager-assignment-requests.request-assignment'),
            ],
        ];
    }

    public static function roles(): array
    {
        return [
            ['account-manager'],
            ['business-development-manager'],
            ['customer-success-manager'],
            ['onboarding-manager'],
            ['sales-development-representative'],
        ];
    }

    protected function setUp(): void
    {
        parent::setUp();

        Permission::findOrCreate('action-company-manager-assignment-requests');

        Role::findOrCreate('account-manager');
        Role::findOrCreate('business-development-manager');
        Role::findOrCreate('customer-success-manager');
        Role::findOrCreate('onboarding-manager');
        Role::findOrCreate('sales-development-representative');

        Role::findOrCreate('admin');
    }

    #[Test, DataProvider('routes')]
    public function it_fails_if_not_authenticated(string $method, callable $route): void
    {
        $this->{$method}($route())->assertRedirectToRoute('login');
    }

    #[Test]
    public function get_pending_requests_with_it_is_forbidden_if_no_permission(): void
    {
        $this->user = User::factory()->create();

        $this->actingAs($this->user)
            ->get(route('internal-api.v2.company-manager-assignment-requests.get-pending-requests'))
            ->assertForbidden();
    }

    #[Test]
    public function get_pending_requests_with_it_returns_empty_requests_if_no_company_manager_assignment_requests(): void
    {
        $this->user = User::factory()->create()->givePermissionTo('action-company-manager-assignment-requests');

        $this->actingAs($this->user)
            ->get(route('internal-api.v2.company-manager-assignment-requests.get-pending-requests'))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    'requests' => [],
                ],
            ]);
    }

    #[Test]
    public function get_pending_requests_with_it_returns_full_requests_if_there_are_company_manager_assignment_requests(): void
    {
        $this->user = User::factory()->create()->givePermissionTo('action-company-manager-assignment-requests');

        $companyManagerAssignmentRequest = CompanyManagerAssignmentRequest::factory()->createQuietly([
            'status' => 'pending',
            'is_unread' => true,
        ]);

        $this->actingAs($this->user)
            ->get(route('internal-api.v2.company-manager-assignment-requests.get-pending-requests'))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    'requests' => [
                        [
                            'id' => $companyManagerAssignmentRequest->id,
                            'status' => $companyManagerAssignmentRequest->status,
                            'role' => str($companyManagerAssignmentRequest->role->name)->headline(),
                            'user' => $companyManagerAssignmentRequest->user->only('id', 'name'),
                            'company' => [
                                'id' => $companyManagerAssignmentRequest->company->id,
                                'name' => $companyManagerAssignmentRequest->company->name,
                                'profile_link' => $companyManagerAssignmentRequest->company->getAdminProfileUrl(),
                            ],
                            'current_owner' => null,
                            'requested_by_current_owner' => false,
                            'date_decided' => null,
                            'date_requested' => $companyManagerAssignmentRequest->created_at->format('F d, Y h:i a T'),
                            'deciding_user' => null,
                        ],
                    ],
                ],
            ]);

        $this->assertDatabaseHas('company_manager_assignment_requests', [
            'user_id' => $companyManagerAssignmentRequest->user->id,
            'company_id' => $companyManagerAssignmentRequest->company->id,
            'status' => 'pending',
            'is_unread' => false,
        ]);
    }

    #[Test]
    public function get_pending_requests_with_it_returns_full_requests_and_it_only_updates_received_requests_to_be_read(): void
    {
        $this->user = User::factory()->create()->givePermissionTo('action-company-manager-assignment-requests');

        CompanyManagerAssignmentRequest::factory()->createQuietly([
            'status' => 'pending',
            'is_unread' => true,
        ]);

        CompanyManagerAssignmentRequest::factory()->createQuietly([
            'status' => 'not-pending',
            'is_unread' => true,
        ]);

        $this->actingAs($this->user)
            ->get(route('internal-api.v2.company-manager-assignment-requests.get-pending-requests'))
            ->assertOk();

        $this->assertCount(1, CompanyManagerAssignmentRequest::where('is_unread', false)->get());
    }

    #[Test]
    public function get_pending_requests_with_it_returns_full_requests_with_current_owner_if_exists(): void
    {
        $this->user = User::factory()->create()->givePermissionTo('action-company-manager-assignment-requests');

        $companyManagerAssignmentRequest = CompanyManagerAssignmentRequest::factory()->createQuietly([
            'status' => 'pending',
            'is_unread' => true,
        ]);

        $companyManagerRelationship = CompanyUserRelationship::factory()->createQuietly([
            'company_id' => $companyManagerAssignmentRequest->company->id,
            'role_id' => $companyManagerAssignmentRequest->role->id,
        ]);

        $this->actingAs($this->user)
            ->get(route('internal-api.v2.company-manager-assignment-requests.get-pending-requests'))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    'requests' => [
                        [
                            'id' => $companyManagerAssignmentRequest->id,
                            'status' => $companyManagerAssignmentRequest->status,
                            'role' => str($companyManagerAssignmentRequest->role->name)->headline(),
                            'user' => $companyManagerAssignmentRequest->user->only('id', 'name'),
                            'company' => [
                                'id' => $companyManagerAssignmentRequest->company->id,
                                'name' => $companyManagerAssignmentRequest->company->name,
                                'profile_link' => $companyManagerAssignmentRequest->company->getAdminProfileUrl(),
                            ],
                            'current_owner' => $companyManagerRelationship->user->name,
                            'requested_by_current_owner' => false,
                            'date_decided' => null,
                            'date_requested' => $companyManagerAssignmentRequest->created_at->format('F d, Y h:i a T'),
                            'deciding_user' => null,
                        ],
                    ],
                ],
            ]);

        $this->assertDatabaseHas('company_manager_assignment_requests', [
            'user_id' => $companyManagerAssignmentRequest->user->id,
            'company_id' => $companyManagerAssignmentRequest->company->id,
            'status' => 'pending',
            'is_unread' => false,
        ]);
    }

    #[Test]
    public function get_pending_requests_with_it_returns_full_requests_with_current_owner_and_requested_by_current_owner_is_true(): void
    {
        $this->user = User::factory()->create()->givePermissionTo('action-company-manager-assignment-requests');

        $companyManagerAssignmentRequest = CompanyManagerAssignmentRequest::factory()->createQuietly([
            'status' => 'pending',
            'is_unread' => true,
        ]);

        $companyManagerRelationship = CompanyUserRelationship::factory()->createQuietly([
            'user_id' => $companyManagerAssignmentRequest->user->id,
            'company_id' => $companyManagerAssignmentRequest->company->id,
            'role_id' => $companyManagerAssignmentRequest->role->id,
        ]);

        $this->actingAs($this->user)
            ->get(route('internal-api.v2.company-manager-assignment-requests.get-pending-requests'))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    'requests' => [
                        [
                            'id' => $companyManagerAssignmentRequest->id,
                            'status' => $companyManagerAssignmentRequest->status,
                            'role' => str($companyManagerAssignmentRequest->role->name)->headline(),
                            'user' => $companyManagerAssignmentRequest->user->only('id', 'name'),
                            'company' => [
                                'id' => $companyManagerAssignmentRequest->company->id,
                                'name' => $companyManagerAssignmentRequest->company->name,
                                'profile_link' => $companyManagerAssignmentRequest->company->getAdminProfileUrl(),
                            ],
                            'current_owner' => $companyManagerRelationship->user->name,
                            'requested_by_current_owner' => true,
                            'date_decided' => null,
                            'date_requested' => $companyManagerAssignmentRequest->created_at->format('F d, Y h:i a T'),
                            'deciding_user' => null,
                        ],
                    ],
                ],
            ]);

        $this->assertDatabaseHas('company_manager_assignment_requests', [
            'user_id' => $companyManagerAssignmentRequest->user->id,
            'company_id' => $companyManagerAssignmentRequest->company->id,
            'status' => 'pending',
            'is_unread' => false,
        ]);
    }

    #[Test]
    public function get_request_history_with_it_is_forbidden_if_no_permission(): void
    {
        $this->user = User::factory()->create();

        $this->actingAs($this->user)
            ->get(route('internal-api.v2.company-manager-assignment-requests.get-request-history'))
            ->assertForbidden();
    }

    #[Test]
    public function get_request_history_with_it_returns_empty_requests_if_no_company_manager_assignment_requests(): void
    {
        $this->user = User::factory()->create()->givePermissionTo('action-company-manager-assignment-requests');

        $this->actingAs($this->user)
            ->get(route('internal-api.v2.company-manager-assignment-requests.get-request-history'))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    'requests' => [],
                ],
            ]);
    }

    #[Test]
    public function get_request_history_with_it_returns_full_requests(): void
    {
        $this->user = User::factory()->create()->givePermissionTo('action-company-manager-assignment-requests');

        $approvedAt = Carbon::create(2025, 04, 27, 12);

        $companyManagerAssignmentRequestOne = CompanyManagerAssignmentRequest::factory()->decided(decidedAt: $approvedAt, status: 'approved')->createQuietly([
            'created_at' => Carbon::create(2023, 04, 27, 12),
        ]); // Came in earlier so should be the least recent

        $companyManagerAssignmentRequestTwo = CompanyManagerAssignmentRequest::factory()->decided(decidedAt: $approvedAt, status: 'denied')->createQuietly([
            'created_at' => Carbon::create(2023, 04, 27, 14),
        ]); // Came in later so should be the most recent

        $requests = [];

        foreach ([$companyManagerAssignmentRequestTwo, $companyManagerAssignmentRequestOne] as $companyManagerAssignmentRequest) {
            $requests[] = [
                'id' => $companyManagerAssignmentRequest->id,
                'status' => $companyManagerAssignmentRequest->status,
                'role' => str($companyManagerAssignmentRequest->role->name)->headline(),
                'user' => $companyManagerAssignmentRequest->user->only('id', 'name'),
                'company' => [
                    'id' => $companyManagerAssignmentRequest->company->id,
                    'name' => $companyManagerAssignmentRequest->company->name,
                    'profile_link' => $companyManagerAssignmentRequest->company->getAdminProfileUrl(),
                ],
                'current_owner' => null,
                'requested_by_current_owner' => false,
                'date_decided' => $approvedAt->format('F d, Y h:i a T'),
                'date_requested' => $companyManagerAssignmentRequest->created_at->format('F d, Y h:i a T'),
                'deciding_user' => $companyManagerAssignmentRequest->deciding_user->only('id', 'name'),
            ];
        }

        $this->actingAs($this->user)
            ->get(route('internal-api.v2.company-manager-assignment-requests.get-request-history'))
            ->assertOk()
            ->assertExactJson([
                'data' => compact('requests'),
            ]);
    }

    #[Test]
    public function get_request_history_with_it_is_approved_but_has_no_deciding_user_and_date(): void
    {
        $this->user = User::factory()->create()->givePermissionTo('action-company-manager-assignment-requests');

        $companyManagerAssignmentRequest = CompanyManagerAssignmentRequest::factory()->createQuietly([
            'status' => 'approved',
            'is_unread' => false,
        ]);

        $this->actingAs($this->user)
            ->get(route('internal-api.v2.company-manager-assignment-requests.get-request-history'))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    'requests' => [
                        [
                            'id' => $companyManagerAssignmentRequest->id,
                            'status' => $companyManagerAssignmentRequest->status,
                            'role' => str($companyManagerAssignmentRequest->role->name)->headline(),
                            'user' => $companyManagerAssignmentRequest->user->only('id', 'name'),
                            'company' => [
                                'id' => $companyManagerAssignmentRequest->company->id,
                                'name' => $companyManagerAssignmentRequest->company->name,
                                'profile_link' => $companyManagerAssignmentRequest->company->getAdminProfileUrl(),
                            ],
                            'current_owner' => null,
                            'requested_by_current_owner' => false,
                            'date_decided' => null,
                            'date_requested' => $companyManagerAssignmentRequest->created_at->format('F d, Y h:i a T'),
                            'deciding_user' => null,
                        ],
                    ],
                ],
            ]);
    }

    #[Test]
    public function approve_with_it_is_forbidden_if_no_permission(): void
    {
        $this->user = User::factory()->create();

        $companyManagerAssignmentRequest = CompanyManagerAssignmentRequest::factory()->createQuietly([
            'status' => 'pending',
            'is_unread' => true,
        ]);

        $this->actingAs($this->user)
            ->patch(route('internal-api.v2.company-manager-assignment-requests.approve',
                $companyManagerAssignmentRequest))
            ->assertForbidden();

        $this->assertDatabaseHas('company_manager_assignment_requests', [
            'company_id' => $companyManagerAssignmentRequest->company->id,
            'user_id' => $companyManagerAssignmentRequest->user->id,
            'role_id' => $companyManagerAssignmentRequest->role->id,
            'status' => 'pending',
            'is_unread' => true,
        ]);
    }

    #[Test]
    public function approve_with_it_is_ok_if_permission(): void
    {
        $this->user = User::factory()->create()->givePermissionTo('action-company-manager-assignment-requests');

        $companyManagerAssignmentRequest = CompanyManagerAssignmentRequest::factory()->createQuietly([
            'status' => 'pending',
            'is_unread' => true,
        ]);

        $this->actingAs($this->user)
            ->patch(route('internal-api.v2.company-manager-assignment-requests.approve',
                $companyManagerAssignmentRequest))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    'id' => $companyManagerAssignmentRequest->id,
                    'status' => 'approved',
                    'role' => str($companyManagerAssignmentRequest->role->name)->headline(),
                    'user' => $companyManagerAssignmentRequest->user->only('id', 'name'),
                    'company' => [
                        'id' => $companyManagerAssignmentRequest->company->id,
                        'name' => $companyManagerAssignmentRequest->company->name,
                        'profile_link' => $companyManagerAssignmentRequest->company->getAdminProfileUrl(),
                    ],
                    'date_decided' => $companyManagerAssignmentRequest->refresh()->decided_at->format('F d, Y h:i a T'),
                    'date_requested' => $companyManagerAssignmentRequest->created_at->format('F d, Y h:i a T'),
                    'deciding_user' => $companyManagerAssignmentRequest->deciding_user->only('id', 'name'),
                ],
            ]);

        $this->assertDatabaseHas('company_manager_assignment_requests', [
            'company_id' => $companyManagerAssignmentRequest->company->id,
            'user_id' => $companyManagerAssignmentRequest->user->id,
            'role_id' => $companyManagerAssignmentRequest->role->id,
            'status' => 'approved',
            'is_unread' => false,
        ]);

        $this->assertDatabaseHas('company_user_relationships', [
            'company_id' => $companyManagerAssignmentRequest->company->id,
            'user_id' => $companyManagerAssignmentRequest->user->id,
            'role_id' => $companyManagerAssignmentRequest->role->id,
        ]);
    }

    #[Test]
    public function deny_with_it_is_forbidden_if_no_permission(): void
    {
        $this->user = User::factory()->create();

        $companyManagerAssignmentRequest = CompanyManagerAssignmentRequest::factory()->createQuietly([
            'status' => 'pending',
            'is_unread' => true,
        ]);

        $this->actingAs($this->user)
            ->patch(route('internal-api.v2.company-manager-assignment-requests.deny', $companyManagerAssignmentRequest))
            ->assertForbidden();

        $this->assertDatabaseHas('company_manager_assignment_requests', [
            'company_id' => $companyManagerAssignmentRequest->company->id,
            'user_id' => $companyManagerAssignmentRequest->user->id,
            'role_id' => $companyManagerAssignmentRequest->role->id,
            'status' => 'pending',
            'is_unread' => true,
        ]);
    }

    #[Test]
    public function deny_with_it_is_ok_if_permission(): void
    {
        CompanyUserRelationship::query()->forceDelete();

        $this->user = User::factory()->create()->givePermissionTo('action-company-manager-assignment-requests');

        $companyManagerAssignmentRequest = CompanyManagerAssignmentRequest::factory()->createQuietly([
            'status' => 'pending',
            'is_unread' => true,
        ]);

        $this->actingAs($this->user)
            ->patch(route('internal-api.v2.company-manager-assignment-requests.deny', $companyManagerAssignmentRequest))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    'id' => $companyManagerAssignmentRequest->id,
                    'status' => 'denied',
                    'role' => str($companyManagerAssignmentRequest->role->name)->headline(),
                    'user' => $companyManagerAssignmentRequest->user->only('id', 'name'),
                    'company' => [
                        'id' => $companyManagerAssignmentRequest->company->id,
                        'name' => $companyManagerAssignmentRequest->company->name,
                        'profile_link' => $companyManagerAssignmentRequest->company->getAdminProfileUrl(),
                    ],
                    'date_decided' => $companyManagerAssignmentRequest->refresh()->decided_at->format('F d, Y h:i a T'),
                    'date_requested' => $companyManagerAssignmentRequest->created_at->format('F d, Y h:i a T'),
                    'deciding_user' => $companyManagerAssignmentRequest->deciding_user->only('id', 'name'),
                ],
            ]);

        $this->assertDatabaseHas('company_manager_assignment_requests', [
            'company_id' => $companyManagerAssignmentRequest->company->id,
            'user_id' => $companyManagerAssignmentRequest->user->id,
            'role_id' => $companyManagerAssignmentRequest->role->id,
            'status' => 'denied',
            'is_unread' => false,
        ]);

        $this->assertDatabaseCount('company_user_relationships', 0);
    }

    #[Test]
    public function get_my_requests_with_it_gets_requests_for_authenticated_user_only(): void
    {
        $this->user = User::factory()->create()->givePermissionTo('action-company-manager-assignment-requests');

        $companyManagerAssignmentRequest = CompanyManagerAssignmentRequest::factory()->createQuietly([
            'user_id' => $this->user->id,
        ]);

        CompanyManagerAssignmentRequest::factory()->createQuietly();

        $this->actingAs($this->user)
            ->get(route('internal-api.v2.company-manager-assignment-requests.get-my-requests'))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    'requests' => [
                        [
                            'id' => $companyManagerAssignmentRequest->id,
                            'status' => $companyManagerAssignmentRequest->refresh()->status,
                            'role' => str($companyManagerAssignmentRequest->role->name)->headline(),
                            'user' => $companyManagerAssignmentRequest->user->only('id', 'name'),
                            'company' => [
                                'id' => $companyManagerAssignmentRequest->company->id,
                                'name' => $companyManagerAssignmentRequest->company->name,
                                'profile_link' => $companyManagerAssignmentRequest->company->getAdminProfileUrl(),
                            ],
                            'current_owner' => null,
                            'requested_by_current_owner' => false,
                            'date_decided' => null,
                            'date_requested' => $companyManagerAssignmentRequest->created_at->format('F d, Y h:i a T'),
                            'deciding_user' => null,
                        ],
                    ],
                ],
            ]);
    }

    #[Test, DataProvider('roles')]
    public function request_assignment_with_it_requests_for_given_role(string $role): void
    {
        $this->user = User::factory()->create()->givePermissionTo('action-company-manager-assignment-requests');

        $company = Company::factory()->createQuietly();

        $this->actingAs($this->user)
            ->post(route('internal-api.v2.company-manager-assignment-requests.request-assignment'), [
                'company_id' => $company->id,
                'role' => $role,
            ])
            ->assertCreated()
            ->assertExactJson([
                'data' => [
                    'id' => CompanyManagerAssignmentRequest::first()->id,
                    'status' => 'pending',
                    'role' => str(Role::findByName($role)->name)->headline(),
                    'user' => $this->user->only('id', 'name'),
                    'company' => [
                        'id' => $company->id,
                        'name' => $company->name,
                        'profile_link' => $company->getAdminProfileUrl(),
                    ],
                    'date_decided' => null,
                    'date_requested' => CompanyManagerAssignmentRequest::first()->created_at->format('F d, Y h:i a T'),
                    'deciding_user' => null,
                ],
            ]);

        $this->assertDatabaseHas('company_manager_assignment_requests', [
            'user_id' => $this->user->id,
            'company_id' => $company->id,
            'role_id' => Role::findByName($role)->id,
            'status' => 'pending',
            'is_unread' => true,
        ]);
    }

    #[Test]
    public function request_assignment_with_it_does_not_request_for_invalid_role(): void
    {
        $this->user = User::factory()->create()->givePermissionTo('action-company-manager-assignment-requests');

        $company = Company::factory()->createQuietly();

        $this->actingAs($this->user)
            ->post(route('internal-api.v2.company-manager-assignment-requests.request-assignment'), [
                'company_id' => $company->id,
                'role' => 'admin',
            ])
            ->assertRedirect()
            ->assertSessionHasErrors(['role'])
            ->assertSessionDoesntHaveErrors(['company_id']);
    }

    #[Test]
    public function request_assignment_with_it_does_not_request_for_missing_company(): void
    {
        $this->user = User::factory()->create()->givePermissionTo('action-company-manager-assignment-requests');

        $company = Company::factory()->createQuietly();

        $company->delete();

        $this->actingAs($this->user)
            ->post(route('internal-api.v2.company-manager-assignment-requests.request-assignment'), [
                'company_id' => $company->id,
                'role' => 'account-manager',
            ])
            ->assertRedirect()
            ->assertSessionHasErrors(['company_id'])
            ->assertSessionDoesntHaveErrors(['role']);
    }

    #[Test]
    public function request_assignment_with_it_does_not_request_for_valid_role_that_is_missing(): void
    {
        $this->user = User::factory()->create()->givePermissionTo('action-company-manager-assignment-requests');

        $company = Company::factory()->createQuietly();

        Role::findByName('account-manager')?->delete();

        $this->actingAs($this->user)
            ->post(route('internal-api.v2.company-manager-assignment-requests.request-assignment'), [
                'company_id' => $company->id,
                'role' => 'account-manager',
            ])
            ->assertRedirect()
            ->assertSessionHasErrors(['role'])
            ->assertSessionDoesntHaveErrors(['company_id']);
    }
}
