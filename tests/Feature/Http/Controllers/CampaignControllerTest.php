<?php

namespace Tests\Feature\Http\Controllers;

use App\Enums\Campaigns\CampaignStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseTruncation;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CampaignControllerTest extends TestCase
{
    use DatabaseTruncation;

    protected array $connectionsToTruncate = ['mysql', 'readonly'];

    protected array $tablesToTruncate = [
        'mysql' => ['companies', 'users', 'company_industries', 'task_categories', 'industries', 'industry_services', 'product_assignments', 'company_campaign_location_modules', 'company_campaign_location_module_locations', 'budget_containers'],
        'readonly' => ['locations', 'tblcompany', 'non_purchasing_company_locations'],
    ];

    #[Test]
    public function it_does_not_show_table_if_missing_zip_code(): void
    {
        $this->actingAs(User::factory()->create())
            ->get(route('experimental.campaigns.index'))
            ->assertOk()
            ->assertViewIs('campaigns.index')
            ->assertSeeInOrder([
                'Select an',
                'industry',
                'Apply Filters',
                'reset',
            ])
            ->assertDontSee([
                'Name',
                'Company',
                'Service and Industry',
            ]);
    }

    #[Test]
    public function it_shows_table_if_not_missing_zip_code(): void
    {
        $this->actingAs(User::factory()->create())
            ->get(route('experimental.campaigns.index', [
                'zip_code' => '12345',
            ]))
            ->assertOk()
            ->assertViewIs('campaigns.index')
            ->assertSeeInOrder([
                'Select an',
                'industry',
                'Apply Filters',
                'reset',
                'Name',
                'Company',
                'Service and Industry',
                'Date of Last Lead Purchased',
                'No campaigns found',
            ]);
    }

    #[Test]
    public function it_shows_full_table_if_there_is_a_campaign_with_the_given_zip_code(): void
    {
        $company = Company::factory()->createQuietly([
            'name' => 'Company Inc.',
            'consolidated_status' => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS,
        ]);

        $companyCampaign = CompanyCampaign::factory()
            ->location([
                'zip_code' => '12345',
            ])->dateOfLastLeadPurchased(
                countOfProductAssignments: 2,
                dateOfLatest: now(),
            )->createQuietly([
                'company_id' => $company->id,
                'name' => 'Company Campaign',
                'status' => CampaignStatus::ACTIVE,
            ]);

        $productAssignment = ProductAssignment::orderByDesc('created_at')->first();

        $this->actingAs(User::factory()->createQuietly())
            ->get(route('experimental.campaigns.index', [
                'zip_code' => '12345',
            ]))
            ->assertSeeInOrder([
                $companyCampaign->name,
                $company->name,
                $companyCampaign->service->name,
                $companyCampaign->service->industry->name,
                $productAssignment->created_at?->format('F j, Y g:i a T'),
            ]);
    }

    #[Test]
    public function it_shows_full_table_if_there_is_a_campaign_with_the_given_zip_code_and_industry(): void
    {
        $company = Company::factory()->createQuietly([
            'name' => 'Company Inc.',
            'consolidated_status' => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS,
        ]);

        $industry = Industry::factory()->createQuietly([
            'name' => 'Solar',
            'slug' => 'solar',
        ]);

        $service = IndustryService::factory()->createQuietly([
            'name' => 'Solar Installation',
            'slug' => 'solar-installation',
            'industry_id' => $industry->id,
        ]);

        $companyCampaign = CompanyCampaign::factory()
            ->location([
                'zip_code' => '12345',
            ])
            ->dateOfLastLeadPurchased(
                countOfProductAssignments: 2,
                dateOfLatest: now(),
            )
            ->createQuietly([
                'company_id' => $company->id,
                'name' => 'Company Campaign',
                'status' => CampaignStatus::ACTIVE,
                'service_id' => $service->id,
            ]);

        $productAssignment = ProductAssignment::orderByDesc('created_at')->first();

        $this->actingAs(User::factory()->createQuietly())
            ->get(route('experimental.campaigns.index', [
                'zip_code' => '12345',
                'industries' => [$industry->id],
            ]))
            ->assertSeeInOrder([
                $companyCampaign->name,
                $company->name,
                $service->name,
                $industry->name,
                $productAssignment->created_at?->format('F j, Y g:i a T'),
            ]);
    }

    #[Test]
    public function only_industries_that_have_at_least_one_campaign_show_up_in_the_industries_select(): void
    {
        $industry = Industry::factory()->createQuietly([
            'name' => 'Solar',
            'slug' => 'solar',
        ]);

        $service = IndustryService::factory()->createQuietly([
            'industry_id' => $industry->id,
            'name' => 'Solar Installation',
            'slug' => 'solar-installation',
        ]);

        CompanyCampaign::factory()->createQuietly([
            'service_id' => $service->id,
            'name' => 'Company Campaign',
        ]);

        $hiddenIndustry = Industry::factory()->createQuietly([
            'name' => 'Roofing',
            'slug' => 'roofing',
        ]);

        $this->actingAs(User::factory()->create())
            ->get(route('experimental.campaigns.index'))
            ->assertOk()
            ->assertViewIs('campaigns.index')
            ->assertSee([
                $industry->name,
            ])
            ->assertDontSee($hiddenIndustry->name);
    }

    #[Test]
    public function it_only_accepts_industry_ids_that_exist(): void
    {
        $this->actingAs(User::factory()->createQuietly())
            ->get(route('experimental.campaigns.index', [
                'industries' => [200],
            ]))
            ->assertRedirect()
            ->assertSessionHasErrors(['industries.*']);
    }
}
