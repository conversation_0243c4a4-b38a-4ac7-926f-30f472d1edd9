<?php

namespace Tests\Feature\Watchdog;

use App\Actions\GetWatchdogPlaybackUrl;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Config;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class VideoUrlFromConsumerProductControllerTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_calls_the_get_watchdog_playback_url_action(): void
    {
        Config::set('watchdog.url', 'http://127.0.0.1:8000');
        Config::set('watchdog.token', 'test');

        $response = [
            'data' => [
                'url' => rtrim(config('watchdog.url'), '/').'/videos/01J1QVSG0CE0K39XS96SVAP9GZ',
            ],
        ];

        $this->mock(GetWatchdogPlaybackUrl::class)
            ->expects('handle')
            ->once()
            ->andReturn(new JsonResponse($response));

        $consumerProduct = ConsumerProduct::factory()->for(Consumer::factory()->create())->lead()->create();

        $this->actingAs(User::factory()->create()->givePermissionTo(Permission::findOrCreate('lead-processing')))
            ->getJson(route('internal-api.v1.lead-processing.consumer-products.playback.watchdog', $consumerProduct))
            ->assertOk()
            ->assertJson($response);
    }
}
