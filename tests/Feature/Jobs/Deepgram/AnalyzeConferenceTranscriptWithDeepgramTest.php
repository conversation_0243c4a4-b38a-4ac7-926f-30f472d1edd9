<?php

namespace Tests\Feature\Jobs\Deepgram;

use App\Jobs\Deepgram\AnalyzeConferenceTranscriptWithDeepgram;
use App\Models\Conference\ConferenceParticipant;
use App\Models\Conference\ConferenceTranscript;
use App\Models\Conference\ConferenceTranscriptEntry;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class AnalyzeConferenceTranscriptWithDeepgramTest extends TestCase
{
    use RefreshDatabase;

    protected Permission $permission;

    public static function invalidTexts(): array
    {
        return [
            [''],
            [null],
        ];
    }

    protected function setUp(): void
    {
        parent::setUp();

        Http::preventStrayRequests();

        Config::set('services.deepgram.api_key', 'testing');
    }

    /**
     * @throws \JsonException
     */
    #[Test]
    public function it_can_analyze_conference_transcript_with_deepgram(): void
    {
        $conferenceTranscript = ConferenceTranscript::factory()->create();

        $firstParticipant = ConferenceParticipant::factory()->create([
            'conference_id' => $conferenceTranscript->conference_id,
            'name' => 'John Doe',
        ]);

        $secondParticipant = ConferenceParticipant::factory()->create([
            'conference_id' => $conferenceTranscript->conference_id,
            'name' => 'Jane Doe',
        ]);

        ConferenceTranscriptEntry::factory()
            ->count(4)
            ->sequence(
                [
                    'conference_participant_id' => $secondParticipant->id,
                    'start_time' => Carbon::create(2025, 5, 1, 9, 5, 5),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 15),
                ], // This happened second according to the start and end time
                [
                    'conference_participant_id' => $firstParticipant->id,
                    'start_time' => Carbon::create(2025, 5, 1, 9),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 5),
                ], // This happened first according to the start and end time
                [
                    'conference_participant_id' => $firstParticipant->id,
                    'start_time' => Carbon::create(2025, 5, 1, 9, 15, 5),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 30),
                ], // This happened last according to the start and end time
                [
                    'conference_participant_id' => $firstParticipant->id,
                    'start_time' => Carbon::create(2025, 5, 1, 9, 15, 5),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 30),
                ], // This happened last according to the start and end time
                [
                    'conference_participant_id' => $secondParticipant->id,
                    'text' => '',
                ], // This is an invalid entry because the text is blank
            )
            ->createQuietly([
                'conference_transcript_id' => $conferenceTranscript->id,
            ]);

        $responseJson = json_decode(file_get_contents(base_path('/tests/Fixtures/deepgram-analyze-text-response-sample.json')),
            true, 512, JSON_THROW_ON_ERROR);

        Http::fake([
            $this->getUrl() => Http::response($responseJson),
        ]);

        Log::shouldReceive('debug')->times(6);

        AnalyzeConferenceTranscriptWithDeepgram::dispatch($conferenceTranscript);

        $this->assertConferenceTranscriptAnalyzed($conferenceTranscript, $responseJson);

        Http::assertSentCount(1);

        Http::assertSent(function ($request) {
            $this->assertSame($this->getUrl(), $request->url());

            return true;
        });

        AnalyzeConferenceTranscriptWithDeepgram::dispatch($conferenceTranscript);

        $this->assertConferenceTranscriptAnalyzed($conferenceTranscript, $responseJson);

        Http::assertSentCount(2);

        Http::assertSent(function ($request) {
            $this->assertSame($this->getUrl(), $request->url());

            return true;
        });
    }

    private function assertConferenceTranscriptAnalyzed(ConferenceTranscript $conferenceTranscript, array $responseJson): void
    {
        $this->assertDatabaseCount('conference_transcript_deepgram_records', 1);

        $this->assertDatabaseHas('conference_transcript_deepgram_records', [
            'conference_transcript_id' => $conferenceTranscript->id,
            'deepgram_request_id' => $responseJson['metadata']['request_id'],
            'deepgram_requested_at' => Carbon::parse($responseJson['metadata']['created']),
            'deepgram_language' => $responseJson['metadata']['language'],
            'deepgram_average_sentiment' => $responseJson['results']['sentiments']['average']['sentiment'],
            'deepgram_average_sentiment_score' => $responseJson['results']['sentiments']['average']['sentiment_score'],
            'deepgram_summary' => $responseJson['results']['summary']['text'],
        ]);

        $totalTopicsCount = 0;

        foreach ($responseJson['results']['topics']['segments'] as $segment) {
            $totalTopicsCount += count($segment['topics']);
        }

        $this->assertEquals(13, $totalTopicsCount);

        $this->assertDatabaseCount('conference_transcript_deepgram_record_topics', $totalTopicsCount);

        $this->assertDatabaseHas('conference_transcript_deepgram_record_topics', [
            'conference_transcript_id' => $conferenceTranscript->id,
            'topic' => $responseJson['results']['topics']['segments'][0]['topics'][0]['topic'],
            'confidence_score' => $responseJson['results']['topics']['segments'][0]['topics'][0]['confidence_score'],
            'text' => $responseJson['results']['topics']['segments'][0]['text'],
        ]);

        $totalSentimentsCount = count($responseJson['results']['sentiments']['segments']);

        $this->assertEquals(41, $totalSentimentsCount);

        $this->assertDatabaseCount('conference_transcript_deepgram_record_sentiments', $totalSentimentsCount);

        $this->assertDatabaseHas('conference_transcript_deepgram_record_sentiments', [
            'conference_transcript_id' => $conferenceTranscript->id,
            'sentiment' => $responseJson['results']['sentiments']['segments'][0]['sentiment'],
            'sentiment_score' => $responseJson['results']['sentiments']['segments'][0]['sentiment_score'],
            'text' => $responseJson['results']['sentiments']['segments'][0]['text'],
        ]);
    }

    private function getUrl(): string
    {
        return AnalyzeConferenceTranscriptWithDeepgram::getUrl();
    }

    #[Test]
    public function it_fails_if_the_conference_transcript_entries_do_not_exist(): void
    {
        $conferenceTranscript = ConferenceTranscript::factory()->create();

        AnalyzeConferenceTranscriptWithDeepgram::dispatch($conferenceTranscript);

        $this->assertDatabaseCount('conference_transcript_deepgram_records', 0);

        Http::assertNothingSent();
    }

    #[Test, DataProvider('invalidTexts')]
    public function it_fails_if_the_conference_transcript_has_only_invalid_entries($text): void
    {
        $conferenceTranscript = ConferenceTranscript::factory()->create();

        ConferenceTranscriptEntry::factory()->createQuietly([
            'conference_transcript_id' => $conferenceTranscript->id,
            'text' => $text,
        ]);

        AnalyzeConferenceTranscriptWithDeepgram::dispatch($conferenceTranscript);

        $this->assertDatabaseCount('conference_transcript_deepgram_records', 0);

        Http::assertNothingSent();
    }

    #[Test]
    public function it_fails_if_missing_api_key(): void
    {
        Config::set('services.deepgram.api_key');

        $conferenceTranscript = ConferenceTranscript::factory()->create();

        $firstParticipant = ConferenceParticipant::factory()->create([
            'conference_id' => $conferenceTranscript->conference_id,
            'name' => 'John Doe',
        ]);

        $secondParticipant = ConferenceParticipant::factory()->create([
            'conference_id' => $conferenceTranscript->conference_id,
            'name' => 'Jane Doe',
        ]);

        ConferenceTranscriptEntry::factory()
            ->count(4)
            ->sequence(
                [
                    'conference_participant_id' => $secondParticipant->id,
                    'start_time' => Carbon::create(2025, 5, 1, 9, 5, 5),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 15),
                ], // This happened second according to the start and end time
                [
                    'conference_participant_id' => $firstParticipant->id,
                    'start_time' => Carbon::create(2025, 5, 1, 9),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 5),
                ], // This happened first according to the start and end time
                [
                    'conference_participant_id' => $firstParticipant->id,
                    'start_time' => Carbon::create(2025, 5, 1, 9, 15, 5),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 30),
                ], // This happened last according to the start and end time
                [
                    'conference_participant_id' => $firstParticipant->id,
                    'start_time' => Carbon::create(2025, 5, 1, 9, 15, 5),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 30),
                ], // This happened last according to the start and end time
                [
                    'conference_participant_id' => $secondParticipant->id,
                    'text' => '',
                ], // This is an invalid entry because the text is blank
            )
            ->createQuietly([
                'conference_transcript_id' => $conferenceTranscript->id,
            ]);

        AnalyzeConferenceTranscriptWithDeepgram::dispatch($conferenceTranscript);

        $this->assertDatabaseCount('conference_transcript_deepgram_records', 0);

        Http::assertSentCount(0);
    }

    #[Test]
    public function it_fails_if_invalid_response_json(): void
    {
        $conferenceTranscript = ConferenceTranscript::factory()->create();

        $firstParticipant = ConferenceParticipant::factory()->create([
            'conference_id' => $conferenceTranscript->conference_id,
            'name' => 'John Doe',
        ]);

        $secondParticipant = ConferenceParticipant::factory()->create([
            'conference_id' => $conferenceTranscript->conference_id,
            'name' => 'Jane Doe',
        ]);

        ConferenceTranscriptEntry::factory()
            ->count(4)
            ->sequence(
                [
                    'conference_participant_id' => $secondParticipant->id,
                    'start_time' => Carbon::create(2025, 5, 1, 9, 5, 5),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 15),
                ], // This happened second according to the start and end time
                [
                    'conference_participant_id' => $firstParticipant->id,
                    'start_time' => Carbon::create(2025, 5, 1, 9),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 5),
                ], // This happened first according to the start and end time
                [
                    'conference_participant_id' => $firstParticipant->id,
                    'start_time' => Carbon::create(2025, 5, 1, 9, 15, 5),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 30),
                ], // This happened last according to the start and end time
                [
                    'conference_participant_id' => $firstParticipant->id,
                    'start_time' => Carbon::create(2025, 5, 1, 9, 15, 5),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 30),
                ], // This happened last according to the start and end time
                [
                    'conference_participant_id' => $secondParticipant->id,
                    'text' => '',
                ], // This is an invalid entry because the text is blank
            )
            ->createQuietly([
                'conference_transcript_id' => $conferenceTranscript->id,
            ]);

        Http::fake([
            $this->getUrl() => Http::response([]),
        ]);

        Log::shouldReceive('debug')->once();

        AnalyzeConferenceTranscriptWithDeepgram::dispatch($conferenceTranscript);

        $this->assertDatabaseCount('conference_transcript_deepgram_records', 0);

        $this->assertDatabaseCount('conference_transcript_deepgram_record_topics', 0);

        $this->assertDatabaseCount('conference_transcript_deepgram_record_sentiments', 0);

        Http::assertSentCount(1);

        Http::assertSent(function ($request) {
            $this->assertSame($this->getUrl(), $request->url());

            return true;
        });
    }

    #[Test]
    public function unique_id(): void
    {
        $conferenceTranscript = ConferenceTranscript::factory()->create();

        $this->assertSame(
            'AnalyzeConferenceTranscriptWithDeepgram-'.$conferenceTranscript->id,
            (new AnalyzeConferenceTranscriptWithDeepgram($conferenceTranscript))->uniqueId()
        );
    }

    #[Test]
    public function tags(): void
    {
        $conferenceTranscript = ConferenceTranscript::factory()->create();

        $this->assertSame(
            ['AnalyzeConferenceTranscriptWithDeepgram', 'conference_transcript:'.$conferenceTranscript->id],
            (new AnalyzeConferenceTranscriptWithDeepgram($conferenceTranscript))->tags()
        );
    }
}
