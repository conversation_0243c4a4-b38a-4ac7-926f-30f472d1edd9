<?php

namespace Tests\Feature;

use App\Contracts\Services\CompanyMetricsServiceContract;
use App\Enums\CompanyMetrics\CompanyMetricRequestTypes;
use App\Http\Controllers\CompanyMetrics\CompanyMetricsController;
use App\Models\CompanyMetric;
use App\Models\Odin\Company;
use App\Services\CompanyMetrics\SimilarWebDummyMetricsService;
use App\Services\CompanyMetricsService\CompanyMetricsService;
use DateTime;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Collection;
use Mockery;
use Mockery\MockInterface;
use Tests\TestCase;

class CompanyMetricsTest extends TestCase
{
    /**
     * Test getting a response from the company metrics service contract depending on app binding
     *
     * @throws BindingResolutionException
     */
    public function test_get_response(): void
    {
        $dummyService = $this->app->make(SimilarWebDummyMetricsService::class);
        $this->app->instance(CompanyMetricsServiceContract::class, $dummyService);

        $service = app()->make(CompanyMetricsServiceContract::class);

        $company = new Company;
        $company[Company::FIELD_WEBSITE] = 'test.com';

        $date = new DateTime('yesterday');

        $response = $service->getCompanyMetrics($company, $date, $date);

        $this->assertEquals('dummyrequest', $response->getRequest());
    }

    /**
     * Test getting company metrics from the CompanyMetricsController
     *
     * @throws BindingResolutionException
     */
    public function test_controller_get_metrics(): void
    {
        $ppcSpend = 1000;
        $companyId = 10;
        $companyMetric = new CompanyMetric;
        $companyMetric[CompanyMetric::FIELD_REQUEST_RESPONSE] = ['monthlySpend' => $ppcSpend, 'year' => 2024, 'month' => 5];
        $metricCollection = new Collection;
        $metricCollection->push($companyMetric);

        $this->instance(
            CompanyMetricsService::class,
            Mockery::mock(CompanyMetricsService::class, function (MockInterface $mock) use ($ppcSpend, $metricCollection, $companyMetric, $companyId) {
                $mock->shouldReceive('getCompanyMetrics')->once()->withArgs(function ($idArg, $typeArg) use ($companyId) {
                    $this->assertEquals($companyId, $idArg);
                    $this->assertEquals(CompanyMetricRequestTypes::PPC_SPEND, $typeArg);

                    return true;
                })->andReturn($metricCollection);
                $mock->shouldReceive('getLatestCompanyMetric')->once()->once()->withArgs(function ($idArg, $typeArg) use ($companyId) {
                    $this->assertEquals($companyId, $idArg);
                    $this->assertEquals(CompanyMetricRequestTypes::PPC_SPEND, $typeArg);

                    return true;
                })->andReturn($companyMetric);
                $mock->shouldReceive('getAverageCompanyPpcSpend')->once()->andReturn($ppcSpend);
            })
        );

        $controller = app()->make(CompanyMetricsController::class);

        $response = $controller->getCompanyPpcSpendData($companyId);

        $this->assertTrue($response->getData()->{'data'}->{CompanyMetricsController::STATUS});

    }
}
