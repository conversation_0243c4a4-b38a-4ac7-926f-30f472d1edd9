<?php

namespace Tests\Feature;

use App\Models\Odin\Company;
use App\Services\CompanyMetrics\SimilarWebMetricsService;
use DateTime;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\Client\Request as ClientRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class SimilarWebMetricsServiceTest extends TestCase
{
    /**
     * Testing a successful HTTP response from similar web api
     *
     * @throws GuzzleException
     */
    public function test_success(): void
    {
        $service = new SimilarWebMetricsService;

        $company = new Company;
        $domain = 'test.com';
        $company[Company::FIELD_WEBSITE] = $domain;

        $startDate = new DateTime('yesterday');
        $endDate = new DateTime('today');

        $status = 'Success';

        $monthlySpend = 10440.47;

        Http::fake(function (ClientRequest $request) use ($status, $startDate, $endDate, $domain, $monthlySpend) {
            Log::info('URL: '.json_encode($request->url())."\n\n");
            Log::info('Data: '.json_encode($request->data())."\n\n");

            return Http::response(
                $this->get_test_response(
                    $status,
                    $startDate->format('Y-m'),
                    $endDate->format('Y-m'),
                    $domain,
                    $monthlySpend),
                200);
        });

        $response = $service->getCompanyMetrics($company, $startDate, $endDate);

        $this->assertEquals($startDate->format('Y'), $response->getYear());
        $this->assertEquals((int) $startDate->format('m'), (int) $response->getMonth());
        $this->assertEquals($monthlySpend, $response->getMonthlySpend());
    }

    /**
     * Testing a failed HTTP response from similar web api
     *
     * @throws GuzzleException
     * @throws BindingResolutionException
     */
    public function test_failure(): void
    {
        $service = app()->make(SimilarWebMetricsService::class);

        $company = new Company;
        $domain = 'test.com';
        $company[Company::FIELD_WEBSITE] = $domain;
        $company[Company::FIELD_NAME] = 'Test inner response status failure';

        $startDate = new DateTime('yesterday');
        $endDate = new DateTime('today');

        $status = 'Failure';
        $monthlySpend = 11111.34;

        Http::fake(function (ClientRequest $request) use ($status, $startDate, $endDate, $domain, $monthlySpend) {
            return Http::response(
                $this->get_test_response(
                    $status,
                    $startDate->format('Y-m'),
                    $endDate->format('Y-m'),
                    $domain,
                    $monthlySpend),
                200);
        });

        // Expect exception for bad status in response
        $this->expectException(Exception::class);
        $service->getCompanyMetrics($company, $startDate, $endDate);
    }

    /**
     * Testing a 404 HTTP response from similar web api
     *
     * @throws GuzzleException
     */
    public function test_not_found(): void
    {
        $service = new SimilarWebMetricsService;

        $company = new Company;
        $domain = 'test.com';
        $company[Company::FIELD_WEBSITE] = $domain;
        $company[Company::FIELD_NAME] = 'Test request 404 failure';

        $startDate = new DateTime('yesterday');
        $endDate = new DateTime('today');

        Http::fake(function (ClientRequest $request) {
            return Http::response('{}', 404);
        });

        // Expect exception for bad status in response
        $this->expectException(Exception::class);
        $service->getCompanyMetrics($company, $startDate, $endDate);
    }

    private function get_test_response(string $status, string $start_date, string $end_date, string $domain, float $monthlySpend): string
    {
        return '{
  "meta": {
    "request": {
      "granularity": "Monthly",
      "main_domain_only": false,
      "format": "json",
      "domain": "'.$domain.'",
      "start_date": "'.$start_date.'",
      "end_date": "'.$end_date.'",
      "country": "us"
    },
    "status": "'.$status.'",
    "last_updated": "2023-05-31"
  },
  "breakdown": [
    {
      "date": "'.$start_date.'-01",
      "currencies": {
        "aud": 14807.09,
        "eur": 9613.1,
        "gbp": 8482.39,
        "jpy": 1358462.11,
        "usd": '.$monthlySpend.'
      }
    },
    {
      "date": "2023-02-01",
      "currencies": {
        "aud": 9049.98,
        "eur": 5758.67,
        "gbp": 5061.71,
        "jpy": 830479.13,
        "usd": 6090.74
      }
    },
    {
      "date": "2023-03-01",
      "currencies": {
        "aud": 4684.82,
        "eur": 2883.1,
        "gbp": 2538.04,
        "jpy": 416012.12,
        "usd": 3133.21
      }
    }
  ]
}';
    }
}
