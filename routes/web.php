<?php

use App\Http\Controllers\AdvertisingController;
use App\Http\Controllers\CalculatorFlowBuilderController;
use App\Http\Controllers\CampaignController;
use App\Http\Controllers\ConsumerReviewsController;
use App\Http\Controllers\ConsumerProductController;
use App\Http\Controllers\IndustryManagementController;
use App\Http\Controllers\MissedRevenueController;
use App\Http\Controllers\Odin\CompanyQualityScoreManagementController;
use App\Http\Controllers\Odin\Portal\CommunicationPortalController;
use App\Http\Controllers\Prospects\ProspectingController;
use App\Http\Controllers\SalesManagementController;
use App\Http\Controllers\EmailTemplatesController;
use App\Http\Controllers\RolesPermissionsManagementController;
use App\Http\Controllers\TestLeadController;
use App\Http\Controllers\UserActivityController;
use App\Http\Controllers\Dashboard\DashboardController;
use App\Http\Controllers\LeadProcessing\LeadProcessingController;
use App\Http\Controllers\LeadProcessing\LeadProcessingManagementController;
use App\Http\Controllers\UserSettingsController;
use App\Http\Middleware\DetermineCompany;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Companies\CompanyController;
use App\Enums\PermissionType;
use App\Http\Controllers\RulesetManagementController;
use App\Http\Controllers\GlobalConfigurationsManagementController;
use App\Http\Controllers\ContractManagementController;
use App\Http\Controllers\Companies\CompanyUserController;
use App\Http\Controllers\BDMDashboard\BDMDashboardController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix('/2fa')->controller(\App\Http\Controllers\TwoFactorAuthentication\TwoFactorAuthenticationController::class)->group(function () {
    Route::get('/setup', 'setup')->name('2fa.setup');
    Route::post('/setup', 'verifySetup')->name('2fa.verify_setup');
    Route::get('/verify', 'show')->name('2fa.show');
    Route::post('/verify', 'verify')->name('2fa.verify');
});

Route::group(['middleware' => ['permission:dashboard']], function () {
    Route::get('/dashboard',
        [DashboardController::class, 'index']
    )->middleware(['auth'])->name('dashboard');
});

Route::group(['middleware' => ['permission:prospecting']], function () {
    Route::get('/prospecting', [ProspectingController::class, 'index'])->middleware(['auth'])->name('prospecting');
});

Route::group(['middleware' => ['permission:prospecting']], function () {
    Route::get('/bdm-dashboard', [BDMDashboardController::class, 'index'])->middleware(['auth'])->name('bdm-dashboard');
});

Route::group(['middleware' => ['permission:admin']], function () {

    Route::get('/prospect-configurations', function() {
        return view('prospect-configurations.index');
    });

    Route::get('/admin', function () {
        return view('admin');
    });

    Route::get('/admin/reports/lead-issue', function () {
        return view('admin-reports-lead-issue');
    });

    Route::get('/admin/sales-bait-management', function () {
        return view('admin-sales-bait-management');
    });

    Route::get('/user-management', function () {
        return view('user-management');
    })->middleware(['auth', 'permission:' . PermissionType::USER_MANAGEMENT->value])->name('user-management');

    Route::get('/task-management', function () {
        return view('task-management');
    });
});

Route::get('/privacy-management', function () {
    return view('privacy-management');
})->middleware(['auth', 'permission:' . PermissionType::PRIVACY_MANAGEMENT_VIEW->value])->name('privacy-management');

Route::get('/sales-overview', function () {
    return view('sales-overview');
})->middleware(['auth', 'permission:' . PermissionType::SALES_OVERVIEW_VIEW->value]);

Route::get('/roles-permissions-management', [RolesPermissionsManagementController::class, 'index'])->middleware('permission:permission-management/view');

Route::controller(MissedRevenueController::class)->middleware('permission:missed-revenue')->group(function () {
    Route::get('/missed-revenue', 'index');
});

Route::get('/tasks/queue', [\App\Http\Controllers\TaskController::class, 'tasksQueue'])->name('task-queue');

Route::get('/tasks', function () {
    return view('tasks-list');
});

Route::prefix("/impersonate")->controller(\App\Http\Controllers\ImpersonateController::class)->group(function() {
    Route::get('/clear', 'clear');
    Route::get('/{id}', 'impersonate')->middleware('protect_from_impersonation:throw_exception')->where('id', '[0-9]+');
});

Route::group(['middleware' => ['permission:reports']], function () {
    Route::prefix('/reports')->group(function() {
        Route::get('/', function() {
            return view('reports.index');
        });

        Route::get('/historical-available-budgets', function() {
            return view('reports.historical-available-budgets');
        });

        Route::get('/estimated-revenue-per-lead', function() {
            return view('reports.estimated-revenue-per-lead');
        });

        Route::get('/profitability-simulator', function() {
            return view('reports.profitability-simulator');
        });

        Route::get('/leads-report', function() {
            return view('reports.leads-report');
        });

        Route::get('/county-coverage-report', function() {
            return view('reports.county-coverage-report');
        });
    });
});


Route::group(['middleware' => ['permission:lead-processing']], function () {
    Route::get('/lead-processing',
        [LeadProcessingController::class, 'index']
    )->middleware(['auth'])->name('lead-processing');
});

Route::group(['middleware' => ['permission:lead-processing-management']], function () {
    Route::get('/lead-processing-management',
        [LeadProcessingManagementController::class, 'index']
    )->middleware(['auth'])->name('lead-processing-management');
});

Route::group(['middleware' => ['permission:sales-management']], function () {
    Route::get('/sales-management',
        [SalesManagementController::class, 'index']
    )->middleware(['auth'])->name('sales-management');
});

Route::group(['middleware' => ['permission:email-templates']], function () {
    Route::get('/email-templates',
        [EmailTemplatesController::class, 'index']
    )->middleware(['auth'])->name('email-templates');
});

Route::name('companies.')->middleware(['permission:companies'])->controller(CompanyController::class)->prefix('/companies')->group(function() {
    Route::get('/', 'search')->name('search');
    Route::get('/{id}', 'get')->name('index')->middleware(DetermineCompany::class);
    Route::get('/{company}/delete/cancel', [CompanyController::class, 'cancelDelete'])
        ->name('delete.cancel')
        ->missing(function () {
            return Redirect::route('companies.search')->with('status', 'Company has already been deleted');
        });
});

Route::group(['middleware' => ['permission:' . PermissionType::COMPANY_QUALITY_SCORE_MANAGEMENT->value]], function () {
    Route::get('/company-quality-score-management',
        [CompanyQualityScoreManagementController::class, 'index']
    )->middleware(['auth'])->name('company-quality-score-management');
});

Route::get('/user-settings', [UserSettingsController::class, 'index']);

Route::get('/edit-profile', [UserSettingsController::class, 'editProfile'])->name('edit-profile');

Route::get('/filter-presets', [UserSettingsController::class, 'filterPresets']);

Route::group(['middleware' => ['permission:industry-management', 'auth']], function () {
    Route::get('/industry-management', [IndustryManagementController::class, 'index']);
    Route::get('/industry-management/{global}', [IndustryManagementController::class, 'sortTab']);
    Route::get('/industry-management/industries/{industry}/{service}', [IndustryManagementController::class, 'sortIndustryPath']);
    Route::get('/industry-management/websites/{website}/{property}', [IndustryManagementController::class, 'sortWebsitePath']);
    Route::get('/industry-management/products/{product}/{service}', [IndustryManagementController::class, 'sortProductPath']);
});

Route::group(['middleware' => [PermissionType::CONSUMER_REVIEWS_MANAGE->getPermissionString(), 'auth']], function () {
    Route::get('/consumer-reviews', [ConsumerReviewsController::class, 'index']);
});


Route::group(['middleware' => ['permission:advertising']], function () {
    Route::get('/advertising',
        [AdvertisingController::class, 'index']
    )->middleware(['auth'])->name('advertising');
});

Route::group(['middleware' => [PermissionType::MINIMUM_PRICE_MANAGEMENT_VIEW->getPermissionString()]], function () {
    Route::get('/minimum-price-management', function () {
        return view('minimum-price-management');
    });
});

Route::group(['middleware' => ['permission:' . PermissionType::INDUSTRY_CONFIGURATION->value]], function () {
    Route::get('/minimum-price-suggestions', function () {
        return view('minimum-price-suggestions');
    });
});

Route::group(['prefix' => 'portal', 'middleware' => ['auth']], function(){
    Route::get('/communication', [CommunicationPortalController::class, 'index']);
});

Route::group(['middleware' => ['permission:manage-bundles|manage-bundle-invoices|view-bundles']], function () {
    Route::get('/bundle-management', function () {
        return view('bundle-management');
    });
});

Route::group(['middleware' => ['permission:'.PermissionType::BILLING_MANAGEMENT_VIEW->value]], function () {
    Route::get('/billing-management', function () {
        return view('billing-management');
    });
});

Route::group(['middleware' => ['permission:company-users/view']], function () {
    Route::get('/company-users', function () { return view('company-users'); });
    Route::get('/company-users/{companyUserId}', [CompanyUserController::class, 'index']);
});


Route::group(['middleware' => ['permission:flow-management']], function() {
    Route::get('/flow-management', function () {
        return view('flow-management');
    });
    Route::get('/flow-management/flow-editor',  [CalculatorFlowBuilderController::class, 'flowEditor']);
});

Route::group(['middleware' => ['permission:opportunity-notifications']], function () {
    Route::get('/opportunity-notifications', function () {
        return view('opportunity-notifications');
    });
});

Route::group(['middleware' => ['permission:' . PermissionType::RULESET_MANAGEMENT->value]], function () {
    Route::get('/ruleset-management',
        [RulesetManagementController::class, 'index']
    )->middleware(['auth'])->name('ruleset-management');
});

Route::group(['middleware' => ['permission:outreach-cadence']], function () {
    Route::get('/outreach-cadence', function () {
        return view('outreach-cadence');
    });
});

Route::group(['middleware' => ['permission:consumer-search']], function () {
    Route::get('/consumer-search', function () {
        return view('consumers.search');
    });
});

Route::group(['middleware' => ['permission:silo-management/view']], function () {
    Route::get('/directory-management', function () {
        return view('directory-management');
    });
});

Route::group(['middleware' => ['permission:'. PermissionType::GLOBAL_CONFIGURATIONS_EDIT->value . '|' . PermissionType::GLOBAL_CONFIGURATIONS_VIEW->value]], function () {
    Route::get('/global-configurations-management', [GlobalConfigurationsManagementController::class, 'index']);
});

Route::group(['middleware' => ['permission:' . PermissionType::CONSUMER_PRODUCT_VIEW->value]], function () {
    Route::get('/consumer-product/',
        [ConsumerProductController::class, 'index']
    )->middleware(['auth'])->name('consumer-product');
});

Route::get('/user-activity/{id}', [UserActivityController::class, 'index']);

Route::prefix('/test-leads')->middleware('permission:test-leads')->controller(TestLeadController::class)->group(function () {
    Route::get('/', 'getTestLeads');
});

Route::prefix('/contract-management')->middleware(PermissionType::CONTRACT_MANAGEMENT_VIEW->getPermissionString())->controller(ContractManagementController::class)->group(function () {
   Route::get('/', 'index');
});

Route::get('/company-users/{companyUserId}', [CompanyUserController::class, 'index']);

Route::get('/mailbox',  function () {
    return redirect('');
});

Route::group(['middleware' => [PermissionType::COMPANY_CAMPAIGN_DELIVERY_LOGS_VIEW->getPermissionString()]], function () {
    Route::get('/company-campaign-delivery-logs', function () {
        return view ('company-campaign-delivery-logs');
    });
});

Route::get('/lead-refunds',  function () {
    return view('lead-refunds');
})->name('lead-refunds');

Route::group(['middleware' => [PermissionType::COMPANY_CAMPAIGN_CUSTOM_PRICING_LOGS_VIEW->getPermissionString()]], function () {
    Route::get('/company-campaign-custom-pricing-logs', function () {
        return view('company-campaign-custom-pricing-logs');
    });
});

Route::view('/affiliates', 'affiliates')->name('affiliates');
Route::view('/template-management', 'template-management');

Route::group(['middleware' => [PermissionType::MARKETING_CAMPAIGN_VIEW->getPermissionString()]], function () {
    Route::get('/marketing',function () {
        return view('marketing');
    });
});

Route::name('experimental.')->prefix('experimental')->group(function () {
    Route::resource('campaigns', CampaignController::class)->only(['index']);
});
