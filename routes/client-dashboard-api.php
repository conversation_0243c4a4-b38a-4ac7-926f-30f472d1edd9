<?php

/*
|--------------------------------------------------------------------------
| Client Dashboard API Routes
|--------------------------------------------------------------------------
*/

use App\Http\Controllers\API\Appointments\AppointmentController;
use App\Http\Controllers\API\ClientDashboard\AppointmentCalendarIntegrationController;
use App\Http\Controllers\API\ClientDashboard\CampaignController;
use App\Http\Controllers\API\ReferenceLists\ReferenceListController;
use App\Http\Middleware\CheckClientDashboardCompany;
use Illuminate\Support\Facades\Route;

Route::prefix('/v1')->middleware(['authenticate_client_dashboard'])->group(function() {
    Route::get('timezones', [ReferenceListController::class, 'getTimezones']);
    Route::get('rejection-reasons/{product}', [ReferenceListController::class, 'getRejectionReasons']);

    Route::prefix('/locations')->controller(ReferenceListController::class)->group(function() {
        Route::get('states', 'getStates');
        Route::get('/states/{stateKey}/cities', 'getCitiesForState');
    });

    Route::prefix('/companies/{legacyCompanyId}')->middleware(CheckClientDashboardCompany::class)->group(function() {
        Route::prefix('/appointments/')->group(function() {
            Route::prefix('/calendar-integration')->controller(AppointmentCalendarIntegrationController::class)->group(function() {
                Route::get('/all', 'listAllCalendarIntegrations');
                Route::post('/static-calendar', 'createStaticCalendar');
                Route::get('/calendar/{calendarId}', 'getCalendarById');
                Route::patch('/calendar/{calendarId}', 'saveCalendar');
                Route::delete('/calendar/{calendarId}', 'deleteCalendar');
                Route::get('/calendar-events-date-range/{calendarId}', 'getCalendarEventsInDateRange');

                Route::prefix("/{platform}")->group(function() {
                    Route::prefix('/oauth')->group(function() {
                        Route::get('/authorization-uri', 'getOauthAuthorizationUri');
                    });
                });
            });

            Route::controller(AppointmentController::class)->group(function() {
                Route::get('/search', 'search');
                Route::get('/init-search', 'initSearchOptions');

                Route::post('/reject/{productAssignmentId}', 'rejectAppointment');

                Route::get('/appointments-active', 'appointmentsActive');

                Route::get('/has-sold-appointments', 'hasSoldAppointments');
            });
        });

        Route::prefix('/campaigns/')->controller(CampaignController::class)->group(function() {
            Route::get('/', 'getAll');
            Route::get('/unrestricted-zip-codes', 'getUnrestrictedZipCodeStatus');

            Route::prefix('{legacyCampaignUuid}')->group(function() {
                Route::get('/appointments-prices', 'getAppointmentCampaignPrices');

                Route::get('/{product}', 'getProductCampaignByLegacyUuid');
                Route::patch('/appointments-budget', 'updateAppointmentsCampaignBudgetByLegacyUuid');
                Route::patch('/appointments-status', 'updateAppointmentCampaignStatusByUuid');
                Route::patch('/campaign-schedules', 'updateAppointmentCampaignSchedulesByUuid');
            });
        });
    });
});
