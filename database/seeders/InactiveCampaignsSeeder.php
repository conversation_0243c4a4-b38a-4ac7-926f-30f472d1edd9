<?php

namespace Database\Seeders;

use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Company\CompanySystemStatus;
use App\Models\Campaigns\CampaignReactivation;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;

class InactiveCampaignsSeeder extends Seeder
{
    public function run(): void
    {
        $admin = Role::findOrCreate('admin');
        $accountManager = Role::findOrCreate('account-manager');

        $user = User::firstWhere('email', '<EMAIL>') ?? User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $user->assignRole($admin);
        $user->assignRole($accountManager);

        $company = $this->createCompanyAndCampaigns();

        $company->assign($user)->asAccountManager();
    }

    public function createCompanyAndCampaigns(): Company
    {
        $company = Company::factory()->createQuietly([
            'system_status' => CompanySystemStatus::ELIGIBLE,
        ]);

        $campaignOne = CompanyCampaign::factory()->for($company)->createQuietly([
            'status' => CampaignStatus::PAUSED_TEMPORARILY,
        ]);

        $campaignTwo = CompanyCampaign::factory()->for($company)->createQuietly([
            'status' => CampaignStatus::PAUSED_PERMANENTLY,
        ]);

        $tempInactivation = CampaignReactivation::factory()->for($campaignOne, 'campaign')->create([
            'reason' => 'Leads suck for a bit!',
            'reactivate_at' => now()->addDay(),
        ]);

        $inactivation = CampaignReactivation::factory()->for($campaignTwo, 'campaign')->create([
            'reason' => 'Leads suck forever!',
        ]);

        $containerOne = BudgetContainer::factory()->for($campaignOne, 'companyCampaign')->create();

        $budgetOne = Budget::factory()->for($containerOne)->create();

        $leadOne = ProductAssignment::factory()->chargeableAndDelivered()
            ->for($budgetOne)
            ->for($company)
            ->createQuietly([
                'cost' => 250,
            ]);

        $containerTwo = BudgetContainer::factory()->for($campaignTwo, 'companyCampaign')->create();

        $budgetTwo = Budget::factory()->for($containerTwo)->create();

        $leadTwo = ProductAssignment::factory()->chargeableAndDelivered()
            ->for($budgetTwo)
            ->for($company)
            ->createQuietly([
                'cost' => 500,
            ]);

        return $company;
    }
}
