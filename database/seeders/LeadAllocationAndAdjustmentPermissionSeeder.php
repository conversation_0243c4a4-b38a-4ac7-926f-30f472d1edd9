<?php

namespace Database\Seeders;

use App\Enums\PermissionType;
use App\Enums\RoleType;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class LeadAllocationAndAdjustmentPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Permission::findOrCreate(PermissionType::LEAD_ALLOCATION_AND_ADJUSTMENT->value);

        $admin = Role::findOrCreate(RoleType::ADMIN->value);

        $admin->givePermissionTo(PermissionType::LEAD_ALLOCATION_AND_ADJUSTMENT->value);
    }
}
