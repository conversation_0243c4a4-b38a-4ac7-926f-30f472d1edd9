<?php

namespace Database\Seeders;

use App\Enums\Odin\StateAbbreviation;
use App\Models\Legacy\Location;
use Illuminate\Database\Seeder;

class LocationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //States
        Location::factory(count(StateAbbreviation::cases()))
            ->create();

        //Counties
        Location::factory(5)
            ->county()
            ->create();

        //Cities
        Location::factory(5)
            ->city()
            ->create();

        //Zips
        Location::factory(5)
            ->zipcode()
            ->create();
    }
}
