<?php

namespace Database\Seeders;

use App\Enums\AffiliateKey;
use App\Http\Requests\PingPostAffiliatesCreateLeadRequest;
use App\Models\PingPostAffiliates\PingPostAffiliate;
use App\Models\PingPostAffiliates\PingPostAffiliateApiKey;
use App\Services\Odin\PingPostAffiliateService;
use Illuminate\Database\Seeder;

class PingPostAffiliatePXSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        // Verify ping post affiliate PX does not already exist to prevent duplicate creation
        $pxSearch = PingPostAffiliate::query()->where(PingPostAffiliate::FIELD_NAME, 'PX')->get();
        if ($pxSearch->count() > 0) {
            print "PX Ping Post Affiliate Already exists.";
            return;
        }

        // Create affiliate entry
        $pxAffiliate = PingPostAffiliate::query()->create([
            PingPostAffiliate::FIELD_NAME => 'PX',
            PingPostAffiliate::FIELD_TYPE => PingPostAffiliate::TYPE_POST,
            PingPostAffiliate::FIELD_STATUS => PingPostAffiliate::STATUS_ACTIVE,
            PingPostAffiliate::FIELD_REQUEST_RULES => AffiliateKey::DEFAULT_RULES,
            PingPostAffiliate::FIELD_KEY_MAP => AffiliateKey::getDefaultKeyMap(),
            PingPostAffiliate::FIELD_DEFAULT_KEY_VALUES => [],
            PingPostAffiliate::FIELD_KEY_VALUE_CONVERSIONS => [],
            PingPostAffiliate::FIELD_DATA => [],
            PingPostAffiliate::FIELD_CREATED_AT => now(),
            PingPostAffiliate::FIELD_UPDATED_AT => now(),
        ]);

        // Create API Key
        $apiKey = PingPostAffiliateApiKey::query()->create([
            PingPostAffiliateApiKey::FIELD_PING_POST_AFFILIATE_ID => $pxAffiliate->id,
            PingPostAffiliateApiKey::FIELD_KEY => 'k9Yqf4iH3Zw0j2D6Jt5bLz7N8Q1pXsV4A0vU6mFhL9RkP2TgM0w7',
            PingPostAffiliateApiKey::FIELD_TYPE => PingPostAffiliateApiKey::TYPE_POST,
            PingPostAffiliateApiKey::FIELD_STATUS => PingPostAffiliateApiKey::STATUS_ACTIVE,
            PingPostAffiliateApiKey::FIELD_CREATED_AT => now(),
            PingPostAffiliateApiKey::FIELD_UPDATED_AT => now(),
        ]);
    }
}
