<?php

namespace Database\Seeders;

use App\Models\Legacy\EloquentQuote;
use App\Models\SalesBaitLead;
use App\Models\SalesBaitRestrictedCompany;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;

class SalesBaitManagementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // The development pipeline does not have access to SR database.
        // The seeders below need SR database to seed data.
        // So, run this seeder only if the environment is local.
        if (!App::isLocal()) return;

        $this->call(SalesBaitConfigurationSeeder::class);

        SalesBaitRestrictedCompany::factory()->count(5)->create();

        /** @var Collection<EloquentQuote> $leads */
        $leads = EloquentQuote::query()
            ->latest(EloquentQuote::TIMESTAMP_ADDED)
            ->take(100)
            ->get()
            ->shuffle()
            ->take(10);

        foreach ($leads as $lead) {
            SalesBaitLead::factory()->count(mt_rand(1, 5))->create([
                SalesBaitLead::FIELD_LEAD_ID => $lead->quoteid
            ]);
        }
    }
}
