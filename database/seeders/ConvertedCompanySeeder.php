<?php

namespace Database\Seeders;

use App\Models\Odin\Company;
use App\Models\Sales\Task;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class ConvertedCompanySeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user = User::whereEmail('<EMAIL>')->first();

        $user->assignRole('business-development-manager');

        $user->givePermissionTo(Permission::findOrCreate('prospecting'));

        $company = Company::factory()->createQuietly();

        $company->assign($user)->as('business-development-manager');

        Task::factory()->createQuietly([
            'payload' => [
                'company_id' => $company->id,
            ]
        ]);

        $company = Company::factory()->createQuietly();

        $company->assign($user)->as('business-development-manager');
    }
}
