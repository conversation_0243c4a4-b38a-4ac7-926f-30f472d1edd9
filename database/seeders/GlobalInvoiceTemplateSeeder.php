<?php

namespace Database\Seeders;

use App\DTO\Billing\AddressData;
use App\Models\Billing\InvoiceTemplate;
use App\Repositories\UserRepository;
use App\Services\Billing\InvoiceTemplateRenderer\InvoiceFoundationComponent;
use Illuminate\Database\Seeder;

class GlobalInvoiceTemplateSeeder extends Seeder
{
    public function __construct(protected UserRepository $userRepository)
    {

    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $address = new AddressData(
            line_one : '4 Industrial Way West',
            city     : 'Eatontown',
            state    : 'NJ',
            post_code: 07724,
        );

        $props = InvoiceFoundationComponent::make([
            InvoiceFoundationComponent::FIELD_INVOICE_LOGO            => 'solarreviews',
            InvoiceFoundationComponent::FIELD_INVOICE_CONTACT_ADDRESS => $address->toArray(),
            InvoiceFoundationComponent::FIELD_SUPPORT_EMAIL           => '<EMAIL>',
            InvoiceFoundationComponent::FIELD_CURRENCY                => 'USD',
            InvoiceFoundationComponent::FIELD_INVOICE_BILLING_ACCOUNT => 'solarreviews_account',
        ]);

        InvoiceTemplate::query()->firstOrCreate([InvoiceTemplate::FIELD_IS_GLOBAL => true], [
            InvoiceTemplate::FIELD_NAME          => 'Global',
            InvoiceTemplate::FIELD_IS_GLOBAL     => true,
            InvoiceTemplate::FIELD_PROPS         => $props->toViewData(),
            InvoiceTemplate::FIELD_CREATED_BY_ID => $this->userRepository->getSystemUser()->id
        ]);
    }
}
