<?php

namespace Database\Seeders;

use App\Enums\Odin\Industry as Industries;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class FixrIndustrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        $this->insertIndustries();
        $this->insertIndustryServices();
    }

    /**
     * Handles inserting the industries from FIXR.
     *
     * @return void
     */
    protected function insertIndustries(): void
    {
        DB::table(Industry::TABLE)->insert([
            [
                Industry::FIELD_NAME => Industries::HVAC,
                Industry::FIELD_SLUG => Industries::HVAC->getSlug()
            ],
            [
                Industry::FIELD_NAME => Industries::WINDOWS,
                Industry::FIELD_SLUG => Industries::WINDOWS->getSlug()
            ],
            [
                Industry::FIELD_NAME => Industries::SIDING,
                Industry::FIELD_SLUG => Industries::SIDING->getSlug()
            ],
            [
                Industry::FIELD_NAME => Industries::DOORS,
                Industry::FIELD_SLUG => Industries::DOORS->getSlug()
            ],
            [
                Industry::FIELD_NAME => Industries::BATHROOMS,
                Industry::FIELD_SLUG => Industries::BATHROOMS->getSlug()
            ],
            [
                Industry::FIELD_NAME => Industries::KITCHENS,
                Industry::FIELD_SLUG => Industries::KITCHENS->getSlug()
            ]
        ]);
    }

    /**
     * Handles inserting the services from FIXR.
     *
     * @return void
     */
    protected function insertIndustryServices(): void
    {
        $industries = Industry::all()->pluck(Industry::FIELD_ID, Industry::FIELD_NAME)->toArray();

        $this->initializeHVACServices($industries)
            ->initializeWindowServices($industries)
            ->initializeSidingServices($industries)
            ->initializeDoorServices($industries)
            ->initializeBathroomServices($industries)
            ->initializeKitchenServices($industries);
    }

    /**
     * Handles the initialization of HVAC services.
     *
     * @param array $industries
     * @return $this
     */
    protected function initializeHVACServices(array $industries): FixrIndustrySeeder
    {
        DB::table(IndustryService::TABLE)->insert([
            [
                IndustryService::FIELD_NAME            => "Central Air - Install & Replace",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::HVAC->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "central-air-install-replace"
            ],
            [
                IndustryService::FIELD_NAME            => "Central Air - Repair & Service",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::HVAC->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "central-air-repair-service"
            ],
            [
                IndustryService::FIELD_NAME            => "Window Air - Install & Replace",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::HVAC->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "window-air-install-replace"
            ],
            [
                IndustryService::FIELD_NAME            => "Window Air - Repair & Service",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::HVAC->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "window-air-repair-service"
            ],
            [
                IndustryService::FIELD_NAME            => "Evaporative Air - Install & Replace",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::HVAC->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "evaporative-air-install-replace"
            ],
            [
                IndustryService::FIELD_NAME            => "Evaporative Air - Repair & Service",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::HVAC->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "evaporative-air-repair-service"
            ]
        ]);

        return $this;
    }

    /**
     * Handles the initialization of window services.
     *
     * @param array $industries
     * @return $this
     */
    protected function initializeWindowServices(array $industries): FixrIndustrySeeder
    {
        DB::table(IndustryService::TABLE)->insert([
            [
                IndustryService::FIELD_NAME            => "Install & Replace",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::WINDOWS->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "windows-install-replace"
            ]
        ]);

        return $this;
    }

    /**
     * Handles the initialization of siding services.
     *
     * @param array $industries
     * @return $this
     */
    protected function initializeSidingServices(array $industries): FixrIndustrySeeder
    {
        DB::table(IndustryService::TABLE)->insert([
            [
                IndustryService::FIELD_NAME            => "Replacement",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::SIDING->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "siding-replacement"
            ],
            [
                IndustryService::FIELD_NAME            => "Repair",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::SIDING->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "siding-repair"
            ]
        ]);

        return $this;
    }

    /**
     * Handles the initialization of door services.
     *
     * @param array $industries
     * @return $this
     */
    protected function initializeDoorServices(array $industries): FixrIndustrySeeder
    {
        DB::table(IndustryService::TABLE)->insert([
            [
                IndustryService::FIELD_NAME            => "Install & Replace",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::DOORS->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "doors-install-replace"
            ],
            [
                IndustryService::FIELD_NAME            => "Bifold Doors - Install & Replace",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::DOORS->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "bifold-doors-install-replace"
            ],
            [
                IndustryService::FIELD_NAME            => "Garage Doors - Install & Replace",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::DOORS->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "garage-doors-install-replace"
            ],
            [
                IndustryService::FIELD_NAME            => "Repair Existing",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::DOORS->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "doors-repair-existing"
            ],
            [
                IndustryService::FIELD_NAME            => "Bifold Doors - Repair Existing",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::DOORS->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "bifold-doors-repair-existing"
            ],
            [
                IndustryService::FIELD_NAME            => "Garage Doors - Repair Existing",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::DOORS->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "garage-doors-repair-existing"
            ]
        ]);

        return $this;
    }

    /**
     * Handles the initialization of bathroom services.
     *
     * @param array $industries
     * @return $this
     */
    protected function initializeBathroomServices(array $industries): FixrIndustrySeeder
    {
        DB::table(IndustryService::TABLE)->insert([
            [
                IndustryService::FIELD_NAME            => "Remodeling",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::BATHROOMS->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "bathroom-remodeling"
            ]
        ]);

        return $this;
    }

    /**
     * Handles the initialization of kitchen services.
     *
     * @param array $industries
     * @return $this
     */
    protected function initializeKitchenServices(array $industries): FixrIndustrySeeder
    {
        DB::table(IndustryService::TABLE)->insert([
            [
                IndustryService::FIELD_NAME            => "Remodeling",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::KITCHENS->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "kitchen-remodeling"
            ],
            [
                IndustryService::FIELD_NAME            => "Countertop Replacement",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::KITCHENS->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "kitchen-countertop-replacement"
            ],
            [
                IndustryService::FIELD_NAME            => "Countertop Repair",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::KITCHENS->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "kitchen-countertop-repair"
            ],
            [
                IndustryService::FIELD_NAME            => "Cabinet Replacement",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::KITCHENS->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "kitchen-cabinet-replacement"
            ],
            [
                IndustryService::FIELD_NAME            => "Cabinet Repair",
                IndustryService::FIELD_INDUSTRY_ID     => $industries[Industries::KITCHENS->value],
                IndustryService::FIELD_SHOW_ON_WEBSITE => false,
                IndustryService::FIELD_SLUG            => "kitchen-cabinet-repair"
            ]
        ]);

        return $this;
    }
}
