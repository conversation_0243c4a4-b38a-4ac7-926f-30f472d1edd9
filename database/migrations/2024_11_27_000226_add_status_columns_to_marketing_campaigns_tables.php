<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('marketing_campaigns', function (Blueprint $table) {
            $table->boolean('processing')->default(false);
        });
        Schema::table('marketing_campaign_consumers', function (Blueprint $table) {
            $table->string('status')->default('initialised');
            $table->uuid('external_reference')->nullable()->change();
            $table->unique(['consumer_reference', 'marketing_campaign_id'], 'consumer_campaign_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('marketing_campaigns', function (Blueprint $table) {
            $table->dropColumn('processing');
        });
        Schema::table('marketing_campaign_consumers', function (Blueprint $table) {
            $table->dropColumn('status');
            $table->uuid('external_reference')->change();
            $table->dropIndex('consumer_campaign_index');
        });
    }
};
