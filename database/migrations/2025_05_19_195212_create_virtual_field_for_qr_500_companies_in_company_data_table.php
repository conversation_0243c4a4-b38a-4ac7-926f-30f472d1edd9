<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_data', static function (Blueprint $table) {
            $table->boolean('qr_top_500_company')
                ->nullable()
                ->virtualAs("json_contains(payload, 'true', '$.qr_top_500_company')")
                ->after('payload');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_data', static function (Blueprint $table) {
            $table->dropColumn('qr_top_500_company');
        });
    }
};
