<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('billing_profiles', function (Blueprint $table) {
            $table->unsignedBigInteger('updated_by_id')->nullable();

            $table->foreign('updated_by_id')->references('id')->on('users')->noActionOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('billing_profiles', function (Blueprint $table) {
            $table->dropForeign('billing_profiles_updated_by_id_foreign');
            $table->dropColumn('updated_by_id');
        });
    }
};
