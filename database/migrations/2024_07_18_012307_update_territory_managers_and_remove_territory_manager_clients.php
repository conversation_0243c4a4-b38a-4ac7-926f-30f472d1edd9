<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumns('territory_managers', ['name', 'active_from', 'active_to']))
        {
            Schema::table('territory_managers', function (Blueprint $table) {
                $table->string('name');
                $table->dateTime('active_from')->useCurrent();
                $table->dateTime('active_to')->nullable();
                $table->unsignedBigInteger('created_by_id');

                $table->foreign('created_by_id')->references('id')->on('users');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('territory_manager_clients', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('territory_manager_id');
            $table->string('company_reference');
            $table->tinyInteger('status');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('territory_manager_id')->references('id')->on('territory_managers');

            $table->index('territory_manager_id');
            $table->index('company_reference');
            $table->index(['territory_manager_id', 'company_reference'], 'idx_id_reference');
        });

        Schema::table('territory_managers', function (Blueprint $table) {
            $table->dropForeign('territory_managers_created_by_id_foreign');
            $table->dropColumn(['name', 'active_from', 'active_to', 'created_by_id']);
        });
    }
};
