<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('billing_profiles', function (Blueprint $table) {
            $table->dropForeign('billing_profiles_campaign_id_foreign');
            $table->dropColumn('campaign_id');
        });

        Schema::create('campaign_billing_profile', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('campaign_id');
            $table->unsignedBigInteger('billing_profile_id');
            $table->timestamps();

            $table->foreign('campaign_id')->references('id')->on('company_campaigns');
            $table->foreign('billing_profile_id')->references('id')->on('billing_profiles');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaign_billing_profile');
        Schema::table('billing_profiles', function (Blueprint $table) {
            $table->unsignedBigInteger('campaign_id')->nullable();
            $table->foreign('campaign_id')->references('id')->on('company_campaigns');
        });
    }
};
