<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_manager_companies', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_success_manager_id');
            $table->unsignedBigInteger('company_id');
            $table->dateTime('active_from')->useCurrent();
            $table->dateTime('active_to')->nullable();
            $table->json('campaign_event');
            $table->timestamps();

            $table->foreign('customer_success_manager_id')->references('id')->on('customer_success_managers');
            $table->foreign('company_id')->references('id')->on('companies');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_manager_companies');
    }
};
