<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_user_relationships', function (Blueprint $table) {
            $table->index('company_id');
            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_user_relationships', function (Blueprint $table) {
            $table->dropIndex('company_user_relationships_company_id_index');
            $table->dropIndex('company_user_relationships_user_id_index');
        });
    }
};
