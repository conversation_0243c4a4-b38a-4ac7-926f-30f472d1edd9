<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('daily_ad_costs', function (Blueprint $table) {
            $table->unsignedBigInteger('advertising_account_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('daily_ad_costs', function (Blueprint $table) {
            $table->dropColumn('advertising_account_id');
        });
    }
};
