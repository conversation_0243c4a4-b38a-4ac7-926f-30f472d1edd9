<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('new_buyer_prospects', function (Blueprint $table) {
            $table->date('user_assigned_at')->nullable()->after('user_id');
        });
    }

    public function down(): void
    {
        Schema::table('new_buyer_prospects', function (Blueprint $table) {
            $table->dropColumn('user_assigned_at');
        });
    }
};
