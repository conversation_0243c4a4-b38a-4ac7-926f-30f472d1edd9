<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('calendar_event_attendees', function (Blueprint $table) {
            $table->boolean('is_organizer')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('calendar_event_attendees', function (Blueprint $table) {
            $table->dropColumn('is_organizer');
        });
    }
};
