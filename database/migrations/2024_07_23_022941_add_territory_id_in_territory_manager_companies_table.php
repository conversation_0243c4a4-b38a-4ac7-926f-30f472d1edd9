<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('territory_manager_companies', function (Blueprint $table) {
            $table->unsignedBigInteger('territory_id')->nullable()->default(null);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('territory_manager_companies', function (Blueprint $table) {
            $table->dropColumn('territory_id');
        });
    }
};
