<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('consumer_product_lifecycle_trackers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('consumer_product_id');
            $table->timestamp('consumer_product_created_at');
            $table->json('status_updates')->nullable();
            $table->json('queue_updates')->nullable();
            $table->timestamp('flagged_good_to_sell_at')->nullable();
            $table->json('allocation_attempts_scheduled')->nullable();
            $table->json('allocation_attempts')->nullable();
            $table->timestamp('invoiced_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('consumer_product_lifecycle_trackers');
    }
};
