<?php

namespace Database\Factories\SalesIntel;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SalesIntel\FailedCompanyImportRecord>
 */
class FailedCompanyImportRecordFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [];
    }

    public function zipcodeFilter(string $zipcode): Factory
    {
        return $this->state(fn (array $attributes) => ['filter' => 'zipcode', 'value' => $zipcode]);
    }

    public function stateFilter(string $state): Factory
    {
        return $this->state(fn (array $attributes) => ['filter' => 'state', 'value' => $state]);
    }

    public function domainFilter(string $domain): Factory
    {
        return $this->state(fn (array $attributes) => ['filter' => 'domain', 'value' => $domain]);
    }
}
