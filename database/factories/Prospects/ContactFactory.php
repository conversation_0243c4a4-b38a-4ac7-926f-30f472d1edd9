<?php

namespace Database\Factories\Prospects;

use App\Models\Prospects\Contact;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Prospects\Contact>
 */
class ContactFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            Contact::FIELD_FIRST_NAME   => fake()->firstName(),
            Contact::FIELD_LAST_NAME    => fake()->lastName(),
            Contact::FIELD_EMAIL        => fake()->email(),
            Contact::FIELD_TITLE        => fake()->jobTitle(),
            Contact::FIELD_DEPARTMENT   => fake()->word(),
            Contact::FIELD_CELL_PHONE   => fake()->phoneNumber(),
            Contact::FIELD_OFFICE_PHONE => fake()->phoneNumber(),
        ];
    }
}
