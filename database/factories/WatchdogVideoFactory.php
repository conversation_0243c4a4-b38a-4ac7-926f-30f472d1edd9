<?php

namespace Database\Factories;

use App\Models\Odin\Consumer;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WatchdogVideo>
 */
class WatchdogVideoFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'watchdog_video_id' => str()->ulid()->toString(),
            'consumer_id' => Consumer::factory(),
        ];
    }
}
