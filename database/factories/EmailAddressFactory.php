<?php

namespace Database\Factories;

use App\Enums\EmailAddress\SpamTrapScore;
use App\Models\EmailAddress;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<EmailAddress>
 */
class EmailAddressFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            EmailAddress::FIELD_EMAIL => $this->faker->unique()->safeEmail(),
            EmailAddress::FIELD_VALID => $this->faker->boolean(),
            EmailAddress::FIELD_DISPOSABLE => $this->faker->boolean(),
            EmailAddress::FIELD_FREQUENT_COMPLAINER => $this->faker->boolean(),
            EmailAddress::FIELD_SPAM_TRAP_SCORE => $this->faker->randomElement(SpamTrapScore::class),
            EmailAddress::FIELD_FRAUD_SCORE => $this->faker->randomFloat(0,0,100),
            EmailAddress::FIELD_PAYLOAD => [],
        ];
    }
}
