<?php

namespace Database\Factories;

use App\Enums\BundleInvoiceStatus;
use App\Models\Bundle;
use App\Models\BundleInvoice;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<BundleInvoice>
 */
class BundleInvoiceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $approvedOrDenied = rand(0, 1); // 1 is approved, 0 is denied. If it's denied it can't be paid

        $userId = User::factory()->create()->id;

        return [
            BundleInvoice::FIELD_STATUS => $this->faker->numberBetween(BundleInvoiceStatus::CANCELLED->value, BundleInvoiceStatus::COMPLETE->value),
            BundleInvoice::FIELD_COST => $this->faker->randomFloat(2, 10, 500),
            BundleInvoice::FIELD_CREDIT => $this->faker->randomFloat(2, 500, 1000),
            BundleInvoice::FIELD_NOTE => $this->faker->text(),
            BundleInvoice::FIELD_BUNDLE_ID => Bundle::factory(),
            BundleInvoice::FIELD_COMPANY_ID => Company::factory(),
            BundleInvoice::FIELD_ISSUED_BY => $userId,
            BundleInvoice::FIELD_APPROVED_BY => $userId,
            BundleInvoice::FIELD_DENIED_BY => $userId,
            BundleInvoice::FIELD_CANCELLED_BY => $userId,
            BundleInvoice::FIELD_ISSUED_AT => $this->faker->dateTimeThisMonth(),
            BundleInvoice::FIELD_APPROVED_AT => $approvedOrDenied ? $this->faker->dateTimeThisMonth() : null,
            BundleInvoice::FIELD_DENIED_AT => ! $approvedOrDenied ? $this->faker->dateTimeThisMonth() : null,
            BundleInvoice::FIELD_PAID_AT => ! $approvedOrDenied ? $this->faker->dateTimeThisMonth() : null,
            BundleInvoice::FIELD_CANCELLED_AT => ! $approvedOrDenied ? $this->faker->dateTimeThisMonth() : null,
        ];
    }
}
