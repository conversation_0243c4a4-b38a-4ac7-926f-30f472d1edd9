<?php

namespace Database\Factories\MissedProducts;

use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\Ruleset;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Random\RandomException;

/**
 * @extends Factory<OpportunityNotificationConfig>
 */
class OpportunityNotificationConfigFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     * @throws RandomException
     */
    public function definition(): array
    {
        return [
            OpportunityNotificationConfig::FIELD_UUID => $this->faker->uuid,
            OpportunityNotificationConfig::FIELD_NAME => $this->faker->colorName,
            OpportunityNotificationConfig::FIELD_RULE_ID => Ruleset::factory(),
            OpportunityNotificationConfig::FIELD_MAXIMUM_SEND_FREQUENCY => $this->faker->numberBetween(1,14),
            OpportunityNotificationConfig::FIELD_ATTEMPT_ON_DAYS => [0,1,2,3,4,5,6],
            OpportunityNotificationConfig::FIELD_SEND_TIME => $this->faker->time,
            OpportunityNotificationConfig::FIELD_LEAD_THRESHOLD => random_int(0,1) ? random_int(5,50) : 0, // 50/50 as to whether a threshold is set, if it is make it between 5 and 50
            OpportunityNotificationConfig::FIELD_EXPIRES_AT => now()->addMonth(),
            OpportunityNotificationConfig::FIELD_CREATED_BY => User::factory(),
            OpportunityNotificationConfig::FIELD_UPDATED_BY => User::factory(),
        ];
    }

}
