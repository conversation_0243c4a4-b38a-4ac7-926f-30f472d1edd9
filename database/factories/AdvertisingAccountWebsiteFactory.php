<?php

namespace Database\Factories;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Models\AdvertisingAccountWebsite;
use App\Models\Odin\Website;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AdvertisingAccountWebsite>
 */
class AdvertisingAccountWebsiteFactory extends Factory
{
    protected $model = AdvertisingAccountWebsite::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            AdvertisingAccountWebsite::FIELD_PLATFORM => $this->faker->randomElement(array_keys(AdvertisingPlatform::all())),
            AdvertisingAccountWebsite::FIELD_PLATFORM_ACCOUNT_ID => $this->faker->randomNumber(),
            AdvertisingAccountWebsite::FIELD_WEBSITE_ID => WebSite::factory()
        ];
    }
}
