<?php

namespace Database\Factories;

use App\Models\Ruleset;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class RulesetFactory extends Factory
{
    protected $model = Ruleset::class;

    public function definition(): array
    {
        return [
            'name'          => $this->faker->name(),
            'rules'         => $this->faker->words(),
            'filter'        => $this->faker->words(),
            'type'          => $this->faker->word(),
            'source'        => $this->faker->word(),
            'created_at'    => Carbon::now(),
            'updated_at'    => Carbon::now(),
        ];
    }
}
