<?php

namespace Database\Factories\Odin;

use App\Enums\Odin\QualityTier;
use App\Models\Odin\ProductAppointment;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AppointmentProcessingAllocation>
 */
class ProductAppointmentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            ProductAppointment::LEGACY_ID => null,
            ProductAppointment::LEAD_CONSUMER_PRODUCT_ID => 1,
            ProductAppointment::CONSUMER_PRODUCT_ID => 0,
            ProductAppointment::APPOINTMENT_TYPE => QualityTier::IN_HOME->value,
            ProductAppointment::APPOINTMENT_DATE => '2023-07-07',
            ProductAppointment::APPOINTMENT_TIME => '12:00:00',
            ProductAppointment::ALLOCATED_AS_LEAD => 0,
            ProductAppointment::ORIGINAL_APPOINTMENT_ID => 0,
            ProductAppointment::RELATED_APPT_SOLD_AND_UNSELLABLE => 0
        ];
    }
}
