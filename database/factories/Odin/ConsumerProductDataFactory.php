<?php

declare(strict_types=1);

namespace Database\Factories\Odin;

use App\Abstracts\ResetUniqueFactory;
use App\Enums\LegacyQuoteOrigin;
use App\Enums\Roofing\RoofCondition;
use App\Enums\Roofing\RoofPitch;
use App\Enums\Roofing\RoofShading;
use App\Enums\Roofing\RoofType;
use App\Enums\Solar\SystemType;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\SolarConfigurableFields;
use App\Models\Legacy\EloquentUtility;
use App\Models\Odin\ConsumerProductData;
use App\Repositories\Legacy\UtilityRepository;
use App\Services\TestProducts\TestProductGeneratorService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Factories\Factory;

final class ConsumerProductDataFactory extends ResetUniqueFactory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var  string
     */
    protected $model = ConsumerProductData::class;

    /** @inheritDoc */
    public function getData(bool $reset = false): array
    {
        return [
            ConsumerProductData::FIELD_PAYLOAD => [
                SolarConfigurableFields::SYSTEM_TYPE->value              => $this->faker->randomElement(SystemType::cases()),
                SolarConfigurableFields::SYSTEM_SIZE->value              => $this->faker->randomFloat(2),
                SolarConfigurableFields::SYSTEM_SIZE_OTHER->value        => '',
                GlobalConfigurableFields::STOREYS->value                 => $this->faker->numberBetween(1, 4),
                SolarConfigurableFields::ELECTRIC_COST->value            => $this->faker->randomNumber(3),
                GlobalConfigurableFields::ROOF_DIRECTION->value          => $this->faker->numberBetween(1, 2),
                GlobalConfigurableFields::ROOF_SHADING->value            => $this->faker->randomElement(RoofShading::cases()),
                GlobalConfigurableFields::ROOF_PITCH->value              => $this->faker->randomElement(RoofPitch::cases()),
                GlobalConfigurableFields::ROOF_TYPE->value               => $this->faker->randomElement(RoofType::cases()),
                GlobalConfigurableFields::ROOF_TYPE_OTHER->value         => '',
                GlobalConfigurableFields::ROOF_CONDITION->value          => $this->faker->randomElement(RoofCondition::cases()),
                SolarConfigurableFields::PANEL_TIER->value               => $this->faker->numberBetween(1, 2), //TODO
                GlobalConfigurableFields::BEST_TIME_TO_CALL->value       => $this->faker->dayOfWeek() . " " . $this->faker->time('h A') . '-' . $this->faker->time('h A'),
                GlobalConfigurableFields::BEST_TIME_TO_CALL_OTHER->value => '',
                SolarConfigurableFields::UTILITY_NAME->value             => $this->faker->company(),
                SolarConfigurableFields::UTILITY_ID->value               => $this->faker->randomNumber(3),
                GlobalConfigurableFields::OWN_PROPERTY->value            => $this->faker->boolean(),
                GlobalConfigurableFields::IP_ADDRESS->value              => $this->faker->ipv4(),
                GlobalConfigurableFields::ORIGIN->value                  => LegacyQuoteOrigin::SE->value,
            ]
        ];
    }

    /**
     * Override the utility name and ID.
     * If the given parameter is a ZIP code, select a random utility within that ZIP code.
     * If the given parameter is an Eloquent utility instance, use it directly.
     * @param string|EloquentUtility|null $utility
     * @return Factory
     * @throws BindingResolutionException
     */
    public function withRealUtility(string|EloquentUtility $utility = null): Factory
    {
        if (!$utility instanceof EloquentUtility) {
            /** @var UtilityRepository $repository */
            $repository = app()->make(UtilityRepository::class);

            $utility = $repository->getUtilitiesInZipcodeQuery($utility)->inRandomOrder()->first();
        }

        return $this->state(function (array $attributes) use ($utility) {
            return [
                ConsumerProductData::FIELD_PAYLOAD => array_merge($attributes[ConsumerProductData::FIELD_PAYLOAD], [
                    SolarConfigurableFields::UTILITY_NAME->value => $utility?->{EloquentUtility::FIELD_NAME} ?? $attributes[ConsumerProductData::FIELD_PAYLOAD][SolarConfigurableFields::UTILITY_NAME->value],
                    SolarConfigurableFields::UTILITY_ID->value   => $utility?->{EloquentUtility::FIELD_UTILITY_ID ?? $attributes[ConsumerProductData::FIELD_PAYLOAD][SolarConfigurableFields::UTILITY_ID->value]}
                ]),
            ];
        });
    }

    /**
     * @return Factory
     */
    public function withSolarFields(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                ConsumerProductData::FIELD_PAYLOAD => array_merge($attributes[ConsumerProductData::FIELD_PAYLOAD], [
                    SolarConfigurableFields::UTILITY_NAME->value  => 'Other',
                    SolarConfigurableFields::ELECTRIC_COST->value => $this->faker->numberBetween(100, 250),
                    GlobalConfigurableFields::OWN_PROPERTY->value => true,
                ]),
            ];
        });
    }
}
