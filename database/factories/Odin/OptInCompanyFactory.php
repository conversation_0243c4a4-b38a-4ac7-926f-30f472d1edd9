<?php

namespace Database\Factories\Odin;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\CompanyOptInName;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\OptInCompany;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<OptInCompany>
 */
class OptInCompanyFactory extends Factory
{
    protected $model = OptInCompany::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            OptInCompany::FIELD_COMPANY_ID => Company::factory(),
            OptInCompany::FIELD_CONSUMER_PRODUCT_ID => ConsumerProduct::factory(),
            OptInCompany::FIELD_COMPANY_CAMPAIGN_ID => CompanyCampaign::factory(),
            OptInCompany::FIELD_COMPANY_OPT_IN_NAME_ID => CompanyOptInName::factory(),
        ];
    }
}
