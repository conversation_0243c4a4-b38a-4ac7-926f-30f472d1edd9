<?php

namespace Database\Factories\Odin;

use App\Models\License;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<License>
 */
class LicenseFactory extends Factory
{
    protected $model = License::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            License::FIELD_COMPANY_ID     => $this->faker->randomNumber(),
            License::FIELD_INDUSTRY_ID => $this->faker->randomNumber(),
            License::FIELD_ISSUED_AT => $this->faker->dateTime(),
            License::FIELD_EXPIRES_AT => $this->faker->dateTime(),
            License::FIELD_NAME => $this->faker->name(),
            License::FIELD_URL => $this->faker->url(),
            License::FIELD_COUNTRY => $this->faker->country(),
            License::FIELD_STATE => $this->faker->locale(),
            License::FIELD_LICENSE_NUMBER => $this->faker->randomNumber(),
            License::FIELD_TYPE => $this->faker->randomElement([1,2]),
        ];
    }
}
