<?php

namespace Database\Factories;

use App\DataModels\HistoricalCompanyDailyRevenueDataModel;
use App\DataModels\HistoricalCompanyMonthlyRevenueDataModel;
use App\DataModels\HistoricalCompanyRejectionPercentagesDataModel;
use App\Models\HistoricalCompanyRejectionPercentage;
use App\Models\HistoricalCompanyRevenue;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<HistoricalCompanyRejectionPercentage>
 */
class HistoricalCompanyRejectionPercentageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            HistoricalCompanyRejectionPercentage::FIELD_COMPANY_ID            => fake()->randomNumber(),
            HistoricalCompanyRejectionPercentage::FIELD_YEAR                  => fake()->year(),
            HistoricalCompanyRejectionPercentage::FIELD_PRODUCT_ID            => fake()->randomNumber(),
            HistoricalCompanyRejectionPercentage::FIELD_REJECTION_PERCENTAGES => new HistoricalCompanyRejectionPercentagesDataModel(),
        ];
    }
}
