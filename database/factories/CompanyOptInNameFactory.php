<?php

namespace Database\Factories;

use App\Models\CompanyOptInName;
use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyOptInName>
 */
class CompanyOptInNameFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            CompanyOptInName::FIELD_NAME       => $this->faker->name(),
            CompanyOptInName::FIELD_COMPANY_ID => Company::factory(),
        ];
    }
}
