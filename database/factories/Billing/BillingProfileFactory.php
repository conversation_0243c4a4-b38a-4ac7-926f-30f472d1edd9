<?php

namespace Database\Factories\Billing;

use App\Abstracts\ResetUniqueFactory;
use App\Enums\Billing\BillingProfileFrequenciesEnum;
use App\Enums\Billing\PaymentMethodServices;
use App\Models\Billing\BillingProfile;
use App\Models\Odin\Company;

class BillingProfileFactory extends ResetUniqueFactory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function getData(bool $reset = false): array
    {
        /** @var BillingProfileFrequenciesEnum $cron */
        $cron = $this->faker->randomElement(BillingProfileFrequenciesEnum::cases());

        return [
            BillingProfile::FIELD_PAYMENT_GATEWAY_CLIENT_CODE => $this->faker->numberBetween(1, 100),
            BillingProfile::FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE => $this->faker->numberBetween(1, 100),
            BillingProfile::FIELD_PAYMENT_METHOD              => $this->faker->randomElement(PaymentMethodServices::allMethods()),
            BillingProfile::FIELD_BILLING_CONTACT             => $this->faker->numberBetween(1, 100),
            BillingProfile::FIELD_BILLING_FREQUENCY_CRON      => $cron->value,
            BillingProfile::FIELD_CRON_DATA                   => $cron->cronData(),
            BillingProfile::FIELD_COMPANY_ID                  => Company::factory(),
            BillingProfile::FIELD_THRESHOLD_IN_DOLLARS        => $this->faker->numberBetween(1, 100),
            BillingProfile::FIELD_MAX_ALLOWED_CHARGE_ATTEMPTS => $this->faker->numberBetween(1, 100),
        ];
    }
}
