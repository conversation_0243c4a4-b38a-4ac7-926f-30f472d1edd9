<?php

namespace Database\Factories\Billing;

use App\Abstracts\ResetUniqueFactory;
use App\Models\Billing\CreditType;
use App\Models\Billing\Credit;
use App\Models\Odin\Company;
use Carbon\Carbon;

class CreditFactory extends ResetUniqueFactory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function getData(bool $reset = false): array
    {
        $company = $this->faker->randomElement(Company::all());
        $credit_type = $this->faker->randomElement(CreditType::all());
        $initial_amount = $this->faker->numberBetween(100,10000);

        return [
            Credit::FIELD_COMPANY_ID => $company->{Company::FIELD_ID},
            Credit::FIELD_CREDIT_TYPE => $credit_type->{CreditType::FIELD_SLUG},
            Credit::FIELD_INITIAL_VALUE => $initial_amount,
            Credit::FIELD_REMAINING_VALUE => $this->faker->randomFloat(2,0, $initial_amount),
            Credit::FIELD_EXPIRES_AT => Carbon::now()->addDays($credit_type->{CreditType::FIELD_EXPIRES_IN_DAYS}),
        ];
    }
}
