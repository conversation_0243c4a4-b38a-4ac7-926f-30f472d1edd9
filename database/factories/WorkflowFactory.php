<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Workflow;
use App\Models\WorkflowEvent;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<\App\Models\Workflow>
 */
final class WorkflowFactory extends Factory
{
    /**
    * The name of the factory's corresponding model.
    *
    * @var  string
    */
    protected $model = Workflow::class;

    /**
    * Define the model's default state.
    *
    * @return  array
    */
    public function definition(): array
    {
        return [
            'workflow_event_id' => WorkflowEvent::factory(),
            'entry_action_id' => null,
            'name' => 'Test Workflow',
            'generic' => $this->faker->boolean,
        ];
    }
}
