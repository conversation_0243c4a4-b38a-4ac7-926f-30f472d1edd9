<x-mail-layout>
    <div class="my-0 mx-auto max-w-screen-sm">
        <table class="w-full">
            <tbody>
            <tr class="bg-grey-50">
                <td class="bg-light-module">
                    <div class="flex justify-center">
                        <div class="w-1/2 py-4">
                            @if((isset($industry) && $industry == "solar") || !isset($industry))
                                <svg class="w-full" viewBox="0 0 295 65" fill="none"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M91.9537 38.3454C91.9537 43.393 88.2294 46.1712 81.536 46.1712C74.8427 46.1712 71.1184 43.3148 71.1184 38.1628H74.6213C74.6213 41.3453 77.0695 43.1061 81.523 43.1061C85.9114 43.1061 88.4247 41.4627 88.4247 38.3454C88.4247 30.5849 71.5481 37.2107 71.5481 26.2025C71.5481 21.6245 75.1292 19.1072 81.523 19.1072C87.9168 19.1072 91.4979 21.7027 91.4979 26.3851H87.995C87.995 23.6722 85.6901 22.1723 81.523 22.1723C77.395 22.1723 75.0511 23.6331 75.0511 26.2025C75.0771 33.95 91.9537 27.3242 91.9537 38.3454Z"
                                        fill="#14B1E7"/>
                                    <path
                                        d="M113.362 36.2852C113.362 41.7371 108.947 46.1587 103.504 46.1587C98.0608 46.1587 93.6724 41.7371 93.6724 36.2852C93.6724 30.8333 98.0608 26.4117 103.504 26.4117C108.947 26.4117 113.362 30.8333 113.362 36.2852ZM110.08 36.2852C110.08 32.4767 107.124 29.3725 103.504 29.3725C99.8839 29.3725 96.9669 32.4767 96.9669 36.2852C96.9669 40.0937 99.8839 43.198 103.504 43.198C107.124 43.211 110.08 40.0937 110.08 36.2852Z"
                                        fill="#14B1E7"/>
                                    <path d="M115.809 19.0946H119.312V45.4413H115.809V19.0946Z" fill="#14B1E7"/>
                                    <path
                                        d="M141.059 27.1417V45.4409H137.556V43.0671C135.876 44.9713 133.532 46.1713 130.902 46.1713C125.862 46.1713 121.774 41.7497 121.774 36.2978C121.774 30.8459 125.862 26.4243 130.902 26.4243C133.532 26.4243 135.876 27.6373 137.556 29.5285V27.1547H141.059V27.1417ZM137.765 36.2847C137.765 32.4762 134.913 29.372 131.41 29.372C127.907 29.372 125.055 32.4762 125.055 36.2847C125.055 40.0933 127.907 43.1975 131.41 43.1975C134.913 43.1975 137.765 40.0933 137.765 36.2847Z"
                                        fill="#14B1E7"/>
                                    <path
                                        d="M153.847 26.4117V29.3725C149.68 29.3725 147.557 31.7202 147.557 36.2852V45.4283H144.054V27.1421H147.557V28.9682C148.99 27.2856 151.099 26.4117 153.847 26.4117Z"
                                        fill="#14B1E7"/>
                                    <path
                                        d="M169.474 35.7107L176.493 45.4407H172.365L165.503 35.9324H159.981V45.4407H156.478V19.8245H165.945C173.55 19.8245 176.871 22.2765 176.871 27.8719C176.858 32.5543 174.553 35.0064 169.474 35.7107ZM165.932 32.8543C171.089 32.8543 173.355 31.3544 173.355 27.8719C173.355 24.3895 171.089 22.8895 165.932 22.8895H159.981V32.8413H165.932V32.8543Z"
                                        fill="#0054A6"/>
                                    <path
                                        d="M193.59 39.8063H197.067C197.067 39.8063 195.309 46.1713 188.225 46.1713C182.782 46.1713 178.394 41.7497 178.394 36.2978C178.394 30.8459 182.782 26.4243 188.225 26.4243C193.629 26.4243 197.718 31.0676 197.718 37.2499H181.753C182.157 40.615 184.892 43.2105 188.225 43.2105C192.132 43.2105 193.59 39.8063 193.59 39.8063ZM181.975 34.2761H194.098C193.408 31.4197 191.025 29.372 188.212 29.372C185.256 29.372 182.782 31.4197 181.975 34.2761Z"
                                        fill="#0054A6"/>
                                    <path
                                        d="M217.994 27.142L210.389 45.4412H205.571L197.966 27.142H201.547L207.98 42.2587L214.413 27.142H217.994Z"
                                        fill="#0054A6"/>
                                    <path
                                        d="M219.23 20.7771C219.23 19.4206 220.285 18.3641 221.64 18.3641C222.955 18.3641 224.01 19.4206 224.01 20.7771C224.01 22.0944 222.955 23.1509 221.64 23.1509C220.298 23.1509 219.23 22.0944 219.23 20.7771ZM219.895 27.142H223.398V45.4412H219.895V27.142Z"
                                        fill="#0054A6"/>
                                    <path
                                        d="M241.043 39.8063H244.52C244.52 39.8063 242.762 46.1713 235.678 46.1713C230.235 46.1713 225.846 41.7497 225.846 36.2978C225.846 30.8459 230.235 26.4243 235.678 26.4243C241.082 26.4243 245.171 31.0676 245.171 37.2499H229.206C229.61 40.615 232.344 43.2105 235.678 43.2105C239.585 43.2105 241.043 39.8063 241.043 39.8063ZM229.427 34.2761H241.551C240.861 31.4197 238.478 29.372 235.665 29.372C232.722 29.372 230.235 31.4197 229.427 34.2761Z"
                                        fill="#0054A6"/>
                                    <path
                                        d="M276.775 27.142L271.071 45.4412H266.136L261.279 30.3244L256.422 45.4412H251.486L245.783 27.142H249.259L254.156 42.2587L259.013 27.142H263.584L268.402 42.2587L273.298 27.142H276.775Z"
                                        fill="#0054A6"/>
                                    <path
                                        d="M294.419 40.3155C294.419 44.0849 291.385 46.1717 285.981 46.1717C280.577 46.1717 277.582 44.0066 277.582 40.172H281.084C281.084 42.298 282.842 43.4588 285.981 43.4588C289.158 43.4588 290.916 42.3632 290.916 40.3155C290.916 34.9679 278.024 39.9503 278.024 31.6811C278.024 28.316 280.876 26.4117 285.994 26.4117C291.111 26.4117 293.989 28.3812 293.989 31.8637H290.486C290.486 30.1029 288.885 29.1247 285.994 29.1247C283.142 29.1247 281.54 30.0377 281.54 31.6811C281.527 37.0547 294.419 32.0854 294.419 40.3155Z"
                                        fill="#0054A6"/>
                                    <path
                                        d="M65.7791 30.8465C55.2703 28.3161 54.502 25.4467 62.5887 18.3383C52.0018 20.2687 50.2698 17.9731 55.114 8.3605C46.0637 14.1646 43.6025 12.6647 44.449 1.91731C38.2114 10.6952 35.2815 10.2256 31.7004 0C29.1741 10.5256 26.3093 11.2952 19.2123 3.19551C21.1396 13.7994 18.8477 15.5341 9.25044 10.6821C15.0452 19.747 13.5477 22.2121 2.81756 21.3643C11.5814 27.6249 11.1126 30.5595 0.90332 34.1333C11.4121 36.6636 12.1804 39.533 4.09372 46.6545C14.6806 44.7241 16.4256 47.0197 11.5684 56.6323C20.6187 50.8282 23.0798 52.3281 22.2204 63.0755C28.471 54.2976 31.4009 54.7671 34.9689 64.9928C37.4952 54.4671 40.3731 53.6976 47.4701 61.7973C45.5428 51.1934 47.8347 49.4456 57.4319 54.3106C51.6371 45.2458 53.1347 42.7807 63.8648 43.6285C55.101 37.3549 55.5828 34.4202 65.7791 30.8465Z"
                                        fill="#FFD400"/>
                                    <path
                                        d="M54.581 34.9028C55.9093 23.1642 47.484 12.5603 35.7642 11.23C24.0443 9.8996 13.4574 18.3253 12.1162 30.0639C10.7879 41.8025 19.2132 52.4064 30.933 53.7368C42.6528 55.0802 53.2397 46.6414 54.581 34.9028Z"
                                        fill="white"/>
                                    <path
                                        d="M27.1688 17.347L26.14 17.3992H26.0098L22.7282 17.5427L22.598 17.5557L21.5172 17.6079L20.3062 19.1209L21.4 24.9641L23.184 26.3336L24.2779 26.2423L24.4081 26.2293L27.7287 25.9293L27.8589 25.9162L28.9007 25.8249L29.9945 24.2598L28.8356 18.6774L27.1688 17.347Z"
                                        fill="url(#paint0_radial_308_272)"/>
                                    <path
                                        d="M35.4758 16.9689L34.5252 17.021H34.408L31.413 17.1515L31.2827 17.1645L30.3061 17.2036L29.2383 18.6514L30.3972 24.2338L32.0641 25.5381L33.0537 25.4468L33.184 25.4337L36.2051 25.1598L36.3223 25.1468L37.2729 25.0685L38.2365 23.5816L37.0385 18.2471L35.4758 16.9689Z"
                                        fill="url(#paint1_radial_308_272)"/>
                                    <path
                                        d="M39.3297 34.2893L38.3791 34.4328L38.2619 34.4458L35.2017 34.8762L35.0715 34.9023L34.0688 35.0458L33.001 36.7414L34.2641 42.7802L35.97 44.0584L36.9857 43.8628L37.1159 43.8367L40.2022 43.2367L40.3194 43.2106L41.283 43.028L42.2466 41.2933L40.9444 35.5414L39.3297 34.2893Z"
                                        fill="url(#paint2_radial_308_272)"/>
                                    <path
                                        d="M30.8791 35.5026L29.8244 35.646L29.6941 35.6721L26.3345 36.1417L26.2042 36.1677L25.0974 36.3243L23.8733 38.1242L25.0713 44.4761L26.9204 45.8065L28.0273 45.5978L28.1706 45.5717L31.5693 44.9065L31.6995 44.8804L32.7543 44.6848L33.8482 42.8457L32.5981 36.8069L30.8791 35.5026Z"
                                        fill="url(#paint3_radial_308_272)"/>
                                    <path
                                        d="M37.3634 25.473L36.4128 25.5513L36.2956 25.5643L33.2615 25.8513H33.1443L32.1546 25.9426L31.0868 27.5208L32.2848 33.3118L33.9777 34.6161L34.9804 34.4726L35.1106 34.4596L38.1578 34.0292L38.288 34.0031L39.2386 33.8726L40.2022 32.2684L38.9521 26.7251L37.3634 25.473Z"
                                        fill="url(#paint4_radial_308_272)"/>
                                    <path
                                        d="M28.9918 26.2426L27.95 26.3339L27.8198 26.3469L24.4992 26.6599L24.3689 26.673L23.2751 26.7643L22.051 28.4207L23.197 34.5118L25.02 35.8682L26.1139 35.7117L26.2571 35.6987L29.6168 35.2291L29.747 35.203L30.7888 35.0596L31.8827 33.364L30.6846 27.5599L28.9918 26.2426Z"
                                        fill="url(#paint5_radial_308_272)"/>
                                    <path
                                        d="M43.0799 16.6298L42.2205 16.6689H42.1033L39.3556 16.7993H39.2384L38.3399 16.8384L37.4023 18.234L38.6004 23.5555L40.163 24.8076L41.0746 24.7294L41.1918 24.7163L43.9654 24.4685L44.0696 24.4555L44.9421 24.3772L45.7885 22.9556L44.5514 17.8558L43.0799 16.6298Z"
                                        fill="url(#paint6_radial_308_272)"/>
                                    <path
                                        d="M46.5197 25.9686L45.0351 24.7556L44.1627 24.8339L44.0585 24.8469L41.2848 25.1078L41.1676 25.1208L40.2561 25.1991L39.3185 26.699L40.5686 32.2162L42.1573 33.4683L43.0688 33.3378L43.186 33.3248L45.9858 32.9335L46.0899 32.9074L46.9624 32.79L47.8088 31.264L46.5197 25.9686Z"
                                        fill="url(#paint7_radial_308_272)"/>
                                    <path
                                        d="M51.1805 24.1937L48.6282 24.4285L48.524 24.4415L47.6906 24.5198L46.8572 25.9415L48.1464 31.2238L49.6309 32.4238L50.4773 32.3064L50.5815 32.2934L53.0036 31.9542C52.9385 29.2544 52.3134 26.6197 51.1805 24.1937Z"
                                        fill="url(#paint8_radial_308_272)"/>
                                    <path
                                        d="M48.5631 34.3944L47.0655 33.1814L46.18 33.3118L46.0759 33.3249L43.2761 33.7292L43.1589 33.7422L42.2474 33.8727L41.3098 35.49L42.612 41.2289L44.2137 42.4549L45.1383 42.2854L45.2555 42.2593L48.0682 41.7115L48.1854 41.6984L49.0579 41.5289L49.9043 39.8855L48.5631 34.3944Z"
                                        fill="url(#paint9_radial_308_272)"/>
                                    <path
                                        d="M32.8464 45.1411L31.7916 45.3498L31.6614 45.3759L28.2626 46.0411L28.1194 46.0672L26.9995 46.2889L25.7754 48.2453L26.2702 50.88C28.523 51.7539 30.9191 52.1973 33.3412 52.1973C34.1486 52.1973 34.9559 52.1452 35.7503 52.0539L34.5913 46.4454L32.8464 45.1411Z"
                                        fill="url(#paint10_radial_308_272)"/>
                                    <path
                                        d="M16.3467 37.094L16.5029 37.0679L20.2012 36.5462L20.3444 36.5201L21.5034 36.3636L22.7535 34.5767L21.6206 28.4726L19.7975 27.0901L18.6516 27.1944L18.5083 27.2075L14.8491 27.5466L14.7059 27.5596L14.2892 27.5988C13.5469 30.5073 13.4818 33.5594 14.0938 36.5071L15.1356 37.2635L16.3467 37.094Z"
                                        fill="url(#paint11_radial_308_272)"/>
                                    <path
                                        d="M22.1934 46.7326L23.3653 46.5108L24.6285 44.5674L23.4435 38.2025L21.5943 36.833L20.4354 37.0026L20.2921 37.0287L16.5939 37.5634L16.4376 37.5895L15.2136 37.7591L14.6276 38.5547C15.6954 41.8545 17.6096 44.8153 20.188 47.1369L22.0501 46.7717L22.1934 46.7326Z"
                                        fill="url(#paint12_radial_308_272)"/>
                                    <path
                                        d="M41.3887 43.459L40.425 43.6546L40.3078 43.6807L37.2216 44.2807L37.0914 44.3068L36.0887 44.5024L35.0209 46.3415L36.1929 51.976C38.9796 51.5717 41.6361 50.5674 44.0061 49.0413L43.0294 44.6981L41.3887 43.459Z"
                                        fill="url(#paint13_radial_308_272)"/>
                                    <path
                                        d="M14.7707 27.0902L18.4299 26.7641L18.5731 26.7511L19.719 26.6467L20.9691 25.0033L19.8753 19.1471L19.3023 18.7036C16.9974 21.0513 15.3176 23.9599 14.4191 27.1293L14.6274 27.1163L14.7707 27.0902Z"
                                        fill="url(#paint14_radial_308_272)"/>
                                    <path
                                        d="M50.164 42.7153L49.1613 41.9327L48.2758 42.1023L48.1716 42.1284L45.3589 42.6762L45.2417 42.7023L44.3171 42.8849L43.3795 44.6196L44.3301 48.8194C46.6871 47.2282 48.6795 45.1413 50.164 42.7153Z"
                                        fill="url(#paint15_radial_308_272)"/>
                                    <path
                                        d="M23.4565 46.9806L22.2845 47.2154L22.1413 47.2414L20.6698 47.5284C22.2064 48.8327 23.9253 49.8892 25.7875 50.6587L25.3447 48.324L23.4565 46.9806Z"
                                        fill="url(#paint16_radial_308_272)"/>
                                    <path
                                        d="M38.2504 16.4604L39.1489 16.4212H39.2661L42.0268 16.3039H42.1309L42.9904 16.2647L43.4331 15.5735C41.2194 14.2431 38.7582 13.3692 36.2059 12.991L36.7008 15.1952L38.2504 16.4604Z"
                                        fill="url(#paint17_radial_308_272)"/>
                                    <path
                                        d="M53.0175 32.333L50.6736 32.6721L50.5694 32.6852L49.723 32.8026L48.9026 34.3416L50.2439 39.8196L51.2596 40.6152C52.4185 38.0588 53.0175 35.2807 53.0175 32.4765C53.0175 32.4374 53.0175 32.3852 53.0175 32.333Z"
                                        fill="url(#paint18_radial_308_272)"/>
                                    <path
                                        d="M46.1287 22.9293L47.6002 24.1293L48.4336 24.051L48.5378 24.038L51.025 23.8162C49.7098 21.1294 47.8085 18.7817 45.4515 16.9426L44.8916 17.8295L46.1287 22.9293Z"
                                        fill="url(#paint19_radial_308_272)"/>
                                    <path
                                        d="M22.5192 17.1385H22.6494L25.9309 16.995H26.0611L27.0899 16.9559L28.1837 15.5212L27.78 13.6039C25.384 14.3082 23.1442 15.469 21.1909 17.0081L21.4383 17.1907L22.5192 17.1385Z"
                                        fill="url(#paint20_radial_308_272)"/>
                                    <path
                                        d="M30.2151 16.8124L31.2048 16.7733H31.322L34.3301 16.6428H34.4473L35.3849 16.6037L36.3485 15.2342L35.8406 12.9647C35.0202 12.8604 34.1868 12.8082 33.3534 12.8082C31.5954 12.8082 29.8635 13.043 28.1707 13.4995L28.5874 15.5081L30.2151 16.8124Z"
                                        fill="url(#paint21_radial_308_272)"/>
                                    <path
                                        d="M35.4758 16.9689L34.5383 17.008H34.421L31.413 17.1384H31.2958L30.3061 17.1776L29.2383 18.6253L30.3972 24.1946L31.0744 24.7294C31.5822 24.2207 32.0901 23.7251 32.598 23.2555L32.7021 23.1512C33.5746 22.3425 34.421 21.573 35.2675 20.8426L35.3717 20.7513C36.0097 20.2035 36.6478 19.6818 37.2599 19.1862L37.0385 18.1949L35.4758 16.9689Z"
                                        fill="url(#paint22_radial_308_272)"/>
                                    <path
                                        d="M27.1688 17.347L26.14 17.3992H26.0098L22.7282 17.5427L22.598 17.5557L21.5172 17.6079L20.3062 19.1209L21.4 24.9641L23.184 26.3336L24.2779 26.2423L24.4081 26.2293L27.7287 25.9293L27.8589 25.9162L28.9007 25.8249L29.9945 24.2598L28.8356 18.6774L27.1688 17.347Z"
                                        fill="url(#paint23_radial_308_272)"/>
                                    <path
                                        d="M39.2384 16.7995L38.3399 16.8386L37.4023 18.2212L37.5716 18.9777C38.1967 18.482 38.8217 18.0125 39.4208 17.569L39.5119 17.5038C39.8635 17.2429 40.2151 16.9951 40.5667 16.7473L39.3556 16.7995H39.2384Z"
                                        fill="url(#paint24_radial_308_272)"/>
                                    <path
                                        d="M17.5575 43.1845C18.4951 41.3976 19.4718 39.689 20.4744 38.0716L20.5916 37.889C20.813 37.5369 21.0214 37.1978 21.2427 36.8586L20.4224 36.976L20.2791 37.0021L16.5809 37.5369L16.4246 37.563L15.2005 37.7325L14.6276 38.5281C15.2266 40.4063 16.1121 42.1801 17.245 43.7975C17.3101 43.667 17.3752 43.5497 17.4403 43.4192L17.5575 43.1845Z"
                                        fill="url(#paint25_radial_308_272)"/>
                                    <path
                                        d="M16.3467 37.094L16.5029 37.0679L20.2012 36.5462L20.3444 36.5201L21.5034 36.3636L22.7535 34.5767L21.6206 28.4726L19.7975 27.0901L18.6516 27.1944L18.5083 27.2075L14.8491 27.5466L14.7059 27.5596L14.2892 27.5988C13.5469 30.5073 13.4818 33.5594 14.0938 36.5071L15.1356 37.2635L16.3467 37.094Z"
                                        fill="url(#paint26_radial_308_272)"/>
                                    <path
                                        d="M27.9484 26.334L27.8182 26.347L24.4976 26.6601L24.3674 26.6731L23.2735 26.7775L22.0625 28.4339L23.1303 34.1337C23.8335 33.1554 24.5367 32.2033 25.2659 31.3033L25.3831 31.1599C26.2947 30.0121 27.2192 28.9165 28.1438 27.8861L28.248 27.7687C28.6256 27.3513 28.9902 26.934 29.3678 26.5427L28.9902 26.2557L27.9484 26.334Z"
                                        fill="url(#paint27_radial_308_272)"/>
                                    <path
                                        d="M22.5192 17.1385H22.6494L25.9309 16.995H26.0611L27.0899 16.9559L28.1837 15.5212L27.78 13.6039C25.384 14.3082 23.1442 15.469 21.1909 17.0081L21.4383 17.1907L22.5192 17.1385Z"
                                        fill="url(#paint28_radial_308_272)"/>
                                    <path
                                        d="M30.2151 16.8124L31.2048 16.7733H31.322L34.3301 16.6428H34.4473L35.3849 16.6037L36.3485 15.2342L35.8406 12.9647C35.0202 12.8604 34.1868 12.8082 33.3534 12.8082C31.5954 12.8082 29.8635 13.043 28.1707 13.4995L28.5874 15.5081L30.2151 16.8124Z"
                                        fill="url(#paint29_radial_308_272)"/>
                                    <path
                                        d="M14.7707 27.0902L18.4299 26.7641L18.5731 26.7511L19.719 26.6467L20.9691 25.0033L19.8753 19.1471L19.3023 18.7036C16.9974 21.0513 15.3176 23.9599 14.4191 27.1293L14.6274 27.1163L14.7707 27.0902Z"
                                        fill="url(#paint30_radial_308_272)"/>
                                    <path
                                        d="M38.2504 16.4604L39.1489 16.4212H39.2661L41.1413 16.343C41.4017 16.1604 41.6491 15.9908 41.8965 15.8213L41.9877 15.7561C42.2611 15.5735 42.5346 15.3909 42.8081 15.2083C40.7506 14.0735 38.5238 13.3301 36.2059 12.991L36.7008 15.1952L38.2504 16.4604Z"
                                        fill="url(#paint31_radial_308_272)"/>
                                    <defs>
                                        <radialGradient id="paint0_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.8183 26.4758) scale(55.2147 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint1_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.8173 26.4758) scale(55.2147 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint2_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.8166 26.4753) scale(55.2147 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint3_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.8174 26.4756) scale(55.2147 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint4_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.8167 26.476) scale(55.2147 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint5_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.8182 26.476) scale(55.2147 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint6_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.8166 26.4758) scale(55.2147 55.3031)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint7_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.8185 26.476) scale(55.2147 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint8_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.687 26.398) scale(55.215 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint9_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.8175 26.476) scale(55.2145 55.3033)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint10_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.815 26.4767) scale(55.2172 55.3057)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint11_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.6873 26.3988) scale(55.2148 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint12_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.6871 26.3987) scale(55.2147 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint13_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.6878 26.3989) scale(55.2149 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint14_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.687 26.3989) scale(55.2147 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint15_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.6863 26.3987) scale(55.2149 55.3031)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint16_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.6871 26.3989) scale(55.2147 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint17_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.6881 26.3991) scale(55.2145 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint18_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.6882 26.3985) scale(55.2147 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint19_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.6879 26.3987) scale(55.2147 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint20_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.6874 26.399) scale(55.2146 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint21_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(10.6873 26.3989) scale(55.2147 55.3032)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint22_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(7.95898 16.3272) scale(51.6636 51.7464)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint23_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(8.09019 16.404) scale(51.6636 51.7464)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint24_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(7.95825 16.3273) scale(51.6636 51.7464)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint25_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(7.95899 16.3265) scale(51.6635 51.7464)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint26_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(7.95916 16.3271) scale(51.6637 51.7464)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint27_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(7.95836 16.3275) scale(51.6635 51.7464)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint28_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(7.95927 16.3272) scale(51.6635 51.7464)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint29_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(7.95915 16.3272) scale(51.6636 51.7464)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint30_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(7.95884 16.3272) scale(51.6636 51.7464)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                        <radialGradient id="paint31_radial_308_272" cx="0" cy="0" r="1"
                                                        gradientUnits="userSpaceOnUse"
                                                        gradientTransform="translate(7.95997 16.3273) scale(51.6634 51.7464)">
                                            <stop offset="0.06" stop-color="#ECF8FE"/>
                                            <stop offset="0.24" stop-color="#5FCCF5"/>
                                            <stop offset="0.28" stop-color="#50C5F1"/>
                                            <stop offset="0.34" stop-color="#2BB4E7"/>
                                            <stop offset="0.4" stop-color="#00A0DC"/>
                                            <stop offset="0.59" stop-color="#1B75BC"/>
                                            <stop offset="0.8" stop-color="#0054A6"/>
                                        </radialGradient>
                                    </defs>
                                </svg>
                            @elseif($industry == "roofing")
                                <svg class="w-full" viewBox="0 0 300 51" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#clip0_12_350)">
                                        <path d="M0.106667 0L0 49.908L15.5467 23.0704L48.6133 23.1224L58.4 6.20927H99.8133V0H0.106667Z" fill="#00B2FF"/>
                                        <path d="M32.3201 47.206L47.0134 25.6165H17.6267L32.3201 47.206Z" fill="#141C24"/>
                                        <path d="M99.8133 8.52148V49.908H82.5066L59.2533 8.52148H99.8133Z" fill="#141C24"/>
                                        <path d="M107.387 28.1886C107.093 28.1943 106.807 28.0923 106.587 27.9028C106.478 27.8248 106.39 27.723 106.33 27.6056C106.27 27.4882 106.239 27.3586 106.24 27.2273V1.40296C106.241 1.25627 106.281 1.11237 106.356 0.985003C106.431 0.857638 106.538 0.751078 106.667 0.675515C106.887 0.485952 107.173 0.38396 107.467 0.389733H119.707C122.835 0.244201 125.937 1.01324 128.613 2.59805C129.679 3.30431 130.54 4.26538 131.114 5.38907C131.688 6.51275 131.957 7.76137 131.893 9.01516C131.975 10.8101 131.366 12.5701 130.187 13.9514C128.938 15.302 127.319 16.2757 125.52 16.7573L132.48 26.9155C132.512 27.0611 132.512 27.2116 132.48 27.3572C132.485 27.4726 132.462 27.5876 132.41 27.6918C132.359 27.7961 132.282 27.8864 132.187 27.9547C132.002 28.1104 131.764 28.1937 131.52 28.1886H127.627C127.178 28.2118 126.736 28.0833 126.373 27.8248C126.123 27.6178 125.899 27.3823 125.707 27.1234L119.467 17.5886H112.613V27.2273C112.614 27.3558 112.585 27.4828 112.53 27.5995C112.475 27.7161 112.394 27.8196 112.293 27.9028C112.054 28.0958 111.751 28.1974 111.44 28.1886H107.387ZM112.72 13.2499H119.467C121.033 13.3657 122.596 12.9828 123.92 12.1588C124.396 11.7827 124.773 11.3011 125.019 10.7542C125.264 10.2073 125.372 9.611 125.333 9.01516C125.366 8.40368 125.25 7.79335 124.995 7.23364C124.74 6.67392 124.354 6.18059 123.867 5.79362C122.555 4.93879 120.987 4.53637 119.413 4.65049H112.613L112.72 13.2499Z" fill="#141C24"/>
                                        <path d="M149.627 28.5782C147.146 28.636 144.677 28.2394 142.347 27.4091C140.436 26.7074 138.797 25.4456 137.653 23.7978C136.448 21.9783 135.809 19.8579 135.813 17.6925C135.813 16.5753 135.813 15.4322 135.813 14.341C135.813 13.2499 135.813 12.0807 135.813 10.9116C135.822 8.76858 136.471 6.6743 137.68 4.88423C138.895 3.23746 140.57 1.96586 142.507 1.22102C147.106 -0.406842 152.147 -0.406842 156.747 1.22102C158.678 1.97344 160.352 3.24353 161.573 4.88423C162.798 6.66579 163.44 8.76742 163.413 10.9116C163.413 12.0807 163.413 13.2239 163.413 14.341C163.413 15.4582 163.413 16.5753 163.413 17.6925C163.439 19.8572 162.808 21.9811 161.6 23.7978C160.446 25.4463 158.797 26.7075 156.88 27.4091C154.559 28.2374 152.098 28.634 149.627 28.5782ZM149.627 24.1875C151.443 24.2321 153.22 23.6732 154.667 22.6027C155.401 21.9476 155.975 21.1413 156.349 20.2424C156.723 19.3435 156.886 18.3748 156.827 17.4067C156.827 16.2376 156.827 15.1724 156.827 14.1851C156.827 13.1979 156.827 12.1327 156.827 10.9636C156.848 9.62146 156.488 8.29967 155.787 7.1445C155.156 6.1679 154.221 5.41258 153.12 4.98815C150.754 4.08691 148.126 4.08691 145.76 4.98815C144.649 5.39769 143.711 6.15655 143.093 7.1445C142.406 8.30691 142.039 9.62235 142.027 10.9636C142.027 12.1327 142.027 13.1979 142.027 14.1851C142.027 15.1724 142.027 16.2376 142.027 17.4067C141.965 18.3774 142.13 19.3491 142.508 20.249C142.887 21.1489 143.47 21.9536 144.213 22.6027C145.76 23.7551 147.684 24.3186 149.627 24.1875Z" fill="#141C24"/>
                                        <path d="M181.333 28.5782C178.862 28.6358 176.401 28.2392 174.08 27.4091C172.163 26.7075 170.514 25.4463 169.36 23.7978C168.175 21.9706 167.547 19.8537 167.547 17.6925C167.547 16.5753 167.547 15.4322 167.547 14.341C167.547 13.2498 167.547 12.0807 167.547 10.9116C167.555 8.76857 168.204 6.67429 169.413 4.88421C170.632 3.2405 172.306 1.96967 174.24 1.221C178.849 -0.406512 183.898 -0.406512 188.507 1.221C190.431 1.97391 192.096 3.24439 193.307 4.88421C194.54 6.66351 195.19 8.7649 195.173 10.9116C195.173 12.0807 195.173 13.2239 195.173 14.341C195.173 15.4582 195.173 16.5753 195.173 17.6925C195.199 19.8572 194.569 21.9811 193.36 23.7978C192.2 25.4405 190.553 26.7001 188.64 27.4091C186.302 28.2426 183.823 28.6393 181.333 28.5782ZM181.333 24.1875C183.186 24.2561 185.005 23.6959 186.48 22.6027C187.215 21.9498 187.789 21.1431 188.159 20.2432C188.528 19.3433 188.684 18.3735 188.613 17.4067C188.613 16.2376 188.613 15.1724 188.613 14.1851C188.613 13.1979 188.613 12.1327 188.613 10.9636C188.643 9.62392 188.292 8.3023 187.6 7.14449C186.962 6.17377 186.03 5.42023 184.933 4.98813C182.567 4.0869 179.94 4.0869 177.573 4.98813C176.469 5.40777 175.534 6.16421 174.907 7.14449C174.229 8.30936 173.871 9.62465 173.867 10.9636C173.867 12.1327 173.867 13.1979 173.867 14.1851C173.867 15.1724 173.867 16.2376 173.867 17.4067C173.808 18.3748 173.971 19.3435 174.344 20.2424C174.718 21.1412 175.293 21.9476 176.027 22.6027C177.545 23.731 179.427 24.2932 181.333 24.1875Z" fill="#141C24"/>
                                        <path d="M201.547 28.1885C201.253 28.1943 200.967 28.0923 200.747 27.9027C200.642 27.8209 200.558 27.7182 200.498 27.6017C200.438 27.4852 200.405 27.3575 200.4 27.2272V1.40291C200.399 1.2642 200.429 1.12696 200.489 1.00115C200.549 0.875331 200.637 0.764083 200.747 0.675464C200.967 0.485901 201.253 0.383909 201.547 0.389682H222.08C222.383 0.379895 222.678 0.481982 222.907 0.675464C223.009 0.766668 223.089 0.879717 223.14 1.00586C223.191 1.132 223.212 1.26786 223.2 1.40291V4.00093C223.209 4.12811 223.188 4.25566 223.137 4.37309C223.086 4.49053 223.007 4.59449 222.907 4.67642C222.678 4.8699 222.383 4.97199 222.08 4.9622H206.693V12.7563H221.12C221.417 12.7505 221.709 12.842 221.947 13.0161C222.051 13.102 222.133 13.2097 222.189 13.3312C222.244 13.4526 222.271 13.5847 222.267 13.7175V16.3156C222.271 16.4484 222.244 16.5805 222.189 16.7019C222.133 16.8234 222.051 16.9311 221.947 17.017C221.709 17.1911 221.417 17.2825 221.12 17.2768H206.693V27.2272C206.694 27.3585 206.663 27.4881 206.603 27.6055C206.543 27.723 206.455 27.8248 206.347 27.9027C206.107 28.0957 205.804 28.1973 205.493 28.1885H201.547Z" fill="#141C24"/>
                                        <path d="M228.88 28.1885C228.586 28.1942 228.3 28.0922 228.08 27.9027C227.972 27.8247 227.884 27.7229 227.823 27.6055C227.763 27.4881 227.732 27.3585 227.733 27.2272V1.3509C227.732 1.21964 227.763 1.09001 227.823 0.972598C227.884 0.855191 227.972 0.753353 228.08 0.675419C228.3 0.485856 228.586 0.383864 228.88 0.389637H233.12C233.414 0.383864 233.699 0.485856 233.92 0.675419C234.024 0.757239 234.109 0.859898 234.169 0.97644C234.228 1.09298 234.262 1.22069 234.267 1.3509V27.3311C234.262 27.4613 234.228 27.589 234.169 27.7056C234.109 27.8221 234.024 27.9248 233.92 28.0066C233.699 28.1962 233.414 28.2982 233.12 28.2924L228.88 28.1885Z" fill="#141C24"/>
                                        <path d="M241.733 28.1884C241.432 28.1888 241.14 28.0879 240.907 27.9026C240.806 27.8195 240.725 27.716 240.67 27.5993C240.615 27.4827 240.586 27.3556 240.587 27.2272V1.40282C240.58 1.26591 240.606 1.12937 240.661 1.0034C240.717 0.877434 240.801 0.765298 240.907 0.675378C241.14 0.490161 241.432 0.389209 241.733 0.389595H245.333C245.665 0.372521 245.994 0.463955 246.267 0.649397C246.414 0.765114 246.548 0.89575 246.667 1.0391L260.667 19.2252V1.40282C260.66 1.26591 260.686 1.12937 260.741 1.0034C260.797 0.877434 260.881 0.765298 260.987 0.675378C261.22 0.490161 261.512 0.389209 261.813 0.389595H265.573C265.884 0.38078 266.187 0.482357 266.427 0.675378C266.536 0.763997 266.624 0.875244 266.684 1.00106C266.744 1.12688 266.775 1.26411 266.773 1.40282V27.2012C266.772 27.3358 266.741 27.4686 266.681 27.5899C266.621 27.7112 266.534 27.818 266.427 27.9026C266.206 28.0922 265.921 28.1942 265.627 28.1884H262C261.666 28.2218 261.332 28.1288 261.067 27.9286L260.64 27.5389L246.667 9.79443V27.2272C246.667 27.3556 246.639 27.4827 246.583 27.5993C246.528 27.716 246.447 27.8195 246.347 27.9026C246.107 28.0957 245.804 28.1972 245.493 28.1884H241.733Z" fill="#141C24"/>
                                        <path d="M285.813 28.5782C283.27 28.6523 280.738 28.2191 278.373 27.3052C276.466 26.5721 274.831 25.2954 273.68 23.642C272.57 21.9232 271.987 19.9311 272 17.9003C272 16.7572 272 15.5361 272 14.2371C272 12.9381 272 11.6391 272 10.522C272.021 8.50968 272.652 6.54832 273.813 4.88425C274.971 3.28067 276.57 2.02873 278.427 1.273C280.777 0.370898 283.289 -0.0620129 285.813 -3.09311e-05C287.86 -0.0352139 289.9 0.227485 291.867 0.779375C293.397 1.21028 294.846 1.87686 296.16 2.75387C297.22 3.4352 298.127 4.31905 298.827 5.35189C299.363 6.12489 299.684 7.01994 299.76 7.94991C299.766 8.06535 299.742 8.1803 299.69 8.28454C299.639 8.38879 299.562 8.4791 299.467 8.54746C299.256 8.71268 298.99 8.79599 298.72 8.78128H294.107C293.832 8.80267 293.558 8.72909 293.333 8.57344C293.141 8.43116 292.978 8.25488 292.853 8.05383C292.581 7.42095 292.182 6.84699 291.68 6.36512C291.04 5.74009 290.266 5.26097 289.413 4.96219C288.241 4.56134 287.002 4.37634 285.76 4.41661C283.866 4.30837 281.988 4.82049 280.427 5.8715C279.716 6.4662 279.153 7.20978 278.779 8.0466C278.405 8.88342 278.23 9.79181 278.267 10.7038C278.267 13.0161 278.267 15.3543 278.267 17.6925C278.22 18.6271 278.393 19.5598 278.772 20.4196C279.151 21.2794 279.726 22.0438 280.453 22.6547C282.004 23.7351 283.883 24.2751 285.787 24.1875C287.125 24.204 288.455 23.9749 289.707 23.5121C290.779 23.1036 291.706 22.3993 292.373 21.4856C293.08 20.439 293.434 19.204 293.387 17.9523V16.6273H287.333C287.191 16.6353 287.049 16.614 286.915 16.5648C286.782 16.5155 286.661 16.4395 286.56 16.3415C286.46 16.2636 286.38 16.1647 286.324 16.0523C286.269 15.9399 286.24 15.8167 286.24 15.692V13.6656C286.232 13.5322 286.256 13.3988 286.312 13.2767C286.368 13.1546 286.453 13.0474 286.56 12.9641C286.774 12.7833 287.051 12.6901 287.333 12.7043H298.613C298.921 12.6858 299.225 12.7782 299.467 12.9641C299.583 13.0412 299.676 13.1465 299.736 13.2696C299.797 13.3928 299.824 13.5293 299.813 13.6656V17.7964C299.861 19.8391 299.255 21.8457 298.08 23.538C296.879 25.1846 295.237 26.4787 293.333 27.2792C290.939 28.1891 288.383 28.6305 285.813 28.5782Z" fill="#141C24"/>
                                        <path d="M114.907 50.3496C113.228 50.3948 111.558 50.103 110 49.4923C108.731 49.0147 107.649 48.1603 106.907 47.0501C106.166 45.905 105.76 44.5844 105.733 43.2311C105.733 42.5036 105.733 41.7242 105.733 40.8928C105.733 40.0615 105.733 39.2821 105.733 38.5286C105.752 37.174 106.158 35.8513 106.907 34.7095C107.672 33.6037 108.758 32.7446 110.027 32.2414C111.578 31.6424 113.239 31.3595 114.907 31.4101C116.212 31.3903 117.513 31.5477 118.773 31.8777C119.805 32.1413 120.78 32.5812 121.653 33.1767C122.401 33.6922 123.028 34.3563 123.493 35.1252C123.92 35.8476 124.166 36.6573 124.213 37.4894C124.211 37.5661 124.191 37.6412 124.154 37.7088C124.117 37.7765 124.064 37.8348 124 37.8791C123.863 37.9839 123.694 38.0391 123.52 38.035H120.693C120.497 38.0515 120.302 37.9971 120.144 37.882C119.987 37.767 119.879 37.5995 119.84 37.4115C119.742 36.9057 119.534 36.4262 119.231 36.005C118.927 35.5838 118.535 35.2307 118.08 34.9693C117.093 34.5017 116.003 34.2786 114.907 34.3198C113.665 34.2536 112.439 34.612 111.44 35.3331C110.974 35.7557 110.609 36.2722 110.37 36.8466C110.13 37.421 110.022 38.0393 110.053 38.6585C110.053 40.1134 110.053 41.5943 110.053 43.1012C110.022 43.7204 110.13 44.3387 110.37 44.9131C110.609 45.4875 110.974 46.004 111.44 46.4266C112.439 47.1477 113.665 47.5061 114.907 47.4398C116.004 47.4607 117.091 47.2293 118.08 46.7644C118.532 46.4992 118.922 46.1449 119.225 45.7244C119.528 45.3038 119.737 44.8263 119.84 44.3222C119.879 44.1306 119.982 43.957 120.133 43.8286C120.306 43.7396 120.498 43.6949 120.693 43.6987H123.52C123.694 43.6947 123.863 43.7498 124 43.8546C124.064 43.8989 124.117 43.9572 124.154 44.0249C124.191 44.0925 124.211 44.1676 124.213 44.2443C124.166 45.0764 123.92 45.8861 123.493 46.6085C123.034 47.3744 122.405 48.0312 121.653 48.531C120.787 49.1454 119.81 49.5949 118.773 49.856C117.514 50.1948 116.213 50.361 114.907 50.3496Z" fill="#00B2FF"/>
                                        <path d="M125.627 50.0898C125.453 50.0938 125.283 50.0387 125.147 49.9339C125.086 49.8882 125.037 49.829 125.005 49.7612C124.972 49.6934 124.957 49.619 124.96 49.5442C124.947 49.4667 124.947 49.3878 124.96 49.3104L132.587 32.3193C132.672 32.137 132.799 31.9768 132.96 31.8517C133.171 31.7109 133.425 31.6466 133.68 31.6698H136.933C137.196 31.6526 137.457 31.7163 137.68 31.8517C137.84 31.9768 137.968 32.137 138.053 32.3193L145.733 49.3623C145.746 49.4398 145.746 49.5187 145.733 49.5962C145.731 49.6728 145.711 49.7479 145.674 49.8155C145.637 49.8832 145.584 49.9415 145.52 49.9859C145.404 50.0893 145.251 50.1451 145.093 50.1417H142.427C142.201 50.1608 141.976 50.1059 141.787 49.9859L141.493 49.6481L140.027 46.3746H130.667L129.147 49.6481C129.086 49.7797 128.995 49.8954 128.88 49.9859C128.699 50.1021 128.483 50.1569 128.267 50.1417L125.627 50.0898ZM131.68 43.4129H139.04L135.387 35.0732L131.68 43.4129Z" fill="#00B2FF"/>
                                        <path d="M148.72 50.0899C148.623 50.0962 148.526 50.0833 148.434 50.052C148.343 50.0208 148.258 49.9718 148.187 49.908C148.113 49.8583 148.053 49.7923 148.012 49.7155C147.97 49.6387 147.948 49.5533 147.947 49.4663V32.2934C147.948 32.2065 147.97 32.1211 148.012 32.0442C148.053 31.9674 148.113 31.9014 148.187 31.8518C148.258 31.788 148.343 31.739 148.434 31.7077C148.526 31.6765 148.623 31.6636 148.72 31.6699H151.387C151.484 31.6636 151.581 31.6765 151.672 31.7077C151.764 31.739 151.848 31.788 151.92 31.8518C151.99 31.9027 152.046 31.9696 152.083 32.0467C152.12 32.1237 152.138 32.2084 152.133 32.2934V47.1021H162.24C162.439 47.0907 162.637 47.1456 162.8 47.258C162.866 47.3178 162.919 47.3901 162.956 47.4705C162.993 47.5509 163.012 47.6377 163.013 47.7257V49.3624C163.012 49.4465 162.992 49.5293 162.955 49.6054C162.918 49.6815 162.865 49.7492 162.8 49.8041C162.724 49.8699 162.636 49.9199 162.539 49.9512C162.443 49.9825 162.341 49.9943 162.24 49.9859L148.72 50.0899Z" fill="#00B2FF"/>
                                        <path d="M173.547 50.3496C171.859 50.3976 170.18 50.1057 168.613 49.4923C167.349 49.0071 166.269 48.1544 165.52 47.0501C164.779 45.905 164.373 44.5844 164.347 43.231C164.347 42.5036 164.347 41.7242 164.347 40.8928C164.347 40.0614 164.347 39.282 164.347 38.5286C164.365 37.174 164.771 35.8513 165.52 34.7095C166.298 33.615 167.381 32.7589 168.64 32.2414C170.2 31.6424 171.87 31.3596 173.547 31.41C174.851 31.3922 176.152 31.5496 177.413 31.8777C178.441 32.1516 179.414 32.5906 180.293 33.1767C181.027 33.7 181.644 34.3629 182.107 35.1252C182.533 35.8475 182.78 36.6573 182.827 37.4894C182.824 37.566 182.804 37.6412 182.767 37.7088C182.73 37.7764 182.677 37.8348 182.613 37.8791C182.477 37.9839 182.307 38.039 182.133 38.035H179.307C179.116 38.0426 178.928 37.9877 178.773 37.8791C178.62 37.7589 178.508 37.5956 178.453 37.4115C178.36 36.904 178.155 36.4224 177.851 36.0005C177.547 35.5786 177.152 35.2267 176.693 34.9693C175.716 34.5018 174.635 34.2786 173.547 34.3198C172.296 34.2474 171.059 34.6061 170.053 35.333C169.593 35.7599 169.232 36.2771 168.993 36.8505C168.754 37.4238 168.642 38.0401 168.667 38.6585C168.667 40.1134 168.667 41.5943 168.667 43.1011C168.642 43.7195 168.754 44.3358 168.993 44.9092C169.232 45.4825 169.593 45.9998 170.053 46.4266C171.059 47.1535 172.296 47.5122 173.547 47.4398C174.636 47.4605 175.714 47.229 176.693 46.7643C177.148 46.503 177.541 46.1498 177.844 45.7287C178.148 45.3075 178.355 44.8279 178.453 44.3222C178.501 44.1279 178.614 43.9544 178.773 43.8286C178.936 43.7395 179.12 43.6946 179.307 43.6987H182.133C182.307 43.6946 182.477 43.7498 182.613 43.8546C182.677 43.8989 182.73 43.9572 182.767 44.0249C182.804 44.0925 182.824 44.1676 182.827 44.2443C182.78 45.0764 182.533 45.8861 182.107 46.6085C181.649 47.3678 181.031 48.0232 180.293 48.531C179.422 49.1357 178.446 49.5844 177.413 49.856C176.154 50.1928 174.853 50.3589 173.547 50.3496Z" fill="#00B2FF"/>
                                        <path d="M194.667 50.3496C193.051 50.3808 191.442 50.1256 189.92 49.5962C188.595 49.1558 187.453 48.3078 186.667 47.18C185.876 45.944 185.486 44.5045 185.547 43.0492V32.3193C185.548 32.2288 185.57 32.1396 185.611 32.0586C185.653 31.9776 185.713 31.9068 185.787 31.8517C185.858 31.7879 185.943 31.7389 186.034 31.7077C186.126 31.6764 186.223 31.6635 186.32 31.6698H188.987C189.19 31.6613 189.389 31.726 189.547 31.8517C189.613 31.9115 189.666 31.9838 189.703 32.0642C189.739 32.1446 189.759 32.2314 189.76 32.3193V43.0492C189.714 43.6506 189.803 44.2545 190.019 44.8195C190.235 45.3846 190.575 45.8973 191.013 46.3227C192.024 47.0159 193.23 47.388 194.467 47.388C195.703 47.388 196.909 47.0159 197.92 46.3227C198.363 45.9005 198.705 45.3882 198.922 44.8223C199.139 44.2565 199.224 43.651 199.173 43.0492V32.3193C199.175 32.2314 199.194 32.1446 199.231 32.0642C199.267 31.9838 199.32 31.9115 199.387 31.8517C199.552 31.7221 199.761 31.6572 199.973 31.6698H202.64C202.741 31.6615 202.843 31.6733 202.939 31.7046C203.036 31.7358 203.124 31.7859 203.2 31.8517C203.266 31.9115 203.319 31.9838 203.356 32.0642C203.393 32.1446 203.412 32.2314 203.413 32.3193V43.0492C203.471 44.5006 203.091 45.9368 202.32 47.18C201.579 48.2853 200.495 49.1317 199.227 49.5962C197.765 50.1106 196.221 50.3658 194.667 50.3496Z" fill="#00B2FF"/>
                                        <path d="M208.32 50.0899C208.223 50.0962 208.126 50.0833 208.034 50.052C207.943 50.0208 207.858 49.9718 207.787 49.908C207.714 49.8583 207.654 49.7923 207.612 49.7155C207.57 49.6387 207.548 49.5533 207.547 49.4663V32.2934C207.548 32.2065 207.57 32.1211 207.612 32.0442C207.654 31.9674 207.714 31.9014 207.787 31.8518C207.858 31.788 207.943 31.739 208.034 31.7077C208.126 31.6765 208.223 31.6636 208.32 31.6699H210.987C211.19 31.6613 211.389 31.7261 211.547 31.8518C211.609 31.9079 211.658 31.9764 211.69 32.0527C211.722 32.1289 211.737 32.211 211.733 32.2934V47.1021H221.84C222.04 47.0907 222.237 47.1456 222.4 47.258C222.466 47.3178 222.519 47.3901 222.556 47.4705C222.593 47.5509 222.612 47.6377 222.613 47.7257V49.3624C222.612 49.4465 222.592 49.5293 222.555 49.6054C222.518 49.6815 222.466 49.7492 222.4 49.8041C222.324 49.8699 222.236 49.9199 222.139 49.9512C222.043 49.9825 221.941 49.9943 221.84 49.9859L208.32 50.0899Z" fill="#00B2FF"/>
                                        <path d="M223.627 50.0898C223.453 50.0939 223.283 50.0387 223.147 49.9339C223.09 49.8847 223.044 49.8248 223.012 49.7578C222.98 49.6908 222.962 49.6181 222.96 49.5442C222.937 49.4679 222.937 49.3868 222.96 49.3104L230.56 32.3194C230.645 32.1371 230.773 31.9768 230.933 31.8517C231.156 31.7163 231.418 31.6527 231.68 31.6699H234.907C235.169 31.6527 235.43 31.7163 235.653 31.8517C235.814 31.9768 235.942 32.1371 236.027 32.3194L243.76 49.3624C243.76 49.3624 243.76 49.5183 243.76 49.5962C243.758 49.6701 243.74 49.7427 243.708 49.8097C243.676 49.8767 243.63 49.9367 243.573 49.9859C243.445 50.0869 243.285 50.142 243.12 50.1418H240.453C240.228 50.1608 240.003 50.106 239.813 49.9859C239.706 49.8884 239.616 49.7743 239.547 49.6482L238.053 46.3747H228.613L227.093 49.6482C227.033 49.7797 226.942 49.8955 226.827 49.9859C226.645 50.1022 226.43 50.157 226.213 50.1418L223.627 50.0898ZM229.68 43.4129H237.04L233.387 35.0733L229.68 43.4129Z" fill="#00B2FF"/>
                                        <path d="M249.28 50.0899C249.183 50.0962 249.086 50.0833 248.994 50.052C248.903 50.0208 248.818 49.9718 248.747 49.908C248.673 49.8583 248.613 49.7923 248.572 49.7155C248.53 49.6387 248.508 49.5533 248.507 49.4663V34.7615H242.667C242.476 34.7691 242.288 34.7142 242.133 34.6057C242.063 34.5492 242.007 34.4776 241.97 34.3964C241.933 34.3152 241.916 34.2267 241.92 34.138V32.3194C241.916 32.2307 241.933 32.1422 241.97 32.061C242.007 31.9798 242.063 31.9082 242.133 31.8518C242.205 31.788 242.289 31.739 242.381 31.7077C242.473 31.6765 242.57 31.6636 242.667 31.6699H258.667C258.879 31.6573 259.088 31.7221 259.253 31.8518C259.32 31.9116 259.373 31.9839 259.409 32.0642C259.446 32.1446 259.465 32.2314 259.467 32.3194V34.138C259.465 34.226 259.446 34.3128 259.409 34.3932C259.373 34.4735 259.32 34.5458 259.253 34.6057C259.079 34.7149 258.874 34.7693 258.667 34.7615H252.773V49.3624C252.778 49.4474 252.76 49.5322 252.723 49.6092C252.686 49.6862 252.63 49.7531 252.56 49.8041C252.404 49.932 252.204 49.9971 252 49.9859L249.28 50.0899Z" fill="#00B2FF"/>
                                        <path d="M269.867 50.3495C268.233 50.383 266.607 50.1278 265.067 49.5961C263.784 49.1317 262.683 48.2865 261.92 47.18C261.133 45.9771 260.716 44.5795 260.72 43.153C260.72 42.3996 260.72 41.6462 260.72 40.8927C260.72 40.1393 260.72 39.4119 260.72 38.6325C260.728 37.212 261.154 35.8235 261.947 34.6315C262.752 33.5425 263.863 32.7034 265.147 32.2154C268.206 31.1422 271.554 31.1422 274.613 32.2154C275.939 32.7119 277.075 33.5944 277.867 34.7427C278.659 35.891 279.069 37.2492 279.04 38.6325C279.04 39.4119 279.04 40.1653 279.04 40.8927C279.04 41.6202 279.04 42.3996 279.04 43.153C279.044 44.5795 278.627 45.9771 277.84 47.18C277.065 48.2761 275.968 49.1183 274.693 49.5961C273.144 50.1275 271.509 50.3828 269.867 50.3495ZM269.867 47.4398C271.093 47.4864 272.299 47.1193 273.28 46.4006C273.755 45.9724 274.127 45.4481 274.371 44.8646C274.615 44.2811 274.725 43.6525 274.693 43.0231C274.693 42.2437 274.693 41.5163 274.693 40.8668C274.693 40.2173 274.693 39.5158 274.693 38.7364C274.737 37.8254 274.506 36.922 274.027 36.1384C273.621 35.4971 273.026 34.99 272.32 34.6835C270.749 34.0952 269.011 34.0952 267.44 34.6835C266.729 34.9828 266.132 35.4916 265.733 36.1384C265.273 36.9303 265.034 37.8262 265.04 38.7364C265.04 39.5158 265.04 40.2173 265.04 40.8668C265.04 41.5163 265.04 42.2437 265.04 43.0231C265.006 43.6551 265.117 44.2866 265.367 44.8711C265.616 45.4556 265.996 45.9782 266.48 46.4006C267.454 47.1136 268.649 47.4804 269.867 47.4398Z" fill="#00B2FF"/>
                                        <path d="M283.253 50.0898C283.058 50.0969 282.868 50.032 282.72 49.908C282.654 49.8531 282.602 49.7854 282.565 49.7093C282.528 49.6332 282.508 49.5504 282.507 49.4663V32.3194C282.508 32.2314 282.527 32.1446 282.564 32.0642C282.601 31.9838 282.654 31.9115 282.72 31.8517C282.868 31.7277 283.058 31.6628 283.253 31.6699H291.253C293.342 31.5517 295.417 32.0594 297.2 33.1248C297.913 33.5886 298.49 34.2255 298.873 34.9726C299.257 35.7198 299.434 36.5514 299.387 37.3855C299.434 38.5731 299.036 39.7369 298.267 40.659C297.432 41.5427 296.361 42.1816 295.173 42.5036L299.76 49.2585C299.789 49.3516 299.789 49.4511 299.76 49.5442C299.763 49.619 299.748 49.6934 299.715 49.7612C299.683 49.8291 299.634 49.8883 299.573 49.934C299.447 50.039 299.286 50.0946 299.12 50.0898H296.453C296.158 50.1117 295.864 50.0287 295.627 49.856C295.455 49.7203 295.303 49.563 295.173 49.3884L291.04 43.0752H286.507V49.3624C286.506 49.4493 286.483 49.5347 286.442 49.6116C286.4 49.6884 286.34 49.7544 286.267 49.8041C286.191 49.8699 286.102 49.9199 286.006 49.9512C285.91 49.9825 285.808 49.9943 285.707 49.9859L283.253 50.0898ZM286.747 40.1914H291.28C292.322 40.273 293.362 40.0173 294.24 39.4639C294.552 39.2114 294.8 38.8929 294.966 38.5328C295.133 38.1726 295.213 37.7803 295.2 37.3855C295.221 36.9821 295.145 36.5796 294.978 36.2098C294.812 35.84 294.559 35.5131 294.24 35.2551C293.375 34.6702 292.329 34.3949 291.28 34.4757H286.747V40.1914Z" fill="#00B2FF"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_12_350">
                                            <rect width="300" height="50.3497" fill="white"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                            @endif
                        </div>
                    </div>
                    <div>
                        <table class="w-full">
                            <tbody>
                            <tr>
                                <td>
                                    <div class="w-full">
                                        <table class="w-full" border="0" cellpadding="0" cellspacing="0"
                                               role="presentation">
                                            <tbody>
                                            <tr>
                                                <td>
                                                    <div class="text-2xl px-5 py-4 font-medium">
                                                        @if(isset($company))
                                                            <a class="text-blue-550 cursor-pointer"
                                                               href="{{ env('MIX_LEGACY_ADMIN_BASE_URL') }}/company.php?getcompanyid={{ $company->companyid }}">
                                                                {{ $company->companyname }}
                                                            </a>
                                                        @else
                                                            Company
                                                        @endif
                                                        has expressed interest in lead id:
                                                        <a class="text-blue-550 cursor-pointer"
                                                           href="{{ env('MIX_LEGACY_ADMIN_BASE_URL') }}/quote.php?quoteid={{ $leadId ?? 0 }}">{{ $leadId ?? 0 }}</a>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <div class="px-5 mb-4">
                                                        Hello,
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <div class="px-5">
                                                        The following person has registered their interest via a sales
                                                        bait that was sent out to them:
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-5">
                                                    <table class="w-80 my-4"
                                                           role="presentation">
                                                        <tbody>
                                                        <tr>
                                                            <td class="font-semibold">
                                                                Company:
                                                            </td>
                                                            <td>
                                                                @if(isset($company))
                                                                    <a class="text-blue-550 cursor-pointer"
                                                                       href="{{ env('MIX_LEGACY_ADMIN_BASE_URL') }}/company.php?getcompanyid={{ $company->companyid }}">
                                                                        {{ $company->companyname }} ({{ $company->companyid }})
                                                                    </a>
                                                                @endif
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="font-semibold">
                                                                Industry:
                                                            </td>
                                                            <td>
                                                                @if((isset($industry) && $industry == "solar") || !isset($industry))
                                                                    Solar
                                                                @elseif($industry == "roofing")
                                                                    Roofing
                                                                @endif
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="font-semibold">
                                                                Lead:
                                                            </td>
                                                            <td>
                                                                <a class="text-blue-550 cursor-pointer"
                                                                   href="{{ env('MIX_LEGACY_ADMIN_BASE_URL') }}/quote.php?quoteid={{ $leadId ?? 0 }}">{{ $leadId ?? 0 }}</a>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="font-semibold">
                                                                Name:
                                                            </td>
                                                            <td>
                                                                {{ $name ?? "Not Provided" }}
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="font-semibold">
                                                                Phone:
                                                            </td>
                                                            <td>
                                                                {{ $phone ?? "Not Provided" }}
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="font-semibold">
                                                                Email:
                                                            </td>
                                                            <td>
                                                                {{ $email ?? "Not Provided" }}
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <div class="px-5">

                                                    </div>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="mt-5">
                                        <table class="w-full">
                                            <tbody>
                                            <tr>
                                                <td>
                                                    <div class="w-full pt-3 pb-6">
                                                        <table class="w-full" border="0" cellpadding="0" cellspacing="0"
                                                               role="presentation">
                                                            <tbody>
                                                            <tr>
                                                                <td>
                                                                    <div class="px-5 font-medium text-grey-500">
                                                                        Regards
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    <div class="px-5 text-grey-300">
                                                                        @if((isset($industry) && $industry == "solar") || !isset($industry))
                                                                            SolarReviews
                                                                        @elseif($industry == "roofing")
                                                                            Roofing Calculator
                                                                        @endif
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    <div class="px-5 pb-3">
                                                                        @if((isset($industry) && $industry == "solar") || !isset($industry))
                                                                            <a class="text-blue-500" href="www.solarreviews.com" target="_blank">www.solarreviews.com</a>
                                                                        @elseif($industry == "roofing")
                                                                            <a class="text-blue-500" href="www.roofingcalculator.com" target="_blank">www.roofingcalculator.com</a>
                                                                        @endif
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</x-mail-layout>
