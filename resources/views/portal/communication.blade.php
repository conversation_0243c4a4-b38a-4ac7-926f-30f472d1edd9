<x-portal-layout title="Communication Portal">
    <communication-portal :dark-mode="darkMode"
                          api-driver="<?php echo e(config('services.lead_processing.api_driver')); ?>"
                          communication-driver="<?php echo e(config('services.communication.driver')); ?>"
                          pusher-app-key="<?php echo e(config('broadcasting.connections.pusher.key')); ?>"
                          pusher-app-cluster="<?php echo e(config('broadcasting.connections.pusher.options.cluster')); ?>"

                          @if(\Illuminate\Support\Facades\Auth::user() !== null)
                              user-id="{{ \Illuminate\Support\Facades\Auth::id() }}"
                          @endif
    ></communication-portal>
</x-portal-layout>

