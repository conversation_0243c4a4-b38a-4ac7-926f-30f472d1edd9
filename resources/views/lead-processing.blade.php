<x-app-layout title="Lead Processing">
    <div>
        <lead-processing
            communication-driver="{{ config('services.communication.driver') }}"
            api-driver="{{ config('services.lead_processing.api_driver') }}"
            @if(config('services.communication.driver') == 'twilio')
                communication-token="{{ config('services.twilio.token') }}"
            @endif
            :dark-mode="darkMode"

            @if(request()->get('lead_id', null) !== null)
                initial-lead-id="{{ request()->get('lead_id', null) }}"
            @endif
        />
    </div>
</x-app-layout>
