import { PusherService } from "../../../../services/pusher";

export class NotificationPusherService {
    static PUSHER_AUTH_ENDPOINT = '/internal-api/v1/lead-processing/notifications/auth';

    static CHANNEL_USERS_NOTIFICATION = (userId) => `notifications-${userId}`

    static EVENT_ON_NEW_NOTIFICATION = 'new-notification'
    static EVENT_ON_NEW_MESSAGE = 'new-message'

    _pusher = null

    static _instance = null

    /**
     * Please use static generate
     * @param {string} appKey
     * @param {string} appCluster
     * @param {string} userId
     * @param {string} authEndpoint
     */
    constructor(appKey, appCluster, userId, authEndpoint) {
        this._pusher = new PusherService(appKey, appCluster, authEndpoint, {
            user_id: userId
        })
    }

    /**
     * Please use static generate
     * @param {string} appKey
     * @param {string} appCluster
     * @param {string} userId
     * @param {string} authEndpoint
     */
    static generate(appKey, appCluster, userId, authEndpoint = NotificationPusherService.PUSHER_AUTH_ENDPOINT){
        if (this._instance) return this._instance

        this._instance = new NotificationPusherService(appKey, appCluster, userId, authEndpoint)

        return this._instance
    }

    subscribe(userId) {
        if (!userId) throw new Error('userId is required')

        this._pusher.subscribe(NotificationPusherService.CHANNEL_USERS_NOTIFICATION(userId));
    }

    /**
     * Bindings - Remember to pass clientSide false when it's a server side event
     */
    bindOnNewNotification(cb) {
        this._pusher.bind(NotificationPusherService.EVENT_ON_NEW_NOTIFICATION, cb, { clientSide: false })
    }

    bindOnNewMessage(cb) {
        this._pusher.bind(NotificationPusherService.EVENT_ON_NEW_MESSAGE, cb, { clientSide: false })
    }


    /**
     * Triggers
     */
    triggerOnNewNotification(data) {
        this._pusher.trigger(NotificationPusherService.EVENT_ON_NEW_NOTIFICATION, data)
    }

    triggerOnNewMessage(data) {
        this._pusher.trigger(NotificationPusherService.EVENT_ON_NEW_MESSAGE, data)
    }
}
