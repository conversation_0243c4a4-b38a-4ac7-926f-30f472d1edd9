<script setup>
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import {computed, onMounted, ref, defineEmits} from "vue";
import ApiService from "./../api.js";

const props = defineProps({
    darkMode: false,
})
const themeBackgroundClasses = computed(() => {
    if(props.darkMode === true) {
        return 'bg-dark-background border-dark-border text-slate-50'
    }
    else {
        return 'bg-light-background border-light-border text-slate-900'
    }
})

onMounted(() => {
    getMyConvertedCompanies()
})
const apiService = ApiService.make();
const loading = ref(false)
const convertedCompanies = ref(null);
const getMyConvertedCompanies = () => {
    loading.value = true
    apiService.getMyConvertedCompanies().then(res => {
        if(res.data.companies)
            convertedCompanies.value = res.data.companies.map((i) => {
                return i;
            });
    }).catch((e) => {
        flashAlert('error', e)
    }).finally(() => {
        loading.value = false
    })
}

const emit = defineEmits(['flash-alert', 'edit-prospect'])
function flashAlert(type, message) {
    emit('flash-alert', type, message);
}
</script>

<template>
    <div>
        <h3 class="font-bold text-sm uppercase text-primary-500 p-5">
            My Converted Companies
        </h3>
        <div class="grid grid-cols-1 gap-3 mr-3 px-5 pb-2 text-slate-500">
            <p class="text-xs font-semibold uppercase">Company</p>
        </div>
        <div class="h-80 overflow-y-auto border-y" :class="themeBackgroundClasses">
            <div v-if="loading" class="h-64 flex justify-center items-center" >
                <LoadingSpinner />
            </div>
            <div v-else
                 v-for="company in convertedCompanies" :key="company.id"
                 class="grid grid-cols-1 gap-3 py-3 px-5 items-center border-b relative group h-16 transition-all duration-100"
                 :class="[darkMode ? 'bg-dark-background border-dark-border text-slate-50 hover:bg-dark-module cursor-pointer' : 'bg-light-background border-light-border text-slate-900 hover:bg-primary-50 cursor-pointer']" >
                <a target="_blank" :href="company.profile_link" class="inline-flex items-center group-hover:text-primary-500 font-medium truncate">
                    {{company.name}}
                    <svg class="w-3.5 ml-2" width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10 0.833344L13.293 4.12634L6.293 11.1263L7.707 12.5403L14.707 5.54034L18 8.83334V0.833344H10Z" fill="#0081FF"/>
                        <path d="M16 16.8333H2V2.83334H9L7 0.833344H2C0.897 0.833344 0 1.73034 0 2.83334V16.8333C0 17.9363 0.897 18.8333 2 18.8333H16C17.103 18.8333 18 17.9363 18 16.8333V11.8333L16 9.83334V16.8333Z" fill="#0081FF"/>
                    </svg>
                </a>
                <div class="h-full absolute left-0 w-0.5 bg-transparent group-hover:bg-primary-500 transition-all duration-100"></div>
            </div>
        </div>
        <div class="p-3"></div>
    </div>
</template>

<style scoped>

</style>
