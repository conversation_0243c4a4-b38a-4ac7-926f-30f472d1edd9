<template>
    <modal class="z-40"
           :dark-mode="darkMode"
           :full-width="true"
           confirmText="Save"
           close-text="I don't know"
           no-buttons
           container-classes="px-8 py-4"
           @close="handleClose"
    >
        <template v-slot:header>
            <div class="font-medium">
                Who did you speak to?
            </div>
        </template>
        <template v-slot:content>
            <component
                :is="component"
                :darkMode="darkMode"
                :pusher-app-cluster="pusherAppCluster"
                :pusher-app-key="pusherAppCluster"
            >
            </component>
        </template>
    </modal>
</template>


<script>
import PossibleContactSelection from "./PossibleContactSelection/PossibleContactSelection.vue"
import CreateNewRecord from "./CreateNewRecord/CreateNewRecord.vue"
import Modal from '../../../../components/Shared/components/Modal.vue'
import {mapWritableState} from "pinia";
import {
    useContactIdentificationStore
} from "../../../../../stores/communication/contactIdentification";

export default {
    name: "ContactIdentificationModal",
    components: {Modal, PossibleContactSelection, CreateNewRecord},

    props: {
        darkMode: {
            type: Boolean,
            required: true,
        },
        pusherAppKey: {
            type: String,
            required: true
        },
        pusherAppCluster: {
            type: String,
            required: true
        }
    },

    data(){
        return {
            component: 'PossibleContactSelection'
        }
    },


    computed: {
        ...mapWritableState(useContactIdentificationStore, [
            'isContactIdentificationModalVisible',
            'createNewRecordComponentShown'
        ])
    },


    methods: {
        handleClose(){
            this.isContactIdentificationModalVisible = false
        }
    },

    beforeUnmount() {
        this.createNewRecordComponentShown = null
        this.component = ''
    },

    watch: {
        createNewRecordComponentShown(val){
            // When create new record is null, show possible contact list selection
            // otherwise show create new record component
            if (!val) this.component = 'PossibleContactSelection'
            else this.component = 'CreateNewRecord'
        }
    }
}
</script>

