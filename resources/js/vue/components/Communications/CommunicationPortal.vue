<template>
    <div class="w-full h-full">
        <div v-if="checkUserHasAlerts()">
            <microphone-alert v-if="alerts.showMicrophoneAccess"></microphone-alert>
            <user-no-phone-available-alert v-if="alerts.showUserNoPhoneAvailable" />
        </div>
        <div v-else>
            <div v-if="loading" class="flex items-center justify-center h-full">
                <loading-spinner />
            </div>

            <dialer
                v-if="showDialer"
                :dialer-modal="showDialer"
                @sms="showMessenger"
                @call="handleDialerCall"
                :dark-mode="darkMode"
            />

            <outgoing-call
                :call-active="callState.active"
                :minimize-call="callState.minimized"
                :muted="callState.muted"
                :on-hold="callState.held"
                :dark-mode="darkMode"
                :comment="callInfo?.comment"
                :contact-name="callInfo?.contactName"
                :contact-phone="callInfo?.contactPhone"
                :status="callInfo?.status"
                :isIdentifyingContact="callState.isIdentifyingContact"
                :session="null"
                :relation-type="callInfo?.relationType"
                :relation-id="callInfo?.relationId"
                :pusher-app-key="pusherAppKey"
                :pusher-app-cluster="pusherAppCluster"
                :has-relation-page="callInfo?.identifiedContact?.relationSubtype"
                @minimize-call="toggleMinimizeCall"
                @end-call="endCall"
                @on-hold="handleCallHold"
                @muted="handleCallMute"
                @key-pressed="handleDialerKeyPressed"
                @open-relation-page="handleOpenRelationPage"
            />


            <messenger
                :messenger-active="messengerActive"
                :messages="messages"
                :dark-mode="darkMode"
                :loading="messagesLoading"
                :name="callInfo?.contactName"
                ref="messenger"
                @close-messenger="handleCloseMessenger"
                @send-message="handleSendMessage"
            />
        </div>
    </div>
</template>

<script>
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";

import UserNoPhoneAvailableAlert from "./components/UserNoPhoneAvailableAlert.vue";
import MicrophoneAlert from "./components/MicrophoneNotAllowedAlert.vue";
import OutgoingCall from "./components/OutgoingCall.vue";
import Messenger from "./components/Messenger.vue";
import Dialer from "./components/Dialer.vue";

import {CommunicationPortalLocalStorageService} from "./services/communicationLocalStorage";
import {CommunicationApiFactory} from "../../../services/api/communication/factory";
import {CommunicationPusherService} from "./services/communicationPusher";
import {ApiFactory} from "../LeadProcessing/services/api/factory";

import {waitForCallbackToReturnTrue} from "../../../utilities/loading-utilities";

import {CallModeEnum, CallStatusesEnum, CommunicationRelationTypes} from './enums/communication'

import {DateTime} from "luxon";
import CommunicationFactory from "../../../services/communication/factory";
import {mapWritableState} from "pinia";
import {useContactIdentificationStore} from "../../../stores/communication/contactIdentification";

const INIT_CALL_STATE = {
    active: false,
    muted: false,
    held: false,
    minimized: false,
    isIdentifyingContact: false
}

const INIT_CALL_INFO = {
    comment: null,
    contactName: '',
    contactPhone: '',
    status: CallStatusesEnum.CONNECTING, // CallStatusesEnum
    mode: null, // CallModeEnum
    relationType: null,
    relationId: null,
    identifiedContact: null
}

const INIT_ALERTS_STATE = {
    showMicrophoneAccess: false,
    showUserNoPhoneAvailable: false, // Check if user has a phone number
}

export default {
    name: "CommunicationPortal",
    props: {
        apiDriver: {
            type: String,
            default: 'dummy'
        },
        darkMode: {
            type: Boolean,
            default: false,
        },
        userId: {
            type: [Number, String],
            required: true
        },
        pusherAppKey: {
            type: String,
            required: true
        },
        pusherAppCluster: {
            type: String,
            required: true
        }
    },
    data: function () {
        return {
            communicationPusherService: null,
            api: null,
            communicationApi: null,
            communicationPortalLocalStorageService: null,
            deviceService: null,

            callInfo: { ...INIT_CALL_INFO },
            callState: { ...INIT_CALL_STATE },
            alerts: { ...INIT_ALERTS_STATE },
            showIdentificationModalVisible: true,

            loading: false,
            showDialer: false,

            messengerActive: false,
            messages: [],
            messagesLoading: true,
        }
    },
    components: {LoadingSpinner, OutgoingCall, Messenger, Dialer, MicrophoneAlert, UserNoPhoneAvailableAlert},

    computed: {
        ...mapWritableState(useContactIdentificationStore, [
            'isContactIdentificationModalVisible',
            'isCallActive',
            'currentCallInfo'
        ]),
    },

    async mounted() {
        this.initializeServices()
        this.setHandleCommunicationPortalVisibilityEvent()

        this.loading = true
        await this.initializeDevice()
        this.registerLocalListeners()
        this.initializeGlobalListeners()
        this.loading = false

        this.handlePossibleParams()

        this.showDialer = true
    },

    watch: {
        messengerActive(isActive){
            if (isActive) this.bindCommunicationListener()
            else this.unbindCommunicationListener()
        },
    },

    methods: {
        registerLocalListeners(){
            this.deviceService.onAccept(async () => {
                this.communicationPusherService.triggerOnOutgoingCallAccept()
                this.callInfo.status = CallStatusesEnum.CONNECTED
                // Should create log only after being accepted by Twilio
                // If we create it before, call sid will be different
                this.saveCallLog({
                    serviceName: this.deviceService.serviceName(),
                    userPhoneNumber: this.deviceService.getUserPhone(),
                    otherNumber: this.callInfo.contactPhone,
                    reference: this.deviceService.getCallId(),
                    relationType: this.currentCallInfo.relationType,
                    relationId: this.currentCallInfo.relationId,
                    result: 'initial'
                }).then()
            });

            this.deviceService.onEnd(async () => {
                await this.handleCallEnd()
                this.resetState()
            });
        },

        initializeGlobalListeners(){
            this.communicationPusherService.bindOnGlobalSMS(this.handleSmsEvent)
            this.communicationPusherService.bindOnGlobalCall(this.handleCallEvent)
        },

        /**
         * Parse url params to object
         * @param rawParams
         * @returns {{}}
         */
        parseParams(rawParams){
            const urlParams = new URLSearchParams(rawParams);
            const entries = urlParams.entries();

            const parsedParams = {}
            for(const [key, value] of entries) {
                parsedParams[key] = value;
            }

            return parsedParams
        },

        /**
         * Handle call and sms when detached dialer is not opened
         */
        handlePossibleParams(){
            const parsedParams = this.parseParams(window.location.search)

            switch (parsedParams.eventName){
                case 'sms':
                    this.handleSmsEvent(parsedParams)
                    break;
                case 'call':
                    this.handleCallEvent(parsedParams)
                    break;
            }

            // Remove query params without reloading
            window.history.pushState({}, document.title, location.href.split("?")[0]);
        },

        /**
         * Handle call event from parent
         * @param phone
         * @param name
         * @param comment
         * @param relType
         * @param relId
         * @returns {Promise<void>}
         */
        async handleCallEvent({ phone, name, comment, relType = null, relId = null }){
            this.showIdentificationModalVisible = false

            await this.askMicrophonePermission()
            if (this.checkUserHasAlerts()) return

            this.showDialer = false
            this.callState.active = true;

            this.callInfo.mode = CallModeEnum.OUTBOUND
            this.callInfo.status = CallStatusesEnum.CONNECTING
            this.callInfo.contactPhone = phone
            this.callInfo.contactName = name
            this.callInfo.relationId = relId
            this.callInfo.relationType = relType
            this.callInfo.comment = comment

            this.currentCallInfo.relationId = relId
            this.currentCallInfo.relationType = relType

            this.communicationPusherService.triggerOnOutgoingCallAttempt(this.callInfo)

            // console.log('handleAttemptCall...')
            await this.handleAttemptCall();
        },

        /**
         * Handle sms event
         * @param phone
         * @param name
         * @param id
         * @returns {Promise<void>}
         */
        async handleSmsEvent({ phone, name, id }){
            this.callInfo.contactPhone = phone
            this.callInfo.contactName = name
            this.callInfo.relationId = id

            this.showDialer = false
            this.messagesLoading = true;

            await this.getSMSMessages(this.callInfo.contactPhone)

            this.messagesLoading = false;
            this.messengerActive = true;
        },

        /**
         * Register communication listeners
         */
        bindCommunicationListener(){
            this.communicationPusherService.bindOnNewSMSMessage((message) => this.addNewSMSMessage(message))
        },

        /**
         * Unbind communication listeners
         */
        unbindCommunicationListener(){
            this.communicationPusherService.unbindOnNewSMSMessage()
        },

        /**
         * Ensure the open only one communication portal window
         *
         */
        setHandleCommunicationPortalVisibilityEvent(){
            this.communicationPortalLocalStorageService.setCommunicationPortalVisibility(true)

            window.addEventListener('beforeunload', () =>
                this.communicationPortalLocalStorageService.setCommunicationPortalVisibility(false)
            );
        },

        /**
         * Ask microphone permission
         *
         * @returns {Promise<void>}
         */
        async askMicrophonePermission() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({audio: true})
                if (stream) window.localStream = stream;
            } catch {
                this.alerts.showMicrophoneAccess = true
            }
        },

        /**
         * Initialize services
         *
         */
        initializeServices(){
            this.api = ApiFactory.makeApiService(this.apiDriver);
            this.communicationApi = CommunicationApiFactory.makeApiService(this.apiDriver);

            this.communicationPortalLocalStorageService = new CommunicationPortalLocalStorageService()

            this.communicationPusherService = CommunicationPusherService.generate(this.pusherAppKey, this.pusherAppCluster)
            this.communicationPusherService.subscribe(this.userId);

            this.communicationPusherService.bindOnIncomingCallAccept((callInfo) => {
                // console.log('Accepted')
                this.callInfo.identifiedContact = {}
                this.showIdentificationModalVisible = true
                this.callInfo = { ...callInfo }
                this.callInfo.mode = CallModeEnum.INBOUND
                this.showDialer = false
                this.callState.active = true
                this.callInfo.status = CallStatusesEnum.CONNECTED
                this.deviceService.accept()
            })
        },

        /**
         * Send key pressed on call
         * @param digitPressed
         */
        handleDialerKeyPressed(digitPressed) {
            this.deviceService.sendDigitsToCall(digitPressed)
        },

        /**
         * Close messenger
         */
        handleCloseMessenger() {
            this.showDialer = true
            this.messengerActive = false
        },

        /**
         * Handle send sms message
         *
         * @param data
         * @returns {Promise<void>}
         */
        async handleSendMessage(data) {
            const body = data.message;
            const formatted = {
                direction: 'outbound',
                first_name: 'You',
                last_name: '',
                message_body: body,
                timestamp: Math.round(DateTime.now().toSeconds())
            };

            if (!this.messages) this.messages = [];

            this.messages.push(formatted);
            this.scrollMessagesDivDown();

            await this.communicationApi.createOutboundSMS(this.callInfo.contactPhone, body);
        },

        /**
         * Toggle minimize call
         */
        toggleMinimizeCall() {
            this.callState.minimized = !this.callState.minimized;
        },

        /**
         * Identify contact by number
         * @param number
         * @returns {Promise<void>}
         */
        async identifyNumber(number) {
            this.callState.isIdentifyingContact = true
            this.callInfo.contactName = 'Unknown'
            this.callInfo.contactPhone = number
            this.currentCallInfo.otherNumber = number

            try {
                const { data:lookupData } = await this.communicationApi.lookupCaller(number);

                const { identifiedContact } = lookupData.data


                if (identifiedContact) {
                    this.callInfo.contactName = identifiedContact.contactName
                    this.callInfo.relationId = identifiedContact.relationId
                    this.callInfo.relationType = identifiedContact.relationSubtype
                    if(identifiedContact.contactPhone !== null)
                        this.callInfo.contactPhone = identifiedContact.contactPhone
                    this.callInfo.comment = identifiedContact.comment
                    this.callInfo.identifiedContact = identifiedContact

                    this.currentCallInfo.relationType = identifiedContact.relationSubtype
                    this.currentCallInfo.relationId = identifiedContact.relationId
                }
            } catch (error) {
                console.error(error)
            }

            this.callState.isIdentifyingContact = false
        },

        /**
         * Create a twilio device
         *
         * @returns {Promise<void>}
         */
        async initializeDevice() {
            this.deviceService = new CommunicationFactory.makeService('twilio');

            const { data } = await this.api.getCommunicationToken()

            if (!data.data.status) throw new Error('Not allowed to create a device')

            if (!data.data.number) {
                this.alerts.showUserNoPhoneAvailable = true
                return
            }

            await this.deviceService.initializeDevice(data.data.token, data.data.number);
        },


        /**
         * Check user has alerts to prevent errors
         *
         * @returns {boolean}
         */
        checkUserHasAlerts(){
            return Object.values(this.alerts).filter(v => v).length > 0
        },

        /**
         * Get contact info by phone number and handle call attempt
         *
         * @param phone
         * @returns {Promise<void>}
         */
        async handleDialerCall(phone) {
            this.showIdentificationModalVisible = true
            // Ask microphone permission and if denied, show error message
            await this.askMicrophonePermission()
            if (this.checkUserHasAlerts()) return

            this.callInfo.mode = CallModeEnum.OUTBOUND
            this.callState.active = true;
            this.showDialer = false
            this.callInfo.contactPhone = phone
            this.callInfo.contactName = phone
            this.callInfo.status = CallStatusesEnum.CONNECTING
            this.callInfo.relationId = null
            this.callInfo.relationType = null
            this.callInfo.comment = ''
            this.callInfo.identifiedContact = {}
            this.currentCallInfo.relationId = null
            this.currentCallInfo.relationType = null

            this.currentCallInfo.otherNumber = phone

            this.communicationPusherService.triggerOnOutgoingCallAttempt(this.callInfo)

            // console.log('Looking up caller...')
            this.identifyNumber(phone).then()

            // console.log('handleAttemptCall...')
            await this.handleAttemptCall();
        },


        /**
         * Get contact info and show messenger component
         *
         * @param phone
         * @returns {Promise<void>}
         */
        async showMessenger(phone) {
            this.messengerActive = true;

            this.showDialer = false
            this.callInfo = {...INIT_CALL_INFO}
            this.messagesLoading = true;

            this.callInfo.contactPhone = phone

            this.identifyNumber(phone).then()

            await this.getSMSMessages(this.callInfo.contactPhone)

            this.messagesLoading = false;
        },

        /**
         * Get and set sms messages by contact phone number
         *
         * @param phone
         * @returns {Promise<void>}
         */
        async getSMSMessages(phone){
            const response = await this.api.getSMSHistory(phone)
            this.messages = response.data.data.messages;

            this.scrollMessagesDivDown()
        },

        /**
         * Add new sms message and scroll messages div down
         *
         * @param message
         */
        addNewSMSMessage(message){
            this.messages.push(message)
            this.scrollMessagesDivDown()
        },

        /**
         * Scroll messages div down when loading messages
         */
        scrollMessagesDivDown(){
            setTimeout(() => this.$refs.messenger.scrollDown(), 50)
        },

        /**
         * Handle dialer attempt call
         *
         * @returns {Promise<void>}
         */
        async handleAttemptCall() {
            // Prevent duplicate calls
            if (!this.callState.active) return

            // console.log('initializeCall...')

            this.callInfo.status = CallStatusesEnum.CONNECTING
            this.communicationPusherService.triggerOnOutgoingCallRinging()

            // Create a call
            await this.deviceService.initializeCall(this.callInfo.contactPhone);

            // console.log('waitForCallbackToReturnTrue...')
            try {
                await waitForCallbackToReturnTrue(() => this.deviceService.getCallId(), 500, 20)
            } catch {
                console.error('Error trying to call')
                this.handleCallEnd()

            }
        },

        async saveCallLog(payload){
            const method = this.callInfo.mode === CallModeEnum.OUTBOUND ? 'logOutboundCall' : 'logInboundCall'

            // Prevent create feed activity logs for wrong companies
            if (this.showIdentificationModalVisible) {
                payload['relationType'] = null
                payload['relationId'] = null
            }

            const response = await this.communicationApi[method](payload)

            this.currentCallInfo.logId = response.data.data.call_log_id
        },

        /**
         * Handle call end
         *
         * @returns {Promise<void>}
         */
        async handleCallEnd() {
            this.currentCallInfo.otherNumber = this.callInfo.contactPhone
            const callId = this.deviceService.getCallId()

            this.saveCallLog({
                serviceName: this.deviceService.serviceName(),
                userPhoneNumber: this.deviceService.getUserPhone(),
                otherNumber: this.callInfo.contactPhone,
                reference: callId,
                relationType: this.currentCallInfo.relationType,
                relationId: this.currentCallInfo.relationId,
                result: 'answered'
            }).then()

            // console.log('Handle call end')
            this.deviceService.hangUp();
            this.currentCallInfo.sid = callId
            this.communicationPusherService.triggerOnCallEnd({ ...this.currentCallInfo, showIdentificationModalVisible: this.showIdentificationModalVisible})
            this.resetState()

            this.callInfo.status = CallStatusesEnum.ENDED
        },

        async endCall() {
            this.deviceService.hangUp();
        },

        /**
         * Reset important variables to their initial state and remove call listeners
         *
         */
        resetState() {
            this.callState = { ...INIT_CALL_STATE }
            this.callInfo.contactName = ''
            this.callInfo.relationId = ''
            this.callInfo.relationType = ''
            this.callInfo.comment = ''
            this.alerts = { ...INIT_ALERTS_STATE }
            this.showDialer = true
        },

        /**
         * Handle call hold
         *
         */
        handleCallHold() {
            this.callState.held = !this.callState.held
            // TODO - Implement hold call in twilio
            // this.deviceService.hold()
            this.communicationPusherService.triggerOnCallHold({held: this.callState.held})
        },

        /**
         * Handle call mute
         *
         */
        handleCallMute() {
            this.callState.muted = !this.callState.muted

            this.deviceService.mute(this.callState.muted)
            this.communicationPusherService.triggerOnCallMute({muted: this.callState.muted})
        },

        /**
         * Trigger event to open url on parent window
         */
        handleOpenRelationPage(){
            let url = ''
            const identifiedContact = this.callInfo.identifiedContact

            if (CommunicationRelationTypes.isCompanyRelated(identifiedContact.relationSubtype))
                url = `/companies/${identifiedContact.relationData.id}`

            else if (CommunicationRelationTypes.isLeadRelated(identifiedContact.relationSubtype))
                url = `/lead-processing?lead_id=${identifiedContact.relationId}`

            this.communicationPusherService.triggerOnOpenRelationPage({ url })
        },
    }
}
</script>
