import { defineStore } from "pinia";
import { reactive } from "vue";
import { useProductConfigurationStore } from "./product-configuration.js";
import { useFutureCampaignStore } from "./future-campaigns.js";
import { useLocalityDataStore } from "./locality-data.js";


export const usePricingStore = defineStore('pricing', () => {
    const productConfigurationStore = useProductConfigurationStore();
    const campaignStore = useFutureCampaignStore();
    const localityStore = useLocalityDataStore();

   // Storage of price ranges for campaign budget display
   const priceRanges = reactive({
       lead: {},
       appointment: {},
       'direct leads': {}
   });

   // Storage of bidding and floor prices for Bidding Control
   const prices = reactive({
       lead: {},
       appointment: {},
       'direct leads': {}
   });

    /**
     * Get min/max prices ranges by zipcodes
     */
   async function getPriceRangeForZipCodes(zipCodeIds, productKey, industryKey, serviceKey, propertyTypes) {
        // make sure this is an array of location ids
        if (!zipCodeIds[0] || typeof zipCodeIds[0] !== 'number')
            zipCodeIds = localityStore.transformZipCodeLocationsToIds(zipCodeIds);

       const resp = await campaignStore.apiService.getPriceRangeForZipCodes(
           zipCodeIds,
           productKey,
           industryKey,
           serviceKey,
           propertyTypes,
       );
       if (resp.data?.data?.status) {
           priceRanges[productKey].new = resp.data.data.prices;
           return { status: true }
       }
       else {
           return genericErrorMessage(resp);
       }
   }

   function getFetchedPriceRangesForProductV4() {
       let product = productConfigurationStore.productScope;

       return priceRanges[product].new;
   }

   function genericErrorMessage(resp) {
       return { status: false, message: resp.data?.data?.message || resp.response?.data?.message || resp.err || `An unknown error occurred fetching data.` };
   }

   function clearStore() {
       Object.assign(prices, {
           lead: {},
           appointment: {},
           'direct leads': {},
       });
       Object.assign(priceRanges, {
           lead: {},
           appointment: {},
           'direct leads': {}
       });
   }

   function $reset() {
      clearStore();
   }

   return {
       priceRanges,
       prices,
       getPriceRangeForZipCodes,
       getFetchedPriceRangesForProductV4,
       $reset,
   }

});
