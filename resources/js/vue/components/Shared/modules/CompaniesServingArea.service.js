import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useCompaniesServingAreaStore = defineStore('companies-serving-area', () => {
    const filters = ref([])
    const presets = ref([])

    const keyDataFilters = computed(() => {
        let result = {}

        if (!filters.value && filters.value?.length === 0) {
            return null
        }

        for (const key in filters.value) {
            const filter = filters.value[key]

            result[filter?.id] = filter?.data ?? {}
        }

        return result
    })

    const activeFilters = computed(() => {
        return filters.value?.filter(filter => filter?.data?.active) ?? []
    })

    const activeFiltersCount = computed(() => {
        return activeFilters.value.length
    })

    return {
        filters,
        presets,
        keyDataFilters,
        activeFilters,
        activeFiltersCount,
    }
})
