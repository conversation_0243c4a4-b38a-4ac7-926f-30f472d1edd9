<template>
    <div class="col-span-3 border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div>
            <Tab
                :dark-mode="darkMode"
                :tabs="tabs"
                :total="totalTasks"
                :loading="loadingTasks"
                @selected="processTabFilter"
                :tabs-classes="'md:w-2/3'"
                :default-tab-index="1"
            >
                <template v-slot:extra-tab>
                    <button v-show="false" class="text-blue-550 hover:text-blue-400 font-medium px-5 text-sm">+ Add queue</button>
                </template>
            </Tab>
        </div>
        <div class="flex justify-between items-end flex-wrap gap-3 px-5 my-5">
            <div class="flex items-end flex-wrap gap-3">
                <div class="min-w-[12rem] flex-shrink-0">
                    <CustomInput class="flex-grow" :dark-mode="darkMode" placeholder="Search" v-model="filterTaskName" type="text" v-on:keyup.enter="getTasks" search-icon></CustomInput>
                </div>
                <div class="min-w-[10rem]">
                    <Dropdown :dark-mode="darkMode" v-model="filterTaskPriority" :options="taskPriorities" placeholder="Priority" :selected="filterTaskPriority"></Dropdown>
                </div>
                <div class="min-w-[18rem]">
                    <Dropdown v-model="searchCategory" :options="taskCategories" :dark-mode="darkMode" placeholder="Category" :selected="searchCategory"></Dropdown>
                </div>
                <div class="min-w-[10rem]">
                    <Dropdown v-model="selectedTimezone" :options="timezoneOptions" :dark-mode="darkMode" placeholder="Timezone"></Dropdown>
                </div>
                <div v-if="filterTab === 'Overdue' || filterTab === 'All'" class="max-w-[12rem]">
                    <Datepicker v-model="filterDateTime" :dark="darkMode" :format="'yyyy-MM-dd'"></Datepicker>
                </div>
                <div class="flex items-center gap-3">
                    <button class="transition duration-200 bg-primary-500 font-semibold hover:bg-blue-500 text-white text-sm h-9 justify-center flex items-center font-medium focus:outline-none py-2 rounded-md px-5" @click="getTasks">
                        <svg class="inset-y-0 w-4 my-1 fill-current" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.66667 2C4.69421 2 3.76158 2.38631 3.07394 3.07394C2.38631 3.76158 2 4.69421 2 5.66667C2 6.14818 2.09484 6.62498 2.27911 7.06984C2.46338 7.5147 2.73346 7.91891 3.07394 8.25939C3.41442 8.59987 3.81863 8.86996 4.26349 9.05423C4.70835 9.23849 5.18515 9.33333 5.66667 9.33333C6.14818 9.33333 6.62498 9.23849 7.06984 9.05423C7.5147 8.86996 7.91891 8.59987 8.25939 8.25939C8.59987 7.91891 8.86996 7.5147 9.05423 7.06984C9.23849 6.62498 9.33333 6.14818 9.33333 5.66667C9.33333 4.69421 8.94703 3.76158 8.25939 3.07394C7.57176 2.38631 6.63913 2 5.66667 2ZM1.65973 1.65973C2.72243 0.597022 4.16377 0 5.66667 0C7.16956 0 8.6109 0.597022 9.6736 1.65973C10.7363 2.72243 11.3333 4.16377 11.3333 5.66667C11.3333 6.41082 11.1868 7.14769 10.902 7.83521C10.7458 8.21219 10.5498 8.57029 10.3178 8.90361L13.7071 12.2929C14.0976 12.6834 14.0976 13.3166 13.7071 13.7071C13.3166 14.0976 12.6834 14.0976 12.2929 13.7071L8.90361 10.3178C8.57029 10.5498 8.21219 10.7458 7.83521 10.902C7.14769 11.1868 6.41082 11.3333 5.66667 11.3333C4.92251 11.3333 4.18564 11.1868 3.49813 10.902C2.81062 10.6172 2.18593 10.1998 1.65973 9.6736C1.13353 9.14741 0.716126 8.52272 0.431349 7.83521C0.146573 7.1477 0 6.41082 0 5.66667C0 4.16377 0.597022 2.72243 1.65973 1.65973Z"/>
                        </svg>
                    </button>
                    <button class="transition duration-200 h-9 text-sm font-semibold focus:outline-none py-2 rounded-md px-4"
                            :class="{'bg-grey-250 hover:bg-light-background text-white': !darkMode, 'bg-grey-500 hover:bg-grey-600 text-white': darkMode}" @click="resetFilters">
                        Reset
                    </button>
                </div>

            </div>
            <div class="flex flex-shrink-0 items-center gap-3">
                <button class="transition duration-200 text-sm font-semibold focus:outline-none py-2 rounded-md px-5" @click="showCreateTask"
                        :class="{'bg-grey-475 hover:bg-blue-800 text-white': !darkMode, 'bg-blue-400 hover:bg-blue-500 text-white': darkMode}">
                    Create task
                </button>
                <button
                    class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-semibold focus:outline-none py-2 rounded-md px-5"
                    @click="startTasks"
                    v-if="!readOnly"
                >
                    Start tasks
                </button>
            </div>
        </div>
        <div v-if="!loadingTasks && tasks.length" class="grid grid-cols-12 items-center gap-x-3 my-2 px-5">
            <div class="text-slate-400 font-medium tracking-wide uppercase text-xs flex col-span-1">
                <input
                    v-model="taskSelected"
                    :class="[!darkMode ? 'hover:bg-grey-50 border-grey-200' : 'bg-dark-background hover:bg-dark-175 border-blue-400']"
                    @click="toggleBulkSelect($event)"
                    class="rounded-sm w-5 h-5 cursor-pointer border"
                    type="checkbox"
                    v-if="!readOnly"
                >
                <span v-if="headerVisible" class="text-center flex-1 text-slate-400 font-medium tracking-wide uppercase text-xs">Status</span>
            </div>
            <div v-if="headerVisible" class="col-span-2 uppercase text-xs text-center cursor-pointer rounded-lg flex items-center"
                 :class="filterSortCol === sortColumns.taskName ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400 text-blue-400'"
                 @click="sort(sortColumns.taskName)">
                Task Name
                <svg v-if="filterSortCol === sortColumns.taskName"
                     :class="{
                                'text-blue-550 rotate-180': filterSortCol === sortColumns.taskName && filterSortDir === 'asc',
                                'text-blue-550': filterSortCol === sortColumns.taskName && filterSortDir !== 'asc',
                                'hover:text-cyan-400': filterSortCol !== sortColumns.taskName
                            }"
                     class="ml-2 transform transition-all duration-200 w-6 fill-current"
                     width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70711 0.292893C10.0976 0.683418 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.51957 5.89464 5.26522 6 5 6C4.73478 6 4.48043 5.89464 4.29289 5.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976315 1.31658 -0.0976314 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893Z"/>
                </svg>
            </div>
            <div v-if="headerVisible" class="col-span-1 uppercase text-xs text-center cursor-pointer rounded-lg flex items-center"
                 :class="filterSortCol === sortColumns.assignedTo ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400 text-blue-400'"
                 @click="sort(sortColumns.assignedTo)">
                Assigned To
                <svg v-if="filterSortCol === sortColumns.assignedTo"
                     :class="{
                                'text-blue-550 rotate-180': filterSortCol === sortColumns.assignedTo && filterSortDir === 'asc',
                                'text-blue-550': filterSortCol === sortColumns.assignedTo && filterSortDir !== 'asc',
                                'hover:text-cyan-400': filterSortCol !== sortColumns.assignedTo
                            }"
                     class="ml-2 transform transition-all duration-200 w-6 fill-current"
                     width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70711 0.292893C10.0976 0.683418 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.51957 5.89464 5.26522 6 5 6C4.73478 6 4.48043 5.89464 4.29289 5.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976315 1.31658 -0.0976314 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893Z"/>
                </svg>
            </div>
            <p v-if="headerVisible" class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Category</p>
            <p v-if="headerVisible" class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Timezone</p>
            <div v-if="actionVisible" class="col-span-4 flex items-center">
                <p class="mr-10 text-blue-550 hover:text-blue-400 text-sm font-medium cursor-pointer inline-flex items-center" @click="showBulkComplete = true">
                    <svg class="mr-2" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3.59213 6.71136L1.07909 4.0464L0 5.19072L3.59213 9L11 1.14432L9.92091 0L3.59213 6.71136Z" fill="#339AFF"/>
                    </svg>
                    Mark as completed
                </p>
                <p class="mr-10 text-blue-550 hover:text-blue-400 text-sm font-medium cursor-pointer inline-flex items-center" @click="showReschedule = true">
                    <svg class="mr-2" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" />
                    </svg>
                    Reschedule
                </p>
                <p class="mr-10 text-blue-550 hover:text-blue-400 text-sm font-medium cursor-pointer inline-flex items-center" v-if="false">
                    <svg class="mr-2" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3.07066 9.3043L8.07078 4.30418L5.69582 1.92922L0.695701 6.92934C0.626865 6.99826 0.577967 7.08453 0.554193 7.17899L0 10L2.82047 9.44581C2.91516 9.42213 3.00179 9.37317 3.07066 9.3043V9.3043ZM9.68493 2.69003C9.88667 2.48823 10 2.21457 10 1.92922C10 1.64388 9.88667 1.37022 9.68493 1.16842L8.83158 0.315069C8.62978 0.11333 8.35612 0 8.07078 0C7.78543 0 7.51177 0.11333 7.30997 0.315069L6.45662 1.16842L8.83158 3.54338L9.68493 2.69003Z" fill="#339AFF"/>
                    </svg>
                    Edit
                </p>
                <p class="mr-10 text-blue-550 hover:text-blue-400 text-sm font-medium cursor-pointer inline-flex items-center" v-if="false">
                    <svg class="mr-2" width="9" height="11" viewBox="0 0 9 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1.5 2.75H1V9.9C1 10.1917 1.10536 10.4715 1.29289 10.6778C1.48043 10.8841 1.73478 11 2 11H7C7.26522 11 7.51957 10.8841 7.70711 10.6778C7.89464 10.4715 8 10.1917 8 9.9V2.75H1.5ZM6.809 1.1L6 0H3L2.191 1.1H0V2.2H9V1.1H6.809Z" fill="#339AFF"/>
                    </svg>
                    Delete
                </p>
                <p class="text-blue-550 hover:text-blue-400 text-sm font-medium cursor-pointer inline-flex items-center" v-if="false">
                    <svg class="mr-2" width="9" height="11" viewBox="0 0 9 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 1.1C9 0.49335 8.5515 0 8 0H1C0.4485 0 0 0.49335 0 1.1V4.95H9V1.1ZM6.5 3.3H2.5V1.65H3.5V2.2H5.5V1.65H6.5V3.3ZM1 11H8C8.5515 11 9 10.5067 9 9.9V6.05H0V9.9C0 10.5067 0.4485 11 1 11ZM2.5 7.7H3.5V8.25H5.5V7.7H6.5V9.35H2.5V7.7Z" fill="#339AFF"/>
                    </svg>
                    Change queue
                </p>
            </div>
            <p v-if="headerVisible" class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Company</p>
            <p v-if="headerVisible" class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">State : City</p>
            <p v-if="headerVisible" class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Source</p>
            <div v-if="headerVisible" class="col-span-1 uppercase text-xs text-center cursor-pointer rounded-lg flex items-center"
                 :class="filterSortCol === sortColumns.priority ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400 text-blue-400'"
                 @click="sort(sortColumns.priority)">
                Priority
                <svg v-if="filterSortCol === sortColumns.priority"
                     :class="{
                                'text-blue-550 rotate-180': filterSortCol === sortColumns.priority && filterSortDir === 'asc',
                                'text-blue-550': filterSortCol === sortColumns.priority && filterSortDir !== 'asc',
                                'hover:text-cyan-400': filterSortCol !== sortColumns.priority
                            }"
                     class="ml-2 transform transition-all duration-200 w-6 fill-current"
                     width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70711 0.292893C10.0976 0.683418 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.51957 5.89464 5.26522 6 5 6C4.73478 6 4.48043 5.89464 4.29289 5.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976315 1.31658 -0.0976314 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893Z"/>
                </svg>
            </div>
            <div v-if="headerVisible && filterTab !== 'Completed'" class="col-span-1 uppercase text-xs text-center cursor-pointer rounded-lg flex items-center"
                 :class="filterSortCol === sortColumns.due ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400 text-blue-400'"
                 @click="sort(sortColumns.due)">
                Due
                <svg v-if="filterSortCol === sortColumns.due"
                     :class="{
                                'text-blue-550 rotate-180': filterSortCol === sortColumns.due && filterSortDir === 'asc',
                                'text-blue-550': filterSortCol === sortColumns.due && filterSortDir !== 'asc',
                                'hover:text-cyan-400': filterSortCol !== sortColumns.due
                            }"
                     class="ml-2 transform transition-all duration-200 w-6 fill-current"
                     width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70711 0.292893C10.0976 0.683418 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.51957 5.89464 5.26522 6 5 6C4.73478 6 4.48043 5.89464 4.29289 5.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976315 1.31658 -0.0976314 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893Z"/>
                </svg>
            </div>
            <div v-if="headerVisible && filterTab === 'Completed'" class="col-span-1 uppercase text-xs text-center cursor-pointer rounded-lg flex items-center"
                 :class="filterSortCol === sortColumns.completedAt ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400 text-blue-400'"
                 @click="sort(sortColumns.completedAt)">
                Completed
                <svg v-if="filterSortCol === sortColumns.completedAt"
                     :class="{
                                'text-blue-550 rotate-180': filterSortCol === sortColumns.completedAt && filterSortDir === 'asc',
                                'text-blue-550': filterSortCol === sortColumns.completedAt && filterSortDir !== 'asc',
                                'hover:text-cyan-400': filterSortCol !== sortColumns.completedAt
                            }"
                     class="ml-2 transform transition-all duration-200 w-6 fill-current"
                     width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70711 0.292893C10.0976 0.683418 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.51957 5.89464 5.26522 6 5 6C4.73478 6 4.48043 5.89464 4.29289 5.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976315 1.31658 -0.0976314 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893Z"/>
                </svg>
            </div>
        </div>
        <div v-if="loadingTasks" class="flex w-full h-100 justify-center items-center">
            <loading-spinner/>
        </div>
        <div v-if="!loadingTasks && !tasks.length" class="h-100 flex items-center justify-center text-center pb-4"
             :class="{'text-grey-800': !darkMode, 'text-grey-120': darkMode}">
            No Tasks found for this type
        </div>
        <div v-if="!loadingTasks && tasks.length" class="border-t border-b h-100 overflow-y-auto divide-y" ref="tasks"
             :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
            <div v-for="(task, index) in tasks">
                <Task :key="task?.id ?? index" @select="selectTask" @deselect="deselectTask" @delete="getTasks" @mute-task="muteTask"  @unmute-task="unmuteTask" :task="task" :dark-mode="darkMode" :filter-tab="filterTab" :read-only="allUserTask && !makeAllUserTaskClickable"></Task>
            </div>
        </div>
        <div v-if="paginationData && paginationData.to" class="p-3">
            <Pagination :dark-mode="darkMode" :pagination-data="paginationData" :show-pagination="true" :show-results-per-page="true" @change-page="handlePaginationEvent"></Pagination>
        </div>
        <modal
            :confirm-text="savingTask ? 'Saving...' : 'Create Task'"
            :close-text="'Cancel'"
            :dark-mode="darkMode"
            @close="closeModal"
            @confirm="createTask"
            :small="true"
            v-if="showCreateTaskModal"
            :disable-confirm="savingTask"
            :noMinHeight="true"
            :no-scroll="true"
        >
            <template v-slot:header>
                <h4 class="text-xl">Create Task</h4>
            </template>
            <template v-slot:content>
                <div class="mb-6" v-if="alertMessage">
                    <alert :text="alertMessage" :alert-type="alertType" :dark-mode="darkMode"></alert>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                    <div>
                        <p class="uppercase font-semibold text-xs mb-2"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Company
                        </p>
                        <autocomplete
                            :dark-mode="darkMode"
                            class="col-span-2"
                            v-model="company"
                            :options="companies"
                            :placeholder="'Company name'"
                            :model-value="companyId"
                            :create-user-input-option="true"
                            @search="searchCompanies('companyname', $event)">
                        </autocomplete>
                    </div>
                    <div>
                        <p class="uppercase font-semibold text-xs mb-2"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Task Type
                        </p>
                        <dropdown :options="taskTypes" :dark-mode="darkMode" :placeholder="'Select Task Type'" v-model="taskType"></dropdown>
                    </div>
                    <div>
                        <p class="uppercase font-semibold text-xs mb-2"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Task Note
                        </p>
                        <input class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                               placeholder="Task note"
                               v-model="taskNote"
                               :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                    </div>
                    <div class="relative">
                        <p class="uppercase font-semibold text-xs mb-2"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Available At
                        </p>

                        <Datepicker
                            v-model="filterDateTime"
                            placeholder="Pick Date Time"
                            class="date-picker"
                            :dark="darkMode"
                            :format="'yyyy-MM-dd'"
                            :start-time="manualTaskDefaultTime"
                            @update:modelValue="setDateTime"
                        />
                    </div>

                    <div>
                        <p class="uppercase font-semibold text-xs mb-2"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Priority
                        </p>
                        <Dropdown :dark-mode="darkMode" class="mr-2" v-model="priority" :options="taskPriorities" placeholder="Priority" :selected="priority"></Dropdown>
                    </div>
                    <div>
                        <p class="uppercase font-semibold text-xs mb-2"  :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Task Category
                        </p>

                        <Dropdown v-model="taskCategory" :options="taskCategories" :dark-mode="darkMode" placeholder="Select Category" :selected="taskCategory"></Dropdown>
                    </div>
                </div>
            </template>
        </modal>
        <bulk-complete :dark-mode="darkMode" :tasks="selectedTasks" v-if="showBulkComplete" @close="showBulkComplete = false" @completed="tasksCompleted"></bulk-complete>
        <Modal v-if="showDatePickerModal" :small="true" @close="showDatePickerModal = false" :no-buttons="true" :full-width="false">
            <template v-slot:header>
                <h4 class="text-xl font-medium">Select Day</h4>
            </template>
            <template v-slot:content>
                <Datepicker :inline="true" @update:modelValue="rescheduleTaskCustomDateTime" :format="'yyyy-MM-dd HH:mm'"></Datepicker>
            </template>
        </Modal>
        <modal :dark-mode="darkMode" v-if="showReschedule" :small="true" @close="showReschedule = false" @confirm="rescheduleTasks">
            <template v-slot:header>
                <h4 class="text-xl font-medium">Reschedule</h4>
            </template>
            <template v-slot:content>
                <div class="min-h-[30vh]">
                    <div class="grid grid-cols-2 gap-5">
                        <div>
                            <ButtonDropdown :options="rescheduleOptions" @selected="rescheduleOptionSelected" :dark-mode="darkMode">
                                <button
                                    class="w-full transition duration-200 inline-flex items-center font-semibold bg-grey-475 hover:bg-grey-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5">
                                    <svg class="mr-2" width="14" height="14" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" fill="none"
                                         xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                    </svg>
                                    Choose a Time to Reschedule
                                </button>
                            </ButtonDropdown>
                        </div>
                        <div>
                            <div class="relative inline-block text-left">
                                <button
                                    class="w-full transition duration-200 inline-flex items-center font-semibold bg-grey-475 hover:bg-grey-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                                    @click="addRescheduleNote = !addRescheduleNote">
                                    <svg class="mr-2" width="14" height="14" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" fill="none"
                                         xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                                    </svg>
                                    Add a Reason for Reschedule
                                </button>
                            </div>
                        </div>
                        <div class="col-span-2 flex items-center" v-if="rescheduleOption">
                            <div class="w-2 h-2 rounded-full bg-primary-500 mr-2 font-medium"></div>
                            Reschedule For: <span class="ml-1 text-blue-550 font-semibold">{{ rescheduleOption.id === 'custom_date_time' ? $filters.dateFromTimestamp(customRescheduleDateTime, 'long') : rescheduleOption.name + ' From Now' }}</span>
                        </div>
                        <div class="col-span-2" v-if="addRescheduleNote">
                            <textarea class="w-full border rounded focus:outline-none focus:border focus:border-primary-500 py-2" placeholder="Reason" type="text" rows="5" v-model="rescheduleNote"
                          :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"/>
                        </div>
                    </div>
                </div>
            </template>
        </modal>
    </div>
</template>

<script>
import Tab from "../components/Tab.vue";
import ApiService from "../../Tasks/services/api";
import LoadingSpinner from "../components/LoadingSpinner.vue";
import Pagination from "../components/Pagination.vue";
import Dropdown from "../components/Dropdown.vue";
import InvoiceBilling from "./InvoiceBilling.vue";
import Leads from "./Leads.vue";
import CompanyContacts from "./Contacts.vue";
import Campaigns from "../../Companies/Campaigns.vue";
import Modal from "../components/Modal.vue";
import Autocomplete from "../components/Autocomplete.vue";
import SharedApiService from "../services/api";
import TaskManagementApiService from "../../TaskManagement/services/api";
import Alert from "../components/Alert.vue";
import BulkComplete from "../../Tasks/BulkComplete.vue";
import DeleteTask from "../../Tasks/DeleteTask.vue";
import dayjs from "dayjs";
import Task from "./Tasks/Task.vue";
import ButtonDropdown from "../components/ButtonDropdown.vue";
import Datepicker from "@vuepic/vue-datepicker";
import _ from "lodash";
import CustomInput from "../components/CustomInput.vue";

export default {
    name: "Tasks",
    components: {
        CustomInput,
        ButtonDropdown,
        Datepicker,
        Task,
        Campaigns,
        CompanyContacts,
        Leads,
        InvoiceBilling,
        Dropdown,
        Tab,
        LoadingSpinner,
        Pagination,
        Modal,
        Autocomplete,
        Alert,
        BulkComplete,
        DeleteTask
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null
        },
        companyName: {
            type: String,
            default: null
        },
        allUserTask: {
            type: Boolean,
            default: false
        },
        makeAllUserTaskClickable: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            apiService: null,
            sharedApi: SharedApiService.make(),
            taskManagementApi: TaskManagementApiService.make(),
            loadingTasks: false,
            addRescheduleNote: false,
            rescheduleOption: null,
            rescheduleNote: null,
            customRescheduleDateTime: null,
            tabs: [
                { name: 'All',          current: false },
                { name: 'Due Today',    current: true },
                { name: 'Overdue',      current: false },
                { name: 'Upcoming',     current: false },
                { name: 'Completed',    current: false },
                { name: 'Muted',       current: false },
            ],
            taskPriorities: [
                { id: 1, name: 'Low'   },
                { id: 2, name: 'Medium'},
                { id: 3, name: 'High'  },
                { id: 4, name: 'Urgent'}
            ],
            sortColumns: {
                taskName    : 'subject',
                priority    : 'priority',
                due         : 'available_at',
                completedAt : 'completed_at',
                assignedTo  : 'assigned_user_id'
            },
            paginationData: null,
            totalTasks: null,
            tasks: [],
            taskSelected: false,
            selectedTasks: [],
            companies: [],
            taskTypes: [],
            showCreateTaskModal: false,
            taskNote: null,
            available_at: null,
            priority: 1,
            taskType: null,
            company: null,
            showCalender: false,
            createTaskError: null,
            filterTab: null,
            filterTaskName: null,
            filterTaskPriority: null,
            filterSortCol: null,
            filterSortDir: 'desc',
            alertMessage: null,
            savingTask: false,
            alertType: 'success',
            rescheduleOptions: [
                {id: 'one_hour', name: 'One Hour'},
                {id: 'one_day', name: 'One Day'},
                {id: 'one_week', name: 'One Week'},
                {id: 'one_month', name: 'One Month'},
                {id: 'custom_date_time', name: 'Custom (Date & Time)'},
            ],
            perPage: '25',
            showBulkComplete: false,
            showReschedule: false,
            manualTaskDefaultTime: {hours: 8, minutes: 0, seconds: 0},
            taskCategory: 1,
            taskCategories: [],
            searchCategory: null,
            showDatePickerModal: false,
            timezoneOptions: [
                {id: "any", name: "Any Timezone"},
                {id: "-05:00", name: "Eastern"},
                {id: "-06:00", name: "Central"},
                {id: "-07:00", name: "Mountain"},
                {id: "-08:00", name: "Pacific"},
                {id: "-09:00", name: "Alaska"},
                {id: "-10:00", name: "Hawaii"},
            ],
            selectedTimezone: "any",
            filterDateTime: null,
        }
    },
    created() {
      this.apiService = ApiService.make();
      this.load();
      this.getTaskCategories();

      if (this.companyId && this.companyName) {
          this.companies = [{
              id: this.companyId,
              name: `${this.companyId}: ${this.companyName}`
          }];
      }
    },
    computed: {
        actionVisible() {
            return this.selectedTasks.length > 0;
        },
        headerVisible() {
            return this.selectedTasks.length === 0;
        },
        readOnly() {
            return this.allUserTask && !this.makeAllUserTaskClickable;
        }
    },
    methods: {
        load() {
            this.loadingTasks = true;
            this.filterTab = this.tabs[1].name;
            this.filterSortCol = this.sortColumns.priority;

            this.getTasksOverview();
            this.getTasks();
            this.getTaskTypes();
        },
        selectTask(task) {
            this.selectedTasks.push(task.id);
        },
        deselectTask(task) {
            const index = this.selectedTasks.indexOf(task.id);

            if (index !== -1) {
                this.selectedTasks.splice(index, 1);
            }
        },
        rescheduleTasks() {
            this.apiService.batchReschedule({
                tasks: this.selectedTasks,
                option: this.rescheduleOption.id,
                available_at: this.customRescheduleDateTime,
                note: this.rescheduleNote ?? ""
            }).then(() => {})
                .catch(e => {console.error(e)})
                .finally(() => {
                    this.rescheduleOption = null;
                    this.customRescheduleDateTime = null;
                    this.addRescheduleNote = false;
                    this.rescheduleNote = null;
                    this.showReschedule = false;
                    this.getTasksOverview();
                    this.getTasks();
                });
        },
        toggleBulkSelect(event) {
            if (event.target.checked) {
                this.selectedTasks = this.tasks.map(task => task.id);
                this.$refs.tasks.querySelectorAll('input[type=checkbox]').forEach(checkbox => checkbox.checked = true);
            }
            else {
                this.selectedTasks = [];
                this.$refs.tasks.querySelectorAll('input[type=checkbox]').forEach(checkbox => checkbox.checked = false);
            }
        },
        async getTasks() {
            this.selectedTasks = [];
            this.taskSelected = false;
            this.tasks = [];
            if(!this.loadingTasks) this.loadingTasks = true;

            let params = this.getParams();

            switch (this.filterTab) {

                case 'All':
                    await this.apiService.getAllTasks(params).then(resp => {
                        if(resp.data.data.status === true) {
                            this.addTaskAndPaginationData(resp);
                            this.totalTasks[this.tabs[0].name] = resp.data.data.tasks.total ?? 0;
                        }
                    }).catch(e => console.error(e)).finally(() => this.loadingTasks = false);
                    break;

                case 'Due Today':
                    this.apiService.getTodayTasks(params).then(resp => {
                        if(resp.data.data.status === true) {
                            this.addTaskAndPaginationData(resp);
                            this.totalTasks[this.tabs[1].name] = resp.data.data.tasks.total ?? 0;
                        }
                    }).catch(e => console.error(e)).finally(() => this.loadingTasks = false);
                    break;

                case 'Overdue':
                    this.apiService.getOverdueTasks(params).then(resp => {
                        if(resp.data.data.status === true) {
                            this.addTaskAndPaginationData(resp);
                            this.totalTasks[this.tabs[2].name] = resp.data.data.tasks.total ?? 0;
                        }
                    }).catch(e => console.error(e)).finally(() => this.loadingTasks = false);
                    break;

                case 'Upcoming':
                    this.apiService.getUpcomingTasks(params).then(resp => {
                        if(resp.data.data.status === true) {
                            this.addTaskAndPaginationData(resp);
                            this.totalTasks[this.tabs[3].name] = resp.data.data.tasks.total ?? 0;
                        }
                    }).catch(e => console.error(e)).finally(() => this.loadingTasks = false);
                    break;

                case 'Completed':
                    this.apiService.getCompletedTasks(params).then(resp => {
                        if(resp.data.data.status === true) {
                            this.addTaskAndPaginationData(resp);
                            this.totalTasks[this.tabs[4].name] = resp.data.data.tasks.total ?? 0;
                        }
                    }).catch(e => console.error(e)).finally(() => this.loadingTasks = false);
                    break;

                case 'Muted':
                    this.apiService.getMutedTasks(params).then(resp => {
                        if(resp.data.data.status === true) {
                            this.addTaskAndPaginationData(resp);
                            this.totalTasks[this.tabs[5].name] = resp.data.data.tasks.total ?? 0;
                        }
                    }).catch(e => console.error(e)).finally(() => this.loadingTasks = false);
                    break;
            }
        },
        addTaskAndPaginationData(resp) {
            let {data, ...paginationData} = resp.data.data.tasks;
            this.tasks = data ?? [];
            this.paginationData = paginationData;
        },
        getTasksOverview() {
            this.apiService.getTaskOverview(this.companyId, this.allUserTask).then(resp => {
                if(resp.data.data.status === true) {
                    this.totalTasks = resp.data.data.overview;
                }
            }).catch(e => {});
        },
        processTabFilter(filter) {
            this.filterTab = filter;
            this.filterSortCol = filter === 'Completed' ? this.sortColumns.completedAt : this.sortColumns.priority;
            this.getTasksOverview();
            this.getTasks();
        },
        startTasks() {
            this.$emit('start-tasks', this.selectedTasks.reverse());
        },
        resetFilters() {
            this.filterTaskName = this.filterTaskPriority = null;
            this.filterSortCol = this.sortColumns.priority;
            this.filterSortDir = 'desc';
            this.searchCategory = null;
            this.filterDateTime = null;
            this.getTasks();
        },
        sort(column = this.sortColumns.due) {
            this.filterSortDir = this.filterSortCol === column && this.filterSortDir === 'asc' ? 'desc' : 'asc';
            this.filterSortCol = column;
            this.getTasks();
        },
        showCreateTask() {
            this.showCreateTaskModal = true;
            this.taskNote = null;
            this.available_at = null;
            this.taskType = null;
            this.company = null;
            this.showCalender = false;
            this.alertMessage = null;
        },
        closeModal() {
            this.showCreateTaskModal = false;
        },
        createTask() {
            if (this.savingTask) return;

            this.alertMessage = null;
            this.savingTask = true;

            if (_.isString(this.company)) {
                this.alertType = 'error';
                this.alertMessage = "Please select a company from the dropdown";
                this.savingTask = false;
                return;
            }

            this.apiService.createTask({
                company_id: this.company,
                task_type_id: this.taskType,
                subject: this.taskNote,
                available_at: this.available_at,
                priority: this.priority,
                task_category_id: this.taskCategory
            }).then(() => {
                this.filterTab = this.tabs[1].name;
                this.getTasks();
                this.resetTabs();
                this.alertType = 'success';
                this.alertMessage = 'A task was created successfully'
                this.closeModal();
            }).catch(e => {
                this.alertType = 'error';
                this.alertMessage = e.response.data.message;
            }).finally(() => {
                this.savingTask = false;
                this.load();
            });
        },
        resetTabs() {
            this.tabs.forEach(tab => {
                tab.current = tab.name === 'Due Today';
            });
        },
        searchCompanies(nameType, query) {
            this.sharedApi.searchCompanyNamesAndId(query).then(res => {
                if (res.data.data.status === true) {
                    this.companies = res.data.data.companies;
                }
            })
        },
        getTaskTypes() {
            this.taskManagementApi.getTaskTypes().then(resp => this.taskTypes = resp.data.data.task_types);
        },
        setDateTime(date) {
            this.available_at = this.$filters.dateFromTimestamp(date, 'YYYY-MM-DD HH:mm');
            this.showCalender = false;
        },
        async handlePaginationEvent(newPageUrl) {
            this.loadingTasks = true;
            this.perPage = newPageUrl.perPage;
            await axios.get(newPageUrl.link, {
                params: this.getParams()
            }).then(resp => {
                let {data, ...paginationData} = resp.data.data.tasks;
                this.tasks = data;
                this.paginationData = paginationData;
            }).catch(e => console.error(e)).finally(() => this.loadingTasks = false);
        },
        getTaskRelation(task) {
            if (task.manual) return task.payload?.company_name;

            return task?.event?.company?.company_name ?? task?.event?.campaign?.name;
        },
        tasksCompleted() {
            this.getTasks();
            this.showBulkComplete = false;
            this.selectedTasks = [];
            this.taskSelected = false;
        },
        getParams() {
            return {
                all_user: this.allUserTask,
                subject: this.filterTaskName,
                priority: this.filterTaskPriority,
                sort_col: this.filterSortCol,
                sort_dir: this.filterSortDir,
                company_id: this.companyId,
                task_category_id: this.searchCategory ?? undefined,
                timezone: this.selectedTimezone,
                date: this.filterDateTime ? dayjs(this.filterDateTime).format('YYYY-MM-DD') : null,
                perPage: this.perPage,
            };
        },
        getTaskCategories() {
            this.taskManagementApi.getTaskCategories().then(resp => this.taskCategories = resp.data.data.categories);
        },
        rescheduleOptionSelected(option) {
            if (option.id === 'custom_date_time') {
                this.showDatePickerModal = true;
                this.showReschedule = false;
            }
            this.rescheduleOption = option;
        },

        rescheduleTaskCustomDateTime(date) {
            this.customRescheduleDateTime = date.toISOString();
            this.showDatePickerModal = false;
            this.showReschedule = true;
        },
        async muteTask(actionId) {
            try {
                await this.apiService.muteTasksByActionId(actionId);
                this.getTasksOverview();
                await this.getTasks();
            } catch (e) {
                console.error(e);
            }
        },
        async unmuteTask(actionId) {
            try {
                await this.apiService.unmuteTaskByActionId(actionId);
                this.getTasksOverview();
                await this.getTasks();
            } catch (e) {
                console.error(e);
            }
        },
    }
}
</script>

<style scoped>

</style>
