<template xmlns="http://www.w3.org/1999/html">
    <alerts-container v-if="alertActive" :text="alertText" :alert-type="alertType" :dark-mode="darkMode"/>
    <Modal
        :small="true"
        :confirm-text="confirmText"
        :close-text="'Cancel'"
        :dark-mode="darkMode"
        @close="handleModalClosure(true, false)"
        @confirm="createTestLead(true, true)"
        :disable-confirm="disableCreateTestLeadAction"
    >
        <template v-slot:header>
            <h4 class="text-xl font-medium">Create Test Lead</h4>
        </template>
        <template v-slot:content>
            <div class="grid grid-cols-1 gap-5">
                <simple-alert v-if="showSimpleAlert" :dark-mode="darkMode" dismissible @dismiss="showSimpleAlert = false" content="Please note that test leads may take some time to appear in list." ></simple-alert>
                <div v-if="companyId === null">

                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Company
                    </p>
                    <autocomplete
                        :dark-mode="darkMode"
                        class="col-span-2"
                        v-model="selectedCompanyId"
                        :value="selectedCompanyId"
                        :options="companyOptions"
                        placeholder="Search by Company Name or Id"
                        :create-user-input-option="true"
                        @search="searchCompanies($event)">
                    </autocomplete>
                </div>
                <div v-if="selectedCompany && !loading && campaigns?.length === 0">
                    <p>No active campaigns were found for the selected company</p>
                </div>
                <div v-else-if="campaigns.length > 0">
                    <span class="block mb-1 text-sm font-medium">Campaign</span>
                    <Dropdown
                        v-model="selectedDropdownCampaignId"
                        :dark-mode="darkMode"
                        :options="campaigns"
                        :selected="campaigns" placeholder="select a campaign"
                        :class="{'opacity-50 pointer-events-none': campaigns.length === 0}"
                    />
                </div>
            </div>
            <div v-if="saving || loading" class="my-5">
                <loading-spinner :dark-mode="darkMode" />
            </div>
        </template>
        <template v-slot:buttons>
                <button @click="createTestLead(false, true)"
                        class="transition duration-200 text-white font-medium focus:outline-none py-2 rounded-md px-5 flex gap-1 bg-primary-500 hover:bg-blue-500"
                        :class="{'opacity-50 pointer-events-none cursor-not-allowed': disableCreateTestLeadAction}"
                >
                    Create and add new
                </button>
        </template>
    </Modal>
</template>

<script>
import AlertsContainer from "../components/AlertsContainer.vue";
import MultiSelect from "../components/MultiSelect.vue";
import Modal from "../components/Modal.vue";
import SharedApiService from "../services/api";
import AlertsMixin from "../../../mixins/alerts-mixin";
import Dropdown from "../components/Dropdown.vue";
import DropdownSelector from "../components/DropdownSelector.vue";
import CustomButton from "../components/CustomButton.vue";
import Autocomplete from "../components/Autocomplete.vue";
import CompanyApi from "../../Companies/services/api";
import LoadingSpinner from "../components/LoadingSpinner.vue";
import SimpleAlert from "../components/SimpleAlert.vue";
import ToggleSwitch from "../components/ToggleSwitch.vue";
import Campaigns from "../../Companies/Campaigns.vue";

export default {
    name: "AddTestLead",
    components: {
        Campaigns,
        ToggleSwitch,
        SimpleAlert,
        LoadingSpinner,
        Autocomplete,
        CustomButton,
        DropdownSelector,
        Dropdown,
        MultiSelect,
        AlertsContainer,
        Modal,
    },
    mixins: [AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null
        }
    },
    emits: ['close-add-test-lead-module'],
    data() {
        return {
            sharedApi: SharedApiService.make(),
            companyApi: CompanyApi.make(),
            confirmText: 'Create',
            selectedCompany: null,
            companyOptions: [],
            selectedCompanyId: null,
            campaigns: [],
            selectedDropdownCampaignId: null,
            saving: false,
            loading: false,
            showSimpleAlert: true,
        }
    },
    beforeMount() {
        if (this.companyId !== null) {
            this.selectedCompanyId = this.companyId;
            this.getCampaignsForCompany();
        }
    },
    computed: {
        disableCreateTestLeadAction(){
            return !this.selectedDropdownCampaignId || this.saving
        }
    },
    methods: {
        createTestLead(closeModal, createdLead) {
            this.saving = true;
            const campaign = this.campaigns.find(c => c.id === this.selectedDropdownCampaignId);

            if (!campaign) return;

            this.companyApi.createTestLead(this.selectedCompanyId, campaign._id, campaign.payload.source).then(resp => {
                if (resp.data.data.status === true) {
                    this.showAlert('success', 'A test lead is being created and will soon be available');
                    //if we are not on the company page
                    if(this.companyId === null)
                        this.clearCompanyAndCampaignData()

                    this.selectedDropdownCampaignId = null;

                    if(closeModal)
                        this.handleModalClosure(closeModal, createdLead)
                } else {
                    this.showAlert('error', this.message.error);
                }
            }).catch(e => this.showAlert('error', e?.response?.data?.message || this.message.error)).finally(() => this.saving = false);
        },
        handleModalClosure(closeModal, createdLead) {
            this.$emit('close-add-test-lead-module', closeModal, createdLead);
        },
        searchCompanies(query) {
            this.loading = true

            this.sharedApi.searchCompanyNamesAndId(query).then(res => {
                if (res.data.data.status === true) {
                    this.companyOptions = res.data.data.companies;
                }
            }).catch(e => this.showAlert('error', e?.response?.data?.message || this.message.error))
                .finally(() => {
                    this.loading = false
                })
        },
        getCampaignsForCompany() {
            this.loading = true
            this.campaigns = [];
            this.selectedDropdownCampaignId = null;
            this.companyApi.getAllCampaignsForSelect(this.selectedCompanyId,{
                product: 'lead',
                source: 'all',
                status: 1
            })
                .then(resp => {
                    this.campaigns = this.formatCampaigns(resp?.data?.data?.campaigns ?? [])
                }).catch(e => this.showAlert('error', e?.response?.data?.message || this.message.error))
                .finally(() => {
                    this.loading = false
                })
        },
        clearCompanyAndCampaignData() {
            this.selectedCompany = null;
            this.selectedCompanyId = null;
            this.companyOptions = [];
            this.campaigns = [];
        },

        formatCampaigns(campaigns) {
            return campaigns.map(c => {
                return {
                    ...c,
                    _id: c.id,
                    id: crypto.randomUUID(),
                    name: `${c.name}`
                }
            })
        }
    },
    watch: {
        selectedCompanyId: function () {
            const company = this.companyOptions.find(company => company.id === this.selectedCompanyId);
            if (company) {
                this.selectedCompany = {...company};
                this.getCampaignsForCompany()
            } else {
                this.selectedCompany = null;
                this.campaigns = [];
            }
        }
    },
}
</script>

