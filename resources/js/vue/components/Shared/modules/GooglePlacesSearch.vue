<template>
    <div class="border rounded-md" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <div class="px-5 pt-5 pb-1">
            <div class="flex items-center justify-between mb-3">
                <h5 class="text-blue-550 text-sm uppercase font-semibold leading-tight mr-4">
                    Google Places Search</h5>
                <div class="hidden inline-flex items-center cursor-pointer"
                     :class="[darkMode ? 'text-slate-100 hover:text-white' : 'text-slate-700 hover:text-slate-900']">
                    <svg class="mr-1 fill-current w-4" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M11 1.83333C11 2.17084 10.7264 2.44444 10.3889 2.44444L4.78404 2.44444C4.69374 2.69983 4.54711 2.9345 4.35192 3.1297C4.0081 3.47351 3.54179 3.66667 3.05556 3.66667C2.56933 3.66667 2.10301 3.47351 1.75919 3.1297C1.564 2.9345 1.41737 2.69983 1.32707 2.44444L0.611112 2.44444C0.273605 2.44444 1.0972e-06 2.17084 1.11196e-06 1.83333C1.12671e-06 1.49583 0.273605 1.22222 0.611112 1.22222L1.32707 1.22222C1.41737 0.966836 1.564 0.732162 1.75919 0.53697C2.10301 0.193154 2.56933 -3.68516e-07 3.05556 -3.47263e-07C3.54179 -3.26009e-07 4.0081 0.193154 4.35192 0.536971C4.54711 0.732162 4.69374 0.966837 4.78404 1.22222L10.3889 1.22222C10.7264 1.22222 11 1.49583 11 1.83333ZM11 5.5C11 5.83751 10.7264 6.11111 10.3889 6.11111L9.67293 6.11111C9.58263 6.3665 9.436 6.60117 9.24081 6.79636C8.89699 7.14018 8.43067 7.33333 7.94444 7.33333C7.45821 7.33333 6.9919 7.14018 6.64808 6.79636C6.45289 6.60117 6.30626 6.3665 6.21596 6.11111L0.611112 6.11111C0.273605 6.11111 9.36927e-07 5.83751 9.5168e-07 5.5C9.66433e-07 5.16249 0.273605 4.88889 0.611112 4.88889L6.21596 4.88889C6.30626 4.6335 6.45289 4.39883 6.64808 4.20364C6.9919 3.85982 7.45821 3.66667 7.94444 3.66667C8.43068 3.66667 8.89699 3.85982 9.24081 4.20364C9.436 4.39883 9.58263 4.6335 9.67293 4.88889L10.3889 4.88889C10.7264 4.88889 11 5.16249 11 5.5ZM11 9.16667C11 9.50418 10.7264 9.77778 10.3889 9.77778L4.78404 9.77778C4.69374 10.0332 4.54711 10.2678 4.35192 10.463C4.0081 10.8068 3.54179 11 3.05556 11C2.56933 11 2.10301 10.8068 1.75919 10.463C1.564 10.2678 1.41737 10.0332 1.32707 9.77778L0.611112 9.77778C0.273605 9.77778 7.76652e-07 9.50417 7.91405e-07 9.16667C8.06158e-07 8.82916 0.273605 8.55556 0.611112 8.55556L1.32707 8.55556C1.41737 8.30017 1.564 8.0655 1.75919 7.8703C2.10301 7.52649 2.56933 7.33333 3.05556 7.33333C3.54179 7.33333 4.0081 7.52649 4.35192 7.8703C4.54711 8.0655 4.69374 8.30017 4.78404 8.55556L10.3889 8.55556C10.7264 8.55556 11 8.82916 11 9.16667ZM8.55556 5.5C8.55556 5.33792 8.49117 5.18249 8.37657 5.06788C8.26196 4.95327 8.10652 4.88889 7.94444 4.88889C7.78237 4.88889 7.62693 4.95327 7.51232 5.06788C7.39772 5.18249 7.33333 5.33792 7.33333 5.5C7.33333 5.66208 7.39772 5.81751 7.51232 5.93212C7.62693 6.04673 7.78237 6.11111 7.94444 6.11111C8.10652 6.11111 8.26196 6.04673 8.37657 5.93212C8.49117 5.81752 8.55556 5.66208 8.55556 5.5ZM3.66667 1.83333C3.66667 1.67126 3.60228 1.51582 3.48768 1.40121C3.37307 1.28661 3.21763 1.22222 3.05556 1.22222C2.89348 1.22222 2.73804 1.28661 2.62344 1.40121C2.50883 1.51582 2.44445 1.67126 2.44445 1.83333C2.44445 1.99541 2.50883 2.15085 2.62344 2.26545C2.73804 2.38006 2.89348 2.44444 3.05556 2.44444C3.21763 2.44444 3.37307 2.38006 3.48768 2.26545C3.60228 2.15085 3.66667 1.99541 3.66667 1.83333ZM3.66667 9.16667C3.66667 9.00459 3.60228 8.84915 3.48768 8.73455C3.37307 8.61994 3.21763 8.55556 3.05556 8.55556C2.89348 8.55556 2.73804 8.61994 2.62344 8.73455C2.50883 8.84915 2.44444 9.00459 2.44444 9.16667C2.44444 9.32874 2.50883 9.48418 2.62344 9.59879C2.73804 9.71339 2.89348 9.77778 3.05556 9.77778C3.21763 9.77778 3.37307 9.71339 3.48768 9.59879C3.60228 9.48418 3.66667 9.32874 3.66667 9.16667Z" fill="#0F172A"/>
                    </svg>
                    <p>Filters</p>
                </div>
            </div>
        </div>
        <div class="border-y h-[34rem] divide-y overflow-y-auto" :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background border-light-border divide-light-border']">
            <div v-for="result in results" :key="result" class="p-5 grid grid-cols-2" :class="[darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module']">
                <div>
                    <p class="font-semibold text-base leading-tight pb-1">{{ result.name }}</p>
                    <div>
                        <div class="flex items-center pb-1">
                            <p class="text-xs text-slate-500 font-medium">{{ result.rating }}</p>
                            <div class="inline-flex items-center relative mx-1">
                                <svg class="relative z-0 fill-current text-slate-400" width="77" height="12" viewBox="0 0 77 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M6.5 0L7.95934 4.49139H12.6819L8.86126 7.26722L10.3206 11.7586L6.5 8.98278L2.6794 11.7586L4.13874 7.26722L0.318133 4.49139H5.04066L6.5 0Z"/>
                                    <path d="M22.5 0L23.9593 4.49139H28.6819L24.8613 7.26722L26.3206 11.7586L22.5 8.98278L18.6794 11.7586L20.1387 7.26722L16.3181 4.49139H21.0407L22.5 0Z"/>
                                    <path d="M38.5 0L39.9593 4.49139H44.6819L40.8613 7.26722L42.3206 11.7586L38.5 8.98278L34.6794 11.7586L36.1387 7.26722L32.3181 4.49139H37.0407L38.5 0Z"/>
                                    <path d="M54.5 0L55.9593 4.49139H60.6819L56.8613 7.26722L58.3206 11.7586L54.5 8.98278L50.6794 11.7586L52.1387 7.26722L48.3181 4.49139H53.0407L54.5 0Z"/>
                                    <path d="M70.5 0L71.9593 4.49139H76.6819L72.8613 7.26722L74.3206 11.7586L70.5 8.98278L66.6794 11.7586L68.1387 7.26722L64.3181 4.49139H69.0407L70.5 0Z"/>
                                </svg>
                                <div class="absolute top-0 left-0 z-10 overflow-hidden" :style="{width: result.rating * 20 +'%'}">
                                    <svg class="fill-current text-amber-500" width="77" height="12" viewBox="0 0 77 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M6.5 0L7.95934 4.49139H12.6819L8.86126 7.26722L10.3206 11.7586L6.5 8.98278L2.6794 11.7586L4.13874 7.26722L0.318133 4.49139H5.04066L6.5 0Z"/>
                                        <path d="M22.5 0L23.9593 4.49139H28.6819L24.8613 7.26722L26.3206 11.7586L22.5 8.98278L18.6794 11.7586L20.1387 7.26722L16.3181 4.49139H21.0407L22.5 0Z"/>
                                        <path d="M38.5 0L39.9593 4.49139H44.6819L40.8613 7.26722L42.3206 11.7586L38.5 8.98278L34.6794 11.7586L36.1387 7.26722L32.3181 4.49139H37.0407L38.5 0Z"/>
                                        <path d="M54.5 0L55.9593 4.49139H60.6819L56.8613 7.26722L58.3206 11.7586L54.5 8.98278L50.6794 11.7586L52.1387 7.26722L48.3181 4.49139H53.0407L54.5 0Z"/>
                                        <path d="M70.5 0L71.9593 4.49139H76.6819L72.8613 7.26722L74.3206 11.7586L70.5 8.98278L66.6794 11.7586L68.1387 7.26722L64.3181 4.49139H69.0407L70.5 0Z"/>
                                    </svg>
                                </div>

                            </div>
                            <p class="text-xs text-slate-500 font-medium">({{ result.user_ratings_total }})</p>
                        </div>
                        <p class="text-xs text-slate-500 font-medium pb-1">{{ result.vicinity }}</p>
                        <div class="flex items-center">
                            <div v-for="index in result.opening_hours" :key="index">
                                <p class="text-xs text-emerald-500 font-medium">{{ index.open_now ?? 'Open' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-right text-sm">
                    <div class="flex items-center justify-end mb-1">
                        <p class="text-slate-500 font-medium">{{ result.company_status ? result.company_status : 'Not in system' }}</p>
                    </div>
                    <div class="flex items-center justify-end mb-1">
                        <p class="text-slate-500 font-medium">{{ result.account_manager ? result.account_manager : 'Unassigned' }}</p>
                    </div>
                    <div v-if="result.website" class="flex items-start justify-end mb-1">
                        <!-- todo: fix website -->
                        <a :href="'http:' + result.website" target="_blank" class="text-primary-500 font-medium mr-1">Website</a>
                        <svg class="w-2 mt-1" viewBox="0 0 7 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M4.00368 0C3.87124 0 3.74422 0.0526137 3.65056 0.146267C3.55691 0.23992 3.5043 0.366941 3.5043 0.499386C3.5043 0.631832 3.55691 0.758853 3.65056 0.852506C3.74422 0.946159 3.87124 0.998773 4.00368 0.998773H5.2951L2.15246 4.14141C2.10476 4.18748 2.06672 4.24258 2.04054 4.30351C2.01437 4.36444 2.0006 4.42997 2.00002 4.49627C1.99944 4.56258 2.01208 4.62834 2.03719 4.68971C2.0623 4.75109 2.09938 4.80684 2.14627 4.85373C2.19316 4.90062 2.24891 4.9377 2.31029 4.96281C2.37166 4.98792 2.43742 5.00056 2.50373 4.99998C2.57003 4.99941 2.63556 4.98563 2.69649 4.95946C2.75742 4.93328 2.81252 4.89524 2.85859 4.84754L6.00123 1.7049V2.99632C6.00123 3.12876 6.05384 3.25578 6.14749 3.34944C6.24115 3.44309 6.36817 3.4957 6.50061 3.4957C6.63306 3.4957 6.76008 3.44309 6.85373 3.34944C6.94739 3.25578 7 3.12876 7 2.99632V0.499386C7 0.366941 6.94739 0.23992 6.85373 0.146267C6.76008 0.0526137 6.63306 0 6.50061 0H4.00368Z" fill="#94A3B8"/>
                            <path d="M1 1C0.734784 1 0.48043 1.10536 0.292893 1.29289C0.105357 1.48043 0 1.73478 0 2V6C0 6.26522 0.105357 6.51957 0.292893 6.70711C0.48043 6.89464 0.734784 7 1 7H5C5.26522 7 5.51957 6.89464 5.70711 6.70711C5.89464 6.51957 6 6.26522 6 6V4.5C6 4.36739 5.94732 4.24021 5.85355 4.14645C5.75979 4.05268 5.63261 4 5.5 4C5.36739 4 5.24021 4.05268 5.14645 4.14645C5.05268 4.24021 5 4.36739 5 4.5V6H1V2H2.5C2.63261 2 2.75979 1.94732 2.85355 1.85355C2.94732 1.75979 3 1.63261 3 1.5C3 1.36739 2.94732 1.24021 2.85355 1.14645C2.75979 1.05268 2.63261 1 2.5 1H1Z" fill="#94A3B8"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Dropdown from "../../Shared/components/Dropdown.vue";
import Api from "../../Companies/services/api";
export default {
    name: "GooglePlacesSearch",
    components: {Dropdown},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        zipCode: {
            type: String,
            default: null
        },
        location: {
            type: Object,
            default: null
        },
        industry: {
            type: String,
            default: null
        }
    },

    data() {
        return {
            results : [],
            api: Api.make(),
            loading: true
        }
    },
    mounted() {
        this.getCompanies();
    },
    watch: {
        zipCode(newVal, oldVal) {
            if (newVal !== oldVal) {
                this.getCompanies();
            }
        },
        location(newVal, oldVal) {
            if (newVal !== oldVal) {
                this.getCompanies();
            }
        },
        industry(newVal, oldVal) {
            if(newVal !== oldVal) {
                this.getCompanies();
            }
        }
    },
    methods: {
        getCompanies() {
            if(this.location.county_key && this.location.state_key) {
                this.api.getGooglePlacesCompaniesByCounty(this.location.county_key, this.location.state_key, this.industry).then(resp => {
                    console.log(resp.data.data);
                    if(resp.data.data !== null){
                        this.results = resp.data.data.companies;
                    }
                }).catch(e => console.log(e));
            } else if (typeof this.zipCode === 'string' && this.zipCode?.length && this.zipCode.length === 5) {
                this.api.getGooglePlacesCompaniesByZipCode(this.zipCode, this.industry).then(resp => {
                    console.log(resp.data.data);
                    if(resp.data.data !== null){
                        this.results = resp.data.data.companies;
                    }
                }).catch(e => console.log(e));
            }
        }
    }
}
</script>

<style scoped>

</style>
