<template>
    <div>
        <div v-if="loading">
            <loading-spinner/>
        </div>
        <div v-else-if="phoneData">

            <div class="grid grid-cols-1">
                <div v-if="transactionData" class="border-b py-4"
                     :class="{'border-light-border' : !darkMode, 'border-dark-border' : darkMode}">
                    <div class="flex items-center flex-wrap px-5">
                        <div class="flex items-center flex-wrap gap-2">
                            <h5 class="text-sm font-medium mr-2"
                                :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}">Matches:</h5>
                            <p v-if="transactionData.transaction_details.email_name_identity_match === 'Match'" :class="[darkMode ? '' : 'bg-green-150']" class="text-green-550 text-xs inline-flex mr-3 px-4 items-center rounded-full py-1 font-medium whitespace-no-wrap">Name-Email</p>
                            <p v-if="transactionData.transaction_details.name_address_identity_match === 'Match'" :class="[darkMode ? '' : 'bg-green-150']" class="text-green-550 text-xs inline-flex mr-3 px-4 items-center rounded-full py-1 font-medium whitespace-no-wrap">Name-Address</p>
                            <p v-if="transactionData.transaction_details.phone_name_identity_match === 'Match'" :class="[darkMode ? '' : 'bg-green-150']" class="text-green-550 text-xs inline-flex mr-3 px-4 items-center rounded-full py-1 font-medium whitespace-no-wrap">Name-Phone</p>
                            <p v-if="transactionData.transaction_details.phone_address_identity_match === 'Match'" :class="[darkMode ? '' : 'bg-green-150']" class="text-green-550 text-xs inline-flex mr-3 px-4 items-center rounded-full py-1 font-medium whitespace-no-wrap">Phone-Address</p>
                            <p v-if="transactionData.transaction_details.phone_email_identity_match === 'Match'" :class="[darkMode ? '' : 'bg-green-150']" class="text-green-550 text-xs inline-flex mr-3 px-4 items-center rounded-full py-1 font-medium whitespace-no-wrap">Phone-Email</p>
                            <!-- If No Matches -->
                            <p v-if="transactionData.transaction_details.email_name_identity_match !== 'Match' && transactionData.transaction_details.name_address_identity_match !== 'Match' && transactionData.transaction_details.phone_name_identity_match !== 'Match' && transactionData.transaction_details.phone_address_identity_match !== 'Match' && transactionData.transaction_details.phone_email_identity_match !== 'Match'" :class="[darkMode ? '' : 'bg-red-200']" class="text-red-500 text-xs inline-flex mr-3 px-4 items-center rounded-full py-1 font-medium whitespace-no-wrap">No Matches</p>
                        </div>
                    </div>
                </div>
                <div v-if="phoneData" class="border-b py-4"
                     :class="{'border-light-border' : !darkMode, 'border-dark-border' : darkMode}">
                    <div class="grid gap-2 px-5 text-sm mb-2">
                        <div>
                            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">Name:</p>
                            <span class="font-medium" :class="{'text-slate-900' : !darkMode, 'text-slate-50' : darkMode}">
                                {{phoneData?.name ?? "Unknown"}}
                            </span>
                        </div>
                        <div>
                            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">Status:</p>
                            <span class="font-medium" :class="{'text-slate-900' : !darkMode, 'text-slate-50' : darkMode}">
                                {{phoneData?.active_status ?? "Unknown"}}
                            </span>
                        </div>
                        <div>
                            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">Registered Location:</p>
                            <span class="font-medium" :class="{'text-slate-900' : !darkMode, 'text-slate-50' : darkMode}">
                                {{phoneData?.city ?? "?"}}, {{phoneData?.region ?? "?"}}, {{phoneData?.zip_code ?? "?"}}
                            </span>
                        </div>
                    </div>
                    <div class="flex items-center flex-wrap px-5 mb-2">
                        <div class="">
                            <p class="text-sm font-medium mr-2 mb-1"
                                :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">Phone:</p>
                            <div class="flex items-center flex-wrap gap-2">
                                <badge v-if="phoneData.valid === true" color="green">
                                    <svg class="inline mr-1" width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M6.52295 2.57521C5.35758 2.57521 4.23994 3.03815 3.4159 3.86219C2.59186 4.68623 2.12892 5.80387 2.12892 6.96924C2.12892 7.54627 2.24257 8.11765 2.46339 8.65076C2.68421 9.18387 3.00788 9.66826 3.4159 10.0763C3.82392 10.4843 4.30832 10.808 4.84143 11.0288C5.37453 11.2496 5.94592 11.3633 6.52295 11.3633C7.09998 11.3633 7.67136 11.2496 8.20447 11.0288C8.73758 10.808 9.22197 10.4843 9.63 10.0763C10.038 9.66826 10.3617 9.18387 10.5825 8.65076C10.8033 8.11765 10.917 7.54627 10.917 6.96924C10.917 5.80387 10.454 4.68623 9.63 3.86219C8.80596 3.03815 7.68832 2.57521 6.52295 2.57521ZM2.18605 2.63234C3.33627 1.48212 4.8963 0.835938 6.52295 0.835938C8.1496 0.835938 9.70963 1.48212 10.8598 2.63234C12.0101 3.78256 12.6562 5.34259 12.6562 6.96924C12.6562 7.77467 12.4976 8.57222 12.1894 9.31635C11.8812 10.0605 11.4294 10.7366 10.8598 11.3061C10.2903 11.8757 9.61419 12.3274 8.87006 12.6357C8.12594 12.9439 7.32839 13.1025 6.52295 13.1025C5.71751 13.1025 4.91996 12.9439 4.17584 12.6357C3.43171 12.3274 2.75558 11.8757 2.18605 11.3061C1.61652 10.7366 1.16475 10.0605 0.856518 9.31635C0.548291 8.57222 0.389648 7.77467 0.389648 6.96924C0.389648 5.34259 1.03583 3.78256 2.18605 2.63234ZM8.89243 5.18461C9.23204 5.52422 9.23204 6.07485 8.89243 6.41446L6.55302 8.75387C6.21341 9.09348 5.66279 9.09348 5.32317 8.75387L4.15347 7.58416C3.81386 7.24455 3.81386 6.69393 4.15347 6.35431C4.49308 6.0147 5.0437 6.0147 5.38332 6.35431L5.9381 6.90909L7.66258 5.18461C8.00219 4.845 8.55282 4.845 8.89243 5.18461Z" fill="currentColor"/>
                                    </svg>
                                    Valid
                                </badge>
                                <badge v-else color="red">
                                    <svg class="mr-1 inline" width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M2.72028 2.76911C3.79175 1.69765 5.24497 1.0957 6.76025 1.0957C8.27554 1.0957 9.72876 1.69765 10.8002 2.76911C11.8717 3.84058 12.4736 5.2938 12.4736 6.80908C12.4736 8.32436 11.8717 9.77758 10.8002 10.8491C9.72876 11.9205 8.27554 12.5225 6.76025 12.5225C5.24497 12.5225 3.79175 11.9205 2.72028 10.8491C1.64882 9.77758 1.04688 8.32436 1.04688 6.80908C1.04688 5.2938 1.64882 3.84058 2.72028 2.76911ZM3.95761 3.19844L10.3709 9.61173C10.9899 8.81431 11.331 7.82928 11.331 6.80908C11.331 5.59685 10.8494 4.43428 9.99223 3.5771C9.13506 2.71993 7.97248 2.23837 6.76025 2.23837C5.74006 2.23837 4.75502 2.57945 3.95761 3.19844ZM9.5629 10.4197L3.14962 4.00643C2.53062 4.80385 2.18954 5.78888 2.18954 6.80908C2.18954 8.02131 2.6711 9.18389 3.52827 10.0411C4.38545 10.8982 5.54803 11.3798 6.76025 11.3798C7.78045 11.3798 8.76548 11.0387 9.5629 10.4197Z" fill="currentColor"/>
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M2.36673 2.41556C3.53197 1.25033 5.11236 0.595703 6.76025 0.595703C8.40814 0.595703 9.98854 1.25033 11.1538 2.41556C12.319 3.58079 12.9736 5.16119 12.9736 6.80908C12.9736 8.45697 12.319 10.0374 11.1538 11.2026C9.98854 12.3678 8.40814 13.0225 6.76025 13.0225C5.11236 13.0225 3.53197 12.3678 2.36673 11.2026C1.2015 10.0374 0.546875 8.45697 0.546875 6.80908C0.546875 5.16119 1.2015 3.58079 2.36673 2.41556ZM6.76025 1.5957C5.37758 1.5957 4.05154 2.14497 3.07384 3.12267C2.09614 4.10036 1.54688 5.42641 1.54688 6.80908C1.54688 8.19176 2.09614 9.5178 3.07384 10.4955C4.05154 11.4732 5.37758 12.0225 6.76025 12.0225C8.14293 12.0225 9.46897 11.4732 10.4467 10.4955C11.4244 9.5178 11.9736 8.19176 11.9736 6.80908C11.9736 5.42641 11.4244 4.10036 10.4467 3.12267C9.46897 2.14497 8.14293 1.5957 6.76025 1.5957ZM4.74084 3.27457L10.2948 8.82849C10.6431 8.21874 10.831 7.52359 10.831 6.80908C10.831 5.72946 10.4021 4.69406 9.63868 3.93066C8.87527 3.16725 7.83987 2.73837 6.76025 2.73837C6.04575 2.73837 5.3506 2.92622 4.74084 3.27457ZM3.65101 2.80348C4.53566 2.11676 5.62844 1.73837 6.76025 1.73837C8.10509 1.73837 9.39484 2.27261 10.3458 3.22355C11.2967 4.17449 11.831 5.46425 11.831 6.80908C11.831 7.9409 11.4526 9.03367 10.7659 9.91833C10.6779 10.0317 10.5454 10.1018 10.4022 10.1107C10.259 10.1197 10.1188 10.0668 10.0173 9.96528L3.60405 3.552C3.50258 3.45053 3.4496 3.31036 3.45859 3.16714C3.46757 3.02392 3.53766 2.89147 3.65101 2.80348ZM3.11831 3.50741C3.26153 3.49843 3.4017 3.55141 3.50317 3.65288L9.91646 10.0662C10.0179 10.1676 10.0709 10.3078 10.0619 10.451C10.0529 10.5942 9.98285 10.7267 9.8695 10.8147C8.98485 11.5014 7.89207 11.8798 6.76025 11.8798C5.41542 11.8798 4.12566 11.3456 3.17472 10.3946C2.22378 9.44367 1.68954 8.15392 1.68954 6.80908C1.68954 5.67727 2.06793 4.58449 2.75465 3.69984C2.84264 3.58648 2.97509 3.5164 3.11831 3.50741ZM3.22575 4.78967C2.87739 5.39942 2.68954 6.09458 2.68954 6.80908C2.68954 7.8887 3.11842 8.9241 3.88183 9.68751C4.64523 10.4509 5.68063 10.8798 6.76025 10.8798C7.47476 10.8798 8.16991 10.6919 8.77966 10.3436L3.22575 4.78967Z" fill="currentColor"/>
                                    </svg>
                                    Invalid
                                </badge>
                                <badge color="primary">
                                    <svg class="inline mr-1" width="13" height="10" viewBox="0 0 13 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0.126953 1.00977C0.126953 0.457481 0.574668 0.00976562 1.12695 0.00976562H11.7676C12.3199 0.00976562 12.7676 0.457481 12.7676 1.00977C12.7676 1.56205 12.3199 2.00977 11.7676 2.00977H1.12695C0.574668 2.00977 0.126953 1.56205 0.126953 1.00977ZM0.126953 5C0.126953 4.44772 0.574668 4 1.12695 4H11.7676C12.3199 4 12.7676 4.44772 12.7676 5C12.7676 5.55228 12.3199 6 11.7676 6H1.12695C0.574668 6 0.126953 5.55228 0.126953 5ZM0.126953 8.99023C0.126953 8.43795 0.574668 7.99023 1.12695 7.99023H11.7676C12.3199 7.99023 12.7676 8.43795 12.7676 8.99023C12.7676 9.54252 12.3199 9.99023 11.7676 9.99023H1.12695C0.574668 9.99023 0.126953 9.54252 0.126953 8.99023Z" fill="currentColor"/>
                                    </svg>
                                    Line Type: {{ phoneData?.line_type }}
                                </badge>
                                <badge v-if="phoneData.prepaid" color="red">
                                    <svg class="inline mr-1" width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M10.2796 12.243C9.15433 13.3683 7.62807 14.0005 6.03664 14.0005C4.44521 14.0005 2.91895 13.3683 1.79364 12.243C0.668327 11.1177 0.0361328 9.59143 0.0361328 8C0.0361328 6.40857 0.668327 4.88231 1.79364 3.757C1.79364 6 2.03664 8 5.03664 9C5.03664 7 6.03664 1 7.53664 0C8.53664 2 9.10764 2.586 10.2786 3.757C10.8368 4.31352 11.2794 4.97487 11.5811 5.70303C11.8828 6.43119 12.0376 7.21181 12.0366 8C12.0377 8.78811 11.8831 9.56867 11.5815 10.2968C11.28 11.025 10.8376 11.6864 10.2796 12.243Z" fill="currentColor"/>
                                    </svg>
                                    Prepaid
                                </badge>
                            </div>
                        </div>
                    </div>
                    <div class="px-5">
                        <div v-if="(phoneData.associated_email_addresses.emails).length > 0"
                             :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">
                            <div class="text-sm mr-2">
                                Associated Emails:
                            </div>
                            <div class="text-sm font-medium flex items-center flex-wrap" :class="{'text-slate-900' : !darkMode, 'text-slate-50' : darkMode}"
                                 :title="phoneData.associated_email_addresses.emails.join(', ')">
                                {{ phoneData.associated_email_addresses.emails.join(', ') }}
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="emailData" class="border-b py-4"
                     :class="{'border-light-border' : !darkMode, 'border-dark-border' : darkMode}">
                    <div class="flex items-center flex-wrap px-5 mb-2">
                        <div class="flex items-center flex-wrap mr-3 gap-2">
                            <h5 class="text-sm font-medium mr-2"
                                :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}">Email:</h5>
                            <div class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                                 :class="[darkMode ? (emailData.deliverability === 'high' ? 'text-green-550' : emailData.deliverability === 'medium' ? 'text-amber-500' : 'text-red-350') : (emailData.deliverability === 'high' ? 'text-green-550 bg-green-150' : emailData.deliverability === 'medium' ? 'text-amber-700 bg-amber-100' : 'text-red-350 bg-red-75')]"
                            >
                                <p v-if="emailData.deliverability === 'high'" class="text-xs">High Deliverability</p>
                                <p v-else-if="emailData.deliverability === 'medium'" class="text-xs">Medium
                                    Deliverability</p>
                                <p v-else class="text-xs">Low Deliverability</p>
                            </div>
                        </div>
                        <div class="flex items-center flex-wrap mr-3 gap-2">
                            <div class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                                 :class="[darkMode ? (emailData.frequent_complainer === true ? 'text-green-550' : 'text-red-350') : (emailData.frequent_complainer === true ? 'text-green-550 bg-green-150' : 'text-red-350 bg-red-75')]"
                            >
                                <p v-if="emailData.frequent_complainer === true" class="text-xs">No Complaints</p>
                                <p v-else class="text-xs">Has Complaints</p>
                            </div>
                        </div>

                    </div>
                    <div class="grid grid-cols-2 gap-2 px-5">
                        <div class="flex items-center" :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">
                            <div class="text-sm">
                               Created: {{ emailData.first_seen.human }}
                            </div>
                        </div>
                        <div v-if="(emailData.associated_names.names).length > 0" class="flex items-center flex-wrap col-span-2"
                             :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">
                            <div class="text-sm mr-2">
                                Associated Names:
                            </div>
                            <div class="text-sm" :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}"
                                 :title="emailData.associated_names.names.join(', ')">
                                {{ emailData.associated_names.names.join(', ') }}
                            </div>
                        </div>

                        <div v-if="(emailData.associated_phone_numbers.phone_numbers).length > 0" class="flex items-center flex-wrap col-span-2"
                             :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">
                            <div class="text-sm mr-2">
                                Associated Phones:
                            </div>
                            <div class="text-sm" :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}"
                                 :title="emailData.associated_phone_numbers.phone_numbers.join(', ')">
                                {{ emailData.associated_phone_numbers.phone_numbers.join(', ') }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="transactionData" class="py-4 px-5">
                <div class="flex items-center justify-between mb-2">
                    <div class="flex items-center flex-wrap mr-3 gap-2">
                        <h5 class="text-sm font-medium mr-2"
                            :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}">Transaction Data:</h5>
                        <div class="flex items-center flex-wrap mr-3 gap-2">
                            <div class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                                 :class="[darkMode ? (transactionData.recent_abuse === false ? 'text-green-550' : 'text-red-350') : (transactionData.recent_abuse === false ? 'text-green-550 bg-green-150' : 'text-red-350 bg-red-75')]"
                            >
                                <p v-if="transactionData.recent_abuse === true" class="text-xs">Has Recent Abuse</p>
                                <p v-else class="text-xs">No Recent Abuse</p>
                            </div>
                        </div>
                    </div>
                    <div class="font-medium text-sm flex"
                         :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}">
                        Fraud Score: {{ transactionData.fraud_score }}
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-2">
                    <div class="col-span-2 flex items-center flex-wrap" :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">
                        <div class="text-sm mr-2">
                            Risk Factors:
                        </div>
                        <div class="text-sm text-red-350">
                            {{ transactionData.transaction_details.risk_factors.join(', ') || ''}}
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div v-else class="border-t border-l border-r h-auto max-h-48 overflow-y-auto"
            :class="{'bg-light-background border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
            <div class="px-5 py-2 text-s border-b"
                 :class="{'text-grey-800 border-light-border': !darkMode, 'text-grey-200 border-dark-border': darkMode}">
                {{ error }}
            </div>
        </div>
    </div>
</template>

<script>
    import LoadingSpinner from "../../components/LoadingSpinner.vue";
    import ApiService from "../../services/consumer_api";
    import Badge from "../../components/Badge.vue";

    export default {
        name: "IpQualityScoreVerification",
        components: {
            Badge,
            LoadingSpinner
        },
        props: {
            darkMode: {
                type: Boolean,
                default: false
            },
            consumerProductId: {
                type: Number,
                default: null
            },
        },
        data: function() {
            return {
                loading: true,
                phoneData: null,
                emailData: null,
                transactionData: null,
                api: ApiService.make(),
                error: "Problem retrieving IP Quality Score. Please try later!"
            };
        },
        created: function() {
            if(this.phoneData == null && this.consumerProductId !== null) {
                this.getConsumerProductVerification();
            }
        },
        watch: {
            consumerProductId(newVal, oldVal) {
                if(newVal !== oldVal) {
                    this.getConsumerProductVerification();
                }
            }
        },
        methods: {
            async getConsumerProductVerification() {
                this.api.getConsumerProductVerification(this.consumerProductId, 'ip_quality_score').then(res => {
                    if (res.data.data.status === true) {
                        this.phoneData = res.data.data.product_verification.phone;
                        this.emailData = null;
                        this.transactionData = null;
                    }
                }).catch(err => console.log(err)).finally(() => {
                    this.loading = false;
                });
            }
        }
    }
</script>
