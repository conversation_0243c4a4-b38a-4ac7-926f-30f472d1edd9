/**
 * Where the payload structure for A2 is not convenient for use in the Wizard slide, add a transformer here
 * These transforms are called when a slide is validated, and stored in futureCampaignStore
 * The wizardStore will retain the data structure used in the Slide
 */

export class WizardTransformerService {

    transformers = {}

    getTransformer(slideKey) {
        return this.transformers[slideKey] ?? null;
    }

    transformPayload(slideKey, inputKey, payload) {
        const transformer = this.getTransformer(slideKey);
        if (transformer) {
            return transformer(inputKey, payload);
        }
        else {
            return payload;
        }
    }
}
