<template>
    <div
        :id="id"
        :class="[darkMode ? 'bg-dark-module border-dark-border' + ' ' + parentClass : 'bg-light-module border-light-border' + ' ' + parentClass]">
        <div class="grid grid-cols-2 gap-2">
            <CustomButton @click="logicalSelection(AND)" class="justify-center uppercase"
                          :color="logical === AND ? 'primary' : 'primary-outline'" id="and">And
            </CustomButton>
            <CustomButton @click="logicalSelection(OR)" class="justify-center uppercase"
                          :color="logical === OR ? 'primary' : 'primary-outline'" id="or">Or
            </CustomButton>
        </div>
    </div>
</template>
<script setup>
import CustomButton from './CustomButton.vue'

const props = defineProps({
    logical: {
        type: String,
        default: null
    },
    darkMode: {
        type: Boolean,
        default: false
    },
    parentClass: {
        type: String,
        default: 'items-center gap-2 p-2 z-10 border-b w-full'
    },
    id: {
        type: String,
        default: 'logical-selector'
    }
})

const emit = defineEmits(['update:logical']);

const logicalSelection = (selection) => {
    if (selection === props.logical) {
        emit('update:logical', null)
    } else {
        emit('update:logical', selection)
    }
}
</script>

<script>
const AND = 'and';
const OR = 'or';

export default {
    name: "LogicalSelector",
}
</script>
