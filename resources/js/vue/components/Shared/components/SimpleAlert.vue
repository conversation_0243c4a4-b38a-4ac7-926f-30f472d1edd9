<template>
    <div class="flex items-center p-4 mb-4 rounded-lg gap-2" :class="getContainerClasses()">
        <slot v-if="!noIcon" name="icon" />
        <svg v-if="!$slots['icon'] && !noIcon" class="flex-shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
        </svg>
        <div class="ms-3 text-sm font-medium flex-1">
            <slot v-if="!content" name="content" />
            {{content}}
        </div>
        <button
            v-if="dismissible"
            @click="$emit('dismiss')"
            type="button"
            class="ms-auto -mx-1.5 -my-1.5 rounded-lg p-1.5 inline-flex items-center justify-center h-8 w-8 text-white"
            :class="getDismissButtonClasses()"
        >
            <slot name="dismiss-icon" />
            <svg v-if="!$slots['dismiss-icon']" class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
        </button>
    </div>
</template>

<script>
export const SIMPLE_ALERT_VARIANTS = {
    LIGHT_RED: 'light-red',
    LIGHT_BLUE: 'light-blue',
    LIGHT_GREEN: 'light-green',
    LIGHT_YELLOW: 'light-yellow',
}
export default {
    name: "SimpleAlert",
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        variant: {
            type: String,
            default: SIMPLE_ALERT_VARIANTS.LIGHT_BLUE
        },
        content: {
            type: String
        },
        dismissible: {
            type: Boolean,
            default: false
        },
        noIcon: {
            type: Boolean,
            default: false
        },
        dismissInSeconds: {
            type: Number,
            default: null,
            required: false
        }
    },
    created() {
        if (this.dismissInSeconds > 0) {
            setTimeout(() => {
                this.$emit('dismiss')
            }, this.dismissInSeconds * 1000)
        }
    },
    methods: {
        getContainerClasses(){
            return {
                [SIMPLE_ALERT_VARIANTS.LIGHT_RED]: 'bg-red-100 text-red-800',
                [SIMPLE_ALERT_VARIANTS.LIGHT_BLUE]: 'bg-blue-100 text-blue-800',
                [SIMPLE_ALERT_VARIANTS.LIGHT_GREEN]: 'bg-green-100 text-green-800',
                [SIMPLE_ALERT_VARIANTS.LIGHT_YELLOW]: 'bg-yellow-100 text-yellow-800',
            }[this.variant] || ''
        },

        getDismissButtonClasses(){
            return {
                [SIMPLE_ALERT_VARIANTS.LIGHT_RED]: 'text-red-800',
                [SIMPLE_ALERT_VARIANTS.LIGHT_BLUE]: 'text-blue-800',
                [SIMPLE_ALERT_VARIANTS.LIGHT_GREEN]: 'text-green-800',
                [SIMPLE_ALERT_VARIANTS.LIGHT_YELLOW]: 'text-yellow-800',
            }[this.variant] || ''
        },
    }
}
</script>

<style scoped>

</style>
