<template>
    <div class="border-t border-b overflow-y-auto divide-y" :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border']">
        <div class="w-full h-full flex justify-center">
            <loading-spinner></loading-spinner>
        </div>
    </div>
</template>

<script>
import LoadingSpinner from "../../LoadingSpinner.vue";

export default {
    name: "SimpleTableLoadingItems",
    components: { LoadingSpinner },
    props: {
        darkMode: {
            type: Boolean,
            required: true
        }
    }
}
</script>
