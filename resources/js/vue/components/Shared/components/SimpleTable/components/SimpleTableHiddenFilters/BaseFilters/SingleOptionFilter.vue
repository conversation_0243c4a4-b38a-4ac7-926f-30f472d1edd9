<template>
    <base-filter @apply="$emit('apply')" @cancel="$emit('cancel')" :dark-mode="darkMode">
        <p v-for="(option) in filter.options">
            <filter-option
                :option="option"
                @select="handleSelect"
                :is-selected="modelValue === option.id"
                :dark-mode="darkMode"
            />
        </p>
    </base-filter>
</template>

<script>
import CustomButton from "../../../../CustomButton.vue";
import FilterOption from "../FilterOption.vue";
import BaseFilter from "./BaseFilter.vue";

export default {
    name: "SingleOptionFilter",

    components: {
        BaseFilter,
        CustomButton,
        FilterOption
    },

    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        filter: {
            type: Object,
            required: true,
        },
        modelValue: {
            type: [String, Number],
            default: ''
        },
    },
    emits: ['update:modelValue', 'apply', 'cancel'],

    methods: {
        handleSelect(selected) {
            const value = this.modelValue === selected.optionId ? '' : selected.optionId;
            this.$emit('update:modelValue', value);
        }
    }
}
</script>
