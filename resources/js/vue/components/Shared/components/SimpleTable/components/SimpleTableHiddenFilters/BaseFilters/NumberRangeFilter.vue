<template>
    <base-filter @apply="$emit('apply')" @cancel="$emit('cancel')" :dark-mode="darkMode">
        <div class="p-2 gap-2 flex flex-col">
            <div>
                <p class="mb-1">Minimum: </p>
                <custom-input
                    :dark-mode="darkMode"
                    type="number"
                    v-model="localMin"
                    @update:modelValue="handleUpdate"
                ></custom-input>
            </div>
            <div>
                <p class="mb-1">Maximum: </p>
                <custom-input
                    :dark-mode="darkMode"
                    type="number"
                    v-model="localMax"
                    @update:modelValue="handleUpdate"
                ></custom-input>
            </div>
        </div>
    </base-filter>
</template>

<script>
import FilterOption from "../FilterOption.vue";
import BaseFilter from "./BaseFilter.vue";
import CustomInput from "../../../../CustomInput.vue";

export default {
    name: "NumberRangeFilter",

    components: {
        CustomInput,
        BaseFilter,
        FilterOption,
    },

    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        filter: {
            type: Object,
            required: true,
        },
        modelValue: {
            type: Object,
            default: () => {}
        },
    },

    emits: ['update:modelValue', 'apply', 'cancel'],

    data(){
        return {
            localMin: null,
            localMax: null,
        }
    },

    beforeMount() {
        if (this.modelValue?.min) this.localMin = this.modelValue.min
        if (this.modelValue?.max) this.localMax = this.modelValue.max
    },

    methods: {
        handleUpdate(){
            this.$emit('update:modelValue', {
                min: this.localMin ?? null,
                max: this.localMax ?? null,
            })
        },
    }
}
</script>
