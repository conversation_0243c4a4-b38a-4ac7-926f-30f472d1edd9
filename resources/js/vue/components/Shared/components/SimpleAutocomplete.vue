<template>
    <labeled-value :label="label">
        <div v-click-outside="() => showOptions = false" class="relative">
            <div class="relative flex items-center">
                <div v-if="iconLeft" class="absolute left-3 z-10">
                    <simple-icon :icon="iconLeft.icon"/>
                </div>
                <input
                    type="text"
                    v-model="inputValue"
                    @input="handleInput"
                    :placeholder="computedPlaceholder"
                    :class="[computedInputStyle]"
                    @focus="showOptions = true"
                    :disabled="disabled"
                />
            </div>

            <ul
                v-if="showOptions"
                @scroll.prevent="handleScroll"
                class="rounded cursor-pointer border shadow-module max-h-52 overflow-auto absolute w-full z-10"
                :class="{'border-light-border bg-light-background': !darkMode, 'border-dark-border bg-dark-background': darkMode}"
            >
                <li v-if="loading" :class="[
                    listItemStyleClasses,
                    'cursor-default'
                ]">
                    {{ loadingMessage }}
                </li>

                <li v-else-if="!hasResults && !loading"
                    :class="[listItemStyleClasses, 'cursor-default']"
                >
                    {{ noResultsFoundMessage }}
                </li>

                <li v-for="(option, index) in filteredOptions" :key="index" @click.prevent="selectOption(option)"
                    :class="[
                        listItemStyleClasses,
                        getSelectedClass(option),
                        'hover:text-white hover:bg-primary-300'
                    ]
                    "
                >
                    <slot name="option" :option="option" :index="index">
                        {{ _getOptionDisplayString(option) }}
                    </slot>
                </li>

                <li v-if="infiniteScroll && fetchingNextPage && hasResults && inputValue.length > 0"
                    :class="[listItemStyleClasses, 'cursor-default']">
                    Loading more items...
                </li>
            </ul>
        </div>
    </labeled-value>
</template>

<script>
import LabeledValue from "./LabeledValue.vue"
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue"

export default {
    components: {SimpleIcon, LabeledValue},
    props: {
        label: {
            type: String,
            default: null
        },
        placeholder: {
            type: String,
            default: 'Search...'
        },
        modelValue: {},
        labelKey: {
            type: String,
            default: 'label',
        },
        getOptionDisplayString: {
            type: Function,
            default: null
        },
        iconLeft: {
            type: Object,
            default: null
        },
        variant: {
            type: String,
            default: 'normal'
        },
        darkMode: {
            type: Boolean,
            default: false
        },
        options: {
            type: Array,
            default: []
        },
        fetchOptions: {
            type: Function,
            default: null
        },
        disabled: {
            type: Boolean,
            default: false
        },
        noResultsFoundMessage: {
            type: String,
            default: 'No results found'
        },
        loadingMessage: {
            type: String,
            default: 'Loading...'
        },
        infiniteScroll: {
            type: Boolean,
            default: false
        },
        emitId: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            inputValue: '',
            filteredOptions: [],
            loading: false,
            page: 1,
            hasMore: true,
            showOptions: false,
            fetchingNextPage: false
        }
    },
    methods: {
        getSelectedClass(option) {
            const isSelected = JSON.stringify(option) === JSON.stringify(this.modelValue)

            return isSelected ? 'bg-primary-300 text-white' : ''
        },
        handleInput() {
            this.showOptions = true
            this.page = 1
            this.filteredOptions = []
            this.hasMore = true
            this._fetchOptions()
        },

        async _fetchOptions() {
            if (this.loading || !this.hasMore) return

            this.loading = true

            try {
                if (this.inputValue.length > 2 && this.fetchOptions) {
                    await this.fetchOptions({query: this.inputValue, page: this.page})
                } else if (!this.fetchOptions) {
                    await this.filterOptionsClientSide(this.inputValue)
                }
            } catch (err) {
                console.error(err)
            }

            this.loading = false
        },

        async filterOptionsClientSide(query) {
            const queryLower = query.toLowerCase()
            this.filteredOptions = this.options.filter(option =>
                this._getOptionDisplayString(option).toLowerCase().includes(queryLower)
            )
        },

        async handleScroll(event) {
            if (this.isClientSide || !this.infiniteScroll) return

            const list = event.target

            if (list.scrollTop + list.clientHeight >= list.scrollHeight && !this.fetchingNextPage) {
                this.fetchingNextPage = true
                this.page += 1;
                await this._fetchOptions()
                this.fetchingNextPage = false
            }
        },

        _getOptionDisplayString(option) {
            if (typeof option === 'string') {
                return option
            }

            if (this.emitId && Number.isInteger(option)) {
                option = this.options.find(e => e.id === option)
            }

            if (this.getOptionDisplayString) {
                return this.getOptionDisplayString(option)
            }

            try {
                return option[this.labelKey] ?? option;
            } catch (err) {
                return option
            }
        },

        emitUpdate(option) {
            const selected = this.emitId ? option.id : option
            this.$emit('update:modelValue', selected)
        },

        selectOption(option) {
            this.emitUpdate(option)
            this.showOptions = false
            this.inputValue = this._getOptionDisplayString(option)
        },
    },
    watch: {
        modelValue: {
            handler(newVal) {
                if (newVal) {
                    this.inputValue = this._getOptionDisplayString(newVal)
                } else {
                    this.inputValue = ''
                }
            },
            immediate: true,
        },
        options: {
            handler(newVal) {
                if (newVal) {
                    this.filteredOptions = [
                        ...this.filteredOptions,
                        ...newVal,
                    ]
                }
            },
            immediate: true,
        },
        inputValue(newVal) {
            if (newVal === '') {
                this.filteredOptions = []
                this.hasMore = false
                this.page = 1
            } else {
                this._fetchOptions()
            }
        },
    },
    computed: {
        hasResults() {
            return (this.options.length > 0 || this.filteredOptions.length > 0)
        },
        isClientSide() {
            return !this.fetchOptions
        },
        computedPlaceholder() {
            return this.modelValue
                ? this._getOptionDisplayString(this.modelValue)
                : this.placeholder
        },
        computedInputStyle() {
            const variantStyles = {
                normal: [
                    'rounded relative text-sm font-medium inline-flex items-center w-full h-9 border px-3',
                    'focus:outline-none outline-none focus:ring-0 focus:border-primary-500 focus:shadow-lg',
                    'focus:shadow-primary-500/10 transition duration-200'
                ].join(' ')
            }

            const darkModeStyles = this.darkMode
                ? 'text-slate-100 bg-dark-background border-dark-border focus:bg-dark-module hover:bg-dark-module'
                : 'focus:bg-light-module hover:bg-light-module text-slate-900 bg-light-background border-light-border'

            const disabledStyles = this.disabled ? 'cursor-not-allowed opacity-50' : ''

            const iconLeftStyles = this.iconLeft ? 'pl-10' : ''

            return [
                variantStyles[this.variant] ?? variantStyles.normal,
                darkModeStyles,
                disabledStyles,
                iconLeftStyles
            ]
        },
        listItemStyleClasses() {
            const styles = {
                normal: 'py-3 px-3 capitalize text-sm font-medium transition duration-200 border-b'
            }

            return styles[this.variant] ?? styles.normal
        }
    }
}
</script>
