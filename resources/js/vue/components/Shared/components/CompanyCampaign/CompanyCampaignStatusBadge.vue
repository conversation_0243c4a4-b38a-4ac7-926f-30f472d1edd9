<template>
    <badge
        :dark-mode="darkMode"
        :color="companyCampaign.styles[status].badgeColor"
    >
        {{companyCampaign.titles[status]}}
    </badge>
</template>
<script>
import Badge from "../Badge.vue";
import useCompanyCampaign from "../../../../../composables/useCompanyCampaign.js";
const companyCampaign = useCompanyCampaign()
export default {
    name: "CompanyCampaignStatusBadge",
    components: {Badge},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        status: {
            type: Number,
            required: true,
        }
    },
    data() {
        return {
            companyCampaign
        }
    }
}
</script>
