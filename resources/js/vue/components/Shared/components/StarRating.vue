<template>
    <div class="relative inline-block">
        <div aria-hidden="true" class="inline">
            <svg aria-hidden="true" :class="starWidth" viewBox="0 0 144 27" fill="none"
                 xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M71.9621 0.000976562L68.1516 9.57952L57.6045 9.78429L65.8153 16.2686L62.7306 26.0974L71.5992 20.5004L80.2637 26.3932L77.5419 16.4506L85.9568 10.2393L75.4098 9.69328L71.9621 0.000976562Z"
                    fill="#CACFD3"/>
                <path
                    d="M14.3576 0.000976562L10.5471 9.57952L0 9.78429L8.21084 16.2686L5.1261 26.0974L13.9947 20.5004L22.6592 26.3932L19.9374 16.4506L28.3523 10.2393L17.8053 9.69328L14.3576 0.000976562Z"
                    fill="#CACFD3"/>
                <path
                    d="M100.912 0.000976562L97.1018 9.57952L86.5547 9.78429L94.7655 16.2686L91.6808 26.0974L100.549 20.5004L109.214 26.3932L106.492 16.4506L114.907 10.2393L104.36 9.69328L100.912 0.000976562Z"
                    fill="#CACFD3"/>
                <path
                    d="M130.005 0.000976562L126.195 9.57952L115.647 9.78429L123.858 16.2686L120.774 26.0974L129.642 20.5004L138.307 26.3932L135.585 16.4506L144 10.2393L133.453 9.69328L130.005 0.000976562Z"
                    fill="#CACFD3"/>
                <path
                    d="M43.009 0.000976562L39.1984 9.57952L28.6514 9.78429L36.8622 16.2686L33.7775 26.0974L42.6461 20.5004L51.3106 26.3932L48.5887 16.4506L57.0037 10.2393L46.4566 9.69328L43.009 0.000976562Z"
                    fill="#CACFD3"/>
            </svg>

        </div>
        <div role="img" :aria-label="'Rating: ' + rating + ' out of 5 stars'"
             class="absolute inset-0 overflow-hidden whitespace-no-wrap z-10"
             :style="{width: consumerReviewPercentage}">
            <svg aria-hidden="true" :class="starWidth" viewBox="0 0 144 27" fill="none"
                 xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M14.4014 0L10.5792 9.57855L0 9.78331L8.23586 16.2676L5.14172 26.0964L14.0374 20.4995L22.7282 26.3922L19.9981 16.4496L28.4387 10.2384L17.8595 9.69231L14.4014 0Z"
                    fill="#FFBC3A"/>
                <path
                    d="M18.5192 9.73779C17.0859 16.1766 12.2627 21.3868 5.91514 23.662L5.1416 26.0964L14.0372 20.5222L22.7281 26.3922L19.998 16.4724L28.4386 10.2611L18.5192 9.73779Z"
                    fill="#FFCE6D"/>
                <path
                    d="M72.1807 0L68.3585 9.57855L57.7793 9.78331L66.0152 16.2676L62.921 26.0964L71.8167 20.4995L80.5075 26.3922L77.7774 16.4496L86.218 10.2384L75.6388 9.69231L72.1807 0Z"
                    fill="#FFBC3A"/>
                <path
                    d="M76.2985 9.73779C74.8652 16.1766 70.042 21.3868 63.6944 23.662L62.9209 26.0964L71.8165 20.5222L80.5074 26.3922L77.7773 16.4724L86.2179 10.2611L76.2985 9.73779Z"
                    fill="#FFCE6D"/>
                <path
                    d="M43.1416 0L39.3194 9.57855L28.7402 9.78331L36.9761 16.2676L33.882 26.0964L42.7776 20.4995L51.4685 26.3922L48.7384 16.4496L57.179 10.2384L46.5998 9.69231L43.1416 0Z"
                    fill="#FFBC3A"/>
                <path
                    d="M47.2594 9.73779C45.8261 16.1766 41.0029 21.3868 34.6554 23.662L33.8818 26.0964L42.7775 20.5222L51.4684 26.3922L48.7382 16.4724L57.1788 10.2611L47.2594 9.73779Z"
                    fill="#FFCE6D"/>
                <path
                    d="M101.22 0L97.3976 9.57855L86.8184 9.78331L95.0542 16.2676L91.9601 26.0964L100.856 20.4995L109.547 26.3922L106.816 16.4496L115.257 10.2384L104.678 9.69231L101.22 0Z"
                    fill="#FFBC3A"/>
                <path
                    d="M105.338 9.73779C103.904 16.1766 99.081 21.3868 92.7335 23.662L91.96 26.0964L100.856 20.5222L109.546 26.3922L106.816 16.4724L115.257 10.2611L105.338 9.73779Z"
                    fill="#FFCE6D"/>
                <path
                    d="M129.96 0L126.138 9.57855L115.559 9.78331L123.794 16.2676L120.7 26.0964L129.596 20.4995L138.287 26.3922L135.557 16.4496L143.997 10.2384L133.418 9.69231L129.96 0Z"
                    fill="#FFBC3A"/>
                <path
                    d="M134.078 9.73779C132.644 16.1766 127.821 21.3868 121.474 23.662L120.7 26.0964L129.596 20.5222L138.287 26.3922L135.557 16.4724L143.997 10.2611L134.078 9.73779Z"
                    fill="#FFCE6D"/>
            </svg>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'

/**
 * @typedef props
 * @property {string} starWidth
 * @property {number} rating
 */

const props = defineProps({
    starWidth: {
        type: String,
        default: 'w-40',
    },
    rating: {
        type: Number,
        default: 0
    }
})

const consumerReviewPercentage = computed(() => {
    return props.rating * 20 + '%'
})

</script>

<style scoped>

</style>
