<template>
    <div class="grid grid-cols-4 gap-1">
        <labeled-value label="Invoice">
            <entity-hyperlink
                :entity-id="args.invoiceId"
                type="invoice"
                :prefix="args.invoiceId"
            />
        </labeled-value>

        <labeled-value label="Payment Method">
            <payment-method-badge :type="args.paymentMethod" />
        </labeled-value>

        <labeled-value label="Outstanding">
            {{ $filters.centsToFormattedDollars(args.outstanding)}}
        </labeled-value>

        <labeled-value label="Amount to pay">
            {{ $filters.centsToFormattedDollars(args.amount)}}
        </labeled-value>

        <labeled-value label="Date">
            {{ $filters.dateFromTimestamp(args.date) }}
        </labeled-value>
    </div>
</template>
<script>
import EntityHyperlink from "../../../../BillingManagement/components/EntityHyperlink.vue";
import Badge from "../../Badge.vue";
import PaymentMethodBadge from "../../../../Billing/InvoicePaymentsModule/PaymentMethodBadge.vue";
import LimitedList from "../../Simple/LimitedList.vue";
import LabeledValue from "../../LabeledValue.vue";

export default {
    name: "MakeInvoicePayment",
    components: {LabeledValue, LimitedList, PaymentMethodBadge, EntityHyperlink, Badge},
    props: {
        approval: {
            type: Object,
            default: {}
        }
    },
    computed: {
        args() {
            return this.approval?.payload?.arguments
        },
    }
}
</script>
