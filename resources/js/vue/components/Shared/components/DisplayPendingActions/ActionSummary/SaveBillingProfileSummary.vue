<template>
    <div class="grid grid-cols-4 gap-1">
        <labeled-value v-if="args.billing_profile_id" label="Billing Profile">
            <entity-hyperlink
                :entity-id="args.billing_profile_id"
                type="billing_profile"
                :dark-mode="darkMode"
                :prefix="args.billing_profile_id"
            />
        </labeled-value>
        <labeled-value label="Company">
            <entity-hyperlink
                type="company" :entity-id="args.company_id"
                :prefix="args.company_name"
            >
            </entity-hyperlink>
        </labeled-value>
        <labeled-value label="Default">
            {{ args.default ? 'Yes' : 'No' }}
        </labeled-value>
        <labeled-value label="Process auto">
            {{ args.process_auto ? 'Yes' : 'No' }}
        </labeled-value>
        <labeled-value label="Threshold">
            <badge class="h-fit">${{ args.threshold_in_dollars }}</badge>
        </labeled-value>
        <labeled-value label="Max allowed charge attempts">
            <badge class="h-fit">{{ args.max_allowed_charge_attempts }}</badge>
        </labeled-value>
        <labeled-value v-if="args.due_in_days" label="Due In Days">
            {{ args.due_in_days }} days
        </labeled-value>
        <labeled-value
            label="Payment Type"
        >
            <payment-method-badge
                :type="args.payment_method"
                :reference="args.payment_method_number ? `${args.payment_method_number} ${args.payment_method_expiry_month}/${args.payment_method_expiry_year}` : null"
            />
        </labeled-value>
        <labeled-value label="Associated campaigns">
            <p v-if="args?.associated_campaign_ids?.length > 0">{{ args?.associated_campaign_ids?.length }}</p>
            <p v-else>None</p>
        </labeled-value>
        <labeled-value v-if="args?.due_in_days !== null" label="Due In Days">
            <p>{{ args?.due_in_days }}</p>
        </labeled-value>
        <labeled-value v-if="args?.invoice_template_name" label="Invoice Template">
            <p>{{ args?.invoice_template_name ?? 'Automatic' }}</p>
        </labeled-value>
        <labeled-value label="Issue frequency" class="col-span-2">
            {{ simpleFrequencyHelper.getHumanReadableText(args.frequency_type, args.frequency_data) }}
        </labeled-value>
    </div>
</template>
<script>
import EntityHyperlink from "../../../../BillingManagement/components/EntityHyperlink.vue";
import Badge from "../../Badge.vue";
import PaymentMethodBadge from "../../../../Billing/InvoicePaymentsModule/PaymentMethodBadge.vue";
import LimitedList from "../../Simple/LimitedList.vue";
import LabeledValue from "../../LabeledValue.vue";
import {useSimpleFrequencyHelper} from "../../../../../../composables/useSimpleFrequencyHelper.js";

export default {
    name: "SaveBillingProfileSummary",
    components: {LabeledValue, LimitedList, PaymentMethodBadge, EntityHyperlink, Badge},
    props: {
        approval: {
            type: Object,
            default: {}
        }
    },
    computed: {
        args() {
            return this.approval?.payload?.arguments
        },
        simpleFrequencyHelper() {
            return useSimpleFrequencyHelper()
        },
    }
}
</script>
