<template>
    <div>
        <div @click="toggleAccordion"
             class="transition-colors duration-150 ease-in-out"
             :class="{
            'bg-light-module text-dark-175 hover:bg-light-hover': !darkMode,
            'bg-dark-module text-gray-300 hover:bg-dark-hover': darkMode,
            [containerClasses]: true,
            'border-b' : isOpen
        }"
        >
            <div class="flex min-w-0 gap-x-4 items-center">
                <div class="min-w-0 flex-auto">
                    <p class="text-sm font-semibold leading-6" :class="{ 'text-gray-700': !darkMode, 'text-gray-400': darkMode }">
                        <slot name="title">{{ title }}</slot>
                    </p>
                </div>
            </div>
            <div class="flex shrink-0 items-center gap-x-4">
                <div v-if="total !== null" class="hidden sm:flex sm:flex-col sm:items-end">
                    <p class="text-sm leading-6" :class="{ 'text-gray-700': !darkMode, 'text-gray-400': darkMode }">({{ total }})</p>
                </div>
                <ChevronRightIcon :class="{ 'rotate-90': isOpen }" class="h-5 w-5 flex-none text-gray-400 transition-transform duration-150" aria-hidden="true" />
            </div>
        </div>
        <div :class="[bodyClasses]">
            <transition name="accordion">
                <div v-show="isOpen" class="ml-6 mr-6">
                    <slot></slot>
                </div>
            </transition>
        </div>
    </div>
</template>

<script>
import ChevronRightIcon from '@heroicons/vue/solid/ChevronRightIcon'

export default {
    components: {
        ChevronRightIcon,
    },
    props: {
        title: {
            type: String,
            required: false,
        },
        total: {
            type: [Number, String],
            default: null,
        },
        darkMode: {
            type: Boolean,
            default: false,
        },
        containerClasses: {
            type: String,
            default: 'relative flex justify-between items-center cursor-pointer gap-x-6 py-5 px-4'
        },
        bodyClasses: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            isOpen: false,
        };
    },
    methods: {
        toggleAccordion() {
            this.isOpen = !this.isOpen;
        },
    },
};
</script>

<style scoped>
.accordion-enter-from,
.accordion-leave-to {
    max-height: 0;
    opacity: 0;
    overflow: hidden;
}

.accordion-enter-to,
.accordion-leave-from {
    max-height: 200px; /* should be big enough to hold content */
    opacity: 1;
}

.accordion-enter-active,
.accordion-leave-active {
    transition: max-height 0.3s ease, opacity 0.3s ease;
}

.accordion-content {
    overflow: hidden;
    background-color: #f9f9f9;
    padding: 1rem;
}
</style>
