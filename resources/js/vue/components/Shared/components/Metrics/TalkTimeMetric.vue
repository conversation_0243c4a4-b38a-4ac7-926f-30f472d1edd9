<script setup>
import Metric from './Metric.vue';

const props = defineProps({
    darkMode: false,
});
</script>

<template>
    <Metric :dark-mode="darkMode" title="Call Time" route="talk-time">
        <template #icon>
            <svg class="w-4 shrink-0" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10.2568 0C4.74284 0 0.256836 4.486 0.256836 10C0.256836 15.514 4.74284 20 10.2568 20C15.7708 20 20.2568 15.514 20.2568 10C20.2568 4.486 15.7708 0 10.2568 0ZM13.5498 14.707L9.25684 10.414V4H11.2568V9.586L14.9638 13.293L13.5498 14.707Z" fill="#0081FF"/></svg>
        </template>
    </Metric>
</template>
