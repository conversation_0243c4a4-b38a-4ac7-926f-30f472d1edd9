<template>
    <div :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div v-if="list.length === 0">No logs found</div>
        <div v-else v-for="log in filteredLogs" :key="log.id">
            <li v-if="!log.active" @click="log.active = true" class="relative flex gap-x-4 cursor-pointer">
                <div class="absolute -bottom-6 left-0 top-0 flex w-6 justify-center">
                    <div class="w-px bg-gray-200"></div>
                </div>
                <div class="relative flex h-6 w-6 flex-none items-center justify-center" :class="{'bg-white' : !darkMode, '': darkMode}">
                    <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300"></div>
                </div>
                <div class="flex-auto rounded-md p-3 ring-1 ring-inset ring-gray-200">
                    <div class="flex justify-between gap-x-4">
                        <div class="py-0.5 text-xs leading-5 text-gray-500">
                            <span class="font-medium" :class="[darkMode ? 'text-grey-300' : 'text-grey-600']">{{ getUserName(log) }}</span>
                            {{ log.event ?? '' }}
                        </div>
                        <time datetime="2023-01-23T15:56" class="flex-none py-0.5 text-xs leading-5 text-gray-500">
                            {{ log.display_date ?? '' }}
                        </time>
                    </div>
                    <div v-if="log.event === 'updated' && log.changes.old && log.changes.attributes">
                        <p v-for="(value, key) in log.changes.attributes" :key="key" class="text-sm leading-6 text-gray-500">
                            {{ key }} from {{ log.changes.old[key] }} to {{ value }}
                        </p>
                    </div>
                    <div v-else-if="log.event === 'created' && log.changes.attributes">
                        <p v-for="(value, key) in log.changes.attributes" :key="key" class="text-sm leading-6 text-gray-500">
                            {{ key }} set to {{ value }}
                        </p>
                    </div>
                    <div v-else-if="log.changes.attributes">
                        <p v-for="(value, key) in log.changes.attributes" :key="key" class="text-sm leading-6 text-gray-500">
                            {{ key }}: {{ value }}
                        </p>
                    </div>
                </div>
            </li>
            <li v-else @click="log.active = false" class="relative flex gap-x-4 cursor-pointer">
                <div class="absolute -bottom-6 left-0 top-0 flex w-6 justify-center">
                    <div class="w-px bg-gray-200"></div>
                </div>
                <div class="relative flex h-6 w-6 flex-none items-center justify-center" :class="{'bg-white' : !darkMode, '': darkMode}">
                    <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300"></div>
                </div>
                <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">
                    <span class="font-medium" :class="[darkMode ? 'text-grey-300' : 'text-grey-600']">{{ log.causer?.name ?? 'System' }}</span>
                    {{ log.event ?? '' }} {{ log.subject?.name ?? '' }}.
                </p>
                <time datetime="2023-01-23T11:24" class="flex-none py-0.5 text-xs leading-5 text-gray-500">
                    {{ log.display_date ?? '' }}
                </time>
            </li>
        </div>
    </div>
</template>

<script>
export default {
    name: 'TimelineList',
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        list: {
            type: Array,
            default: []
        }
    },
    computed: {
        filteredLogs() {
            // Apply filtering logic to the logs list here
            return this.list.filter(log => {
                return true;
            });
        }
    },
    methods: {
        getUserName(log) {
            if (!log.causer) {
                return 'System';
            }

            if (log.causer.name) {
                return log.causer.name;
            }

            if (log.causer.first_name || log.causer.last_name) {
                return `${log.causer.first_name} ${log.causer.last_name}`;
            }

            return 'Unknown';
        }
    }
}
</script>
