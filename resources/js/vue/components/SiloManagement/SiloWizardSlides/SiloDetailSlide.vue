<template>
    <div class="flex flex-col p-6 my-auto border rounded-sm h-full overflow-auto"
        :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
    >
        <div class="text-center">
            <h6 class="text-lg font-bold">Directory Details</h6>
        </div>
        <hr class="my-6 w-5/6 mx-auto opacity-80"
            :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
        />
        <div class="mx-auto flex justify-center items-center max-w-screen-sm">
            <div class="grid gap-y-6 h-full items-center pb-8">
                <div>
                    <p class="mb-1 font-semibold">Please enter a name for this directory</p>
                    <CustomInput
                        v-model="siloDetails.name"
                        :dark-mode="darkMode"
                        placeholder="Name..."
                        @keyup.enter.stop="requestNextSlide"
                    />
                </div>
                <div>
                    <p class="mb-1 font-semibold">Please enter the root path for this content</p>
                    <CustomInput
                        v-model="siloDetails.rootPath"
                        :dark-mode="darkMode"
                        placeholder="/base-path"
                        @keyup.enter.stop="requestNextSlide"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import CustomInput from "../../Shared/components/CustomInput.vue";

export default {
    name: "SiloDetailSlide",
    components: {
        CustomInput
    },
    mixins: [],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        siloData: {
            type: Object,
            default: {},
        },
        slideId: {
            type: String,
            default: null,
        }
    },
    emits: ['slideError', 'slideProgress', 'siloDataUpdate'],
    expose: ['validate'],
    mounted() {
        if (this.siloData[this.slideId]) {
            Object.assign(this.siloDetails, JSON.parse(JSON.stringify(this.siloData[this.slideId])));
        }
    },
    data () {
        return {
            siloDetails: {
                name: "",
                rootPath: "",
            }
        }
    },
    methods: {
        validate() {
            const errors = [];
            if (!this.siloDetails.name) {
                errors.push('Please enter a name for this directory.');
            }
            if (this.siloDetails.name.length < 4) {
                errors.push('Please enter a name more than 3 characters long.');
            }
            if (this.siloDetails.name.length > 64) {
                errors.push('Please enter a name less than 128 characters long.');
            }
            if (!this.siloDetails.rootPath) {
                errors.push('Please enter a root path - just enter a slash "/" if your content has no root path.');
            }

            if (errors.length) {
                this.$emit('slideError', errors.join("\n"));
                return false;
            }
            else {
                this.$emit('siloDataUpdate', this.slideId, this.siloDetails);
                return true;
            }
        },
        requestNextSlide() {
            this.$emit('slideProgress');
        }
    },
}

</script>
