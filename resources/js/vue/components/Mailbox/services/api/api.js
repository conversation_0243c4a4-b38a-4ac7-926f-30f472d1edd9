import axios from 'axios';
import {BaseApiService} from "./base";

export class ApiService extends BaseApiService {
    constructor(baseUrl, baseEndpoint, version) {
        super("ApiService");

        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    listEmails({page, perPage, tab, query, companyId} = {}) {
        return this.axios().get('/list', {params: {tab, page, perPage, query, company_id: companyId}});
    }

    getDetailedEmail(emailId) {
        return this.axios().get(`/${emailId}/detail`);
    }

    getCustomLabels() {
        return this.axios().get('/labels')
    }

    modifyEmails(emailIds, action) {
        return this.axios().patch('/modify', {
            email_ids: emailIds,
            action: action
        })
    }

    deleteEmails(emailIds) {
        return this.axios().delete('/delete', {
            data: {
                email_ids: emailIds
            }
        })
    }

    sendEmail({to, subject, content, bcc, cc}) {
        return this.axios().post(`send`, {
            to,
            subject,
            content,
            bcc,
            cc
        });
    }

    getPermission() {
        return this.axios().get(`get-permission`);
    }

    syncMailbox() {
        return this.axios().post(`sync-mailbox`);
    }

    replyEmail(emailId, {to, content, bcc, cc}) {
        return this.axios().post(`${emailId}/reply`, {
            to,
            content,
            bcc,
            cc
        });
    }

    forwardEmail(emailId, {to, content, bcc, cc}) {
        return this.axios().post(`/${emailId}/forward`, {
            to,
            content,
            bcc,
            cc
        });
    }

    getUserEmailSignature(){
        return this.axios().get('user-signature');
    }
}
