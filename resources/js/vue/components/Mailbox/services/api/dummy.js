import {BaseApiService} from "./base";
import {REQUEST} from "../../../../../constants/APIRequestKeys";

class DummyApiService extends BaseApiService {
    constructor(delay = 150) {
        super("DummyApiService");

        this.delay = delay;
    }

    _makeResponse(data) {
        return new Promise((resolve, reject) => {
            setTimeout(() => resolve({ data: { data } }), this.delay);
        });
    }

    listEmails() {
        return this._makeResponse({
            [REQUEST.STATUS]: true,
            "emails": {
                "data": [
                    {
                        "id": 1,
                        "from": "<EMAIL>",
                        "to": "<EMAIL>",
                        "sent_at": "2024-02-21 22:20:53",
                        "subject": "Fake subject",
                        "snippet": "Fake snippet",
                        "read_at": "2024-02-22 01:53:48",
                        "content": "<h1>Fake email content</h1>"
                    }
                ],
                "links": {
                    "first": "",
                    "last": "",
                    "prev": null,
                    "next": ""
                },
                "meta": {
                    "current_page": 1,
                    "from": 1,
                    "last_page": 1,
                    "links": [],
                    "path": "",
                    "per_page": 10,
                    "to": 10,
                    "total": 1
                }
            }
        });
    }

    modifyEmail() {
        return this._makeResponse({
            [REQUEST.STATUS]  : true,
        });
    }

    sendEmail() {
        return this._makeResponse({
            [REQUEST.STATUS]  : true,
        });
    }

    getPermission() {
        return this._makeResponse({
            [REQUEST.STATUS]  : false,
            url: null,
            has_token: true
        });
    }

    syncMailbox() {
        return this._makeResponse({
            [REQUEST.STATUS]  : true,
        });
    }

    replyEmail() {
        return this._makeResponse({
            [REQUEST.STATUS]  : true,
        });
    }

    forwardEmail() {
        return this._makeResponse({
            [REQUEST.STATUS]  : true,
        });
    }
    deleteEmail() {
        return this._makeResponse({
            [REQUEST.STATUS]  : true,
        });
    }

    getDetailedEmail() {
        return this._makeResponse({
            [REQUEST.STATUS]  : true,
        });
    }

    getUserEmailSignature(){
        return this._makeResponse({
            [REQUEST.STATUS]  : true,
        });
    }
}

export { DummyApiService };

