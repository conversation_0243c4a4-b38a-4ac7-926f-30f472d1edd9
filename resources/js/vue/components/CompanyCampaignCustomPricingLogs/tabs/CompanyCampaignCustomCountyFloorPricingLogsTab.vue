<template>
    <div class="mx-10">
        <CompanyCampaignCustomCountyFloorPrice :dark-mode="darkMode" />
    </div>
</template>
<script>
import CompanyCampaignCustomCountyFloorPrice
    from "../components/CompanyCampaignCustomCountyFloorPrice/CompanyCampaignCustomCountyFloorPrice.vue";
export default {
    name: "CompanyCampaignCustomStateFloorPricingLogsTab",
    components: {CompanyCampaignCustomCountyFloorPrice},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    }
}
</script>

<style scoped>

</style>
