import axios from "axios";

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseUrl = baseUrl;
        this.baseEndpoint = baseEndpoint;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'company-campaign-custom-pricing-logs', 1);
    }

    getCompanyCampaignCustomStateFloorPrice(params = {}) {
        return this.axios().get('/state', {
            params
        })
    }

    getCompanyCampaignCustomCountyFloorPrice(params = {}) {
        return this.axios().get('/county', {
            params
        })
    }
}
