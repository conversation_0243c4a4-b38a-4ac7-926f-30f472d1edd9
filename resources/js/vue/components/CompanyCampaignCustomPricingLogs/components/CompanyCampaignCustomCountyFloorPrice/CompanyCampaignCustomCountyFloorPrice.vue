<template>
    <simple-table
        title="Company Campaign Custom State Floor Pricing Logs"
        :loading="loading"
        :dark-mode="darkMode"
        :data="data"
        :headers="headers"
        row-classes="gap-5 grid items-center py-4 rounded px-5"
        no-pagination
        not-found-message="No logs found"
        v-model="tableFilter"
        :table-filters="tableFilters"
        @search="handleSearch"
        @reset="handleReset"
    >
        <template v-slot:visible-filters>
            <company-search-autocomplete v-model="tableFilter.company_id" :dark-mode="darkMode"/>
        </template>
        <template #row.col.reference="{item}">
            <p class="text-sm">{{item.county }}</p>
        </template>
        <template #row.col.sale_type="{item}">
            <p class="text-sm">{{ item.sale_type }} <span v-if="item.quality_tier">( {{ item.quality_tier }} )</span></p>
        </template>
        <template #row.col.price="{item}">
            <div class="flex gap-1 items-center">
                <p class="text-sm">{{ formatCurrency(item.price_from) }}</p>
                <simple-icon
                    v-if="item.price_to"
                    :icon="simpleIcon.icons.ARROW_RIGHT"
                ></simple-icon>
                <p class="text-sm" v-if="item.price_to">{{ formatCurrency(item.price_to) }}</p>
                <simple-icon
                    v-if="item.price_to"
                    :icon="item.price_to > item.price_from ? simpleIcon.icons.ARROW_TENDING_UP : simpleIcon.icons.ARROW_TENDING_DOWN"
                    :color="item.price_to > item.price_from ? simpleIcon.colors.GREEN : simpleIcon.colors.RED"
                    :size="simpleIcon.sizes.MD"
                ></simple-icon>
            </div>
        </template>
        <template #row.col.company_name="{ value, item }">
            <div class="flex flex-col items-center gap-1 truncate">
                <div class="flex items-center cursor-pointer">
                    <svg class="mr-1 fill-current text-primary-500" width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.8156 6.42855C7.19448 6.42855 7.55784 6.27804 7.82575 6.01013C8.09366 5.74223 8.24417 5.37886 8.24417 4.99998C8.24417 4.6211 8.09366 4.25774 7.82575 3.98983C7.55784 3.72192 7.19448 3.57141 6.8156 3.57141C6.43671 3.57141 6.07335 3.72192 5.80544 3.98983C5.53753 4.25774 5.38702 4.6211 5.38702 4.99998C5.38702 5.37886 5.53753 5.74223 5.80544 6.01013C6.07335 6.27804 6.43671 6.42855 6.8156 6.42855Z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M0 5C0.91 2.10214 3.61714 0 6.81571 0C10.0143 0 12.7214 2.10214 13.6314 5C12.7214 7.89786 10.0143 10 6.81571 10C3.61714 10 0.91 7.89786 0 5ZM9.67286 5C9.67286 5.75776 9.37184 6.48449 8.83602 7.02031C8.3002 7.55612 7.57348 7.85714 6.81571 7.85714C6.05795 7.85714 5.33123 7.55612 4.79541 7.02031C4.25959 6.48449 3.95857 5.75776 3.95857 5C3.95857 4.24224 4.25959 3.51551 4.79541 2.97969C5.33123 2.44388 6.05795 2.14286 6.81571 2.14286C7.57348 2.14286 8.3002 2.44388 8.83602 2.97969C9.37184 3.51551 9.67286 4.24224 9.67286 5Z"/></svg>
                    <a :href="`/companies/${item.company_id}`" target="_blank" class="font-semibold text-primary-500 text-sm">{{ value }}</a>
                </div>
            </div>
        </template>
    </simple-table>
</template>

<script>
import SimpleTable from "../../../Shared/components/SimpleTable/SimpleTable.vue";
import ApiService from "../../services/api.js";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import {useCurrencyHelper} from "../../../../../composables/useCurrencyHelper.js";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import CompanySearchAutocomplete from "../../../Shared/components/Company/CompanySearchAutocomplete.vue";
import {
    SimpleTableFilterTypesEnum
} from "../../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";

const DEFAULT_TABLE_FILTER = {
    page: 1,
    perPage: 10,
}

const currencyHelper = useCurrencyHelper()
export default {
    name: "CompanyCampaignCustomCountyFloorPrice",
    components: {CompanySearchAutocomplete, SimpleIcon, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            api: ApiService.make(),
            loading: false,
            tableFilter: {},
            headers: [
                {title: 'Company', field: 'company_name'},
                {title: 'Campaign', field: 'campaign_name'},
                {title: 'County', field: 'county'},
                {title: 'Sale Type / Quality Tier', field: 'sale_type'},
                {title: 'Price', field: 'price'},
                {title: 'Causer', field: 'causer_name'},
                {title: 'Date', field: 'date'},
            ],
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'campaign',
                    title: 'Enter Campaign Name or ID',
                },
            ],
            data: [],
            paginationData: {},
            simpleIcon: useSimpleIcon(),
        }
    },
    created() {
        this.tableFilter = {...DEFAULT_TABLE_FILTER};
        this.getCompanyCampaignCustomCountyFloorPrice();
    },
    methods: {
        formatCurrency(amount) {
            const validAmount = amount ?? 0;
            return currencyHelper.formatCurrency(validAmount, {
                decimals: 0
            })
        },
        async getCompanyCampaignCustomCountyFloorPrice() {
            this.loading = true;
            const response = await this.api.getCompanyCampaignCustomCountyFloorPrice(this.tableFilter);
            this.data = response.data.data.county_floor_pricing_logs;
            this.loading = false;
        },
        async handleSearch() {
            this.tableFilter = {...this.tableFilter, ...DEFAULT_TABLE_FILTER};
            await this.getCompanyCampaignCustomCountyFloorPrice();
        },
        async handleReset() {
            this.tableFilter = {...DEFAULT_TABLE_FILTER};
            await this.getCompanyCampaignCustomCountyFloorPrice();
        }
    }
}
</script>

<style scoped>

</style>
