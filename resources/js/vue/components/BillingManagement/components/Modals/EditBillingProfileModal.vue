<template>
    <Modal
        :loadingConfirmation-confirmation="loadingConfirmation"
        :dark-mode="darkMode"
        @confirm="handleSaveProfile"
        @close="$emit('close')"
    >
        <template v-slot:header>
            <div>
                Manage Billing Profile
            </div>
        </template>
        <template v-slot:content>
            <simple-alert
                v-if="errorHandler.message !== null"
                :variant="SIMPLE_ALERT_VARIANTS.LIGHT_RED"
                :dark-mode="darkMode"
                :content="errorHandler.message"
            >
            </simple-alert>
            <simple-alert
                v-if="profileData.default"
                :variant="SIMPLE_ALERT_VARIANTS.LIGHT_BLUE"
                :dark-mode="darkMode"
                content="Default billing profiles are set to all campaigns"
            >
            </simple-alert>
            <div v-if="!loading"
                 class="grid gap-2 items-start"
                 :class="profileData.default ? 'grid-cols-2' : 'grid-cols-3'"
            >
                <div class="flex-1 grid grid-cols-3 col-span-2 gap-x-4 gap-y-2">
                    <labeled-value v-if="!companyId" label="Company">
                        <entity-hyperlink
                            v-if="editing" type="company"
                            :entity-id="profileData.company?.id"
                            :prefix="profileData.company?.name"
                        >
                        </entity-hyperlink>
                        <company-search-autocomplete v-else v-model="profileData.company_id">
                        </company-search-autocomplete>
                    </labeled-value>
                    <labeled-value v-if="false" label="Billing Contact">
                        <div v-if="editing" class="text-primary-500 font-semibold">{{ profileData.contact?.name }}
                            ({{ profileData.contact?.id }})
                        </div>
                        <company-users-autocomplete
                            v-else
                            v-model="profileData.contact"
                            :company-id="profileData.company_id"
                        />
                    </labeled-value>
                    <labeled-value label="Threshold in Dollars">
                        <custom-inline-input
                            v-model="profileData.threshold"
                            :dark-mode="darkMode"
                        />
                    </labeled-value>
                    <labeled-value label="Allowed Charge Attempts">
                        <custom-inline-input
                            v-model="profileData.charge_attempts"
                            :dark-mode="darkMode"
                        />
                    </labeled-value>
                    <labeled-value label="Process Automatically">
                        <toggle-switch
                            v-model="profileData.process_auto"
                            class="flex justify-end"
                            :dark-mode="darkMode"
                        />
                    </labeled-value>
                    <labeled-value label="Default">
                        <toggle-switch
                            v-model="profileData.default"
                            class="flex justify-end"
                            :dark-mode="darkMode"
                        />
                    </labeled-value>
                    <labeled-value label="Payment method">
                        <div class="flex items-center gap-2">
                            <div
                                v-for="paymentMethodOption in paymentMethodOptions"
                                class="flex gap-1 items-center"
                                :class="[
                                    profileData.payment_method !== paymentMethodOption ? 'opacity-50' : 'opacity-100'
                                ]"
                            >
                                <custom-checkbox
                                    :model-value="profileData.payment_method === paymentMethodOption"
                                    @update:modelValue="() => profileData.payment_method = paymentMethodOption"
                                    :input-disabled="profileData.payment_method === paymentMethodOption"
                                />
                                <payment-method-badge
                                    :type="paymentMethodOption"
                                    @click="profileData.payment_method = paymentMethodOption"
                                    class="cursor-pointer hover:opacity-75"
                                />
                            </div>
                        </div>
                    </labeled-value>
                    <labeled-value v-if="profileData.payment_method === 'stripe'" label="Preferred payment method">
                        <loading-spinner v-if="loadingCompanyPaymentMethods"></loading-spinner>
                        <div
                            v-else
                            class="flex items-center gap-1"
                        >
                            <dropdown
                                v-model="profileData.payment_method_id"
                                :model-value="profileData.payment_method_id"
                                :options="companyPaymentMethodOptions"
                                placeholder="Set a preferred payment method"
                                class="flex-1"
                            />
                            <simple-icon
                                v-if="profileData.payment_method_id"
                                :icon="simpleIcon.icons.X_MARK"
                                tooltip="Remove preferred payment method"
                                clickable
                                @click="() => profileData.payment_method_id = null"
                            />
                        </div>
                    </labeled-value>
                    <labeled-value label="Due In Days">
                        <template v-slot:action>
                            <simple-icon
                                :dark-mode="darkMode"
                                :icon="simpleIcon.icons.INFORMATION_CIRCLE"
                                :color="simpleIcon.colors.BLUE"
                                tooltip="How many days after issue date should the invoice be due by. When 0, the due date is the same as the issue date."
                            />
                        </template>
                        <number-with-spinners
                            base-style="flex items-center"
                            v-model="profileData.due_in_days"
                            :dark-mode="darkMode"
                            :number-step="1"
                            :min="0"
                            prefix=""
                            :disabled="false"
                        />
                    </labeled-value>
                    <labeled-value label="Invoice Template">
                        <loading-spinner v-if="loadingInvoiceTemplateOptions"></loading-spinner>
                        <div
                            v-else
                            class="flex items-center gap-1"
                        >
                            <dropdown
                                v-model="profileData.invoice_template_id"
                                :model-value="profileData.invoice_template_id"
                                :options="invoiceTemplateOptions"
                                placeholder="Set an invoice template"
                                class="flex-1"
                            />
                            <simple-icon
                                v-if="profileData.invoice_template_id"
                                :icon="simpleIcon.icons.X_MARK"
                                tooltip="Remove preferred payment method"
                                clickable
                                @click="() => profileData.invoice_template_id = null"
                            />
                        </div>
                    </labeled-value>
                    <labeled-value label="Frequency" class="col-span-full">
                        <simple-frequency-selector
                            :type="profileData.frequency_type"
                            :dark-mode="darkMode"
                            :data="profileData.frequency_data"
                            @cron-updated="handleCronUpdated"
                        />
                    </labeled-value>
                </div>
                <div v-if="!profileData.default" class="flex flex-col gap-1">
                    <campaign-selector
                        v-if="!profileData.default"
                        :dark-mode="darkMode"
                        :model-value="campaigns"
                    />
                    <custom-button @click="handleToggleCampaigns">Toggle all</custom-button>
                </div>
            </div>
            <loading-spinner v-else/>
        </template>
    </Modal>
</template>

<script>
import Modal from "../../../Shared/components/Modal.vue";
import CampaignSelector from "../BillingProfile/CampaignSelector.vue";
import CustomInlineInput from "../../../Shared/components/CustomInlineInput.vue";
import ApiService from "../../services/billing-profiles-api";
import CompanyPaymentMethodsApiService from "../../services/company-payment-methods.js";
import SimpleFrequencySelector from "../../../Shared/components/Simple/SimpleFrequencySelector.vue";
import useErrorHandler from "../../../../../composables/useErrorHandler.js";
import SimpleAlert, {SIMPLE_ALERT_VARIANTS} from "../../../Shared/components/SimpleAlert.vue";
import EntityHyperlink from "../EntityHyperlink.vue";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";
import CustomInlineSelect from "../../../Shared/components/CustomInlineSelect.vue";
import LabeledValue from "../../../Shared/components/LabeledValue.vue";
import CompanySearchAutocomplete from "../../../Shared/components/Company/CompanySearchAutocomplete.vue";
import CompanyUsersAutocomplete from "../../../Shared/components/CompanyUsersAutocomplete.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import PaymentMethodBadge from "../../../Billing/InvoicePaymentsModule/PaymentMethodBadge.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import {useToastNotificationStore} from "../../../../../stores/billing/tost-notification.store.js";
import {ROLES, useRolesPermissions} from "../../../../../stores/roles-permissions.store.js";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import CustomCheckbox from "../../../Shared/SlideWizard/components/CustomCheckbox.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import NumberWithSpinners from "../../../Campaigns/Wizard/components/NumberWithSpinners.vue";
import Api from "../../services/invoice-templates-api.js";
const simpleIcon = useSimpleIcon()

export default {
    name: "EditBillingProfileModal",
    components: {
        SimpleIcon,
        CustomCheckbox,
        NumberWithSpinners,
        CustomButton,
        LoadingSpinner,
        PaymentMethodBadge,
        Dropdown,
        CompanyUsersAutocomplete,
        CompanySearchAutocomplete,
        LabeledValue,
        CustomInlineSelect,
        ToggleSwitch,
        EntityHyperlink,
        SimpleAlert,
        SimpleFrequencySelector,
        CustomInlineInput,
        CampaignSelector,
        Modal
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        data: {
            type: Object,
            default: {}
        },
        billingProfileId: {
            type: Number,
            required: false
        },
        companyId: {
            type: Number,
            required: false
        }
    },
    emits: ['close', 'confirm'],
    data() {
        return {
            loadingCompanyPaymentMethods: false,
            loadingInvoiceTemplateOptions: false,
            invoiceTemplateOptions: [],
            SIMPLE_ALERT_VARIANTS,
            loadingConfirmation: false,
            errorHandler: useErrorHandler(),
            profileData: {
                due_in_days: 0
            },
            campaigns: [],
            billingProfileTypes: [
                {id: 'stripe', name: 'Stripe'},
                {id: 'manual', name: 'Manual'},
            ],
            invoiceTemplateApi :Api.make(),
            apiService: ApiService.make(),
            loading: false,
            toastNotificationStore: useToastNotificationStore(),
            paymentMethodOptions: ['manual', 'stripe'],
            companyPaymentMethodsApi: CompanyPaymentMethodsApiService.make(),
            companyPaymentMethods: [],
            rolesPermissions: useRolesPermissions(),
        }
    },
    async created() {
        this.getInvoiceTemplateOptions()
        this.loading = true;
        this.profileData = {...this.profileData, ...this.data}
        if (!this.editing) {
            this.profileData = {
                threshold: 400,
                charge_attempts: 3,
                process_auto: true,
                default: true,
                payment_method: 'stripe',
                frequency_type: 'daily',
                company_id: this.companyId,
                frequency_data: {
                    day: 14
                },
            }
        } else if (this.billingProfileId) {
            const res = await this.apiService.getBillingProfile(this.billingProfileId)
            this.profileData = res.data.data
        }
        this.loading = false;
    },

    computed: {
        simpleIcon() {
            return useSimpleIcon()
        },
        editing() {
            return this.profileData?.id || this.billingProfileId
        },
        companyPaymentMethodOptions() {
            return this.companyPaymentMethods
                .filter(e => e.type === this.profileData.payment_method)
        }
    },
    watch: {
        'profileData.company_id': {
            handler() {
                this.getCompanyCampaigns(this.profileData.company_id)
                this.getCompanyPaymentMethods(this.profileData.company_id)
            },
        },
        'profileData.payment_method': {
            handler() {
                if (this.profileData.payment_method === 'manual') {
                    this.profileData.payment_method_id = null
                }
            },
        },
        'profileData.company.id': {
            handler() {
                if (this.profileData.company?.id) {
                    this.getCompanyCampaigns(this.profileData.company?.id)
                    this.getCompanyPaymentMethods(this.profileData.company?.id)
                }
            },
            deep: true
        },
    },
    methods: {
        async getInvoiceTemplateOptions() {
            this.loadingInvoiceTemplateOptions = true
            const response = await this.invoiceTemplateApi.getInvoiceTemplateOptions();
            this.invoiceTemplateOptions = response.data.data
            this.loadingInvoiceTemplateOptions = false
        },
        handleToggleCampaigns() {
            const isOneAssociated = this.campaigns.some(c => c.associated)
            this.campaigns = this.campaigns.map(c => ({...c, associated: !isOneAssociated}))
        },
        handleCronUpdated(type, data) {
            this.profileData.frequency_type = type;
            this.profileData.frequency_data = data
        },
        async handleSaveProfile() {
            this.loadingConfirmation = true;

            try {
                const campaigns = this.campaigns.filter(campaign => campaign.associated).map(c => c.id);

                const payload = {
                    ...this.profileData,
                    campaigns
                }

                if (this.editing) {
                    await this.apiService.updateBillingProfile(this.profileData.id, payload)
                } else {
                    await this.apiService.createBillingProfile({
                        ...payload,
                        billing_contact_id: this.profileData.contact?.id
                    })
                }

                const isFinanceOwner = this.rolesPermissions.hasRole(ROLES.FINANCE_OWNER)

                const message = isFinanceOwner ? 'Saved successfully' : 'Action requested';

                this.toastNotificationStore.notifySuccess({
                    message
                })

                this.$emit('confirm')
            } catch (err) {
                this.errorHandler.handleError(err, 'Validation Error')
                this.toastNotificationStore.notifyError({
                    message: this.errorHandler.message
                })
            }

            this.loadingConfirmation = false;
        },
        async getCompanyPaymentMethods(companyId) {
            this.loadingCompanyPaymentMethods = true

            try {
                const res = await this.companyPaymentMethodsApi.getCompanyPaymentMethods(companyId)
                this.companyPaymentMethods = res.data.data
            } catch (err) {
                console.error(err)
            }

            this.loadingCompanyPaymentMethods = false
        },
        async getCompanyCampaigns(companyId) {
            if (!companyId) {
                return
            }

            this.loadingConfirmation = true;

            const response = await this.apiService.getCompanyCampaigns(
                companyId,
                this.data?.id,
            );

            this.campaigns = response.data.data.campaigns

            this.loadingConfirmation = false;
        }
    }
}
</script>
