<template>
    <loading-spinner v-if="loadingTemplates"></loading-spinner>
    <div v-else class="grid grid-cols-3 gap-2">
        <simple-autocomplete
            v-model="modelValue.email_template_id"
            :options="emailTemplateOptions"
            label-key="name"
            label="Email template"
            emit-id
        />
        <labeled-value label="Targets">
            <multi-select
                v-model="modelValue.target_types"
                :options="targetTypes"
                :dark-mode="darkMode"
                text-place-holder="Select targets"
                :show-search-box="false"
                @input="(v) => modelValue.target_types = v"
                :selected-ids="modelValue.target_types"
            />
        </labeled-value>
    </div>
</template>

<script>
import SimpleAutocomplete from "../../../../../Shared/components/SimpleAutocomplete.vue";
import Api from "../../../../../EmailTemplates/API/api.js";
import LoadingSpinner from "../../../../../Shared/components/LoadingSpinner.vue";
import Dropdown from "../../../../../Shared/components/Dropdown.vue";
import MultiSelect from "../../../../../Shared/components/MultiSelect.vue";
import LabeledValue from "../../../../../Shared/components/LabeledValue.vue";

export default {
    name: "SendEmailNotificationConfigForm",
    components: {LabeledValue, MultiSelect, Dropdown, LoadingSpinner, SimpleAutocomplete},
    props: {
        modelValue: {
            type: Object,
            default: {},
        },
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            emailTemplatesApi: Api.make(),
            emailTemplateOptions: [],
            loadingTemplates: false,
        }
    },
    mounted() {
        this.getEmailTemplates();
    },
    computed: {
        targetTypes() {
            return [
                {
                    id: 'company_billing_contacts',
                    name: 'Company Billing Contacts'
                },
                {name: 'Business Development Manager', id: 'business_development_manager'},
                {name: 'Customer Success Manager', id: 'customer_success_manager'},
                {name: 'Account Manager', id: 'account_manager'},
            ]
        }
    },
    methods: {
        async getEmailTemplates() {
            this.loadingTemplates = true
            try {
                const response = await this.emailTemplatesApi.getAllEmailTemplates()
                this.emailTemplateOptions = response.data.data.templates
            } catch (err) {
                console.error(err)
            }
            this.loadingTemplates = false
        },
    },
}
</script>
