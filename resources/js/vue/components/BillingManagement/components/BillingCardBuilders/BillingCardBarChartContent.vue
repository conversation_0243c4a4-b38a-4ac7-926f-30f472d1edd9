<template>
    <div class="flex flex-col">
        <div v-if="isVisible" class="flex gap-2 pl-3 align-items-end">
            <div class="flex flex-col justify-end font-bold text-lg" v-if="primaryInsight">
                <div>
                    {{primaryInsight}}
                </div>
            </div>
            <div class="flex flex-col justify-end font-semibold text-xs text-slate-500 mb-1" v-if="secondaryInsight">
                <div>
                    {{secondaryInsight}}
                </div>
            </div>
        </div>
        <div class="p-5">
            <bar-chart
                :labels-list="columnLabels"
                :statistics="statistics"
                :width="1200"
                :chart-options="options"
                :border-radius=5
                :datasets="datasets"
            />
        </div>
    </div>
</template>
<script>
import BarChart from "../../../Shared/components/BarChart.vue";

export default {
    name: "BillingCardBarChartContent",
    components: {BarChart},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        columnLabels: {
            type: Array,
            default: []
        },
        statistics: {
            type: Array,
            default: []
        },
        primaryInsight: {
            type: String,
            default: null,
        },
        secondaryInsight: {
            type: String,
            default: null,
        },
        orientation: {
            type: String,
            default: 'horizontal'
        },
        datasets: {
            type: Array,
            default: []
        },
        xTitle: {
            type: String,
            default: null,
        },
        yTitle: {
            type: String,
            default: null,
        }
    },
    data() {
    },
    computed: {
        isVisible() {
            return !!(this.primaryInsight || this.secondaryInsight);
        },
        options() {
            return {
                indexAxis: this.orientation === 'horizontal' ? 'y' : 'x',
                responsive: true,
                scales: {
                    x: {
                        ticks: {
                            font: {
                                size: 12,
                                weight: 'bold',
                            },
                        },
                        title: {
                            display: !!this.xTitle,
                            text: this.xTitle ?? '',
                            font: {
                                size: 14,
                                weight: 'bold',
                            }
                        },
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        ticks: {
                            font: {
                                size: 12,
                                weight: 'bold',
                            },
                        },
                        title: {
                            display: !!this.yTitle,
                            text: this.yTitle ?? '',
                            font: {
                                size: 14,
                                weight: 'bold',
                            }
                        },
                        grid: {
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false,
                    }
                }
            }
        }
    }
}
</script>
