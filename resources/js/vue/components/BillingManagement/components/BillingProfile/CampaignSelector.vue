<template>
    <div class="border rounded-md bg-light-background flex flex-col gap-2 overflow-auto">
        <div class="p-2 text-slate-500 uppercase sticky top-0 bg-light-background z-10 border-b">
            Selected Campaigns
        </div>
        <simple-checklist
            class="p-4 max-h-60"
            v-model="modelValue"
            :dark-mode="darkMode"
        />
    </div>
</template>
<script>
import SimpleChecklist from "../../../Shared/components/Simple/SimpleChecklist.vue";
export default {
    name: "CampaignSelector",
    components: {SimpleChecklist},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        modelValue: {
            type: Array,
            required: true
        },
    },
}
</script>
