<template>
    <div>
        <billing-card-wrapper
            heading="Invoice Status"
            :loading="loading"
            :dark-mode="darkMode"
            variant="graph"
        >
            <template v-slot:headerAction>
                <a class="text-primary-500 text-xs font-semibold cursor-pointer" @click="handleViewMoreClicked">View more</a>
            </template>
            <billing-card-bar-chart-content
                v-if="!loading && datasets.length > 0"
                :dark-mode="darkMode"
                :column-labels="labels"
                :primary-insight="primaryInsight"
                :secondary-insight="secondaryInsight"
                orientation="vertical"
                :datasets="datasets"
                x-title="Status"
                y-title="Number of Invoices"
            />
        </billing-card-wrapper>
    </div>
</template>
<script>
import BillingCardWrapper from "../BillingCardBuilders/BillingCardWrapper.vue";
import BillingCardBarChartContent from "../BillingCardBuilders/BillingCardBarChartContent.vue";
import {useBillingManagementStore} from "../../../../../stores/billing/billing-management.store";
import BillingCardTotalsContent from "../BillingCardBuilders/BillingCardTotalsContent.vue";
import useInvoiceHelper from "../../../../../composables/useInvoiceHelper";
const invoiceHelper = useInvoiceHelper()
export default {
    name: "InvoiceStatusesBillingCard",
    components: {BillingCardTotalsContent, BillingCardBarChartContent, BillingCardWrapper},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            invoiceHelper,
            labels: [],
            datasets: [
                {
                    label: "Invoices",
                    data: [],
                    minBarLength: 10,
                    backgroundColor: [
                        invoiceHelper.getStatusStyle([invoiceHelper.STATUSES.DRAFT]).color,
                        invoiceHelper.getStatusStyle([invoiceHelper.STATUSES.ISSUED]).color,
                        invoiceHelper.getStatusStyle([invoiceHelper.STATUSES.PAID]).color,
                        invoiceHelper.getStatusStyle([invoiceHelper.STATUSES.FAILED]).color,
                        invoiceHelper.getStatusStyle([invoiceHelper.STATUSES.VOIDED]).color,
                        invoiceHelper.getStatusStyle([invoiceHelper.STATUSES.DELETED]).color,
                        invoiceHelper.getStatusStyle([invoiceHelper.STATUSES.COLLECTION]).color,
                    ]
                }
            ],
            primaryInsight: "$0.00",
            secondaryInsight: "$0.00 previous period",
            loading: false,
            billingStore: useBillingManagementStore(),
        }
    },
    created() {
        this.getInvoiceStatuses();
        this.billingStore.addPeriodUpdateEventListener('invoice-statuses', this.getInvoiceStatuses)

    },
    methods: {
        async getInvoiceStatuses() {
            this.loading = true;
            const response = await this.billingStore.api.getInvoiceStatuses()
            if (response.data.data.status) {
                this.datasets[0].data = response.data.data.data.dataset
                this.labels = response.data.data.data.labels
            }
            this.loading = false;
        },
        handleViewMoreClicked() {

        }
    }
}
</script>
