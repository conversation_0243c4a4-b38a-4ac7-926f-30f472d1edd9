<template>
    <div>
        <billing-profile-policies-management-modal
            v-if="showCreditTypeModal"
            :dark-mode="darkMode"
            @close="handleModalClose"
            @confirm="handleModalConfirm"
        />
        <billing-card-wrapper
            heading="Global Billing policies"
            :dark-mode="darkMode"
            :loading="billingProfilePoliciesStore.loading"
        >
            <template v-slot:headerAction>
                <simple-icon
                    class="mr-1"
                    clickable
                    :size="simpleIcon.sizes.SM"
                    @click="showCreditTypeModal = true"
                    :icon="simpleIcon.icons.ARROW_TOP_RIGHT_ON_SQUARE"
                    :color="simpleIcon.colors.BLUE"
                />
            </template>
            <billing-card-paginated-content
                v-if="!billingProfilePoliciesStore.loading"
                :dark-mode="darkMode"
                :data="paginateArray(billingProfilePoliciesStore.billingPolicies, page, perPage)"
                :headers="headers"
                client-paginate
                :pagination-data="clientPaginationData"
                @page-change="handlePageChange"
                no-data-message="No policies configured yet"
            >
                <template v-slot:actions="{item}">
                    {{ getActionsText(item) }}
                </template>
            </billing-card-paginated-content>
        </billing-card-wrapper>
    </div>
</template>
<script>
import BillingCardWrapper from "../BillingCardBuilders/BillingCardWrapper.vue";
import BillingCardPaginatedContent from "../BillingCardBuilders/BillingCardPaginatedContent.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon";
import CreditManagementModal from "../Modals/CreditManagementModal.vue";
import SimpleTable from "../../../Shared/components/SimpleTable/SimpleTable.vue";
import {useBillingPoliciesManagementStore} from "../../../../../stores/billing/billing-policies-management.store";
import BillingProfilePoliciesManagementModal
    from "../Modals/BillingProfilePoliciesManagementModal/BillingProfilePolicyManagementModal.vue";
import BillingCardBarChartContent from "../BillingCardBuilders/BillingCardBarChartContent.vue";
import useArrayHelper from "../../../../../composables/useArrayHelper";

const simpleIcon = useSimpleIcon();
export default {
    name: "BillingPoliciesManagementCard",
    components: {
        BillingCardBarChartContent,
        BillingProfilePoliciesManagementModal,
        SimpleTable, CreditManagementModal, SimpleIcon, BillingCardPaginatedContent, BillingCardWrapper
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            simpleIcon,
            paginationData: null,
            page: 1,
            perPage: 3,
            headers: [
                {name: 'Event', field: 'event_name'},
                {name: 'Actions', field: 'actions'},
            ],
            creditTypeFilters: {},
            billingProfilePoliciesStore: useBillingPoliciesManagementStore(),
            showCreditTypeModal: false,
        }
    },
    created() {
        this.billingProfilePoliciesStore.getBillingProfilePolicies()
    },
    computed: {
        clientPaginationData() {
            return {
                page: this.page,
                perPage: this.perPage,
                total: this.billingProfilePoliciesStore?.billingPolicies?.length ?? 0
            }
        }
    },
    methods: {
        ...useArrayHelper(),
        handleModalConfirm() {
            this.showCreditTypeModal = false
            this.billingProfilePoliciesStore.getBillingProfilePolicies()
        },
        handleModalClose() {
            this.showCreditTypeModal = false
        },
        getActionsText(item) {
            const MAX_ACTIONS_VISIBLE = 2;

            const actionDescriptions = item?.actions.map(a => a.action_name) ?? [];
            const totalActions = actionDescriptions.length;

            const previewText = actionDescriptions.slice(0, MAX_ACTIONS_VISIBLE).join(', ');
            const actionsHiddenCount = totalActions - MAX_ACTIONS_VISIBLE;

            const remainingText = actionsHiddenCount > 0 ? `(+${actionsHiddenCount})` : '';

            return [
                previewText,
                remainingText
            ].join(' ');
        },

        handlePageChange(newPage) {
            this.page = newPage;
        },
    },
}
</script>
