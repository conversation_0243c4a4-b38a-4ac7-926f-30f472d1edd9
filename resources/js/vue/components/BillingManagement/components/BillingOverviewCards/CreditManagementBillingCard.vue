<template>
    <div v-if="canViewCreditManagement">
        <credit-management-modal
            v-if="showCreditTypeModal"
            :dark-mode="darkMode"
            @close="showCreditTypeModal = false"
            @confirm="handleCreditManagementSaved"
        >

        </credit-management-modal>
        <billing-card-wrapper
            heading="Credit Management"
            :dark-mode="darkMode"
            :loading="loading"
        >
            <template v-slot:headerAction>
                <simple-icon class="mr-1" clickable :size="simpleIcon.sizes.SM" @click="showCreditTypeModal = true" :icon="simpleIcon.icons.ARROW_TOP_RIGHT_ON_SQUARE" :color="simpleIcon.colors.BLUE"/>
            </template>
            <billing-card-paginated-content
                v-if="!loading && creditStore?.creditTypes?.length > 0"
                :dark-mode="darkMode"
                :data="creditStore?.paginatedCreditTypes(page,perPage)"
                :visible-fields="creditTypeFields"
                :headers="creditTypeHeaders"
                client-paginate
                :pagination-data="clientPaginationData"
                @page-change="handlePageChange"
            >
                <template v-slot:cash="{item, value}" >
                    <div>
                        {{value ? 'Cash' : 'Non-Cash'}}
                    </div>
                </template>
                <template v-slot:active="{item, value}" >
                    <div>
                        {{value ? 'Active' : 'Inactive'}}
                    </div>
                </template>
            </billing-card-paginated-content>
        </billing-card-wrapper>
    </div>
</template>
<script>
import BillingCardWrapper from "../BillingCardBuilders/BillingCardWrapper.vue";
import BillingCardPaginatedContent from "../BillingCardBuilders/BillingCardPaginatedContent.vue";
import {useCreditManagementStore} from "../../../../../stores/credit/credit-management.store";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon";
import CreditManagementModal from "../Modals/CreditManagementModal.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../../stores/roles-permissions.store";
const simpleIcon = useSimpleIcon();
export default {
    name: "CreditManagementBillingCard",
    components: {CreditManagementModal, SimpleIcon, BillingCardPaginatedContent, BillingCardWrapper},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            simpleIcon,
            page: 1,
            perPage: 3,
            loading: false,
            creditTypeFields: [
                'consumption_order',
                'name',
                'cash',
                'active',
            ],
            creditTypeHeaders: [
                {name: 'Consumption Order', field: 'consumption_order'},
                {name: 'Name', field: 'name'},
                {name: 'Type', field: 'cash'},
                {name: 'Status', field: 'active'},
            ],
            creditStore: useCreditManagementStore(),
            permissionStore: useRolesPermissions(),
            showCreditTypeModal: false,
        }
    },
    created() {
        this.getCreditTypes();
        this.creditStore.addCreditTypeAddedEventListener( 'credit-management', this.getCreditTypes)

    },
    computed: {
        clientPaginationData() {
            return {
                page: this.page,
                perPage: this.perPage,
                total: this.creditStore.creditTypes.length
            }
        },
        canViewCreditManagement() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_BILLING_CREDITS_TYPES_VIEW)
        }
    },
    methods: {
        handleCreditManagementSaved() {
            this.showCreditTypeModal = false;
            this.getCreditTypes();
        },
        async getCreditTypes() {
            this.loading = true
            const response = await this.creditStore.api.getCreditTypes()
            this.creditStore.creditTypes = response.data.data ?? []
            this.loading = false
        },
        handlePageChange(newPage){
            this.page = newPage;
        },
    },
}
</script>
