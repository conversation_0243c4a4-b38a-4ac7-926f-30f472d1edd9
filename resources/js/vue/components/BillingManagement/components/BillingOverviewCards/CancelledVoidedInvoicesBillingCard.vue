<template>
    <invoices-billing-card
        custom-title="Cancelled / Voided Invoices"
        :dark-mode="darkMode"
        listener-id="cancelled-invoices-billing"
    />
</template>
<script>
import InvoicesBillingCard from "./InvoicesBillingCard.vue";

export default {
    name: "CancelledVoidedInvoicesBillingCard",
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    provide() {
        return {
            baseFilters: {
                status: ['voided']
            },
            tableTitle: "Cancelled / Voided Invoice Management"
        }
    },
    components: {InvoicesBillingCard},
}
</script>