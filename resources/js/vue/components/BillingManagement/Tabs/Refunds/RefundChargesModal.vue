<template>
    <modal
        :dark-mode="darkMode"
        @close="$emit('close')"
        no-buttons
        small
    >
        <template v-slot:header>
            View Refund Charges
        </template>
        <template v-slot:content>
            <div class="grid grid-cols-4">
                <div class="col-span-4 grid grid-cols-4 uppercase font-semibold px-2">
                    <div>ID</div>
                    <div>Payment Amount</div>
                    <div>Amount Refunded</div>
                    <div>Refund Charge Status</div>
                </div>
                <div
                    v-for="item in refundCharges"
                    class="col-span-4 grid grid-cols-4 gap-2 bg-primary-100 rounded-lg p-2 my-2 border border-rounded"
                >
                    <div>{{ item.id }}</div>
                    <div>{{ $filters.centsToFormattedDollars(item.payment_value) }}</div>
                    <div>{{ $filters.centsToFormattedDollars(item.refund_value) }}</div>
                    <div>{{ item.status }}</div>
                </div>
            </div>
        </template>
    </modal>
</template>
<script>
import Modal from "../../../Shared/components/Modal.vue";

export default {
    name: "RefundChargesModal",
    components: {Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        refundCharges: {
            type: Object,
            default: null,
        }
    },
    emits: ['close']
}
</script>
