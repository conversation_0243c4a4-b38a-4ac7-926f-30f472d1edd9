import axios from "axios";

export default class ApiService {
    defaultQuery = {}

    constructor(baseUrl, baseEndpoint, version, defaultQuery = {}) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
        this.defaultQuery = defaultQuery
    }


    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`,
            params: this.defaultQuery ?? {}
        }

        return axios.create(axiosConfig);
    }

    static make(defaultQuery) {
        return new ApiService('internal-api', 'billing/profile-policies', 1, defaultQuery);
    }

    getBillingProfilePolicies() {
        return this.axios().get('/')
    }

    getEvents() {
        return this.axios().get('/events')
    }

    createBillingProfilePolicy(payload = {}) {
        return this.axios().post('/', payload)
    }

    deleteBillingProfilePolicy(id) {
        return this.axios().delete('/' + id)
    }

    batchUpdate(policies) {
        return this.axios().put('/batch', {policies})
    }
}
