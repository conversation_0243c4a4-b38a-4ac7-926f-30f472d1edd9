import axios from "axios";

export default class ApiService {
    defaultQuery = {}

    constructor(baseUrl, baseEndpoint, version, defaultQuery = {}) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
        this.defaultQuery = defaultQuery
    }


    axios(getPayments) {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`,
            params: this.defaultQuery ?? {}
        }

        return axios.create(axiosConfig);
    }

    static make(defaultQuery) {
        return new ApiService('internal-api', 'billing', 1, defaultQuery);
    }



    getChargebacksAndDisputes(params = {}) {
        return this.axios().get('/', {
            params
        })
    }

    getTotalInvoiceValueByStatus(params = {}) {
        return this.axios().get('/invoices/aggregates/total', {
            params
        })
    }

    getGrossVolumeSummary(params = {}) {
        return this.axios().get('/invoices/aggregates/comparison', {
            params
        })
    }

    getGrossVolumeBilling(params = {}) {
        return this.axios().get('/', {
            params
        })
    }

    getInvoiceStatuses(params = {}) {
        return this.axios().get('/invoices/aggregates/statuses', {
            params
        })
    }

    getBundlesSold(params = {}) {
        return this.axios().get('/invoices/aggregates/bundles-sold', {
            params
        })
    }

    getPayments(params = {}) {
        return this.axios().get('/invoices/aggregates/payments', {
            params
        })
    }

    getFailedPayments(params = {}) {
        return this.axios().get('/invoices/aggregates/failed-payments', {
            params
        })
    }

    getInvoices(params = {}) {
        return this.axios().get('/', {
            params
        })
    }

}