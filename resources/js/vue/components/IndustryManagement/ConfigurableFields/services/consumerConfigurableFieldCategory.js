export const DEFAULT_CONSUMER_FIELD_CATEGORY_CATEGORY = 'uncategorized';

/**
 * Returns the uncategorized category id given list of categories from database
 *
 * @param consumerConfigurableFieldCategories
 * @returns {number|string}
 */
export const getDefaultCategoryId = (consumerConfigurableFieldCategories) =>{
    const defaultCategory = consumerConfigurableFieldCategories.find(c =>
        c.slug === DEFAULT_CONSUMER_FIELD_CATEGORY_CATEGORY
    )

    return defaultCategory ? defaultCategory.id : ''
}
