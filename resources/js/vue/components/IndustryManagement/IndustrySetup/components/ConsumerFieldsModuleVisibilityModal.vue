<template>
    <modal
        :dark-mode="darkMode"
        full-width
        @close="$emit('close')"
        @confirm="handleSave"
        :loading-confirmation="loadingConfirmation"
    >
        <template v-slot:header>
            <h4 class="text-xl font-medium">Fields visibility in modules - {{categoryName}} / {{categoryIdName}}</h4>
        </template>
        <template v-slot:content>
            <div v-if="loading" class="flex items-center justify-center">
                <loading-spinner />
            </div>
            <div v-else>
                <SimpleAlert content="Change the visibility of the fields on each specific module and feature" />

                <div>
                    <div class="sticky top-0 mb-2 z-10 flex flex-col py-1 gap-1" :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']">
                        <SimpleAlert v-if="alert.show" :content="alert.message" :variant="alert.variant" dismissible @dismiss="alert = {}" />
                        <div class="grid grid-cols-3">
                            <div class="font-semibold text-left">
                                Field
                            </div>
                            <div class="font-semibold flex flex-col justify-center gap-2" v-for="module in modules">
                                <div class="flex items-center gap-1">
                                    <toggle-switch :value="areAllFieldsActiveForModule(module)" @change="v => handleModuleVisibilityClick(module, v)"/>
                                    <p>
                                        {{ module.name }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr class="pb-4">
                    <div v-for="(field, idx) in fieldsVisibility">
                        <div class="mb-5">
                            <div class="grid grid-cols-3 items-center gap-2">
                                <div class="flex flex-col">
                                    <custom-input class="w-full" v-model="field.field.name" :disabled="field.consumer_field_type !== 'common'" :dark-mode="darkMode"/>
                                    <p v-if="errors[`fields.${idx}.field.name`]" class="text-xs text-red-500">{{errors[`fields.${idx}.field.name`][0]}}</p>
                                </div>
                                <div v-for="module in modules">
                                    <div v-for="feature in module.features" class="flex items-center gap-2">
                                        <toggle-switch :value="getFieldVisibilityByModuleAndFieldId(module, feature, field)" @change="v => handleVisibilityClick(module, feature, field, v)"/>
                                        <p class="text-sm">{{feature.name}}</p>
                                    </div>
                                </div>
                            </div>
                            <small :title="field.field.key" class="truncate">{{field.field.key}}</small>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </modal>
</template>

<script>
import Modal from "../../../Shared/components/Modal.vue";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";
import ApiService from "../../ConfigurableFields/services/api";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import CustomInlineInput from "../../../Shared/components/CustomInlineInput.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import SimpleAlert, {SIMPLE_ALERT_VARIANTS} from "../../../Shared/components/SimpleAlert.vue";

export default {
    name: "ConsumerFieldsModuleVisibilityModal",
    components: {
        SimpleAlert,
        CustomButton,
        CustomInlineInput,
        CustomInput,
        LoadingSpinner,
        ToggleSwitch,
        Modal,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        category: {
            type: String,
            required: true
        },
        categoryId: {
            type: [String, Number],
            required: true
        },
        categoryName: {
            type: String,
            required: true
        },
        categoryIdName: {
            type: String,
            required: true
        },
        fields: {
            type: Array,
            required: true
        }
    },

    async mounted() {
        this.loading = true;

        try {
            const response = await this.api.getConsumerFieldsVisibility(this.category, this.categoryId)
            this.modules = response.data.data.modules
            this.fieldsVisibility = response.data.data.fields

        } catch (err) {
            this.showAlert(err.response.data.message, SIMPLE_ALERT_VARIANTS.LIGHT_RED)
        }

        this.loading = false;
    },

    data(){
        return {
            api: ApiService.make(),
            loadingConfirmation: false,
            loading: true,
            fieldsVisibility: [],
            modules: [],
            commonFields: [],
            errors: {},
            alert: {}
        }
    },
    methods: {
        areAllFieldsActiveForModule(module){
          return !this.fieldsVisibility.filter(f => f.module_type === module.id).some(e => !e.is_visible)
        },

        getFieldIndex(module, feature, field){
            return this.fieldsVisibility.findIndex(f =>
                f.consumer_field_category === field.consumer_field_category
                && f.consumer_field_category_id === field.consumer_field_category_id
                && f.consumer_field_id === field.consumer_field_id
                && f.consumer_field_type === field.consumer_field_type
                && f.module_type === module.id
                && f.feature_type === feature.id
            );
        },

        getFieldVisibilityByModuleAndFieldId(module, feature, field){
            const index = this.getFieldIndex(module, feature, field)

            if (index === -1) return false;

            return this.fieldsVisibility[index].is_visible
        },

        handleVisibilityClick(module, feature, field, v){
            const index = this.getFieldIndex(module, feature, field)

            if (index === -1) return;

            this.fieldsVisibility[index].is_visible = v
        },

        handleModuleVisibilityClick(module, val){
            this.fieldsVisibility.forEach(f => {
                if (f.module_type === module.id) {
                    f.is_visible = val
                }
            })
        },

        showAlert(message, variant){
            this.alert = {
                show: true,
                message,
                variant
            }
        },

        async handleSave(){
            this.errors = {}

            this.loadingConfirmation = true
            try {
                await this.api.saveConsumerFieldsVisibility(this.fieldsVisibility, this.category, this.categoryId)
                this.showAlert('Fields visibility saved successfully', SIMPLE_ALERT_VARIANTS.LIGHT_GREEN)
            } catch (err) {
                console.error(err)
                this.errors = err.response.data.errors
                this.showAlert(err.response.data.message, SIMPLE_ALERT_VARIANTS.LIGHT_RED)
            }

            this.loadingConfirmation = false
        }
    }
}
</script>
