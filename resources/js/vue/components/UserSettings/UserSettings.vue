<template xmlns="http://www.w3.org/1999/html">
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        <alerts-container :text="alertText" :alert-type="alertType" v-if="alertActive" :dark-mode="darkMode"/>

        <div class="border rounded-lg"
             :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
            <div class="p-5">
                <div class="flex items-center justify-between pb-3">
                    <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Call Forwarding</h5>
                </div>
                <div class="grid gap-4">
                    <div>
                        <label class="block mb-1 font-semibold text-sm" :class="[darkMode ? 'text-slate-500' : 'text-slate-600']" >Status</label>
                        <toggle :dark-mode="darkMode" v-model="phoneForwardStatus" @click="updateCallForwardingStatus" ></toggle><br>
                    </div>
                    <div>
                        <label class="block mb-1 font-semibold text-sm" :class="[darkMode ? 'text-slate-500' : 'text-slate-600']" >To Number</label>
                        <div class="flex flex-wrap items-center mt-2 gap-3">
                            <input onkeydown="return event.keyCode !== 69" :class="[darkMode ? 'border-dark-border bg-dark-background text-slate-100' : 'border-light-border bg-light-background text-slate-800']" class="rounded-md h-9 py-2 px-3 w-full text-black" type="number" v-model="phoneForwardNumber"/>
                            <button class="text-sm bg-primary-500 h-9 px-4 py-2 rounded-md hover:bg-primary-400 transition duration-200 text-white font-medium" @click="updateCallForwardingNumber">Save</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--    Timezone    -->
        <div class="border rounded-lg"
             :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
            <div class="p-5 h-full flex flex-col gap-y-3">
                <div class="flex flex-col">
                    <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight pb-2">Default Timezone</h5>
                    <p class="text-xs text-slate-500">This is used as the fallback timezone when we can't calculate timezones for tasks, etc.</p>
                </div>
                <div class="flex flex-col gap-y-3 justify-between h-full">
                    <div>
                        <label class="block mb-1 font-semibold text-sm" :class="[darkMode ? 'text-slate-500' : 'text-slate-600']" >Timezone</label>
                        <dropdown :dark-mode="darkMode" v-model="selectedTimezone" :options="timezoneOptions"></dropdown>
                    </div>
                    <div>
                        <custom-button
                            @click="saveTimezone"
                            :dark-mode="darkMode"
                        >
                            Save
                        </custom-button>
                    </div>
                </div>
            </div>
        </div>
        <!--    User Links - only meetingUrl so far    -->
        <div class="border rounded-lg"
             :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
            <div class="p-5 h-full flex flex-col gap-y-3">
                <div class="flex items-center justify-between">
                    <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Links</h5>
                </div>
                <loading-spinner v-if="this.loading" />
                <div v-else class="flex flex-col justify-between gap-y-4 h-full">
                    <div>
                        <custom-input
                            :dark-mode="darkMode"
                            label="Meeting URL"
                            v-model="meetingUrl"
                            placeholder="https://www.example.com"
                        />
                    </div>
                    <div>
                        <custom-button
                            :dark-mode="darkMode"
                            @click="updateUserLinks"
                            :disabled="saving"
                        >
                            {{ saving ? 'Saving...' : 'Save' }}
                        </custom-button>
                    </div>
                </div>
            </div>
        </div>

        <div class="border rounded-lg"
             :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
            <div class="p-5">
                <div class="flex flex-col pb-3">
                    <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight pb-2">Filter Presets</h5>
                    <p class="text-xs text-slate-500">Set presets for certain component filters</p>
                </div>
                <a href="/filter-presets" class="text-sm bg-primary-500 h-9 px-4 py-2 rounded-md hover:bg-primary-400 transition duration-200 text-white font-medium">Configure Presets</a>
            </div>
        </div>
    </div>

</template>

<script>
import Toggle from "../Inputs/Toggle/Toggle.vue";
import {UserSettingsApiFactory} from "../../../services/api/user_settings/factory.js";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import alertsMixin from "../../mixins/alerts-mixin.js";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";

export default {
    components: { LoadingSpinner, CustomButton, CustomInput, Toggle, AlertsContainer, Dropdown},
    data() {
        return {
            phoneForwardStatus: false,
            phoneForwardNumber: null,
            oldPhoneForwardNumber: null,
            api: null,
            alertActive: false,
            alertType: '',
            alertText: '',
            timezoneOptions: [
                {id: "-05:00", name: "Eastern"},
                {id: "-06:00", name: "Central"},
                {id: "-07:00", name: "Mountain"},
                {id: "-08:00", name: "Pacific"},
                {id: "-09:00", name: "Alaska"},
                {id: "-10:00", name: "Hawaii"},
            ],
            selectedTimezone: null,
            meetingUrl: '',
            loading: false,
            saving: false,
        }
    },
    mixins: [ alertsMixin ],
    props: {
        user: {
            type: Object,
            default: {},
        },
        apiDriver: {
            type: String,
            default: 'api'
        },
        darkMode : {
            type: Boolean,
            default: false,
        }
    },
    created() {
        this.api = UserSettingsApiFactory.makeApiService(this.apiDriver);
        this.getCallForwardingData();
        this.getTimezone();
        this.getUserLinks();
    },
    methods: {
        getCallForwardingData() {
            this.api.getCallForwardingData(this.user.id).then(resp => {
                this.phoneForwardStatus = resp.data.data[0].status === 1;
                this.phoneForwardNumber = resp.data.data[0].forward_to_number;
                this.oldPhoneForwardNumber = resp.data.data[0].forward_to_number;
            })
        },
        updateCallForwardingStatus() {
            this.api.updateCallForwardingStatus(this.user.id, this.phoneForwardStatus)
                .catch(error => {
                    this.phoneForwardStatus = !this.phoneForwardStatus;
                    this.activateAlert('error', 'There was an issue saving');
                });
            this.activateAlert('success', 'Saved');
        },
        updateCallForwardingNumber() {
            this.api.updateCallForwardingNumber(this.user.id, this.phoneForwardNumber)
                .then(resp => {
                    if('errors' in resp.data.data){
                        this.phoneForwardNumber = this.oldPhoneForwardNumber;
                        this.activateAlert('error', resp.data.data.errors);
                    }else{
                        this.phoneForwardNumber = resp.data.data[0].forward_to_number;
                        this.oldPhoneForwardNumber = resp.data.data[0].forward_to_number;
                        this.activateAlert('success', 'Saved');
                    }
                })
                .catch(error => {
                    this.phoneForwardNumber = this.oldPhoneForwardNumber;
                    this.activateAlert('error', 'There was an issue saving');
                });
        },
        // This method controls the Alert Container
        activateAlert(type, text) {
            this.alertActive = true;
            this.alertType = type;
            this.alertText = text;

            setTimeout(() => {
                this.alertActive = false;
                this.alertType = '';
                this.alertText = '';
            },1000)
        },
        getTimezone() {
            this.api.getUserTimezone(this.user.id).then(resp => {
                this.selectedTimezone = resp.data.data.timezone;
            });
        },
        saveTimezone() {
            this.api.updateUserTimezone(this.user.id, this.selectedTimezone).then(resp => {
                this.activateAlert('success', 'Saved timezone settings');
            });
        },
        getUserLinks() {
            this.loading = true;
            this.api.getUserLinks(this.user.id).then(response => {
                if (response.data.data.status) {
                    this.meetingUrl = response.data.data.user.meeting_url;
                }
                else {
                    this.showAlert(`An unknown error occurred fetching user data.`)
                }
            }).catch(err => {
                this.showAlert('error', err.response.data?.message || err.message || `An unknown error occurred fetching user data.`);
            }).finally(() => {
                this.loading = false;
            });
        },
        updateUserLinks() {
            this.saving = true;
            this.api.updateUserLinks(this.user.id, {
                meeting_url: this.meetingUrl ?? '',
            }).then(response => {
                if (!response.data.data.status) {
                    this.showAlert(`An unknown error occurred saving user data.`);
                }
            }).catch(err => {
                this.showAlert('error', err.response.data?.message || err.message || `An unknown error occurred fetching user data.`);
            }).finally(() => {
               this.saving = false;
            });
        }
    },
};
</script>
<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    /* display: none; <- Crashes Chrome on hover */
    -webkit-appearance: none;
    margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
}

input[type=number] {
    -moz-appearance:textfield; /* Firefox */
}
</style>
