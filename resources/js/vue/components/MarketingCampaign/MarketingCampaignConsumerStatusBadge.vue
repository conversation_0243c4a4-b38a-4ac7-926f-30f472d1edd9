<template>
    <badge
        :dark-mode="darkMode"
        :color="styles[status].badgeColor"
    >
        <div class="flex items-center gap-2">
            <simple-icon
                :dark-mode="darkMode"
                v-if="styles[status]?.badgeIcon"
                :icon="styles[status]?.badgeIcon"
            />
            {{titles[status]}}
        </div>
    </badge>
</template>

<script>
import Badge from "../Shared/components/Badge.vue";
import useMarketingCampaignConsumer from "../../../composables/useMarketingCampaignConsumer.js";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
const {styles, titles} = useMarketingCampaignConsumer()

export default {
    name: "MarketingCampaignConsumerStatusBadge" ,
    components: {SimpleIcon, Badge},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        status: {
            type: String,
        }
    },
    data() {
        return {
            styles,
            titles
        }
    }
}
</script>