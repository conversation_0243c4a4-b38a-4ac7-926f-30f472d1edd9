<template>
    <div>
        <div class="flex">
            <simple-alert
                :dark-mode="darkMode"
            >
                <template #content>
                    <span>
                        Currently new campaigns can only be created from
                    </span>
                    <a target="_blank" class="text-primary-500" href="/consumer-search">Consumer Search</a>
                </template>
            </simple-alert>
        </div>
        <email-marketing-table
            :base-filters="{type: [marketingCampaign.types.INTERNAL_EMAIL, marketingCampaign.types.MAILCHIMP_EMAIL]}"
            :dark-mode="darkMode"
        />
    </div>
</template>
<script>
import EmailMarketingTable from "./EmailMarketingTable.vue";
import SimpleAlert from "../Shared/components/SimpleAlert.vue";
import useMarketingCampaign from "../../../composables/useMarketingCampaign.js";
const marketingCampaign = useMarketingCampaign();
export default {
    name: "EmailMarketingCampaignsTab",
    components: {SimpleAlert, EmailMarketingTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            marketingCampaign,
        }
    }
}
</script>
