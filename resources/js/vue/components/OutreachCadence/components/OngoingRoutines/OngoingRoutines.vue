<template>
    <div class="px-10">
        <div class="border rounded-lg"
             :class="[darkMode ? 'bg-dark-module text-slate-50 border-dark-border' : 'bg-light-module text-slate-900 border-light-border']">
            <div class="grid grid-cols-2 items-start">
                <div class="border-r" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                    <div class="flex justify-between items-center p-5">
                        <h5 class="text-primary-500 text-sm uppercase font-bold leading-tight">Ongoing Routines</h5>
<!--                        <CustomButton :dark-mode="darkMode" color="slate-inverse">-->
<!--                            <svg class="mr-2 fill-current" width="15" height="14" viewBox="0 0 15 14" fill="none"-->
<!--                                 xmlns="http://www.w3.org/2000/svg">-->
<!--                                <path fill-rule="evenodd" clip-rule="evenodd"-->
<!--                                      d="M15 2.33333C15 2.76289 14.6269 3.11111 14.1667 3.11111L6.52369 3.11111C6.40056 3.43615 6.2006 3.73482 5.93443 3.98325C5.46559 4.42083 4.82971 4.66667 4.16667 4.66667C3.50363 4.66667 2.86774 4.42083 2.3989 3.98325C2.13273 3.73482 1.93278 3.43615 1.80964 3.11111L0.833335 3.11111C0.373098 3.11111 1.30974e-06 2.76289 1.32852e-06 2.33333C1.34729e-06 1.90378 0.373098 1.55556 0.833335 1.55556L1.80964 1.55556C1.93278 1.23052 2.13273 0.931843 2.3989 0.683417C2.86774 0.245832 3.50363 -5.02522e-07 4.16667 -4.7354e-07C4.82971 -4.44558e-07 5.46559 0.245832 5.93443 0.683417C6.2006 0.931843 6.40056 1.23052 6.52369 1.55556L14.1667 1.55556C14.6269 1.55556 15 1.90378 15 2.33333ZM15 7C15 7.42956 14.6269 7.77778 14.1667 7.77778L13.1904 7.77778C13.0672 8.10281 12.8673 8.40149 12.6011 8.64992C12.1323 9.0875 11.4964 9.33333 10.8333 9.33333C10.1703 9.33333 9.53441 9.0875 9.06557 8.64992C8.7994 8.40149 8.59944 8.10281 8.47631 7.77778L0.833334 7.77778C0.373098 7.77778 1.10576e-06 7.42956 1.12453e-06 7C1.14331e-06 6.57045 0.373098 6.22222 0.833334 6.22222L8.47631 6.22222C8.59944 5.89719 8.7994 5.59851 9.06557 5.35008C9.53441 4.9125 10.1703 4.66667 10.8333 4.66667C11.4964 4.66667 12.1323 4.9125 12.6011 5.35009C12.8673 5.59851 13.0672 5.89719 13.1904 6.22222L14.1667 6.22222C14.6269 6.22222 15 6.57045 15 7ZM15 11.6667C15 12.0962 14.6269 12.4444 14.1667 12.4444L6.52369 12.4444C6.40056 12.7695 6.2006 13.0682 5.93443 13.3166C5.46559 13.7542 4.82971 14 4.16667 14C3.50363 14 2.86774 13.7542 2.3989 13.3166C2.13273 13.0682 1.93278 12.7695 1.80964 12.4444L0.833334 12.4444C0.373097 12.4444 9.01769e-07 12.0962 9.20545e-07 11.6667C9.39322e-07 11.2371 0.373097 10.8889 0.833334 10.8889L1.80964 10.8889C1.93278 10.5639 2.13273 10.2652 2.3989 10.0168C2.86774 9.57917 3.50363 9.33333 4.16667 9.33333C4.82971 9.33333 5.46559 9.57917 5.93443 10.0168C6.2006 10.2652 6.40056 10.5639 6.52369 10.8889L14.1667 10.8889C14.6269 10.8889 15 11.2371 15 11.6667ZM11.6667 7C11.6667 6.79372 11.5789 6.59589 11.4226 6.45003C11.2663 6.30417 11.0543 6.22222 10.8333 6.22222C10.6123 6.22222 10.4004 6.30417 10.2441 6.45003C10.0878 6.59589 10 6.79372 10 7C10 7.20628 10.0878 7.40411 10.2441 7.54997C10.4004 7.69583 10.6123 7.77778 10.8333 7.77778C11.0543 7.77778 11.2663 7.69583 11.4226 7.54997C11.5789 7.40411 11.6667 7.20628 11.6667 7ZM5 2.33333C5 2.12705 4.9122 1.92922 4.75592 1.78336C4.59964 1.6375 4.38768 1.55556 4.16667 1.55556C3.94565 1.55556 3.73369 1.6375 3.57741 1.78336C3.42113 1.92922 3.33333 2.12705 3.33333 2.33333C3.33333 2.53961 3.42113 2.73744 3.57741 2.88331C3.73369 3.02917 3.94565 3.11111 4.16667 3.11111C4.38768 3.11111 4.59964 3.02917 4.75592 2.88331C4.9122 2.73744 5 2.53961 5 2.33333ZM5 11.6667C5 11.4604 4.9122 11.2626 4.75592 11.1167C4.59964 10.9708 4.38768 10.8889 4.16667 10.8889C3.94565 10.8889 3.73369 10.9708 3.57741 11.1167C3.42113 11.2626 3.33333 11.4604 3.33333 11.6667C3.33333 11.8729 3.42113 12.0708 3.57741 12.2166C3.73369 12.3625 3.94565 12.4444 4.16667 12.4444C4.38768 12.4444 4.59964 12.3625 4.75592 12.2166C4.9122 12.0708 5 11.8729 5 11.6667Z"/>-->
<!--                            </svg>-->
<!--                            Filters-->
<!--                        </CustomButton>-->
                    </div>
                    <div>
                        <div class="grid grid-cols-3 gap-3 mb-2 px-5">
                            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Company</p>
                            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Account Manager</p>
                            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Status</p>
                        </div>
                        <div v-if="loading"
                             class="border-t border-b h-100 overflow-y-auto divide-y flex items-center justify-center"
                             :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border']">
                            <loading-spinner></loading-spinner>
                        </div>
                        <div v-if="!loading" class="border-t border-b h-100 overflow-y-auto divide-y"
                             :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border']">
                            <div @click="selectOngoingRoutine(routine)" v-for="routine in ongoingRoutines"
                                 :key="routine"
                                 class="grid grid-cols-3 gap-x-3 py-3 px-5 group relative transition duration-100 items-center"
                                 :class="[selectedOngoingRoutine === routine ? (darkMode ? 'bg-slate-800 bg-opacity-50 text-primary-500 font-medium' : 'bg-primary-50 text-primary-500 font-medium') : (darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module')]">
                                <div class="absolute left-0 h-full w-1"
                                     :class="[selectedOngoingRoutine === routine ? (darkMode ? 'bg-primary-500' : 'bg-primary-500') : (darkMode ? 'bg-slate-600 invisible group-hover:visible' : 'bg-slate-400 invisible group-hover:visible') ]"></div>
                                <p class="text-sm">
                                    {{ routine.company.name }}
                                </p>
                                <p class="text-sm">
                                    {{ routine.accountManager?.user?.name ?? 'N/A' }}
                                </p>
                                <p class="text-sm">
                                    {{ routine.status }}
                                </p>
                                <div class="absolute right-5">
                                    <svg class="fill-current"
                                         :class="[selectedOngoingRoutine === routine ? (darkMode ? 'text-primary-500' : 'text-primary-500') : (darkMode ? 'text-dark-border' : 'text-light-border')]"
                                         width="7" height="12" viewBox="0 0 7 12" fill="none"
                                         xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                              d="M0.292893 0.292893C0.683417 -0.0976311 1.31658 -0.0976311 1.70711 0.292893L6.70711 5.29289C7.09763 5.68342 7.09763 6.31658 6.70711 6.70711L1.70711 11.7071C1.31658 12.0976 0.683417 12.0976 0.292893 11.7071C-0.0976311 11.3166 -0.0976311 10.6834 0.292893 10.2929L4.58579 6L0.292893 1.70711C-0.0976311 1.31658 -0.0976311 0.683417 0.292893 0.292893Z"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="p-3"></div>
                    </div>
                </div>
                <div class="relative h-full">
                    <div class="flex justify-between items-center p-5">
                        <div class="inline-flex items-center space-x-3">
                            <h5 class="text-primary-500 text-sm uppercase font-bold leading-tight">Details</h5>
                            <svg v-if="selectedOngoingRoutine" width="6" height="10" viewBox="0 0 6 10" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M0.292787 9.69471C0.105316 9.50718 0 9.25288 0 8.98771C0 8.72255 0.105316 8.46824 0.292787 8.28071L3.58579 4.98771L0.292787 1.69471C0.110629 1.50611 0.00983372 1.25351 0.0121121 0.991311C0.0143906 0.729114 0.11956 0.478302 0.304968 0.292894C0.490376 0.107485 0.741189 0.00231622 1.00339 3.78025e-05C1.26558 -0.00224062 1.51818 0.0985542 1.70679 0.280712L5.70679 4.28071C5.89426 4.46824 5.99957 4.72255 5.99957 4.98771C5.99957 5.25288 5.89426 5.50718 5.70679 5.69471L1.70679 9.69471C1.51926 9.88218 1.26495 9.9875 0.999786 9.9875C0.734622 9.9875 0.480314 9.88218 0.292787 9.69471Z"
                                      fill="#64748B"/>
                            </svg>
                            <p v-if="selectedOngoingRoutine" class="font-semibold">
                                {{ selectedOngoingRoutine.company.name }}</p>
                        </div>
                        <ActionsHandle :dark-mode="darkMode" v-if="selectedOngoingRoutine"
                                       :custom-actions="customActions" :no-custom-action="false" :no-edit-button="true"
                                       :no-delete-button="true"
                                       @terminate-ongoing-routine="confirmTerminate = true"></ActionsHandle>
                    </div>
                    <Modal :dark-mode="darkMode" v-if="confirmTerminate" :small="true" @confirm="terminateOngoingRoutine(selectedOngoingRoutine)"
                           @close="confirmTerminate = false" close-text="Cancel" confirm-text="Terminate">
                        <template v-slot:header>
                            <p class="font-semibold">Are you sure you want to terminate this routine?</p>
                            <p v-if="configuringContacts" class="font-semibold">Configure Routine Contacts</p>
                        </template>
                        <template v-slot:content></template>
                    </Modal>
                    <div v-if="selectedOngoingRoutine" class="h-[31rem] overflow-y-auto">
                        <div class="px-5 grid gap-5">
                            <DetailsGroup name="Queued For Execution" :dark-mode="darkMode"
                                          v-if="queuedActionGroups.length > 0"
                                          :actions="queuedActionGroups" @skip-action-group="skipActionGroup"
                                          @un-skip-action-group="unSkipActionGroup"/>
                            <DetailsGroup name="Pending Queue" :dark-mode="darkMode"
                                          v-if="futureActionGroups.length > 0"
                                          :actions="futureActionGroups" @skip-action-group="skipActionGroup"
                                          @un-skip-action-group="unSkipActionGroup"/>
                            <DetailsGroup name="Concluded" :dark-mode="darkMode" v-if="concludedActionGroups.length > 0"
                                          :actions="concludedActionGroups"/>
                            <DetailsGroup name="Skipped" :dark-mode="darkMode" v-if="skippedActionGroups.length > 0"
                                          :actions="skippedActionGroups"/>
                        </div>
                    </div>
                    <div v-else class="absolute inset-0 flex items-center justify-center">
                        <div class="text-center">
                            <div class="flex justify-center">
                                <svg width="23" height="19" viewBox="0 0 23 19" fill="none"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M14.8571 -4.99559e-08C15.1602 -3.67067e-08 15.4509 0.0790176 15.6653 0.21967C15.8796 0.360322 16 0.551088 16 0.75L16 2.25C16 2.44891 15.8796 2.63968 15.6653 2.78033C15.4509 2.92098 15.1602 3 14.8571 3L1.14286 3C0.839753 3 0.549062 2.92098 0.334735 2.78033C0.120408 2.63968 8.46629e-07 2.44891 8.55324e-07 2.25L9.20891e-07 0.749999C9.29586e-07 0.551087 0.120408 0.360321 0.334735 0.219669C0.549062 0.0790169 0.839753 -6.62675e-07 1.14286 -6.49426e-07L14.8571 -4.99559e-08ZM14.8571 4.5C15.1602 4.5 15.4509 4.57902 15.6653 4.71967C15.8796 4.86032 16 5.05109 16 5.25L16 6.75C16 6.94891 15.8796 7.13968 15.6653 7.28033C15.4509 7.42098 15.1602 7.5 14.8571 7.5L1.14286 7.5C0.839753 7.5 0.549062 7.42098 0.334735 7.28033C0.120408 7.13968 6.49928e-07 6.94891 6.58622e-07 6.75L7.2419e-07 5.25C7.32884e-07 5.05109 0.120408 4.86032 0.334735 4.71967C0.549062 4.57902 0.839753 4.5 1.14286 4.5L14.8571 4.5ZM16 9.75C16 9.55109 15.8796 9.36032 15.6653 9.21967C15.4509 9.07902 15.1602 9 14.8571 9L1.14286 9C0.839753 9 0.549062 9.07902 0.334735 9.21967C0.120407 9.36032 5.36183e-07 9.55109 5.27488e-07 9.75L4.61921e-07 11.25C4.53226e-07 11.4489 0.120407 11.6397 0.334735 11.7803C0.549062 11.921 0.839753 12 1.14286 12L14.8571 12C15.1602 12 15.4509 11.921 15.6653 11.7803C15.8796 11.6397 16 11.4489 16 11.25L16 9.75Z"
                                        fill="#CBD5E1"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                          d="M11.672 2.91098C11.6033 2.65478 11.4357 2.43636 11.2059 2.30378C10.9762 2.17119 10.7032 2.13529 10.447 2.20398C10.1908 2.27267 9.9724 2.44033 9.83981 2.67006C9.70723 2.89979 9.67133 3.17278 9.74002 3.42898L9.99902 4.39498C10.0677 4.65118 10.2354 4.8696 10.4651 5.00219C10.6948 5.13478 10.9678 5.17067 11.224 5.10198C11.4802 5.03329 11.6986 4.86564 11.8312 4.63591C11.9638 4.40617 11.9997 4.13318 11.931 3.87698L11.672 2.91098ZM7.42902 5.73998C7.30194 5.70534 7.16927 5.69614 7.03863 5.71292C6.90799 5.7297 6.78195 5.77212 6.66775 5.83775C6.55355 5.90338 6.45344 5.99092 6.37317 6.09535C6.2929 6.19978 6.23405 6.31904 6.20001 6.44627C6.16596 6.57351 6.15738 6.70622 6.17477 6.83678C6.19215 6.96734 6.23516 7.09319 6.30132 7.20708C6.36748 7.32097 6.45549 7.42067 6.56029 7.50045C6.6651 7.58023 6.78463 7.63853 6.91202 7.67198L7.87802 7.93098C8.13376 7.99814 8.40568 7.96128 8.63432 7.82847C8.86295 7.69566 9.02968 7.4777 9.09803 7.22228C9.16638 6.96686 9.13079 6.69476 8.99904 6.46551C8.8673 6.23626 8.65012 6.06852 8.39502 5.99898L7.42902 5.73998ZM16.243 5.17098C16.3359 5.07807 16.4095 4.96778 16.4597 4.84642C16.5099 4.72505 16.5358 4.59498 16.5357 4.46363C16.5357 4.33228 16.5098 4.20223 16.4595 4.08089C16.4091 3.95956 16.3354 3.84933 16.2425 3.75648C16.1496 3.66364 16.0393 3.59 15.918 3.53978C15.7966 3.48956 15.6665 3.46373 15.5352 3.46378C15.4038 3.46383 15.2738 3.48974 15.1524 3.54005C15.0311 3.59036 14.9209 3.66407 14.828 3.75698L14.121 4.46398C14.0281 4.55689 13.9544 4.66719 13.9041 4.78859C13.8538 4.90998 13.828 5.04009 13.828 5.17148C13.828 5.30288 13.8538 5.43298 13.9041 5.55438C13.9544 5.67577 14.0281 5.78607 14.121 5.87898C14.2139 5.97189 14.3242 6.04559 14.4456 6.09588C14.567 6.14616 14.6971 6.17204 14.8285 6.17204C14.9599 6.17204 15.09 6.14616 15.2114 6.09588C15.3328 6.04559 15.4431 5.97189 15.536 5.87898L16.243 5.17098ZM9.17202 12.243L9.87902 11.536C10.0668 11.3485 10.1724 11.0941 10.1726 10.8287C10.1728 10.5633 10.0675 10.3088 9.88002 10.121C9.69251 9.93321 9.43809 9.82761 9.17273 9.82743C8.90736 9.82724 8.65279 9.93247 8.46502 10.12L7.75702 10.827C7.56938 11.0146 7.46396 11.2691 7.46396 11.5345C7.46396 11.7998 7.56938 12.0543 7.75702 12.242C7.94466 12.4296 8.19916 12.535 8.46452 12.535C8.72988 12.535 8.98438 12.4296 9.17202 12.242V12.243ZM12.372 7.07198C12.1903 6.99924 11.9912 6.98143 11.7995 7.02077C11.6077 7.0601 11.4317 7.15485 11.2933 7.29326C11.1549 7.43168 11.0601 7.60767 11.0208 7.79942C10.9815 7.99117 10.9993 8.19025 11.072 8.37198L15.072 18.372C15.1437 18.551 15.2656 18.7054 15.423 18.8167C15.5804 18.928 15.7666 18.9915 15.9593 18.9994C16.1519 19.0073 16.3427 18.9594 16.5087 18.8614C16.6747 18.7634 16.8089 18.6195 16.895 18.447L18.275 15.688L21.293 18.708C21.4807 18.8955 21.7351 19.0008 22.0004 19.0007C22.2656 19.0006 22.52 18.8951 22.7075 18.7075C22.895 18.5198 23.0003 18.2654 23.0002 18.0001C23.0001 17.7349 22.8947 17.4805 22.707 17.293L19.688 14.273L22.448 12.894C22.6202 12.8076 22.7637 12.6734 22.8615 12.5075C22.9592 12.3415 23.0069 12.1508 22.9989 11.9584C22.9908 11.766 22.9274 11.58 22.8162 11.4227C22.705 11.2655 22.5508 11.1437 22.372 11.072L12.372 7.07198Z"
                                          fill="#64748B"/>
                                </svg>
                            </div>
                            <p class="text-slate-500">Select a Routine to see details</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ActionsHandle from "../../../Shared/components/ActionsHandle.vue";
import DetailsGroup from "./components/DetailsGroup.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import ApiService from "../../services/api";
import LoadingSpinner from "../../../LeadProcessing/components/LoadingSpinner.vue";
import Modal from "../../../Shared/components/Modal.vue";

export default {
    name: "OngoingRoutines",
    components: {Modal, LoadingSpinner, CustomButton, DetailsGroup, ActionsHandle},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            apiService: ApiService.make(),
            ongoingRoutines: [],
            filterParams: [],
            selectedOngoingRoutine: null,
            customActions: [
                {event: 'terminate-ongoing-routine', name: 'Terminate'},
            ],
            loading: true,
            confirmTerminate: false,
        }
    },
    computed: {
        queuedActionGroups() {
            if (!this.selectedOngoingRoutine)
                return null
            return this.selectedOngoingRoutine.scheduled_groups.filter(group => ['pending', 'queued'].includes(group.status))
        },
        futureActionGroups() {
            if (!this.selectedOngoingRoutine)
                return null
            return this.selectedOngoingRoutine.scheduled_groups.filter(group => group.status === 'not_started')
        },
        concludedActionGroups() {
            if (!this.selectedOngoingRoutine)
                return null
            return this.selectedOngoingRoutine.scheduled_groups.filter(group => group.status === 'concluded')
        },
        skippedActionGroups() {
            if (!this.selectedOngoingRoutine)
                return null
            return this.selectedOngoingRoutine.scheduled_groups.filter(group => group.status === 'skipped')
        },
    },
    mounted() {
        this.getRoutines();
    },
    methods: {
        selectOngoingRoutine(routine) {
            if (this.selectedOngoingRoutine === routine) {
                this.selectedOngoingRoutine = null;
            } else {
                this.selectedOngoingRoutine = routine;
            }
        },
        terminateOngoingRoutine(routine) {
            this.confirmTerminate = false;
            this.apiService.terminateRoutine(routine.id).then(() => {
                if (this.ongoingRoutines.includes(routine)) {
                    this.ongoingRoutines.splice(routine, 1);
                    this.selectedOngoingRoutine = null;
                }
            })
        },
        getRoutines() {
            this.apiService.getCompanyRoutines(this.filterParams).then((resp) => {
                this.ongoingRoutines = resp.data.data
                this.loading = false
            });
        },
        skipActionGroup(actionGroup) {
            this.apiService.skipActionGroup(actionGroup.id).then(() => actionGroup.skip = 1)
        },
        unSkipActionGroup(actionGroup) {
            this.apiService.unSkipActionGroup(actionGroup.id).then(() => actionGroup.skip = 0)
        },
    }
}
</script>

<style scoped>

</style>
