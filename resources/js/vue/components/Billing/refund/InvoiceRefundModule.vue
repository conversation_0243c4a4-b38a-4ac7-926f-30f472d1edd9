<template>
    <div
        class="border-y px-8 py-4 flex flex-col"
        :class="{'bg-light-background ': !darkMode, 'bg-dark-background': darkMode}"
    >
        <simple-alert
            v-if="showAlert"
            variant="light-red"
            :dark-mode="darkMode"
            :content="errorHandler?.message"
            dismissible
            @dismiss="showAlert = false"
        />
        <div class="flex mb-2 gap-2">
            <div class="text-lg font-bold">
                Refunds
            </div>
            <custom-button
                class="ml-auto"
                v-show="canCreateNewRefund"
                @click="toggleRefund"
                :dark-mode="darkMode"
            >
                {{ creatingRefund ? 'Cancel Refund' : 'Create Refund'}}
            </custom-button>
        </div>
        <div v-if="creatingRefund">
            <div class="font-semibold mb-2"> New Refund </div>
            <div
                class="grid border-b pb-1 font-semibold text-xs text-slate-500 uppercase grid-cols-9"
            >
                <div class="col-span-4">
                    Description
                </div>
                <div>
                    Quantity
                </div>
                <div>
                    Item Price
                </div>
                <div>
                    Added By
                </div>
                <div>
                    Amount
                </div>
            </div>
            <div class="flex flex-col gap-1 mb-2" v-for="(item) in invoiceRefundStore.refundableItems">
                <invoice-item
                    :dark-mode="darkMode"
                    :model-value="item"
                    disabled
                    actionable
                >
                    <template v-slot:actions>
                        <div class="flex justify-end items-center">
                            <simple-icon
                                :dark-mode="darkMode"
                                :icon="getInvoiceItemRefundActionIcon(item)"
                                clickable
                                @click="toggleRefundItem(item)"
                            />
                        </div>
                    </template>
                </invoice-item>
            </div>
            <div class="flex gap-2 mb-2 justify-between">
                <custom-input
                    placeholder="Enter a custom amount."
                    type="number"
                    v-model="invoiceRefundStore.newRefundPayload.custom_amount_refunded_in_dollars"
                    @update:modelValue="() => invoiceRefundStore.newRefundPayload.custom_amount_refunded = invoiceRefundStore.newRefundPayload.custom_amount_refunded_in_dollars * 100"
                >
                </custom-input>
                <div class="flex">
                    <dropdown
                        :dark-mode="darkMode"
                        placeholder="Select Reason"
                        :options="REFUND_REASON_OPTIONS"
                        v-model="invoiceRefundStore.newRefundPayload.refund_reason"
                    />
                </div>
                <div class="flex uppercase text-sm font-semibold">
                    <div class="mr-2">
                        Refund Value:
                    </div>
                    <div class="text-red-500">
                        {{$filters.centsToFormattedDollars(refundValue)}} / {{$filters.centsToFormattedDollars(invoiceRefundStore.invoiceAggregates.paid)}}
                    </div>
                </div>
                <custom-button
                    @click="handleToggleAll"
                    :dark-mode="darkMode"
                >
                    Toggle All
                </custom-button>
                <custom-button
                    @click="handleExecuteRefund"
                    :disabled="!refundEligible"
                    :dark-mode="darkMode"
                >
                    Execute Refund
                </custom-button>
            </div>
        </div>
        <div v-for="refund in invoiceRefundStore.invoiceRefunds">
            <div class="font-semibold mb-2 flex items-center gap-2">
                <span>{{refund.refunded_at}} - Refund #{{refund.id}} ({{refund.reason}})</span>
                <a
                    v-if="refund.related_lead_refund_id"
                    :href="`/lead-refunds?lead_refund_id=${refund.related_lead_refund_id}`"
                    target="_blank"
                    class="text-primary-500 hover:text-primary-400"
                >
                    View lead refund
                </a>
            </div>
            <div
                v-if="refund.items.length > 0"
                class="grid border-b pb-1 font-semibold text-xs text-slate-500 uppercase grid-cols-8"
            >
                <div class="col-span-4">
                    Description
                </div>
                <div>
                    Quantity
                </div>
                <div>
                    Item Price
                </div>
                <div>
                    Added By
                </div>
                <div class="flex justify-end">
                    Amount
                </div>
            </div>
            <div class="flex flex-col gap-1 mb-2" v-for="(item) in refund.items">
                <invoice-item
                    :dark-mode="darkMode"
                    :model-value="item"
                    disabled
                />
            </div>
            <div v-if="refund.custom_amount > 0" class="flex gap-2 mb-2 justify-between">
                <labeled-value
                    label="Custom Refund Amount"
                    orientation="horizontal"
                >
                    {{$filters.centsToFormattedDollars(refund.custom_amount)}}
                </labeled-value>
            </div>
        </div>
    </div>
</template>
<script>
import {useInvoiceRefundStore} from "../../../../stores/invoice/invoice-refund.store.js";
import CustomButton from "../../Shared/components/CustomButton.vue";
import InvoiceItem from "../components/CreateNewInvoice/InvoiceItem.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import useErrorHandler from "../../../../composables/useErrorHandler.js";
import SimpleAlert from "../../Shared/components/SimpleAlert.vue";
const simpleIcon = useSimpleIcon()
const REFUND_REASON_OPTIONS = [
    {name: "Duplicate", id: 'duplicate'},
    {name: "Fraudulent", id: 'fraudulent'},
    {name: "Customer Request", id: 'requested_by_customer'},
    {name: "Other", id: 'other'},
]

export default {
    name: "InvoiceRefundModule",
    components: {SimpleAlert, LabeledValue, CustomInput, Dropdown, SimpleIcon, InvoiceItem, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        invoiceId: {
            type: Number,
            required: true,
        },
        editable: {
            type: Boolean,
            default: true,
        }
    },
    data(){
        return {
            invoiceRefundStore: useInvoiceRefundStore(),
            creatingRefund: false,
            simpleIcon,
            errorHandler: useErrorHandler(),
            showAlert: false,
            REFUND_REASON_OPTIONS,
            loading: false,
        }
    },
    created() {
        this.getRefunds();
    },
    unmounted() {
        this.invoiceRefundStore.$reset();
    },
    computed: {
        canCreateNewRefund() {
            return this.editable && this.invoiceRefundStore.invoiceAggregates.paid > 0 // TODO - Permissions
        },
        refundValue() {
            const customAmountRefunded = Number(this.invoiceRefundStore.newRefundPayload.custom_amount_refunded);

            const totalItemsValue = this.invoiceRefundStore.newRefundPayload.items.reduce((previousValue, currentValue) => {
                return previousValue + (currentValue.unit_price * currentValue.quantity);
            }, 0);

            const totalRefund = customAmountRefunded + totalItemsValue;

            return totalRefund.toFixed(2);
        },
        refundEligible() {
            const hasReason = this.invoiceRefundStore.newRefundPayload.refund_reason !== null;

            const hasCustomAmountOrLead = this.invoiceRefundStore.newRefundPayload.items.length > 0
                || this.invoiceRefundStore.newRefundPayload.custom_amount_refunded > 0;

            const refundValueIsLessThanOrEqualTotalPaid = this.refundValue <= this.invoiceRefundStore.invoiceAggregates.paid

            return hasReason && hasCustomAmountOrLead && refundValueIsLessThanOrEqualTotalPaid
        }
    },
    methods: {
        async getRefunds() {
            this.loading = true;
            await this.invoiceRefundStore.getRefunds(this.invoiceId)
            this.loading = false;
        },
        toggleRefund() {
            this.creatingRefund = !this.creatingRefund
        },
        toggleRefundItem(item) {
            this.invoiceRefundStore.toggleItemRefunded(item)
        },
        getInvoiceItemRefundActionIcon(item){
           return this.invoiceRefundStore.isSelectedForRefund(item.invoice_item_id) ? simpleIcon.icons.X_MARK : simpleIcon.icons.CHECK
        },
        handleToggleAll(){
            const hasItems = this.invoiceRefundStore.newRefundPayload.items.length > 0;

            this.invoiceRefundStore.newRefundPayload.items = hasItems ? [] : [
                ...this.invoiceRefundStore.refundableItems
            ]
        },
        async handleExecuteRefund() {
            this.loading = true;
            try {
                const resp = await this.invoiceRefundStore.issueRefund(this.invoiceId);
                if (resp.data.data.status === true) {
                    this.invoiceRefundStore.$reset();
                    this.creatingRefund = false;
                    await this.getRefunds(this.invoiceId);
                }
            } catch (err) {
                this.errorHandler.handleError(err, 'Validation error')

                console.error(err)
                this.showAlert = true;
                setTimeout(() => {
                    this.showAlert = false;
                }, 4000)
            }
            this.loading = false;
        }
    }
}
</script>
