<template>
    <event-side-line-icon :icon="simpleIcon.icons.INFORMATION_CIRCLE" :icon-color="simpleIcon.colors.YELLOW" :dark-mode="darkMode"/>
    <div class="flex-auto">
        <div class="flex justify-between gap-x-4">
            <div class="flex items-center gap-1 py-0.5 text-xs leading-5 text-gray-500">
                Invoice chargeback created
                <time class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ daysAgo + ' (' + createdAt + ')' }}</time>
            </div>
            <div @click="show = !show" class="text-xs cursor-pointer font-semibold text-primary-500">{{show ? "Show less" : "Show more"}}</div>
        </div>
        <div v-if="show" class="flex gap-2 text-xs leading-6 text-gray-500">
            {{payload}}
        </div>
    </div>
</template>
<script>
import EventSideLineIcon from "../EventSideLineIcon.vue";
import useInvoiceHelper from "../../../../../../composables/useInvoiceHelper";
import SimpleIcon from "../../../../Mailbox/components/SimpleIcon.vue";
import Badge from "../../../../Shared/components/Badge.vue";
import useSimpleIcon from "../../../../../../composables/useSimpleIcon.js";
const invoiceHelper = useInvoiceHelper()
export default {
    name: "InvoiceChargebackCreated",
    components: {Badge, SimpleIcon, EventSideLineIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        createdAt: {
            type: String,
            default: null
        },
        daysAgo: {
            type: String,
            default: ""
        },
        title: {
            type: String,
            default: ""
        },
        payload: {
            type: Object,
            default: {}
        },
        icon: {
            type: String,
            default: null
        },
        iconColor: {
            type: String,
            default: null
        },
        eventType: {
            type: String,
            default: null,
        },
        totalEvents: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            simpleIcon: useSimpleIcon(),
            invoiceHelper,
            show: false,
        }
    },
}
</script>
