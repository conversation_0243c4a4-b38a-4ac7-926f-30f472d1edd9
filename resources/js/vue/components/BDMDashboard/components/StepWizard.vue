<script setup>
import CustomButton from "../../Shared/components/CustomButton.vue";
import {defineEmits, ref, watch} from "vue";
const props = defineProps({
    prospect: {},
    darkMode: false,
    themeClasses: '',
    themeBackgroundClasses: '',
    requiredFieldsFilled: false,
})

const steps = ref([
    {
        step: 1,
        name: 'Check for Duplicates',
        complete: false,
        questions: [
            {
                step: 1,
                message: 'Are there any duplicates in our system?',
                current: true,
                buttons: [
                    {text: 'Yes', action: 'trigger-event', event: 'select-dupe-prompt', color: 'primary-outline'},
                    {text: 'No', action: 'next-step', color: 'primary-outline'},
                ],
            },
        ]
    },
    {
        step: 2,
        name: 'Confirm Decision Maker',
        complete: false,
        questions: [
            {
                step: 1,
                message: 'Were you able to contact and confirm the Decision Maker?',
                current: true,
                buttons: [
                    {text: 'Yes', action: 'next-step-trigger-event', event: 'decision-maker-confirmed', color: 'primary-outline'},
                    {text: 'No Answer', action: 'next-step-trigger-event', event: 'add-note-for-no-answer', color: 'primary-outline'},
                    {text: 'Follow up needed', action: 'next-step-trigger-event', event: 'add-note-for-follow-up-needed', color: 'primary-outline'},
                    {text: 'Go back', action: 'previous-step', color: 'slate-outline'},
                ],
            },
        ]
    },
    {
        step: 3,
        name: 'Confirm Information',
        questions: [
            {
                step: 1,
                message: 'Does the rest of the Company’s Information check out?',
                current: true,
                buttons: [
                    {text: 'Yes', action: 'next-step', color: 'primary-outline'},
                    {text: 'Go back', action: 'previous-step', color: 'slate-outline'},
                ],
            },
        ]
    },
    {
        step: 4,
        name: 'Actions',
        questions: [
            {
                step: 1,
                message: 'If Required fields have been filled out and are valid, you may Convert Company or Book a Demo',
                current: true,
                buttons: [
                    {text: 'Convert Company', action: 'trigger-event', event: 'convert', color: 'primary', disabled: !props.requiredFieldsFilled , tooltip: 'Required fields are not filled or valid'},
                    {text: 'Book Demo', action: 'trigger-event', event: 'book-demo', color: 'primary', disabled: !props.requiredFieldsFilled , tooltip: 'Required fields are not filled or valid'},
                    {text: 'Next Company', action: 'trigger-event', event: 'next-company', color: 'primary-outline'},
                    {text: 'Go back', action: 'previous-step', color: 'slate-outline'},
                ],
            },
        ]
    },
])

watch(() => props.requiredFieldsFilled, (newValue) => {
    steps.value.forEach(step => {
        step.questions.forEach(question => {
            question.buttons.forEach(button => {
                if (button.action === 'trigger-event' && ['convert', 'book-demo'].includes(button.event)) {
                    button.disabled = !newValue;
                }
            });
        });
    });
});

const currentStep = ref(steps.value[0]);
function stepStatus(step) {
    if(currentStep.value.step - 1 <= steps.value.length) {
        if(currentStep.value.step === step.step) {
            return 'bg-primary-500 text-white'
        }
        else {
            if (step.complete) {
                return 'bg-emerald-400 text-white'
            }
            else {
                if(props.darkMode) {
                    return 'bg-slate-800'
                }
                else {
                    return 'bg-slate-300'
                }
            }
        }
    }
}

function nextStep() {
    if(currentStep.value.step < steps.value.length) {
        steps.value[currentStep.value.step - 1].complete = true;
        currentStep.value = steps.value[currentStep.value.step]
    }
}

function previousStep() {
    if(currentStep.value.step > 1) {
        steps.value[currentStep.value.step - 1].complete = false;
        currentStep.value = steps.value[currentStep.value.step - 2]
        currentStep.value.questions.forEach((question) => {
            question.current = false;
        })
        currentStep.value.questions[0].current = true;
    }
}

function nextQuestion(question) {
    currentStep.value.questions[question.step - 1].current = false;
    currentStep.value.questions[question.step].current = true;
}

function previousQuestion(question) {
    currentStep.value.questions[question.step - 1].current = false;
    currentStep.value.questions[question.step - 2].current = true;
}

const emit = defineEmits(['trigger-event']);
function triggerEvent(eventType) {
    emit('trigger-event', eventType);
}

function nextStepTrigger(eventType) {
    if(currentStep.value.step < steps.value.length) {
        steps.value[currentStep.value.step - 1].complete = true;
        currentStep.value = steps.value[currentStep.value.step]
    }
    triggerEvent(eventType)
}

function nextQuestionTrigger(question, eventType) {
    currentStep.value.questions[question.step - 1].current = false;
    currentStep.value.questions[question.step].current = true;
    triggerEvent(eventType)
}

function previousQuestionTrigger(question, eventType) {
    currentStep.value.questions[question.step - 1].current = false;
    currentStep.value.questions[question.step - 2].current = true;
    triggerEvent(eventType)
}
</script>

<template>
    <div class="rounded-lg p-5 border" :class="[themeClasses]">
        <div class="flex items-center gap-3 mb-5">
            <p v-for="step in steps" :key="step.step" class="font-semibold inline-flex items-center gap-2">
                        <span class="rounded-full w-8 h-8 inline-flex items-center justify-center"
                              :class="[stepStatus(step)]">
                            {{ step.step }}
                        </span>
                {{ step.name }}
                <svg v-if="step.step < steps.length" class="fill-current" :class="[darkMode ? 'text-slate-500' : 'text-slate-300']" width="10" height="17" viewBox="0 0 10 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M0.451018 15.6958C0.162232 15.4069 0 15.0152 0 14.6067C0 14.1982 0.162232 13.8065 0.451018 13.5176L5.52367 8.44496L0.451018 3.37231C0.170416 3.08178 0.0151482 2.69266 0.0186579 2.28877C0.0221677 1.88487 0.184174 1.49851 0.469783 1.2129C0.755392 0.927293 1.14175 0.765287 1.54565 0.761777C1.94955 0.758267 2.33866 0.913535 2.62919 1.19414L8.79092 7.35587C9.07971 7.64475 9.24194 8.03649 9.24194 8.44496C9.24194 8.85343 9.07971 9.24517 8.79092 9.53404L2.62919 15.6958C2.34032 15.9846 1.94857 16.1468 1.5401 16.1468C1.13164 16.1468 0.739892 15.9846 0.451018 15.6958Z"/>
                </svg>
            </p>
        </div>
        <div v-for="question in currentStep.questions" :key="question.step">
            <div v-if="question.current">
                <p class="mb-5">
                    {{ question.message }}
                </p>
                <div v-for="buttons in question" class="flex items-center gap-3">
                    <div v-for="button in buttons" :key="button.text">
                        <custom-button
                            v-if="button.action === 'next-question'"
                            @click="nextQuestion(question)"
                            :dark-mode="darkMode"
                            :color="button.color"
                            :disabled="button.disabled"
                            :tooltip="button.tooltip"
                        >
                            {{ button.text }}
                        </custom-button>
                        <custom-button
                            v-if="button.action === 'previous-question'"
                            @click="previousQuestion(question)"
                            :dark-mode="darkMode"
                            :color="button.color"
                            :disabled="button.disabled"
                            :tooltip="button.tooltip"
                        >
                            {{ button.text }}
                        </custom-button>
                        <custom-button
                            v-if="button.action === 'next-step'"
                            @click="nextStep"
                            :dark-mode="darkMode"
                            :color="button.color"
                            :disabled="button.disabled"
                            :tooltip="button.tooltip"
                        >
                            {{ button.text }}
                        </custom-button>
                        <custom-button
                            v-if="button.action === 'previous-step'"
                            @click="previousStep"
                            :dark-mode="darkMode"
                            :color="button.color"
                            :disabled="button.disabled"
                            :tooltip="button.tooltip"
                        >
                            {{ button.text }}
                        </custom-button>
                        <custom-button
                            v-if="button.action === 'trigger-event'"
                            @click.once="triggerEvent(button.event)"
                            :dark-mode="darkMode"
                            :color="button.color"
                            :disabled="button.disabled"
                            :tooltip="button.tooltip"
                        >
                            {{ button.text }}
                        </custom-button>
                        <custom-button
                            v-if="button.action === 'next-step-trigger-event'"
                            @click.once="nextStepTrigger(button.event)"
                            :dark-mode="darkMode"
                            :color="button.color"
                            :disabled="button.disabled"
                            :tooltip="button.tooltip"
                        >
                            {{ button.text }}
                        </custom-button>
                        <custom-button
                            v-if="button.action === 'next-question-trigger-event'"
                            @click.once="nextQuestionTrigger(question, button.event)"
                            :dark-mode="darkMode"
                            :color="button.color"
                            :disabled="button.disabled"
                            :tooltip="button.tooltip"
                        >
                            {{ button.text }}
                        </custom-button>
                        <custom-button
                            v-if="button.action === 'previous-question-trigger-event'"
                            @click.once="previousQuestionTrigger(question, button.event)"
                            :dark-mode="darkMode"
                            :color="button.color"
                            :disabled="button.disabled"
                            :tooltip="button.tooltip"
                        >
                            {{ button.text }}
                        </custom-button>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<style scoped>

</style>
