<template>
        <Modal
            :container-classes="'overflow-scroll max-h-[70vh] p-8'"
            :dark-mode="darkMode"
            :small="false"
            no-buttons
            @close="closeModal"
        >
            <template v-slot:header>
                <p class="font-semibold">Setup Opportunity Notifications</p>
            </template>
            <template v-slot:content>
                <div class="config--edit-modal px-1 relative" :class="darkMode ? 'darkmode' : ''">
                    <div class="mb-4">
                        <label class="block font-medium leading-6" :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                            Companies filter preset
                        </label>
                        <div class="flex gap-2 items-center justify-between w-full" >
                            <div class="flex-1 flex flex-col gap-1">
                                <p class="uppercase font-semibold text-xs" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Select an existing preset</p>
                                <Dropdown
                                    :disabled="presetName?.length > 0"
                                    v-model="localSelectedPreset"
                                    :dark-mode="darkMode"
                                    :options="presets"
                                    placeholder="Select"
                                />
                            </div>
                            <div class="px-4">
                                <p class="font-semibold uppercase">OR</p>
                            </div>
                            <div class="flex-1 flex gap-1 flex-col">
                                <p class="mb-2 uppercase font-semibold text-xs" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Create based on the current filter ({{totalCompaniesFiltered}} returned) *</p>
                                <div class="inline-flex gap-1">
                                    <div class="flex flex-col w-full">
                                        <input
                                            :disabled="localSelectedPreset"
                                            :readonly="localSelectedPreset"
                                            v-model="presetName"
                                            type="text"
                                            class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                            placeholder="Unique name"
                                        />
                                        <div class="text-sm text-red-700">{{ presetErrorMessage }}</div>
                                    </div>
                                    <div>
                                        <CustomButton
                                            :disabled="savingPreset || !!localSelectedPreset"
                                            :dark-mode="darkMode"
                                            @click="savePreset"
                                            icon
                                        >
                                            <template v-slot:icon>
                                                <svg v-if="savingPreset" aria-hidden="true" class="w-6 h-6 animate-spin fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/></svg>
                                            </template>
                                            Save
                                        </CustomButton>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <label class="block font-medium leading-6" :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                        Opportunity Notification Configuration
                    </label>
                    <div class="mb-1 text-sm text-red-700">{{ errorMessage }}</div>
                    <div class="grid grid-cols-2 gap-x-3">
                        <div class="mb-4">
                            <label class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Name *
                            </label>
                            <input
                                :disabled="!localSelectedPreset"
                                :readonly="!localSelectedPreset"
                                required
                                v-model="editingConfig.name"
                                type="text"
                                placeholder="Name"
                                class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                        </div>
                        <div class="flex items-center">
                            <label for="config-active"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Active
                                <input
                                    :disabled="!localSelectedPreset"
                                    :readonly="!localSelectedPreset"
                                    id="config-active"
                                    name="config-active"
                                    type="checkbox"
                                    v-model="editingConfig.active"
                                    style="background-color: rgb(0, 76, 149);"
                                    class="h-4 w-4 ml-4 rounded disabled:opacity-50 disabled:border-white border-blue-400 bg-dark-background text-blue-600"
                                >
                            </label>
                        </div>
                        <div class="mb-4">
                            <label for="config-send-day"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Day to Send
                            </label>
                            <select
                                :disabled="!localSelectedPreset"
                                id="config-send-day"
                                name="config-send-day"
                                v-model="editingConfig.send_day"
                                class="z-30 truncate cursor-pointer uppercase block w-full rounded-md border-0 py-1.5 pl-3 pr-10 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-blue-600 sm:text-sm sm:leading-6"
                                :class="{
                                    'text-gray-900': !this.darkMode,
                                    'hover:border-blue-400 border-blue-700 bg-dark-background text-blue-400': this.darkMode,
                                }">
                                <option :value="null"></option>
                                <option value="Monday">Monday</option>
                                <option value="Tuesday">Tuesday</option>
                                <option value="Wednesday">Wednesday</option>
                                <option value="Thursday">Thursday</option>
                                <option value="Friday">Friday</option>
                                <option value="Saturday">Saturday</option>
                                <option value="Sunday">Sunday</option>
                            </select>
                        </div>
                        <div class="mb-4 relative">
                            <label for="config-send-time"
                                   class="flex items-center gap-2 text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Time to send *
                                <InformationCircleIcon
                                    v-on:mouseleave="showSendTimeTooltip = false"
                                    v-on:mouseenter="showSendTimeTooltip = true"
                                    class="h-4 w-4"
                                />
                            </label>
                            <div v-if="showSendTimeTooltip"
                                 class="text-xs bg-primary-50 text-slate-900 top-5 shadow-xl absolute transition-all duration-150 p-2 rounded border-grey-200 z-20 mt-2">
                                Setting this field to 9:00am ensures that companies on the east coast receive emails at 9:00am in their timezone, while those on the west coast receive them 3 hours later.
                            </div>
                            <input
                                :disabled="!localSelectedPreset"
                                :readonly="!localSelectedPreset"
                                id="config-send-time"
                                name="config-send-time"
                                type="time"
                                v-model="editingConfig.send_time"
                                class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                            <span class="text-xs">
                                    Recipient company timezone, defaulting to Mountain Time.
                                </span>
                        </div>
                        <div class="mb-4">
                            <label for="config-frequency"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Frequency *
                            </label>
                            <select
                                :disabled="!localSelectedPreset"
                                id="config-frequency"
                                name="config-frequency"
                                v-model="editingConfig.frequency"
                                class="z-30 truncate cursor-pointer uppercase block w-full rounded-md border-0 py-1.5 pl-3 pr-10 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-blue-600 sm:text-sm sm:leading-6"
                                :class="{
                                        'text-gray-900': !this.darkMode,
                                        'hover:border-blue-400 border-blue-700 bg-dark-background text-blue-400': this.darkMode,
                                    }">
                                <option :value="null"></option>
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                                <option value="annually">Annually</option>
                            </select>
                        </div>
                        <div class="mb-4 relative">
                            <label for="config-frequency"
                                   class="text-sm font-medium leading-6 flex items-center gap-2"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Lead threshold *
                                <InformationCircleIcon
                                    v-on:mouseleave="showLeadThresholdTooltip = false"
                                    v-on:mouseenter="showLeadThresholdTooltip = true"
                                    class="h-4 w-4"
                                />
                            </label>
                            <div v-if="showLeadThresholdTooltip"
                                 class="text-xs bg-primary-50 text-slate-900 top-5 shadow-xl absolute transition-all duration-150 p-2 rounded border-grey-200 z-20 mt-2"
                            >
                                Define the quantity of leads created since the last notification was sent to inform companies about fresh missed leads
                            </div>
                            <input
                                :disabled="!localSelectedPreset"
                                :readonly="!localSelectedPreset"
                                v-model="editingConfig.lead_threshold"
                                type="text"
                                placeholder="Lead threshold"
                                class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                        </div>
                        <div class="mb-4">
                            <label for="config-email-template"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Email Template *
                            </label>
                            <Dropdown
                                placement="top"
                                :disabled="!localSelectedPreset"
                                :dark-mode="darkMode"
                                :options="notificationStore.emailTemplateOptions"
                                placeholder="Choose Option"
                                v-model="editingConfig.email_template_id"
                                :selected="editingConfig.email_template_id"
                            />
                        </div>
                        <div class="mb-4">
                            <label for="config-send-day"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Product Type *
                            </label>
                            <Dropdown
                                placement="top"
                                :disabled="!localSelectedPreset"
                                :dark-mode="darkMode"
                                :options="notificationStore.productTypeOptions"
                                placeholder="Choose Option"
                                v-model="editingConfig.product_type"
                                :selected="editingConfig.product_type"
                            />
                        </div>
                        <div class="col-span-2 my-4" v-if="editingConfig.email_template_id && notificationStore.emailTemplatePreview">
                            <h3 class="text-md">Preview</h3>
                            <iframe width="100%" height="500px" :srcdoc="notificationStore.emailTemplatePreview"></iframe>
                        </div>
                    </div>
                    <div v-if="modalError" class="lg:col-span-4 rounded-lg bg-red-100 p-4 mb-3">
                        <p class="text-sm font-medium text-red-800">{{ modalError }}</p>
                    </div>
                    <div class="flex justify-end gap-3 pt-3 sticky bottom-0" :class="darkMode ? 'bg-dark-module' : 'bg-white'">
                        <CustomButton color="slate-light" :dark-mode="darkMode" @click="closeModal">
                            Cancel
                        </CustomButton>
                        <CustomButton :disabled="isConfirmDisabled" :dark-mode="darkMode" @click="saveConfig">
                            <svg aria-hidden="true" v-if="savingConfig" class="w-6 h-6 mr-2 text-gray-200 animate-spin fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/></svg>
                            Save
                        </CustomButton>
                    </div>
                </div>
            </template>
        </Modal>

</template>

<script>
import CustomInput from '../../Shared/components/CustomInput.vue'
import ToggleSwitch from '../../Shared/components/ToggleSwitch.vue'
import LoadingSpinner from '../../Shared/components/LoadingSpinner.vue'
import AlertsContainer from '../../Shared/components/AlertsContainer.vue'
import Dropdown from '../../Shared/components/Dropdown.vue'
import Modal from '../../Shared/components/Modal.vue'
import {InformationCircleIcon} from "@heroicons/vue/solid";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import Tab from "../../Shared/components/Tab.vue";
import {useOpportunityNotificationStore} from "../../../../stores/opportunity-notification.store.js";
import HoverTooltip from "../../Shared/components/HoverTooltip.vue";
import {DialogOverlay} from "@headlessui/vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import {ApiFactory} from "../services/api/factory";
export default {
    name: "SetupOpportunityNotifications",
    components: {
        CustomButton,
        InformationCircleIcon,
        CustomInput,
        ToggleSwitch,
        LoadingSpinner,
        AlertsContainer,
        Dropdown,
        Modal,
        DialogOverlay,
        HoverTooltip,
        Autocomplete,
        Tab,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        selectedPreset: {
            default: null
        },
        totalCompaniesFiltered: {
            type: Number,
            default: null
        },
        filterInputs: {
            type: Object,
            default: null
        },
        presets: {
            type: Array,
            default: []
        }
    },
    data() {
        return {
            userPresetAPI: ApiFactory.makeApiService('api'),
            localSelectedPreset: null,
            editingConfig: {},
            modalError: null,
            notificationStore: useOpportunityNotificationStore(),
            showSendTimeTooltip: false,
            showLeadThresholdTooltip: false,
            presetName: '',
            presetErrorMessage: '',
            savingPreset: false,
            errorMessage: '',
            savingConfig: false,
        }
    },
    created() {
        this.fetchCompanyFilterPresets();
        this.fetchEmailTemplateOptions();
    },
    methods: {
        async saveConfig() {
            this.errorMessage = ''
            this.savingConfig = true

            try {
                await this.notificationStore.createOpportunityNotificationConfig({
                    ...this.editingConfig,
                    filter_preset: this.localSelectedPreset
                })

                this.closeModal();

            } catch (err) {
                this.errorMessage = err?.response?.data?.message ?? err.message
            }

            this.savingConfig = false
        },
        closeModal() {
            this.notificationStore.resetTemplate()
            this.localSelectedPreset = null
            this.editingConfig = {}
            this.modalError = null
            this.showSendTimeTooltip = false
            this.showLeadThresholdTooltip = false
            this.presetName = ''
            this.$emit('close');
        },
        fetchCompanyFilterPresets() {
            this.notificationStore.getCompanyFilterPresets();
        },
        fetchEmailTemplateOptions() {
            this.notificationStore.getEmailTemplateOptions();
        },
        savePreset () {
            this.presetErrorMessage = ''

            if (this.presets.find(p => p.name === this.presetName.trim())) {
                this.presetErrorMessage = 'Name must be unique'
                return
            }

            const payload = {
                name: this.presetName.trim(),
                value: this.filterInputs,
            }
            this.updatePresets(payload)
        },
        updatePresets (payload) {
            if (this.savingPreset) return
            this.savingPreset = true

            this.userPresetAPI.saveUserPreset(payload)
                .then(resp => {
                    const preset = resp.data.data.presets.find(e => e.name === this.presetName)
                    this.presets.push(preset)
                    this.localSelectedPreset = preset.id
                    this.presetName = ''
                })
                .catch(err => {
                    this.presetErrorMessage = err.message
                })
                .finally(() => {
                    this.savingPreset = false
                })
        },
    },

    computed: {
        isConfirmDisabled(){
            return !this.localSelectedPreset
                || !this.editingConfig?.name
                || !this.editingConfig?.send_time
                || !this.editingConfig?.frequency
                || !this.editingConfig?.lead_threshold
                || !this.editingConfig?.email_template_id
                || this.savingConfig
        }
    },

    watch: {
        selectedPreset(val){
            this.localSelectedPreset = val?.id
        }
    }
}
</script>

<style lang="postcss" scoped>
.config--edit-modal input:disabled {
    @apply bg-slate-50 text-slate-500 border-slate-200 shadow-none;
}
.config--edit-modal input {
    @apply border-grey-350 bg-light-module;
}
.config--edit-modal.darkmode input {
    @apply bg-dark-background text-blue-100 border-blue-800 shadow-none;
}
.config--edit-modal.darkmode input:disabled {
    @apply bg-dark-background text-white border-none shadow-none opacity-50;
}
</style>
