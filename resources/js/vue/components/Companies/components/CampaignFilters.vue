<template>
    <div v-if="canMangeFilters">
        <div class="flex items-center border-b pb-1" :class="[{'border-dark-border': darkMode}, {'border-light-border': !darkMode}]">
            <div class="flex items-center mr-5">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z" />
                </svg>
                <p class="font-semibold ml-1">Campaign Filters</p>
            </div>
            <CustomButton height="h-5" color="primary" @click="create" :disabled="saving || loading" :dark-mode="darkMode">+</CustomButton>
        </div>
        <LoadingSpinner v-if="loading" :dark-mode="darkMode"></LoadingSpinner>
        <div v-else>
            <div class="flex gap-5 items-end mt-4" v-if="editing">
                <div class="w-[12rem]">
                    <p class="text-sm mb-1 font-semibold">Filter</p>
                    <Dropdown :options="filterKeys" v-model="filter.key" placeholder="Select" :disabled="saving" :dark-mode="darkMode"/>
                </div>
                <div class="w-[12rem]">
                    <p class="text-sm mb-1 font-semibold">Operator</p>
                    <Dropdown :options="filterOperators" v-model="filter.operator" placeholder="Select" :disabled="saving" :dark-mode="darkMode"/>
                </div>
                <div>
                    <p class="text-sm mb-1 font-semibold">Value</p>
                    <CustomInput v-model="filter.value" :disabled="saving" :dark-mode="darkMode"/>
                </div>
                <CustomButton @click="save" :disabled="saving" :dark-mode="darkMode">{{ saveButtonText }}</CustomButton>
                <CustomButton color="slate" @click="cancel" :disabled="saving" :dark-mode="darkMode">Cancel</CustomButton>
            </div>
            <div v-else class="mt-3 w-max">
                <div v-for="campaignFilter in campaignFilters" class="flex items-center justify-between border-b first:border-t p-3"
                     :class="[{'border-dark-border bg-dark-175': darkMode}, {'border-light-border bg-gray-200': !darkMode}]">
                    <div class="flex gap-2 items-center text-sm">
                        <div>{{ getFilterName(campaignFilter.key) }}</div>
                        <div>{{ campaignFilter.operator.label }}</div>
                        <div>{{ campaignFilter.value }}</div>
                    </div>
                    <div class="ml-7 flex gap-2" :class="{'pointer-events-none opacity-50': saving}">
                        <SimpleIcon icon="pencil-square" color="blue" clickable @click="edit(campaignFilter)"/>
                        <SimpleIcon icon="trash" color="red" clickable @click="deleteFilter(campaignFilter.id)"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import CustomButton from "../../Shared/components/CustomButton.vue";
import {useFutureCampaignStore} from "../../Campaigns/Wizard/stores/future-campaigns.js";
import CustomInput from "../../Shared/components/CustomInput.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";

export default {
    name: "CampaignFilters",
    components: {SimpleIcon, LoadingSpinner, Dropdown, CustomInput, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        campaign: {
            type: Object,
            required: true
        }
    },
    emits:['show-alert'],
    data() {
        return {
            campaignStore: useFutureCampaignStore(),
            permissionStore: useRolesPermissions(),
            filterOperators: [],
            filterKeys: [],
            campaignFilters: [],
            filter: {},
            editing: false,
            saving: false,
            loading: false,
        }
    },
    mounted() {
        this.initialize();
    },
    computed: {
        canMangeFilters() {
            return this.campaign.campaign_filter_enabled && this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_CAMPAIGN_MANAGE_PERMISSION);
        },
        saveButtonText() {
            return this.filter.id ? 'Update' : 'Create';
        }
    },
    methods: {
        initialize() {
            this.campaignStore.apiService.getCampaignFilterOptions(this.campaign.reference)
                .then(resp => {
                    this.filterKeys = resp.data.data.filter_keys;
                    this.filterOperators = resp.data.data.filter_operators;
                })
                .catch(e => {
                    console.error(e);
                    this.$emit('show-alert', 'Something went wrong. Please try again.');
                });

            this.getFilters();
            this.resetFilter();
        },
        getFilters() {
            this.loading = true;
            this.campaignStore.apiService.getCampaignFilters(this.campaign.reference)
                .then(resp => this.campaignFilters = resp.data.data.campaign_filters)
                .catch(e => {
                    console.error(e);
                    this.$emit('show-alert', 'Something went wrong. Please try again.');
                })
                .finally(() => this.loading = false);
        },
        resetFilter(id = null, key = null, operator = null, value = null) {
            this.filter = {id: id, key: key, operator: operator, value: value}
        },
        create() {
            this.editing = true;
            this.resetFilter();
        },
        edit(filter) {
            this.editing = true;
            this.resetFilter(filter.id, filter.key, filter.operator.value, filter.value)
        },
        deleteFilter(filterId) {
            this.saving = true;
            this.campaignStore.apiService.deleteCampaignFilter(this.campaign.reference, filterId)
                .then(() => this.getFilters())
                .catch(e => {
                    console.error(e);
                    this.$emit('show-alert', 'Something went wrong. Please try again.');
                })
                .finally(() => this.saving = false);
        },
        cancel() {
            this.editing = false;
            this.resetFilter();
        },
        save() {
            if (!this.validate()) {
                return;
            }

            let response = {};
            this.saving = true;

            if (this.filter.id) {
                response = this.campaignStore.apiService.updateCampaignFilter(
                    this.campaign.reference,
                    this.filter.id,
                    {key: this.filter.key, operator: this.filter.operator, value: this.filter.value}
                );
            } else {
                response = this.campaignStore.apiService.createCampaignFilter(
                    this.campaign.reference,
                    {key: this.filter.key, operator: this.filter.operator, value: this.filter.value}
                );
            }

            response.then(() => {
                this.editing = false;
                this.resetFilter();
                this.getFilters();
            })
                .catch(e => {
                    console.error(e);
                    this.$emit('show-alert', 'Something went wrong. Please try again.');
                })
                .finally(() => this.saving = false);
        },
        validate() {
            if (!this.filter.key || !this.filter.operator || !this.filter.value) {
                this.$emit('show-alert', 'Please fill in all the mandatory fields.');
                return false;
            }

            return true;
        },
        getFilterName(filterKey) {
            return this.filterKeys.find(key => key.id === filterKey)?.name ?? filterKey;
        }
    }
}
</script>
