<template>
    <modal
        :dark-mode="darkMode"
        @close="closeModal"
        :small="true"
        :confirm-text="confirmText"
        :close-text="'Cancel'"
        @confirm="saveAction"
        :z-index-hundred="zIndexHundred"
    >
        <template v-slot:header>
            <h4 class="text-xl font-medium">{{ action ? 'Edit' : 'Add' }} Action</h4>
        </template>
        <template v-slot:content>
            <div class="grid gap-4">
                <alerts-container
                    v-if="alertActive"
                    :alert-type="alertType"
                    :text="alertText"
                    :dark-mode="darkMode"
                >
                </alerts-container>
                <div>
                    <p class="capitalize font-semibold text-sm" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Subject</p>
                    <div class="mt-2">
                        <custom-input v-model="subject" :dark-mode="darkMode"
                                      placeholder="Enter subject" type="text"/>
                    </div>
                </div>
                <div>
                    <p class="capitalize font-semibold text-sm" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Category</p>
                    <div class="mt-2">
                        <Dropdown
                            :placeholder="categories.placeholder"
                            :dark-mode="darkMode"
                            :options="categories.options"
                            v-model="categoryId"
                        />
                    </div>
                </div>
                <div>
                    <p class="capitalize font-semibold text-sm" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Display Date (optional)</p>
                    <div class="mt-2">
                        <custom-input v-model="displayDate" :dark-mode="darkMode"
                                      placeholder="Enter date" type="date"
                        />
                    </div>
                </div>
                <!-- todo tag users -->
                <div class="pb-10">
                    <p class="capitalize font-semibold text-sm mb-2" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Notes</p>
                    <wysiwyg-editor
                        v-model="note"
                        :dark-mode="darkMode"
                        auto-width="98%"
                        editor-height="350"
                    >
                    </wysiwyg-editor>
                </div>
            </div>
        </template>

    </modal>
</template>
<script>
import Modal from "../../Shared/components/Modal.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import WysiwygEditor from "../../Shared/components/WysiwygEditor.vue";
import SharedApiService from "../../Shared/services/api";
import ApiService from "../../Tasks/services/api";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import MarkdownEditorMixin from "../../Shared/mixins/markdown-editor-mixin";
import AlertsMixin from "../../../mixins/alerts-mixin";
import Dropdown from "../../Shared/components/Dropdown.vue";
export default {
    name: "CompanyUserActionModal",
    components: {
        Dropdown,
        AlertsContainer,
        WysiwygEditor,
        CustomInput,
        Modal
    },
    mixins: [
        MarkdownEditorMixin, AlertsMixin
    ],
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        action: {
            type: Object,
            default: () => {}
        },
        companyId: {
            type: Number,
            default: null
        },
        userId: {
            type: Number,
            default: null
        },
        taskId: {
            type: Number,
            default: 0
        },
        zIndexHundred: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            sharedApi: SharedApiService.make(),
            taskApi: ApiService.make(),
            categories: {
                options: [],
                placeholder: 'Select Category',
            },
            taggableUsers: [],


            actionId: this.action?.id || 0,
            subject: this.action?.subject || '',
            displayDate: this.action?.display_date || null,
            categoryId: this.action?.action_category_id || 0,
            note: this.action?.message || '',
            tags: this.action?.tags || [],
            tag_by_email: this.action?.tag_by_email || false,
            targetType: 'company_contact',
            companyContactId: this.action?.for_id || 0,
            updateSalesStatus: -1,

            confirmText: 'Save',
            saving: false,

            errorMessages: {
                getCategories: 'Error fetching Action Categories',
                saveAction: 'An unknown error occurred while saving an Action',
                getTaggableUsers: 'An unknown error occurred while fetching users',
            },
        }
    },
    async created() {
        await this.getActionCategories();
    },
    methods: {
        async getActionCategories() {
            return this.sharedApi.getActionCategories().then(response => {
                if (response.data.data.status) {
                    this.categories.options = response.data.data.categories;
                } else {
                    this.showAlert('error', this.errorMessages.getCategories);
                }
            }).catch(err => {
                this.showAlert('error', err.response?.data?.message || this.errorMessages.getCategories);
            });
        },
        saveAction(){
            if (this.saving)
                return;

            this.saving = true;
            this.confirmText = 'Saving...';

            this.taskApi.saveAction(
                this.actionId,
                this.taskId,
                this.userId,
                this.targetType,
                this.subject,
                this.note,
                this.categoryId,
                this.displayDate,
                this.tags,
                this.tags.length > 0 ? this.tag_by_email : false,
                this.updateSalesStatus
            ).then(() => {
                this.reloadActions();
                this.closeModal();
            }).catch(err => {
                this.showAlert('error', err.response?.data?.message || this.errorMessages.saveAction);
            }).finally(() => {
                this.confirmText = 'Save';
                this.saving = false;
            });
        },
        closeModal() {
            if (window.tinymce?.editors.length) {
                window.tinymce?.editors.forEach(editor => window.tinymce.remove(editor));
            }
            this.$emit('close');
        },
        reloadActions() {
            this.$emit('reload-actions');
        }
    }
}
</script>
