<template>
    <div>
        <div class="border rounded-lg"
             :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
        >
            <div class="p-5 grid grid-cols-2">
                <div class="inline-flex mr-auto">
                    <button
                        class="transition duration-200 bg-cyan-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                        @click="loadTestLeads"
                    >
                        Refresh
                    </button>
                </div>
                <div class="inline-flex ml-auto">
                    <button
                        class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                        @click="createTestLeadModal = true"
                    >
                        + Create A Test Lead
                    </button>
                </div>
            </div>
            <TestLeadsHeader
                :dark-mode="darkMode"
                :test-leads="testLeads"
            />
            <LoadingSpinner v-if="loadingLeads"/>
            <TestLeadsBody
                v-else
                :dark-mode="darkMode"
                :test-leads="testLeads"
            />
            <add-test-lead
                v-if="createTestLeadModal"
                :dark-mode="darkMode"
                @close-add-test-lead-module="createTestLeadModal = false"
                :company-id="companyId"
            />
        </div>
        <div class="p-3 flex items-center justify-end gap-2" :class="{'opacity-50 pointer-events-none': loadingLeads}">
            <div>
                <span class="text-sm text-slate-500">Results Per Page</span>
            </div>
            <div>
                <Dropdown
                    :dark-mode="darkMode" placement="top"
                    :options="perPageOptions"
                    v-model="perPage"
                    :selected="10"
                />
            </div>
            <Pagination
                :dark-mode="darkMode"
                :pagination-data="paginationData ?? {}"
                :show-pagination="true"
                @change-page="handlePageChange"
            />
        </div>
    </div>
</template>

<script setup>
import {onBeforeMount, ref, watch} from "vue";
import ApiService from "../services/api";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import AddTestLead from "../../Shared/modules/AddTestLead.vue";
import TestLeadsHeader from "../../TestLeads/Components/TestLeadsHeader.vue";
import TestLeadsBody from "../../TestLeads/Components/TestLeadsBody.vue";
import Pagination from "../../Shared/components/Pagination.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";

const props = defineProps({
    companyId: {
        type: Number,
        require: true
    },
    darkMode: {
        type: Boolean,
        default: false
    }
});

const testLeads = ref([]);
const loadingLeads = ref(false);
const createTestLeadModal = ref(false);
const paginationData = ref({});
const perPageOptions = [ { id: 10, name: "10" }, { id: 20, name: "20" }, { id: 50, name: "50" }, { id: 100, name: "100" } ];
const perPage = ref(10);
const page = ref(1);

const api = ApiService.make();

onBeforeMount(async () => {
    loadTestLeads();
});

const loadTestLeads = () => {
    loadingLeads.value = true;
    testLeads.value = [];

    api.getTestLeads(props.companyId, getParams()).then(resp => {
        const {data, links, meta} = resp.data;
        testLeads.value = data;
        paginationData.value = {...links, ...meta}
    }).finally(() => loadingLeads.value = false);
}

const getParams = () => {
    return {
        per_page: perPage.value,
        page: page.value
    };
};

const handlePageChange = ({ newPage }) => {
    page.value = newPage
    loadTestLeads();
}

watch(() => perPage.value, () => {
    page.value = 1;
    loadTestLeads();
});
</script>
