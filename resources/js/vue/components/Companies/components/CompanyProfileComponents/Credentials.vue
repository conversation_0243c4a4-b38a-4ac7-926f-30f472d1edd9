<template>
    <div class="m-6">
        <div class="grid lg:grid-cols-2 gap-8">
            <div>
                <p class="font-semibold">Licenses</p>
                <div class="flex gap-4 mb-3">
                    <div class="flex-grow">
                        <p class="text-sm font-medium mb-1">Add a license</p>
                        <div class="flex items-center gap-1">
                            <CustomInput class="flex-grow" :dark-mode="darkMode" v-model="newLicense" type="text"></CustomInput>
                            <custom-button :dark-mode="darkMode" color="primary-outline" class="w-8 justify-center" v-on:click="addNewLicense" v-on:keyup.enter="addNewLicense">+</custom-button>
                        </div>
                    </div>
                </div>
                <div class="w-full inline-flex gap-2">
                    <p class="text-sm font-medium mb-1">Licenses</p>
                    <p class="text-sm font-medium mb-1 text-slate-500">({{licenses.length}})</p>
                </div>
                <div class="w-full border rounded-lg p-2 h-64 overflow-y-auto" :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border']">
                    <div class="relative flex items-center p-2 rounded-md group cursor-pointer border border-transparent" v-on:click="removeLicense(license)" :class="[darkMode ? 'hover:bg-dark-module hover:border-dark-border' : 'hover:bg-light-module hover:border-light-border']"
                         v-for="license in licenses">
                        {{license}}
                        <svg class="absolute fill-current w-3 right-4 invisible group-hover:visible text-rose-400" width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 5H2V18C2 18.5304 2.21071 19.0391 2.58579 19.4142C2.96086 19.7893 3.46957 20 4 20H14C14.5304 20 15.0391 19.7893 15.4142 19.4142C15.7893 19.0391 16 18.5304 16 18V5H3ZM13.618 2L12 0H6L4.382 2H0V4H18V2H13.618Z"/>
                        </svg>
                    </div>
                </div>
            </div>
            <div>
                <p class="font-semibold">Certifications</p>
                <div class="flex gap-4 mb-3">
                    <div class="flex-grow">
                        <p class="text-sm font-medium mb-1">Add a certification</p>
                        <div class="flex items-center gap-1">
                            <CustomInput class="flex-grow" :dark-mode="darkMode" v-model="newCertification" type="text"></CustomInput>
                            <custom-button :dark-mode="darkMode" color="primary-outline" class="w-8 justify-center" v-on:click="addNewCertification" v-on:keyup.enter="addNewCertification">+</custom-button>
                        </div>
                    </div>
                </div>
                <div class="w-full inline-flex gap-2">
                    <p class="text-sm font-medium mb-1">Certifications</p>
                    <p class="text-sm font-medium mb-1 text-slate-500">({{certifications.length}})</p>
                </div>
                <div class="w-full border rounded-lg p-2 h-64 overflow-y-auto" :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border']">
                    <div class="relative flex items-center p-2 rounded-md group cursor-pointer border border-transparent" v-on:click="removeCertification(certification)" :class="[darkMode ? 'hover:bg-dark-module hover:border-dark-border' : 'hover:bg-light-module hover:border-light-border']"
                         v-for="certification in certifications">
                        {{certification}}
                        <svg class="absolute fill-current w-3 right-4 invisible group-hover:visible text-rose-400" width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 5H2V18C2 18.5304 2.21071 19.0391 2.58579 19.4142C2.96086 19.7893 3.46957 20 4 20H14C14.5304 20 15.0391 19.7893 15.4142 19.4142C15.7893 19.0391 16 18.5304 16 18V5H3ZM13.618 2L12 0H6L4.382 2H0V4H18V2H13.618Z"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
        <custom-button :dark-mode="darkMode" :disabled="!caneSave" class="my-6" v-on:click="save">Save Changes</custom-button>
    </div>
</template>

<script>

import ApiService from "../../services/api";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import CustomInput from "../../../Shared/components/CustomInput.vue";

export default {
    name: 'Credentials',
    components: {
        CustomInput,
        CustomButton,
        Dropdown
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        hasEditRights: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            default: null
        },
    },
    data() {
        return {
            api: ApiService.make(),
            caneSave: false,
            licenses: [],
            certifications: [],
            newLicense: null,
            newCertification: null,
        }
    },
    created() {
        if (this.companyId) {
            this.getCredentials()
        }
    },
    methods: {
        getCredentials() {
            this.api.getCredentials(this.companyId).then(resp => {
                this.licenses = resp.data.data.licenses
                this.certifications = resp.data.data.certifications
            })
        },
        removeLicense(license) {
            const index = this.licenses.indexOf(license)
            if (index > -1)
                this.licenses.splice(index, 1)
            this.caneSave = true
        },
        removeCertification(certification) {
            const index = this.certifications.indexOf(certification)
            if (index > -1)
                this.certifications.splice(index, 1)
            this.caneSave = true
        },
        addNewLicense() {
            if (this.newLicense && this.licenses.indexOf(this.newLicense) < 0) {
                this.licenses.push(this.newLicense)
                this.newLicense = null
                this.caneSave = true
            }
        },
        addNewCertification() {
            if (this.newCertification && this.certifications.indexOf(this.newCertification) < 0) {
                this.certifications.push(this.newCertification)
                this.newCertification = null
                this.caneSave = true
            }
        },
        save() {
            if (this.caneSave)
                this.api.updateCredentials(this.companyId, this.licenses, this.certifications).then(() => {
                    this.caneSave = false
                    this.newLicense = null
                    this.newCertification = null
                })
        }
    }
}
</script>
