<template>
    <modal :dark-mode="darkMode" hide-confirm>
        <template v-slot:header>Auxiliary Campaigns</template>
        <template v-slot:content>
            <custom-button :dark-mode="darkMode" @click="createNewCampaign">Create New</custom-button>
            <div class="mt-4">
                <div v-for="(campaign, index, i) in campaigns" class="border-b border-amber-500 p-4 mt-4">
                    <div>
                        <Badge class="border border-amber-600" :dark-mode="darkMode" color="amber">{{ 1 + i++ }}</Badge>
                    </div>
                    <div class="inline-flex items-center gap-2 mb-3">
                        <label class="font-medium">Include Weekends?</label>
                        <toggle-switch :dark-mode="darkMode" v-model="campaign['include_weekends']"></toggle-switch>
                    </div>
                    <div class="grid grid-cols-3 gap-3 mb-3">
                        <div>
                            <label class="font-medium">Budget</label>
                            <custom-input :dark-mode="darkMode" v-model="campaign['budget']" type="numeric"></custom-input>
                        </div>

                        <div>
                            <label class="font-medium">Price Type</label>
                            <dropdown :dark-mode="darkMode" v-model="campaign['price_type']" :selected="campaign['price_type']" :options="priceTypeOptions" type="numeric"></dropdown>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 md:grid-cols-5 gap-3 mb-3" v-if="campaign['price_type'] === 'flat'">
                        <div>
                            <label class="font-medium">Exclusive</label>
                            <custom-input :dark-mode="darkMode" v-model="campaign['flat_prices']['exclusive']" type="numeric"></custom-input>
                        </div>
                        <div>
                            <label class="font-medium">Duo</label>
                            <custom-input :dark-mode="darkMode" v-model="campaign['flat_prices']['duo']" type="numeric"></custom-input>
                        </div>
                        <div>
                            <label class="font-medium">Trio</label>
                            <custom-input :dark-mode="darkMode" v-model="campaign['flat_prices']['trio']" type="numeric"></custom-input>
                        </div>
                        <div>
                            <label class="font-medium">Quad</label>
                            <custom-input :dark-mode="darkMode" v-model="campaign['flat_prices']['quad']" type="numeric"></custom-input>
                        </div>
                        <div>
                            <label class="font-medium">Unverified</label>
                            <custom-input :dark-mode="darkMode" v-model="campaign['flat_prices']['unverified']" type="numeric"></custom-input>
                        </div>
                    </div>

                    <div v-else class="grid grid-cols-3 gap-3 mb-3">
                        <div>
                            <label class="font-medium capitalize">{{campaign['price_type'].replace('-', ' ')}}</label>
                            <custom-input :dark-mode="darkMode" v-model="campaign['price_value']" type="numeric"></custom-input>
                        </div>
                    </div>

                    <div class="flex gap-2 mb-3">
                        <custom-button @click="deleteCampaign(index)" class="bg-red-400 hover:bg-red-500">Delete</custom-button>
                    </div>

                </div>
            </div>

        </template>
    </modal>
</template>

<script>

import Locations from "./Locations.vue";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import CustomCheckbox from "../../../Shared/SlideWizard/components/CustomCheckbox.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import MultiSelect from "../../../Shared/components/MultiSelect.vue";
import Modal from "../../../Shared/components/Modal.vue";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";
import Badge from "../../../Shared/components/Badge.vue";

export default {
    name: "AuxiliaryCampaignModal",
    components: {Badge, ToggleSwitch, Modal, MultiSelect, CustomButton, CustomCheckbox, Dropdown, CustomInput, Locations},
    data: function () {
        return {
            campaigns: {},
            priceTypeOptions: [{id: 'percentage-discount', name:'Percentage Discount'}, {id: 'dollar-discount', name:'Dollar Discount'}, {id: 'flat', name: 'Flat'}]
        }
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        campaigns: {},
    },
    created() {

    },
    methods: {
        createNewCampaign(){
            this.$props.campaigns[this.generateUuid()] = {
                "include_weekends": true,
                "budget": 500,
                "price_type": "flat",
                "price_value": 5,
                "flat_prices": {
                    "exclusive": 100,
                    "duo": 80,
                    "trio": 60,
                    "quad": 40,
                    "unverified": 10,
                }
            }
        },
        generateUuid(){
            return "10000000-1000-4000-8000-100000000000".replace(/[018]/g, c =>
                (+c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> +c / 4).toString(16)
            );
        },
        deleteCampaign(index){
            delete this.$props.campaigns[index];
        },
    },
    computed: {

    }
}
</script>

<style scoped>

</style>
