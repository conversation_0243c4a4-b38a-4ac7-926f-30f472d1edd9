<template>
    <modal :dark-mode="darkMode" small :disable-confirm="!newCompany || !newCampaign" @confirm="setCampaign">
        <template v-slot:header>Campaign Selection</template>
        <template v-slot:content>
            <div v-if="campaign" class="mb-4 p-4 bg-primary-100 rounded flex justify-between items-center">
                <div>
                    <div>Company: <b>{{campaign.companyName}}</b></div>
                    <div>Campaign: <b>{{campaign.campaignName}}</b></div>
                </div>
                <custom-button :dark-mode="darkMode" class="text-red-500" color="" @click="this.$emit('clear-campaign')">Clear</custom-button>
            </div>

            <div class="mb-4">
                <label>Company</label>
                <company-search-autocomplete :dark-mode="darkMode" emit-value="company-payload" @update:modelValue="setCompany"></company-search-autocomplete>
            </div>

            <div v-if="campaignOptions">
                <label>Campaign</label>
                <dropdown :dark-mode="darkMode" @change="(campaignOption) => newCampaign=campaignOption" :model-value="newCampaign?.id" :options="campaignOptions" :placeholder="campaignOptions.length > 0 ? 'Choose Campaign' : 'No Campaigns Found'"></dropdown>

                <div class="mt-4 flex gap-1 justify-center">
                    <custom-checkbox :dark-mode="darkMode" v-model="useCampaignLocations"></custom-checkbox>
                    <label>Use Campaign Locations</label>
                </div>
                <div class="flex justify-center">
                    <div v-if="useCampaignLocations && hasLocationsSelected" class="text-sm text-red-500">*This will overwrite your current location selections</div>
                </div>

            </div>

        </template>
    </modal>
</template>

<script>

import Locations from "./Locations.vue";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import CustomCheckbox from "../../../Shared/SlideWizard/components/CustomCheckbox.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import MultiSelect from "../../../Shared/components/MultiSelect.vue";
import Modal from "../../../Shared/components/Modal.vue";
import CompanySearchAutocomplete from "../../../Shared/components/Company/CompanySearchAutocomplete.vue";
import SharedApiService from "../../../Shared/services/api.js";

export default {
    name: "CampaignSelectionModal",
    components: {
        CompanySearchAutocomplete,
        Modal, MultiSelect, CustomButton, CustomCheckbox, Dropdown, CustomInput, Locations},
    data: function () {
        return {
            sharedApi: SharedApiService.make(),
            newCompany: null,
            newCampaign: null,
            company: null,
            campaign: null,
            campaignOptions: null,
            useCampaignLocations: true,
        }
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        apiService: null,
        campaign: null,
        hasLocationsSelected: false,
    },
    created() {

    },
    methods: {
        setCompany(company){
            this.newCompany = company
            this.getCompanyCampaigns(company.id)
        },
        getCompanyCampaigns(companyId){
            this.sharedApi.searchCompanyCampaigns(companyId).then(res => {
                this.campaignOptions = res.data.data.status === true ? res.data.data.results : []
            })
        },
        setCampaign(){
            this.$emit('set-campaign', {
                'companyId': this.newCompany.id,
                'companyName': this.newCompany.name,
                'campaignId': this.newCampaign.id,
                'campaignName': this.newCampaign.name,
                'useCampaignLocations': this.useCampaignLocations,
            });
        },
    },
    computed: {

    }
}
</script>

<style scoped>

</style>
