import axios from "axios";

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'company-users', 1);
    }

    getCompanyUsers(params = {}) {
        return this.axios().get('/', {
            params
        })
    }

    getFilterOptions() {
        return this.axios().get('/filter-options')
    }

}