import DispatchesGlobalEventsMixin from "../../../mixins/dispatches-global-events-mixin";
export default {
    data: function () {
        return {
            loading: true,
            data: {
                lead: null,
                consumerProductData: null
            },
            locked: false,
            publicComment: null
        }
    },
    mixins: [DispatchesGlobalEventsMixin],
    props: ["api", "consumerApi", "minimumReviewTimeMillisecs", "nextLeadRecheckTimeMillisecs", "initialLeadId", "displayedQueue"],
    created() {
        this.getNextLead(this.initialLeadId ? (Number(this.initialLeadId) ? Number(this.initialLeadId) : null) : null);
    },
    methods: {
        getNextLead(specificLeadId = null) {
            this.loading = true;
            this.locked = true;

            this.getLeadConsumerData(specificLeadId).then(async resp => {
                if (resp.data.data.status) {
                    this.data.consumerProductData = resp.data.data;
                } else {
                    this.showAlert('error', this.error);
                }
            }).catch(e => {
                this.$emit('activate-alert', {
                    type: 'error',
                    text: e.response.data?.data?.msg || 'Error getting next lead'
                });
            }).finally(() => {
                this.loading = false;
                setTimeout(() => this.locked = false, this.minimumReviewTimeMillisecs);
            });
        },

        async getLeadConsumerData(leadId){
            return this.consumerApi.getConsumerProductLead(leadId );
        },

        async lockLead() {
            try {
                const lockRes = await this.consumerApi.lockConsumerProduct(this.lead.basic.id);

                return lockRes.data.data.status;
            }
            catch(e) {
                this.$emit('activate-alert', {
                    type: 'error',
                    text: e.response?.data?.data?.msg || 'Error locking lead'
                });

                throw e;
            }
        },

        async cancelConsumerProduct(reason, publicComment = '') {
            this.loading = true;

            const locked = await this.lockLead();

            if(!locked) {
                this.$emit('activate-alert', {
                    type: 'error',
                    text: 'Failed to lock lead. Is it already locked by someone else?'
                });

                return;
            }

            try {
                const cancelRes = await this.consumerApi.cancelConsumerProduct(this.lead.basic.id, reason, publicComment);

                if(cancelRes.data.data.status === true) {
                    location.reload();
                }
                else {
                    this.$emit('activate-alert', {
                        type: 'error',
                        text: cancelRes.data?.data?.msg || "Could not cancel lead"
                    });

                    this.loading = false;
                }
            }
            catch(e) {
                this.$emit('activate-alert', {
                    type: 'error',
                    text: e.response.data?.data?.msg || 'Error cancelling lead'
                });

                this.loading = false;
            }
        },
    }
}
