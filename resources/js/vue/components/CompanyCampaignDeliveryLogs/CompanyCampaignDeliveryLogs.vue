<template>
    <div class="main-layout font-body">
        <div class="w-full">
            <div class="w-full flex-auto relative"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div :class="[darkMode ? 'text-white' : 'text-slate-900']">
                    <div class="flex flex-col px-5 pt-5 border-b mb-5 gap-4"
                         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
                        <a href="/"
                           class="text-base text-primary-500 font-medium pb-0 leading-none mr-5 mb-4 inline-flex items-center pl-4">
                            <svg class="mr-2" width="7" height="12" viewBox="0 0 7 12" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M6.70711 11.7071C6.31658 12.0976 5.68342 12.0976 5.29289 11.7071L0.292894 6.70711C-0.0976305 6.31658 -0.0976304 5.68342 0.292894 5.29289L5.29289 0.292893C5.68342 -0.0976316 6.31658 -0.0976315 6.70711 0.292893C7.09763 0.683417 7.09763 1.31658 6.70711 1.70711L2.41421 6L6.70711 10.2929C7.09763 10.6834 7.09763 11.3166 6.70711 11.7071Z"
                                      fill="#0081FF"/>
                            </svg>
                            Back
                        </a>
                        <div class="flex items-center justify-between flex-wrap pl-4">
                            <h3 class="text-xl font-medium pb-0 leading-none mr-5">Company Campaign Delivery Logs</h3>
                        </div>
                        <div class="flex justify-between">
                            <tab
                                :dark-mode="darkMode"
                                :tabs="tabTitles"
                                @selected="selectTab"
                                tab-style="fit"
                                background-color="light"
                                :tab-type="'Normal'"
                            />
                        </div>
                    </div>
                    <component :is="currentTabComponent" :dark-mode="darkMode"/>
                </div>
            </div>
        </div>
        <AlertsContainer
            v-if="alertActive"
            :alert-type="alertType"
            :text="alertText"
            :dark-mode="darkMode"
        />
    </div>
</template>

<script>
import Tab from "../Shared/components/Tab.vue";
import {markRaw} from "vue";
import CampaignCrmDeliveryLogsTab from "./Tabs/CampaignCrmDeliveryLogsTab.vue";
import CampaignContactDeliveryLogsTab from "./Tabs/CampaignContactDeliveryLogsTab.vue";
import AlertsMixin from "../../mixins/alerts-mixin.js";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import useQueryParams from "../../../composables/useQueryParams.js";
import {useRolesPermissions} from "../../../stores/roles-permissions.store.js";

const DEFAULT_SELECTED_TAB = "CRM Delivery Logs";
export default {
    name: "CompanyCampaignDeliveryLogs",
    components: {AlertsContainer, Tab},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    mixins: [AlertsMixin],
    created() {
        const { tab = DEFAULT_SELECTED_TAB } = this.queryParamsHelper.getCurrentParams();
    },
    data() {
        return {
            permissionStore: useRolesPermissions(),
            tabTitles: [
                {name: DEFAULT_SELECTED_TAB, current: true, component: markRaw(CampaignCrmDeliveryLogsTab)},
                {name: "Contact Delivery Logs", current: false, component: markRaw(CampaignContactDeliveryLogsTab)},
            ],
            selectedTab: null,
            queryParamsHelper: useQueryParams()
        }
    },
    computed: {
        currentTabComponent() {
            return this.tabTitles.find(e => e.current)?.component
        }
    },
    methods: {
        setSelectedTab(tab) {
            this.selectedTab = tab;
            this.tabTitles.forEach(e => {
                e.current = e.name === this.selectedTab
            })
        },
        selectTab(tab) {
            this.queryParamsHelper.setQueryParamsOnCurrentUrl({tab})
            this.setSelectedTab(tab)
        },
    }

}
</script>

<style scoped>

</style>
