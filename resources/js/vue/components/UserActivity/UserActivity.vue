<template>
    <div class="main-layout font-body">
        <alerts-container :text="alertText" :alert-type="alertType" v-if="alertActive" :dark-mode="darkMode"/>
        <div class="w-full">
            <div class="w-full flex-auto relative"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">

                <div class="px-10 border-b relative" :class="[darkMode ? 'text-white bg-dark-module border-dark-border' : 'bg-light-module border-light-border text-slate-900']">
                    <div class="flex items-center justify-between flex-wrap pt-4 pb-5">
                        <a href="/dashboard" class="text-base text-primary-500 font-medium pb-0 leading-none mr-5 inline-flex items-center">
                            <svg class="mr-2" width="7" height="12" viewBox="0 0 7 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M6.70711 11.7071C6.31658 12.0976 5.68342 12.0976 5.29289 11.7071L0.292894 6.70711C-0.0976305 6.31658 -0.0976304 5.68342 0.292894 5.29289L5.29289 0.292893C5.68342 -0.0976316 6.31658 -0.0976315 6.70711 0.292893C7.09763 0.683417 7.09763 1.31658 6.70711 1.70711L2.41421 6L6.70711 10.2929C7.09763 10.6834 7.09763 11.3166 6.70711 11.7071Z" fill="#0081FF"/>
                            </svg>
                            Back to dashboard
                        </a>
                    </div>
                    <div class="grid md:flex gap-4 items-center">
                        <div class="hidden w-32 h-32 flex-shrink-0 xl:block border rounded-lg overflow-hidden p-2 relative" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">

                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 flex-grow">
                            <div>
                                <div class="flex items-center gap-2 mb-1">
                                    <h3 class="text-xl font-semibold">{{ user.name }}</h3>
                                </div>

                            </div>
                            <div class="md:col-span-2 columns-1 md:columns-2 2xl:columns-3 space-y-1" :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">

                            </div>
                        </div>
                    </div>
                    <Tab
                        :dark-mode="darkMode"
                        :tabs="tabs"
                        @selected="selectTab"
                        :tabs-classes="'w-full'"
                        :tab-type="'Normal'"
                        :default-tab-index="defaultTab"
                    />
                    <div class="p-3"></div>
                </div>
                <div>
                    <UserActivityPage
                            :dark-mode="darkMode"
                            :user-id="user.id"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import SharedApiService from "../Shared/services/api";
import LoadingSpinner from "../LeadProcessing/components/LoadingSpinner.vue";
import AlertsContainer from "../LeadProcessing/components/AlertsContainer.vue";
import HasAlertsMixin, {AlertTypes} from "../Shared/mixins/has-alerts-mixin";
import UserActivityPage from "./components/UserActivityPage.vue";
import Activity from "../Shared/modules/Activity.vue";
import Tab from "../Shared/components/Tab.vue";

export default {
    name: "UserActivity",
    components: {
        Activity,
        UserActivityPage,
        AlertsContainer,
        LoadingSpinner,
        Tab,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        user: {
            type: Object,
            default: {}
        }
    },
    mixins: [HasAlertsMixin],
    data() {
        return {
            api: SharedApiService.make(),
            tabs : [
            ],
            defaultTab: 0,
            selectedTab: 'User Activity',
        }
    },
    computed: {
    },
    mounted() {
    },
    created() {
    },
    methods: {
      selectTab(selected) {
        this.selectedTab = selected;
      },
    },
    watch: {
    }
}
</script>

<style scoped>

</style>
