export function useGmailLink() {
    const baseUrl = "https://mail.google.com/mail/";

    const openMail = (params, openTab = true) => {
        const filteredParams = Object.fromEntries(
            Object.entries(params).filter(([_, value]) => value) // Remove empty values
        );

        const url = `${baseUrl}?${new URLSearchParams(filteredParams).toString()}`;
        if (openTab) window.open(url, "_blank", "noopener,noreferrer");
        return url;
    };

    const composeMail = (to = "", subject = "", body = "", cc = "", bcc = "", openTab = true) =>
        openMail({view: "cm", fs: "1", to, su: subject, body, cc, bcc}, openTab);

    const replyMail = (threadId, body = "", openTab = true) =>
        openMail({view: "cm", fs: "1", th: threadId, body}, openTab);

    const forwardMail = (threadId, body = "", openTab = true) =>
        openMail({view: "cm", fs: "1", th: threadId, body, forward: "1"}, openTab);

    return {composeMail, replyMail, forwardMail};
}
