export default function useArrayHelper()
{
    /**
     * @param {string} item
     * @param {string[]} fieldNames
     */
    const createUniqueKeyForObject = (item, fieldNames = []) => {
        return fieldNames.length === 0 ? item : fieldNames.map(fieldName => item[fieldName]).join('|');
    }


    /**
     * @param {any[]} arr
     * @param {string[]} fieldNames
     */
    const removeDuplicatesByFields = (arr, fieldNames = []) => {
        const unique = new Map();

        return arr.filter(item => {
            const key = createUniqueKeyForObject(item, fieldNames)

            if (unique.has(key)) {
                return false;
            } else {
                unique.set(key, true);
                return true;
            }
        });
    }

    /**
     * @param {any[]} arr
     * @param {string|object} el
     * @param {any[]} fieldNames
     */
    const checkElementExistsInArrayByFields = (arr, el, fieldNames = []) => {
        const unique = new Map();

        arr.forEach(item => {
            const key = createUniqueKeyForObject(item, fieldNames)
            unique.set(key, true);
        });

        return unique.has(createUniqueKeyForObject(el, fieldNames))
    }

    const paginateArray = (array = [], page = 1, perPage = 10) => {
        const startingPoint = page - 1;
        const chunk = startingPoint * perPage;
        return array.slice(chunk, chunk + perPage)
    }

    return {
        removeDuplicatesByFields,
        checkElementExistsInArrayByFields,
        paginateArray
    }
}
