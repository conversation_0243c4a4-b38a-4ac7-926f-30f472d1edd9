/**
 * @param header
 * @param data
 * @returns {`data:text/csv;charset=utf-8,${string}`}
 */
export const createCsvString = (header, data) => {
    const csvFriendlyString = [ header, ...data ].map(r => Array.isArray(r) ? r.map(val => `"${val ?? ''}"`).join(",") : `"${r ?? ''}"`).join("\n");
    return `data:text/csv;charset=utf-8,${encodeURIComponent(csvFriendlyString)}`;
}

/**
 *
 * @param encodedDownloadString
 * @param filename
 */
const downloadFile = (encodedDownloadString, filename) => {
    const link = document.createElement('a');
    link.href = encodedDownloadString;
    link.download = filename;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * @param headers
 * @param data
 * @param filename
 */
export const downloadCsvString = (headers, data, filename) => {
    const downloadString = createCsvString(headers, data);
    downloadFile(downloadString, filename);
}
