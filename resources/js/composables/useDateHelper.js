import {DateTime} from "luxon";

export default function useDateHelper() {
    const calculateDateDifference = (isoEndDate, isoStartDate = null) => {
        if (!isoStartDate) {
            isoStartDate = new Date().toISOString()
        }

        const start = DateTime.fromISO(isoStartDate).startOf('day');
        const end = DateTime.fromISO(isoEndDate).startOf('day');
        const diff = end.diff(start, ["months", "days"]);
        const months = Math.floor(diff.months);
        const days = Math.floor(diff.days);

        if (months > 0 && days > 0) {
            return `${months} month${months > 1 ? "s" : ""} and ${days} day${days > 1 ? "s" : ""}`;
        } else if (months > 0) {
            return `${months} month${months > 1 ? "s" : ""}`;
        } else if (days > 0) {
            return `${days} day${days > 1 ? "s" : ""}`;
        }
    }

    const parseDate = (date, format = null, timezone = 'MST') => {
        const parsed = DateTime.fromISO(date, { zone: 'utc' }).setZone(timezone);

        if (format) {
            return parsed.toLocaleString(format)
        }

        return parsed
    }

    return {
        calculateDateDifference,
        parseDate
    }
}
