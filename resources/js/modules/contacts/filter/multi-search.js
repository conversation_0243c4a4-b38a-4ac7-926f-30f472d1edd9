/**
 * @typedef contact
 * @property {string} first_name
 * @property {string} last_name
 * @property {string} title
 * @property {string} email
 * @property {string} office_phone
 * @property {string} cell_phone
 * @property {string} department
 */

/**
 * Returns true if the contact meets the filter requirements.
 * @param contact
 * @param search
 * @return {boolean}
 */
export const getContactMeetsFilterRequirements = (contact, search) => {
    /**
     * @param string
     * @return {string}
     */
    const lowerCaseTrim = (string) => {
        return string?.replace(/\s/g, '')?.toLowerCase()?.trim() ?? "";
    }

    search = search.replace(/\s/g, '').toLowerCase().trim();
    const fullName = lowerCaseTrim((contact?.first_name + contact?.last_name))
    const title = lowerCaseTrim(contact?.title);
    const email = lowerCaseTrim(contact?.email);
    const officePhone = lowerCaseTrim(contact?.office_phone);
    const cellPhone = lowerCaseTrim(contact?.cell_phone);
    const department = lowerCaseTrim(contact?.department);

    return fullName.includes(search) || title.includes(search) || email.includes(search) || officePhone.includes(search) || cellPhone.includes(search) || department.includes(search);
}

/**
 * Returns the contacts that meet the filter requirements.
 * @param contacts
 * @param search
 * @return {contact[]}
 */
export const filterContacts = (contacts, search) => {
    if (!search) {
        return contacts;
    }

    return contacts?.filter(contact => {
        return getContactMeetsFilterRequirements(contact, search)
    }) ?? [];
};
