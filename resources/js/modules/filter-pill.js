/**
 * @param {string} filterName The name of the filter (e.g., Status).
 * @param {array<object>} options The options for the filter.
 * @param {array<number>} selected The currently selected options of the filter.
 * @param {'and'|'or'} logical The logical operator to use between the selected options.
 * @return {string}
 */
export const filterFillHtmlString = (filterName, options, selected, logical = 'or') => {
    const starterHtml = `${filterName}<span class="text-slate-500 mx-1">is</span>`;

    selected = [...selected];

    if (selected?.length === 2) {
        const first = getOptionNameById(options, selected[0]);

        const last = getOptionNameById(options, selected[1]);

        return `${starterHtml}<span>${first}<span class="text-slate-500 mx-1">${logical}</span>${last}</span>`;
    } else if (selected?.length === 1) {
        const only = getOptionNameById(options, selected[0]);

        return `${starterHtml}<span>${only}</span>`;
    } else if (selected?.length > 2) {
        const last = selected.pop();

        const lastText = getOptionNameById(options, last);

        const listHtml = selected.map(element => {
            return getOptionNameById(options, element);
        }).join(`<span class="text-slate-500 mr-1">,</span>`);

        return `${starterHtml}<span>${listHtml}<span class="text-slate-500 mr-1">, ${logical}</span>${lastText}</span>`;
    }

    return '';
}

/**
 * @param options
 * @param id
 * @return {*}
 */
const getOptionNameById = (options, id) => {
    for (const option of options) {
        if (option.id === id) {
            return option.name;
        }
    }
}