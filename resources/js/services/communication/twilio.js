import {Device} from '@twilio/voice-sdk';
import {markRaw} from "vue";

const STATUS_UNAVAILABLE = 'unavailable';
const STATUS_READY = 'ready';


export default class TwilioCommunicationService {
    constructor() {
        this.phone = null;
        this.status = STATUS_UNAVAILABLE;
        this._call = null;
        this.incomingCall = null;
        this.incomingListeners = {start: [], end: []};
        this._device = null;
        this.listeners = {accept: [], answer: [], missed: [], end: []};
    }

    /**
     * Get twilio device
     * @returns {Device|null}
     */
    get device() {
        return this._device;
    }

    /**
     * Bind events to incoming call
     * @param connection
     * @private
     */
    _incoming(connection) {
        this.incomingCall = markRaw(connection);

        this.incomingCall.on('error', () => {
            console.log('Triggering error')
            this.listeners.end.forEach(cb => cb());
            this.incomingCall = null
        });

        this.incomingCall.on('cancel', () => {
            console.log('Triggering Cancel')
            this.listeners.missed.forEach(cb => cb());
            this.incomingCall = null
        });

        this.incomingListeners.start.forEach(cb => cb(this._formatConnection(connection)));
    }

    /**
     * Format connection
     * @param connection
     * @returns {{from: *, id: *}}
     * @private
     */
    _formatConnection(connection) {
        return {from: connection.parameters.From, id: connection.parameters.CallSid}
    }

    /**
     * Initialize a twilio device
     *
     * @param token
     * @param phone
     * @returns {Promise<void>}
     */
    async initializeDevice(token, phone) {
        this.phone = phone;
        // this._device = markRaw(new Device(token, {logLevel: 0}));
        this._device = markRaw(new Device(token));
        this._device.register();

        this._device.on('registered', () => {
            console.log('Device registered');
            this.status = STATUS_READY
        });
        this._device.on('incoming', this._incoming.bind(this));
    }

    /**
     * Initialize call
     *
     * @param number
     * @returns {Promise<void>}
     */
    initializeCall(number) {
        return new Promise(async (resolve, reject) => {
            const connection = await this._device.connect({
                params: {
                    To: number,
                    From: this.phone,
                    Direction: "outbound",
                    Record: true,
                    Outgoing: true
                },
            });

            this._call = markRaw(connection);
            this._registerCallEvents();

            resolve(connection);
        });
    }

    /**
     * Handle call hangup
     */
    hangUp() {
        this.device.disconnectAll();
        this.incomingCall ? this.incomingCall.reject() : null;
        this.clearIncomingCall();
    }

    /**
     * Should handle put call in hold
     * At present, it's not implemented
     * @param status
     */
    hold(status) {

    }

    /**
     * Mute the call
     * @param status
     */
    mute(status) {
        if (this._call != null) {
            this._call.mute(status);
        }
    }

    /**
     * Add a callback to on accept callback list
     * @param cb
     */
    onAccept(cb) {
        this.listeners.accept.push(cb);
    }

    /**
     * Add a callback to on missed callback list
     * @param cb
     */
    onMissed(cb) {
        this.listeners.missed.push(cb);
    }

    /**
     * Accept incoming call
     */
    accept() {
        if (!!this.incomingCall) {
            this.incomingCall.accept(false, false);
            this._call = markRaw(this.incomingCall);
            this.clearIncomingCall();
            this._registerCallEvents();
        }
    }

    /**
     * Register twilio events listeners
     * @private
     */
    _registerCallEvents() {
        // Possible events:
        // accept, cancel, disconnect, reject

        this._call.on('accept', call => {
            console.log('Call Accepted')
            this.listeners.accept.forEach(cb => cb());
        });

        this._call.on('disconnect', call => {
            console.log('Call disconnected')
            this.listeners.end.forEach(cb => cb());
        });

        this._call.on('cancel', call => {
            console.log('Call canceled')
            this.listeners.end.forEach(cb => cb());
        });
    }

    /**
     * Decline incoming call
     */
    decline() {
        if (!!this.incomingCall) {
            this.incomingCall.reject();
            this.clearIncomingCall();
            this.device.disconnectAll()
        }
    }

    /**
     * Handle missed
     */
    missed() {
        if(!!this.incomingCall) {
            this.listeners.missed.forEach(cb => cb());
            this.clearIncomingCall();
        }
    }

    /**
     * Clear incoming call
     */
    clearIncomingCall() {
        this.incomingListeners.end.forEach(cb => cb())
    }

    onIncomingCall(cb) {
        this.incomingListeners.start.push(cb);
    }

    /**
     * Add callback to call end event
     * @param cb
     */
    onEnd(cb) {
        this.listeners.end.push(cb);
    }

    /**
     * Get service name
     * @returns {string}
     */
    serviceName() {
        return 'twilio';
    }

    /**
     * Get current call sid
     * @returns {null|*}
     */
    getCallId() {
        return this._call && this._call.parameters.CallSid;
    }

    /**
     * Get contact phone
     * @returns {string}
     */
    getUserPhone() {
        return this.phone;
    }

    /**
     * Send digits to call
     * @param digit
     */
    sendDigitsToCall(digit){
        if (this._call) this._call.sendDigits(digit)
        else console.warn('User is not in a call')
    }
}
