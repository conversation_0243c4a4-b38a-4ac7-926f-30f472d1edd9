import {defineStore} from "pinia";
import {ref} from "vue";
import SharedApiService from "../../vue/components/Shared/services/api.js";

export const useCompanyStore = defineStore('company', () => {
    const company = ref({});
    const sharedApi = SharedApiService.make();

    const initialize = (initCompany) => {
        company.value = initCompany;
    }

    return {
        company,
        sharedApi,
        initialize
    }
});
