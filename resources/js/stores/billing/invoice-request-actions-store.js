import {defineS<PERSON>} from "pinia";
import {computed, ref} from "vue";
import ApiService from "../../vue/components/BillingManagement/services/invoce-action-requests";
import {PERMISSIONS, useRolesPermissions} from "../roles-permissions.store.js";

export const useInvoiceRequestActionStore = defineStore('invoice-request-actions-store', () => {
    const api = ApiService.make();

    const rolesPermissions = useRolesPermissions();

    const filters = ref({
        per_page: 25
    })
    const data = ref([])
    const loading = ref(false)
    const loadingReviewSubmission = ref(false)
    const paginationData = ref({})

    const getRequestActions = async () => {
        loading.value = true

        const res = await api.getActionRequests(filters.value);

        data.value = res.data.data
        paginationData.value = {links: res.data.links, ...res.data.meta}

        loading.value = false
    }

    const getRequestAction = async (id) => {
        loading.value = true

        const res = await api.getActionRequest(id);

        loading.value = false

        return res.data.data;
    }

    const submitReview = async ({requestId, status, reason}) => {
        loadingReviewSubmission.value = true

        await api.submitReview(requestId, {
            status,
            reason
        });

        loadingReviewSubmission.value = false

        getRequestActions()
    }

    const setFilters = (newFilters = {}) => {
        filters.value = {...filters.value, ...newFilters}
    }

    const canReview = computed(() => {
        return rolesPermissions.hasPermission(PERMISSIONS.PERMISSION_BILLING_ACTION_APPROVALS_REVIEW)
    })
    const canView = computed(() => {
        return rolesPermissions.hasPermission(PERMISSIONS.PERMISSION_BILLING_ACTION_APPROVALS_VIEW)
    })

    return {
        api,
        filters,
        loading,
        data,
        paginationData,
        loadingReviewSubmission,
        canReview,
        canView,
        setFilters,
        getRequestActions,
        submitReview,
        getRequestAction
    }
});
