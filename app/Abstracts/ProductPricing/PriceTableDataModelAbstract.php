<?php

namespace App\Abstracts\ProductPricing;

use App\Enums\Odin\Product;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Models\Legacy\LeadPrice;
use App\Models\Legacy\Location;
use App\Models\Odin\Industry;
use App\Repositories\LocationRepository;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;

abstract class PriceTableDataModelAbstract implements Arrayable
{
    const ARRAY_KEY_QUALITY_TIERS  = 'quality_tiers';
    const ARRAY_KEY_STATES         = 'states';
    const ARRAY_KEY_SALES_TYPES    = 'sales_types';
    const ARRAY_KEY_COUNTIES       = 'counties';
    const ARRAY_KEY_NAME           = 'name';
    const ARRAY_KEY_KEY            = 'key';

    // These are the various factors affecting price, in order of precedence
    const ARRAY_KEY_INHERENT_PRICE = 'inherent_price'; // Inherited from parent (e.g. county inherits from state) -- this is only used if no explicit price or formula is defined
    const ARRAY_KEY_FORMULA        = 'formula';        // Dictates how the price should be calculated -- supersedes inherent price
    const ARRAY_KEY_EXPLICIT_PRICE = 'explicit_price'; // Explicitly defined price -- supersedes inherent price and formula
    const ARRAY_KEY_PRICE          = 'price';          // Final price after taking above into account

    const LEAD_QUALITY_TIERS = [
        QualityTier::STANDARD,
        QualityTier::PREMIUM
    ];

    const APPOINTMENT_QUALITY_TIERS = [
        QualityTier::ONLINE,
        QualityTier::IN_HOME
    ];

    const LEAD_SALES_TYPES = [
        SaleTypes::EXCLUSIVE,
        SaleTypes::DUO,
        SaleTypes::TRIO,
        SaleTypes::QUAD,
        SaleTypes::UNVERIFIED,
        SaleTypes::EMAIL_ONLY
    ];

    const APPOINTMENT_SALES_TYPES = [
        SaleTypes::EXCLUSIVE,
        SaleTypes::DUO,
        SaleTypes::TRIO
    ];

    /** @var array $priceData */
    protected array $priceData = [];

    /**
     * @param Product $product
     * @param string $industry
     * @param PropertyType $propertyType
     * @param bool $includeCounties
     * @param array|null $exclusiveStateList
     * @param array|null $exclusiveCountyKeys
     */
    public function __construct(
        protected Product               $product = Product::LEAD,
        protected readonly string       $industry = Industry::INDUSTRY_SOLAR,
        protected readonly PropertyType $propertyType = PropertyType::RESIDENTIAL,
        protected readonly bool         $includeCounties = true,
        protected readonly ?array       $exclusiveStateList = null,
        protected readonly ?array       $exclusiveCountyKeys = null
    ) {
        $this->createBaseDataStructure();
    }

    /**
     * @return void
     */
    final public function createBaseDataStructure(): void
    {
        /** @var LocationRepository $locationRepository */
        $locationRepository = app(LocationRepository::class);

        $counties = !empty($this->exclusiveStateList) ? $locationRepository->getCountiesInStatesList($this->exclusiveStateList) : $locationRepository->getCounties();

        $this->priceData[self::ARRAY_KEY_QUALITY_TIERS] = [];

        $qualityTiers = match ($this->product) {
            Product::LEAD => self::LEAD_QUALITY_TIERS,
            Product::APPOINTMENT => self::APPOINTMENT_QUALITY_TIERS
        };
        foreach ($qualityTiers as $qualityTier) {
            $this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier->value] = [
                self::ARRAY_KEY_STATES => []
            ];

            foreach ($counties as $county) {
                if(!array_key_exists($county->{Location::STATE_ABBREVIATION}, $this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier->value][self::ARRAY_KEY_STATES])) {
                    $this->addStateToPriceData($qualityTier, $county);
                }

                if($this->includeCounties
                && (empty($this->exclusiveCountyKeys) || in_array($county->{Location::COUNTY_KEY}, $this->exclusiveCountyKeys, true))) {
                    $this->addCountyToPriceData($qualityTier, $county);
                }
            }
        }
    }

    /**
     * @return array
     */
    final public function toArray(): array
    {
        return $this->priceData;
    }

    /**
     * @param QualityTier $qualityTier
     * @param Location $location
     * @return void
     */
    final public function addStateToPriceData(QualityTier $qualityTier, Location $location):void
    {
        $this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier->value][self::ARRAY_KEY_STATES][$location->{Location::STATE_ABBREVIATION}] = [
            self::ARRAY_KEY_NAME => $location->{Location::STATE},
            self::ARRAY_KEY_KEY => $location->{Location::STATE_KEY},
            self::ARRAY_KEY_SALES_TYPES => $this->getSalesTypeStructure(),
            self::ARRAY_KEY_COUNTIES => []
        ];
    }

    /**
     * @param QualityTier $qualityTier
     * @param Location $location
     * @return void
     */
    final public function addCountyToPriceData(QualityTier $qualityTier, Location $location):void
    {
        $this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier->value][self::ARRAY_KEY_STATES][$location->{Location::STATE_ABBREVIATION}][self::ARRAY_KEY_COUNTIES][$location->{Location::COUNTY_KEY}] = [
            self::ARRAY_KEY_NAME => $location->{Location::COUNTY},
            self::ARRAY_KEY_SALES_TYPES => $this->getSalesTypeStructure()
        ];
    }

    /**
     * @param LeadPrice $price
     * @return SaleTypes|null
     */
    final public function getSalesTypeFromLegacyPrice(LeadPrice $price): ?SaleTypes
    {
        return match($price->lead_sales_type_id) {
            LeadPrice::SALES_TYPE_EXCLUSIVE => SaleTypes::EXCLUSIVE,
            LeadPrice::SALES_TYPE_DUO => SaleTypes::DUO,
            LeadPrice::SALES_TYPE_TRIO => SaleTypes::TRIO,
            LeadPrice::SALES_TYPE_QUAD => SaleTypes::QUAD,
            LeadPrice::SALES_TYPE_UNVERIFIED => SaleTypes::UNVERIFIED,
            LeadPrice::SALES_TYPE_EMAIL_ONLY => SaleTypes::EMAIL_ONLY,
            default => null
        };
    }

    /**
     * @param LeadPrice $price
     * @return QualityTier|null
     */
    public function getQualityTierFromLegacyPrice(LeadPrice $price): ?QualityTier
    {
        return match($price->lead_type_id) {
            LeadPrice::LEAD_TYPE_STANDARD => QualityTier::STANDARD,
            LeadPrice::LEAD_TYPE_PREMIUM => QualityTier::PREMIUM,
            default => null
        };
    }

    /**
     * @return array
     */
    final public function getAllStates(): array
    {
        $states = [];
        foreach($this->priceData[self::ARRAY_KEY_QUALITY_TIERS] as $qualityTier => $qualityTierData) {
            $states = array_merge($states, array_keys($qualityTierData[self::ARRAY_KEY_STATES]));
        }

        return array_unique($states);
    }

    /**
     * @param string $qualityTier
     * @param string $salesType
     * @param string $stateAbbr
     * @param string|null $countyKey
     * @return float|null
     */
    abstract public function getPrice(string $qualityTier, string $salesType, string $stateAbbr, ?string $countyKey = null): ?float;

    /**
     * @param Collection $legacyPrices
     * @return bool
     */
    abstract public function ingestLegacyData(Collection $legacyPrices): bool;

    /**
     * @param Collection $prices
     * @return bool
     */
    abstract public function ingestData(Collection $prices): bool;

    /**
     * @return array
     */
    abstract public function getSalesTypeStructure(): array;
}
