<?php

namespace App\DataModels\Odin\Prices;

use App\Enums\Odin\QualityTier;
use App\Models\ComputedRejectionStatistic;
use App\Rules\InputTypes;
use ArrayIterator;
use Exception;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use IteratorAggregate;

/**
 * @property-read QualityTier $qualityTier
 */
class FilteredBRSPrices implements IteratorAggregate, Arrayable
{
    const PRICE = 'price';
    const UNRECTIFIED_PRICE = 'unrectified_price';
    const COMPANY_ID = 'company_id';
    const CAMPAIGN_ID = 'campaign_id';
    const BUDGET_USAGE = 'budget_usage';
    const BUDGET = 'budget';
    const CAMPAIGN_BUDGET_ID = 'campaign_budget_id';
    const SALE_TYPE_ID = 'sale_type_id';
    const SCHEDULE_ID = 'schedule_id';

    const ALLOWED_KEYS = [
        'qualityTier'
    ];

    /**
     * @var Collection $price
     */
    private Collection $prices;

    /**
     * @param QualityTier $qualityTier
     */
    public function __construct(
        private readonly QualityTier $qualityTier
    ) {
        $this->prices = collect();
    }

    /**
     * @param int $saleTypeId
     * @param int $campaignId
     * @param int $companyId
     * @param int $campaignBudgetId
     * @param float $price
     * @param float $unrectifiedPrice
     * @param float $rejectionPercentageImpactingEligibility
     * @param float $rejectionPercentageImpactingBid
     * @param float $usage
     * @param string $budget
     * @param int|null $scheduleId
     * @return bool
     * @throws Exception
     */
    public function setPrice(
        int $saleTypeId,
        int $campaignId,
        int $companyId,
        int $campaignBudgetId,
        float $price,
        float $unrectifiedPrice,
        float $rejectionPercentageImpactingEligibility,
        float $rejectionPercentageImpactingBid,
        float $usage,
        string $budget,
        ?int $scheduleId = 0
    ): bool
    {
        $validator = Validator::make(
            [
                self::SALE_TYPE_ID                                                     => $saleTypeId,
                self::CAMPAIGN_ID                                                      => $campaignId,
                self::COMPANY_ID                                                       => $companyId,
                self::CAMPAIGN_BUDGET_ID                                               => $campaignBudgetId,
                self::PRICE                                                            => $price,
                self::UNRECTIFIED_PRICE                                                => $unrectifiedPrice,
                ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY => $rejectionPercentageImpactingEligibility,
                ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID         => $rejectionPercentageImpactingBid,
                self::BUDGET_USAGE                                                     => $usage,
                self::BUDGET                                                           => $budget,
                self::SCHEDULE_ID                                                      => $scheduleId
            ],
            [
                self::SALE_TYPE_ID                                                     => ['required', 'integer', 'min:1'],
                self::PRICE                                                            => ['required', new InputTypes(['integer', 'double']), 'gt:0'],
                self::UNRECTIFIED_PRICE                                                => ['required', new InputTypes(['integer', 'double']), 'gt:0'],
                ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY => ['required', new InputTypes(['double']), 'min:0'],
                ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID         => ['required', new InputTypes(['double']), 'min:0'],
                self::BUDGET_USAGE                                                     => ['required', new InputTypes(['double']), 'min:0'],
                self::COMPANY_ID                                                       => ['required', 'integer', 'min:1'],
                self::CAMPAIGN_ID                                                      => ['required', 'integer', 'min:1', 'distinct'],
                self::CAMPAIGN_BUDGET_ID                                               => ['required', 'integer', 'min:0'],
                self::BUDGET                                                           => ['required', 'string'],
                self::SCHEDULE_ID                                                      => ['present', 'nullable']
            ]
        );

        if($validator->fails()) {
            throw new Exception((string) $validator->getMessageBag());
        }

        if(empty($this->prices->get($saleTypeId))) {
            $this->prices->put($saleTypeId, collect());
        }

        $this->prices->get($saleTypeId)->put(
            $campaignId,
            collect([
                self::PRICE                                                            => $price,
                self::UNRECTIFIED_PRICE                                                => $unrectifiedPrice,
                self::COMPANY_ID                                                       => $companyId,
                self::CAMPAIGN_ID                                                      => $campaignId,
                self::CAMPAIGN_BUDGET_ID                                               => $campaignBudgetId,
                ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY => $rejectionPercentageImpactingEligibility,
                ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID         => $rejectionPercentageImpactingBid,
                self::BUDGET_USAGE                                                     => $usage,
                self::BUDGET                                                           => $budget,
                self::SCHEDULE_ID                                                      => $scheduleId ?? 0
            ])
        );

        return true;
    }

    /**
     * @param string $name
     * @return mixed
     * @throws Exception
     */
    public function __get(string $name): mixed
    {
        if(!in_array($name, self::ALLOWED_KEYS, true)) {
            throw new Exception("Invalid property $name");
        }

        return $this->{$name};
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return $this->prices->toArray();
    }

    /**
     * @return ArrayIterator
     */
    public function getIterator(): ArrayIterator
    {
        return $this->prices->getIterator();
    }

    /**
     * @return bool
     */
    public function isEmpty(): bool
    {
        return $this->prices->isEmpty();
    }

    /**
     * @return Collection
     */
    public function getCampaignIds(): Collection
    {
        $campaignIds = collect();
        foreach($this->prices as $saleTypeId => $campaignPrices) {
            $campaignIds->put($saleTypeId, $campaignPrices->keys());
        }

        return $campaignIds;
    }

    /**
     * @param int $saleTypeId
     * @return Collection
     */
    public function getSaleTypeCampaigns(int $saleTypeId): Collection
    {
        return $this->prices->get($saleTypeId);
    }
}
