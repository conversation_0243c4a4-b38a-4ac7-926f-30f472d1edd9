<?php

namespace App\DataModels\Campaigns;

use App\Models\AvailableBudget;

class AvailableBudgetDataModel
{
    public function __construct(
        public string $industryType,
        public int $locationId,
        public int $countyLocationId,
        public int $budgetType = AvailableBudget::BUDGET_TYPE_VERIFIED,
        public int $availableCampaignCount = 0,
        public int $unlimitedBudgetCount = 0,
        public float $availableBudgetVolume = 0,
        public float $availableBudgetDollars = 0,
        public float $potentialQueuedRevenue = 0,
        public array $campaignIds = [],
        public array $campaignCompanyIds = [],
        public array $companyIds = [],
        public array $odinCompanyIds = [],
        public int $industryId = 0
    ) {}
}
