<?php

namespace App\Transformers\LeadProcessing;

use App\Models\LeadProcessorNotification;
use App\Models\Notification;
use Illuminate\Support\Collection;
use Php<PERSON><PERSON>er\Node\Stmt\If_;

class LeadProcessorNotificationTransformer
{
    /**
     * @param Collection $notifications
     * @return array
     */
    public function transformNotifications(Collection $notifications): array
    {
        return $notifications->map(function ($notification) {
            return $this->transformLeadProcessingNotification($notification);
        })->values()->toArray();
    }

    /**
     * Transforms notifications that can be user or lead processing specific.
     *
     * @param Collection $notifications
     * @return array
     */
    public function transformGenericNotifications(Collection $notifications): array
    {
        return $notifications->map(function ($notification) {
            return $notification instanceof Notification
                ? $this->transformUserNotification($notification)
                : $this->transformLeadProcessingNotification($notification);
        })->values()->toArray();
    }

    /**
     * @param LeadProcessorNotification $notification
     * @return array
     */
    public function transformLeadProcessingNotification(LeadProcessorNotification $notification): array
    {
        return [
            'id'        => $notification->{LeadProcessorNotification::FIELD_ID},
            'message'   => $notification->{LeadProcessorNotification::FIELD_BODY},
            'subject'   => $notification->{LeadProcessorNotification::FIELD_SUBJECT},
            'lead_id'   => $notification->{LeadProcessorNotification::FIELD_LEAD_ID},
            'read'      => $notification->{LeadProcessorNotification::FIELD_READ} === LeadProcessorNotification::READ,
            'timestamp' => $notification->{LeadProcessorNotification::CREATED_AT}?->timestamp,
            "type"      => "lead-processor",
        ];
    }

    /**
     * @param Notification $notification
     * @return array
     */
    public function transformUserNotification(Notification $notification): array
    {
        return [
            'id'        => $notification->{Notification::FIELD_ID},
            'message'   => $notification->{Notification::FIELD_BODY},
            'subject'   => $notification->{Notification::FIELD_SUBJECT},
            'type'      => $notification->{Notification::FIELD_TYPE},
            'type_id'   => $notification->{Notification::FIELD_FROM_ID},
            'link'      => $notification->{Notification::FIELD_LINK},
            'link_type' => $notification->{Notification::FIELD_LINK_TYPE},
            'read'      => $notification->{Notification::FIELD_READ} === LeadProcessorNotification::READ,
            'payload'   => $notification->{Notification::FIELD_PAYLOAD},
            'timestamp' => $notification->{Notification::CREATED_AT}?->timestamp
        ];
    }

}
