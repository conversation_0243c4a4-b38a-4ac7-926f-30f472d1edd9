<?php

namespace App\Transformers;

use App\Models\Legacy\EloquentInvoice;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class CompanyInvoiceTransformer
{
    /**
     * Handles transforming a collection of invoice to their client side representation.
     *
     * @param Collection|EloquentInvoice $invoices
     * @return array
     */
    public function transformInvoices(Collection $invoices): array
    {
        return $invoices->map(function (EloquentInvoice $invoice) {
            return $this->transformInvoices($invoice);
        })->values()->toArray();
    }

    /**
     * Handles transforming a single lead campaign to its client side representation.
     *
     * @param EloquentInvoice $invoice
     * @return array
     */
    public function transformInvoice(EloquentInvoice $invoice): array
    {
        return [
            "id" => $invoice->{EloquentInvoice::ID},
            "date_issued" => Carbon::parse($invoice->{EloquentInvoice::TIMESTAMP_ADDED})->format('Y-m-d') ,
            "date_due" => Carbon::parse($invoice->{EloquentInvoice::TIMESTAMP_PAYMENT_DUE})->format('Y-m-d'),
            "status" => $invoice->{EloquentInvoice::STATUS},
            "payment_type" => $invoice->getInvoicePaymentTypeDisplay(),
            "amount" => $invoice->getTotalPriceDisplay(),
        ];
    }
}
