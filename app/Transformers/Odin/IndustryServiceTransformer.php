<?php

namespace App\Transformers\Odin;

use App\Models\Odin\CompanyService;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use Illuminate\Support\Collection;

class IndustryServiceTransformer
{
    /**
     * @param CompanyServiceTransformer $companyServiceTransformer
     */
    public function __construct(protected CompanyServiceTransformer $companyServiceTransformer){}

    /**
     * @param Collection $industryServices
     * @return array
     */
    public function transform(Collection $industryServices): array
    {
        return $industryServices->map(fn(IndustryService $industryService) => $this->transformIndustryService($industryService))->toArray();
    }

    /**
     * @param IndustryService $industryService
     * @return array
     */
    public function transformIndustryService(IndustryService $industryService): array
    {
        $relationData = array_key_exists('company_services', $industryService->toArray()) ? $industryService->toArray() : $industryService->getRelations();

        return
            [
                'id'                      => $industryService->{IndustryService::FIELD_ID},
                'industry'                => $industryService->{IndustryService::RELATION_INDUSTRY}?->{Industry::FIELD_NAME},
                'name'                    => $industryService->{IndustryService::FIELD_NAME},
                'slug'                    => $industryService->{IndustryService::FIELD_SLUG},
                'show_on_website'         => $industryService->{IndustryService::FIELD_SHOW_ON_WEBSITE},
                'companies'               => array_key_exists('company_services', $relationData) ? $this->handleCompanyServices($relationData['company_services']) : [],
                'created_at'              => $industryService->{IndustryService::CREATED_AT}?->toIso8601String(),
                'show_on_registration'    => $industryService->{IndustryService::FIELD_SHOW_ON_REGISTRATION},
                'show_on_dashboard'       => $industryService->{IndustryService::FIELD_SHOW_ON_DASHBOARD},
                'campaign_filter_enabled' => $industryService->campaign_filter_enabled,
            ];
    }

    /**
     * @param $companyServices
     * @return array
     */
    protected function handleCompanyServices($companyServices): array
    {
        return collect($companyServices)->map(function ($service) {
            return $this->companyServiceTransformer->transformCompanyService((new CompanyService())->fill($service));
        })->toArray();
    }
}
