<?php

namespace App\Events\LeadProcessing;

use App\Concerns\HasLeadProcessingData;

/**
 * Event for when a lead has been approved to sell.
 */
class LeadApprovedEvent
{
    use HasLeadProcessingData;

    /** @var string $processingScenario */
    public string $processingScenario;

    /**
     * @param string $leadReference
     * @param int $processorId
     * @param string $processingScenario
     */
    public function __construct(string $leadReference, int $processorId, string $processingScenario)
    {
        $this->leadReference      = $leadReference;
        $this->processorId        = $processorId;
        $this->processingScenario = $processingScenario;
    }
}
