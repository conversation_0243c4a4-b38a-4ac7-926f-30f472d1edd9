<?php

namespace App\Events\ProductAssignment;

use App\Concerns\HasProductAssignmentData;
use Illuminate\Foundation\Events\Dispatchable;

/**
 * Base event for product assignments
 */
class ProductAssignmentEvent
{
    use HasProductAssignmentData;
    use Dispatchable;

    /**
     * @param int $productAssignmentId
     */
    public function __construct(int $productAssignmentId)
    {
        $this->productAssignmentId = $productAssignmentId;
    }
}
