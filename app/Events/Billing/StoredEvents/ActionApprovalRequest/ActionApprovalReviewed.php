<?php

namespace App\Events\Billing\StoredEvents\ActionApprovalRequest;

use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Events\Base\Authored;

class ActionApprovalReviewed extends Authored
{
    public function __construct(
        public string $actionApprovalUuid,
        public int     $modelId,
        public string  $modelClass,
        int            $authorId,
        public string  $status,
        public ?string $reason = null,
    )
    {
        parent::__construct(InvoiceEventAuthorTypes::USER->value, $authorId);
    }
}
