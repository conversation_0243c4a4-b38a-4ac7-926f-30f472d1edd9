<?php

namespace App\Events\Billing\StoredEvents\Invoice;

use Spatie\EventSourcing\StoredEvents\ShouldBeStored;

class InvoiceSnapshotCreated extends ShouldBeStored
{
    public function __construct(
        public string $uuid,
        public string $invoiceUuid,
        public int $companyId,
        public string $status,
        public float $totalValue,
        public float $totalOutstanding,
        public float $totalRefunded,
        public float $totalPaid,
        public float $totalCollections,
        public float $totalCollectionsRecovered,
        public float $totalCollectionsLost,
        public float $totalCreditsApplied,
        public float $totalChargebackWon,
        public float $totalChargebackLost,
        public float $totalChargeback,
        public float $totalWrittenOff,
        public ?string $scenario = null,
        public ?int $accountManagerId = null,
        public ?int $successManagerId = null,
        public ?int $businessDevelopmentManagerId = null,
        public ?string $date = null,
    )
    {
    }
}
