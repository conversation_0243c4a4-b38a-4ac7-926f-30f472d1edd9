<?php

namespace App\Company\DeleteCompany\Deletes;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\CompanyCampaignData;
use Illuminate\Database\Eloquent\Builder;

class DeleteCompanyCampaignData extends DeleteContract
{
    function query(int $companyId): Builder
    {
        return CompanyCampaignData::query()
            ->whereHas(CompanyCampaignData::RELATION_CAMPAIGN, function(Builder $query) use ($companyId) {
                $query->where(CompanyCampaign::FIELD_COMPANY_ID, $companyId);
            });
    }

    function modelClass(): string
    {
        return CompanyCampaignData::class;
    }
}
