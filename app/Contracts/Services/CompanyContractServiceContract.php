<?php

namespace App\Contracts\Services;

use App\Enums\ContractType;
use App\Models\CompanyContract;
use App\Models\Contract;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;

interface CompanyContractServiceContract
{
    /**
     * @param Company $company
     * @param CompanyUser $companyUser
     * @param ContractType $contractType
     * @param string $ip
     * @param Contract $contract
     * @param string $signatureId
     * @return CompanyContract|null
     */
    function createNewContract(Company $company, CompanyUser $companyUser, ContractType $contractType, string $ip, Contract $contract, string $signatureId): ?CompanyContract;

    /**
     * @param Company $company
     * @param CompanyUser $companyUser
     * @param CompanyContract $companyContract
     * @return bool
     */
    public function agreeToContract(Company $company, CompanyUser $companyUser, CompanyContract $companyContract): bool;

    /**
     * If multiple contracts were generated for the same user/company/type, clean up any which are unsigned
     * @param CompanyContract $companyContract
     * @return void
     */
    public function cleanUpRedundantContracts(CompanyContract $companyContract): void;

    /**
     * Get the signed file url for downloading on the front end
     *
     * @param string|null $companyContractUrl
     * @return string|null
     */
    public function getSignedContractFileUrl(?string $companyContractUrl): ?string;

}
