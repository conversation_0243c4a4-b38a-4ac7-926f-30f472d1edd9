<?php

namespace App\Contracts\Services\Advertising;

use Exception;
use Illuminate\Support\Collection;
use JetBrains\PhpStorm\ArrayShape;

interface AdvertisingLocationsServiceContract
{
    const FUNC_RETURN_CAMPAIGN_LOCATIONS = 'campaign_locations';
    const FUNC_RETURN_PLATFORM_LOCATION_IDS = 'platform_location_ids';
    const FUNC_RETURN_MISC_TARGETING_DATA = 'misc_targeting_data';

    /**
     * @param array|null $locationIds
     * @return Collection
     */
    public function getPlatformIdsByLocation(?array $locationIds = null): Collection;

    /**
     * @param $accountId
     * @param array $campaignIds
     * @return array
     */
    #[ArrayShape([self::FUNC_RETURN_CAMPAIGN_LOCATIONS => "array", self::FUNC_RETURN_PLATFORM_LOCATION_IDS => "array"])]
    public function fetchCampaignLocationsFromPlatformAPI($accountId, array $campaignIds): array;

    /**
     * @param bool $force
     * @return bool
     */
    public function storePlatformLocations(bool $force = false): bool;
}
