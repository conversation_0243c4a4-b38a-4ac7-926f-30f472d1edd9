<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;

interface CompanyContactRepositoryContract
{
    /**
     * <PERSON>les firing a PubSub event to create a company contact.
     *
     * @param string $companyReference
     * @param array $contact
     * @return bool
     */
    public function addCompanyContact(string $companyReference, array $contact): bool;

    /**
     * <PERSON>les firing a PubSub event to update an existing company contact against the requested params.
     *
     * @param string $companyReference
     * @param int $contactId
     * @param array $data
     * @return bool
     */
    public function updateCompanyContact(string $companyReference, int $contactId, array $data): bool;

    /**
     * @param string $companyReference
     * @param int $contactId
     * @return bool
     */
    public function deleteCompanyContact(string $companyReference, int $contactId): bool;

    /**
     * <PERSON>les fetching a list of companies having similar contact details based on the requested data set.
     *
     * @param array $cellPhones
     * @param array $officePhones
     * @param array $emailAddresses
     * @return Collection
     */
    public function getCompaniesWithSimilarContactDetails(array $cellPhones, array $officePhones, array $emailAddresses): Collection;
}
