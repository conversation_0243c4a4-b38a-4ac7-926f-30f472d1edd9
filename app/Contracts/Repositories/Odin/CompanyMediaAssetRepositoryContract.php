<?php

namespace App\Contracts\Repositories\Odin;

use App\Models\Odin\CompanyMediaAsset;
use Illuminate\Support\Collection;

interface CompanyMediaAssetRepositoryContract
{
    /**
     * Handles finding/retrieving a media file and returns its model.
     *
     * @param int|null    $company
     * @param string|null $url
     * @return CompanyMediaAsset|null
     */
    public function getMediaFile(?int $company = null, ?string $url = null): ?CompanyMediaAsset;

    /**
     * Handles finding/retrieving a YouTube asset and returns its model.
     *
     * @param int|null    $company
     * @param string|null $url
     * @return CompanyMediaAsset|null
     */
    public function getYoutubeAsset(?int $company = null, ?string $url = null): ?CompanyMediaAsset;

    /**
     * Handles finding/retrieving an attachment and returns its model.
     *
     * @param int|null    $company
     * @param string|null $url
     * @return CompanyMediaAsset|null
     */
    public function getAttachment(?int $company = null, ?string $url = null): ?CompanyMediaAsset;

    /**
     * Handles fetching a list of all media assets and returns their collection.
     *
     * @param int|null    $company
     * @param array|null  $types
     * @param string|null $sortCol
     * @param string|null $sortDir
     * @return Collection<CompanyMediaAsset>
     */
    public function getAllMediaAssets(?int    $company = null,
                                      ?array  $types   = null,
                                      ?string $sortCol = null,
                                      ?string $sortDir = null,
    ): Collection;

}
