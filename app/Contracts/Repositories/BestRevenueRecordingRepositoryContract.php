<?php

namespace App\Contracts\Repositories;

use App\Models\Legacy\BestRevenueScenarioRoofing;
use App\Models\Legacy\BestRevenueScenarioSolar;

interface BestRevenueRecordingRepositoryContract
{
    /**
     * @param string $zipCode
     * @param int $utilityId
     * @param bool $electricOver100
     * @param int $leadTypeId
     * @param int $revenue
     * @return BestRevenueScenarioSolar
     */
    public function updateSolar(string $zipCode, int $utilityId, bool $electricOver100, int $leadTypeId, int $revenue): ?BestRevenueScenarioSolar;

    /**
     * @param string $zipCode
     * @param int $leadTypeId
     * @param int $revenue
     * @return BestRevenueScenarioRoofing
     */
    public function updateRoofing(string $zipCode, int $leadTypeId, int $revenue): ?BestRevenueScenarioRoofing;
}
