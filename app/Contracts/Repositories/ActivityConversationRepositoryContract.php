<?php

namespace App\Contracts\Repositories;

use App\Models\ActivityConversation;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

interface ActivityConversationRepositoryContract
{
    /**
     * <PERSON>le creating a new conversation against an activity based on the requested parameters.
     *
     * @param int      $activityId
     * @param int      $parentId
     * @param int      $userId
     * @param string   $comment
     * @param int|null $id
     * @return ActivityConversation|Model
     */
    public function addActivityConversation(
        int    $activityId,
        int    $parentId,
        int    $userId,
        string $comment,
        ?int   $id = null
    ): ActivityConversation|Model;

    /**
     * Returns list of conversations against the requested activity.
     *
     * @param int $activityId
     * @return Collection<ActivityConversation>|null
     */
    public function getActivityConversations(int $activityId): ?Collection;
}
