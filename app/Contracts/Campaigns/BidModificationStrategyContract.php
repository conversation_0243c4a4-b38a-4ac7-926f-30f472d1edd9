<?php

namespace App\Contracts\Campaigns;

use App\Models\Campaigns\Modules\Budget\Budget;
use Illuminate\Support\Collection;

interface BidModificationStrategyContract
{
    /**
     * @param Budget $budget
     * @return float
     */
    public function calculateModifiedBidForBudget(Budget $budget): float;

    /**
     * @param Budget $budget
     * @return Budget
     */
    public function appendModifiedBidToBudget(Budget $budget): Budget;

    /**
     * @param Collection<int, Budget> $budgets
     * @return Collection<int, Budget>
     */
    public function appendModifiedBidToBudgets(Collection $budgets): Collection;
}
