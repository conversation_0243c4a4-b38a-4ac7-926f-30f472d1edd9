<?php

namespace App\Contracts\Marketing;

use App\Enums\MarketingCampaigns\EventInterpreter;
use App\Services\MarketingCampaign\MailchimpEventInterpreter;
use App\Services\MarketingCampaign\SocketLabsEventInterpreter;
use App\Services\MarketingCampaign\TwilioEventInterpreter;

class MarketingEventInterpreterFactory
{
    /**
     * @param EventInterpreter $interpreter
     * @return MarketingEventInterpreterContract
     */
    public static function make(
        EventInterpreter $interpreter
    ): MarketingEventInterpreterContract
    {
        return match ($interpreter) {
            EventInterpreter::SOCKET_LABS => new SocketLabsEventInterpreter(),
            EventInterpreter::MAILCHIMP   => new MailchimpEventInterpreter(),
            EventInterpreter::TWILIO      => new TwilioEventInterpreter(),
        };
    }

}
