<?php

namespace App\Observers;

use App\Enums\ActivityType;
use App\Models\Action;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Repositories\ActivityFeedRepository;

class ActionObserver
{
    public function __construct(
        protected ActivityFeedRepository $activityFeedRepository,
    ) {}

    public bool $afterCommit = true;

    /**
     * @param Action $action
     * @return bool
     */
    private function createActivityFeedEntry(Action $action): bool
    {
        if ($action->{Action::FIELD_FOR_RELATION_TYPE} === Action::RELATION_TYPE_COMPANY || $action->{Action::FIELD_FOR_RELATION_TYPE} === Action::RELATION_TYPE_COMPANY_CONTACT) {
            if($action->{Action::FIELD_FOR_RELATION_TYPE} === Action::RELATION_TYPE_COMPANY_CONTACT)
                $company = CompanyUser::query()->findOrFail($action->{Action::FIELD_FOR_ID})->company;
            else
                $company = Company::query()->findOrFail($action->{Action::FIELD_FOR_ID});

            $userId = $action->{Action::FIELD_FROM_USER_ID};

            $newActivity = $this->activityFeedRepository->createActivity(
                $action->{Action::FIELD_ID},
                ActivityType::ACTION,
                $company->{Company::FIELD_ID},
                $userId
            );

            if (!$newActivity) {
                logger()->error("Error creating new ActivityFeed item from Action model - id '{$action->{Action::FIELD_ID}}'.");

                return false;
            }
        }

        return true;
    }

    /**
     * Handle the Action "created" event.
     *
     * @param Action $action
     * @return void
     */
    public function created(Action $action): void
    {
        $this->createActivityFeedEntry($action);
    }

    /**
     * @param Action $action
     */
    public function updated(Action $action): void {
        $this->createActivityFeedEntry($action);
    }
}
