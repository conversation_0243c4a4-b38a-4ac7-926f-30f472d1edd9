<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Legacy\LeadCampaign;
use App\Workflows\WorkflowPayload;

class CampaignBudgetShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "campaign-budget";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return LeadCampaign::query()
                              ->where(LeadCampaign::ID, $payload->event->get('campaign_id'))
                              ->first()?->formatBudget() ?? "Unknown Campaign";
    }

    public function getLabel(): string
    {
        return "Campaign Budget";
    }
}
