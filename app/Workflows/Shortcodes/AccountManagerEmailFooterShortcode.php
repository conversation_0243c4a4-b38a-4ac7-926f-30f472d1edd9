<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\AccountManager;
use App\Models\AccountManagerClient;
use App\Models\Cadence\CadenceEmailHeaderFooter;
use App\Models\Odin\Company;
use App\Workflows\WorkflowPayload;
use Illuminate\Support\Str;

class AccountManagerEmailFooterShortcode extends WorkflowPipelineShortcode
{
    protected bool $isDeprecated = true;

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "account-manager-email-footer";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $companyId = $payload->event->get('company_id');

        if (!$companyId) return '';

        /** @var Company $company */
        $company = Company::query()->find($companyId);

        if (!$company) return '';

        $accountManagerId = $company
            ->accountManagerClient()
            ?->{AccountManagerClient::RELATION_ACCOUNT_MANAGER}
            ?->{AccountManager::FIELD_USER_ID};

        if (!$accountManagerId) return '';

        $cadenceFooter = CadenceEmailHeaderFooter::query()
            ->where(CadenceEmailHeaderFooter::FIELD_TYPE, CadenceEmailHeaderFooter::TYPE_FOOTER)
            ->where(CadenceEmailHeaderFooter::FIELD_USER_ID, $accountManagerId)
            ->first();

        if (!$cadenceFooter) return '';

        return Str::markdown($cadenceFooter->{CadenceEmailHeaderFooter::FIELD_CONTENT});
    }

    public function getLabel(): string
    {
        return "Account Manager Email Footer";
    }
}
