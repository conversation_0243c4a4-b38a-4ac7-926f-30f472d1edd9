<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Odin\Company;
use App\Models\Territory\RelationshipManager;
use App\Models\User;

abstract class BaseRelationshipManagerShortcode extends WorkflowPipelineShortcode
{
    /**
     * @param string $companyReference
     * @return User|null
     */
    protected function getRelationshipManager(string $companyReference): ?User
    {
        return Company::query()
            ->where(Company::FIELD_REFERENCE, $companyReference)
            ->first()
            ?->{Company::RELATION_RELATIONSHIP_MANAGER}
            ?->{RelationshipManager::RELATION_USER}
            ?? null;
    }
}