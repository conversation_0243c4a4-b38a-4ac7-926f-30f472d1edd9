<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Legacy\EloquentCompany;
use App\Workflows\WorkflowPayload;

class CompanyStatusShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "company-status";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $status = EloquentCompany::query()
                    ->where(EloquentCompany::REFERENCE, $payload->event->get('company_reference'))
                    ->first()
                    ?->{EloquentCompany::STATUS} ?? "Unknown Status";

        return ucwords($status);
    }

    public function getLabel(): string
    {
        return "Company Status";
    }
}
