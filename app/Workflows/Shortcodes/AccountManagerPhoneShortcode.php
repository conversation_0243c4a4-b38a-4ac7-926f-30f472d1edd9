<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\AccountManager;
use App\Models\AccountManagerClient;
use App\Models\Phone;
use App\Models\User;
use App\Workflows\WorkflowPayload;

class AccountManagerPhoneShortcode extends WorkflowPipelineShortcode
{
    protected bool $isDeprecated = true;

    /**
     * @return string
     */
    public function getKey(): string
    {
        return "account-manager-phone";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $companyReference = $payload->event->get('company_reference');

        /** @var AccountManager|null $accountManager */
        $accountManager = AccountManager::query()
            ->whereHas(AccountManager::RELATION_CLIENTS,
                fn($query) =>
                    $query->where(AccountManagerClient::FIELD_COMPANY_REFERENCE, $companyReference)
                          ->where(AccountManagerClient::FIELD_STATUS, AccountManagerClient::STATUS_ACTIVE)
            )->first();

        /** @var Phone|null $phone */
        $phone = $accountManager
            ?->user
            ?->primaryPhone();

        return $phone && $phone->{Phone::FIELD_STATUS} === Phone::STATUS_ACTIVE
            ? $phone->{Phone::FIELD_PHONE}
            : 'Unknown ' . $this->getLabel();
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return "Account Manager Phone";
    }
}
