<?php

namespace App\Workflows;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

/**
 * Data model for representing a given event for a workflow.
 */
class WorkflowEvent implements Arrayable
{
    public function __construct(public string $eventCategory, public string $eventName, public Collection $eventData) { }

    /**
     * <PERSON>les converting this data model to an array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            "event_category" => $this->eventCategory,
            "event_name"     => $this->eventName,
            "event_data"     => $this->eventData->toArray(),
        ];
    }

    /**
     * <PERSON>les creating a new WorkflowEvent from a serialized array.
     *
     * @param array $array
     * @return WorkflowEvent
     *
     * @see WorkflowEvent::toArray()
     */
    public static function fromArray(array $array): WorkflowEvent
    {
        if (!Arr::has($array, 'event_category') || !Arr::has($array, 'event_name') || !Arr::has($array, 'event_data'))
            throw new \RuntimeException("Invalid array spec passed.");

        return new WorkflowEvent($array["event_category"], $array['event_name'], collect($array["event_data"]));
    }

    /**
     * Sets a value on the event.
     *
     * @param string $key
     * @param mixed $value
     * @return $this
     */
    public function set(string $key, mixed $value): WorkflowEvent
    {
        $this->eventData->put($key, $value);

        return $this;
    }

    /**
     * Returns whether or not this event has data.
     *
     * @param string $key
     * @return bool
     */
    public function has(string $key): bool
    {
        return $this->eventData->has($key);
    }

    /**
     * Gets a value from the event.
     *
     * @param string $key
     * @param mixed|null $default
     * @return mixed
     */
    public function get(string $key, mixed $default = null): mixed
    {
        return $this->eventData->get($key, $default);
    }
}
