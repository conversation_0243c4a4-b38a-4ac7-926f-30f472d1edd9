<?php

namespace App\Listeners\ConsumerProcessing;

use App\Enums\ConsumerProcessing\ConsumerProcessingActivityType;
use App\Events\ConsumerProcessing\ConsumerProductApprovedEvent;
use Illuminate\Queue\InteractsWithQueue;

class ConsumerProductApprovedListener extends ConsumerProductQueueChangeBaseListener
{
    use InteractsWithQueue;

    /**
     * @param ConsumerProductApprovedEvent $event
     * @return void
     */
    public function handle(ConsumerProductApprovedEvent $event): void
    {
        $this->dispatchConsumerProcessingActivity(ConsumerProcessingActivityType::APPROVED, $event);
    }
}