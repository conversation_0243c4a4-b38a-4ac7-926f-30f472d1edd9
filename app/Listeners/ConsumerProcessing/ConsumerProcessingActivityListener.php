<?php

namespace App\Listeners\ConsumerProcessing;

use App\Enums\CommunicationRelationTypes;
use App\Enums\ConsumerProcessing\ConsumerProcessingActivityType;
use App\Events\ConsumerProcessing\ConsumerProcessingActivityEvent;
use App\Models\Call;
use App\Models\User;
use App\Services\ConsumerProcessingActivityService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class ConsumerProcessingActivityListener implements ShouldQueue
{
    use InteractsWithQueue;

    public function __construct(
        protected ConsumerProcessingActivityService $consumerProcessingActivityService,
    ) {}

    /**
     * @param ConsumerProcessingActivityEvent $event
     * @return void
     */
    public function handle(ConsumerProcessingActivityEvent $event): void
    {
        if (in_array($event->type, [ConsumerProcessingActivityType::TEXT, ConsumerProcessingActivityType::CALL]))
            $this->handleCallOrTextActivity($event);
        else
            $this->handleGenericActivity($event);
    }

    /**
     * @param ConsumerProcessingActivityEvent $event
     * @return void
     */
    private function handleCallOrTextActivity(ConsumerProcessingActivityEvent $event): void
    {
        if ($event->relatedActivity?->relation_type === CommunicationRelationTypes::CONSUMER_PRODUCT->value && $event->relatedActivity->direction === Call::DIRECTION_OUTBOUND) {
            $user = $event->user ?? $event->relatedActivity?->phone?->primaryUser() ?? User::systemUser();
            $this->consumerProcessingActivityService->createActivity(
                consumerProduct: $event->consumerProduct,
                type: $event->type,
                user: $user,
                relatedActivity: $event->relatedActivity,
            );
        }
    }

    /**
     * @param ConsumerProcessingActivityEvent $event
     * @return void
     */
    private function handleGenericActivity(ConsumerProcessingActivityEvent $event): void
    {
        $user = $event->user ?? User::systemUser();
        $this->consumerProcessingActivityService->createActivity(
            consumerProduct: $event->consumerProduct,
            type: $event->type,
            user: $user,
            comment: $event->comment,
            reason: $event->reason,
            scope: $event->scope ?? null,
        );
    }
}
