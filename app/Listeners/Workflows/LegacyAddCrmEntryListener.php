<?php

namespace App\Listeners\Workflows;

use App\Events\Workflows\ActionCreatedEvent;
use App\Services\Workflows\LegacyCRMEntryService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class LegacyAddCrmEntryListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @param LegacyCRMEntryService $legacyCRMEntryService
     */
    public function __construct(protected LegacyCRMEntryService $legacyCRMEntryService) {}

    /**
     * Handle the event.
     *
     * @param ActionCreatedEvent $event
     *
     * @return void
     */
    public function handle(ActionCreatedEvent $event): void
    {
        $this->legacyCRMEntryService->createCRMEntry($event->addNotificationAction);
    }
}
