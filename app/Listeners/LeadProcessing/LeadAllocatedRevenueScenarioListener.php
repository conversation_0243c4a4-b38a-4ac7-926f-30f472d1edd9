<?php

namespace App\Listeners\LeadProcessing;

use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Events\LeadProcessing\LeadAllocatedEvent;
use App\Repositories\Legacy\LeadRevenueScenarioRepository;
use Illuminate\Contracts\Queue\ShouldQueue;

class LeadAllocatedRevenueScenarioListener implements ShouldQueue
{
    /**
     * @var LeadRevenueScenarioRepository
     */
    protected $repository;

    /**
     * LeadAllocatedRevenueScenarioListener constructor.
     *
     * @param LeadRevenueScenarioRepository $repository
     */
    public function __construct(LeadRevenueScenarioRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Handles updating the revenue scenario sold for value for a given lead, once it has been allocated.
     *
     * @param LeadAllocatedEvent $event
     */
    public function handle(LeadAllocatedEvent $event)
    {
        $lead = $event->getLead();

        if($lead) {
            $companies = $lead->quoteCompanies;
            $total = $companies->sum(EloquentQuoteCompany::COST);

            $this->repository->updateSoldFor($lead->{EloquentQuote::REFERENCE}, $total);
        }
    }
}
