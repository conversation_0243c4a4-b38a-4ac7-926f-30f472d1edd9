<?php

namespace App\Listeners\CompanyCampaign;

use App\Events\CompanyCampaign\CampaignOverBudgetEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CampaignOverBudgetListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct() {}

    /**
     * Handle the event.
     */
    public function handle(CampaignOverBudgetEvent $event): void
    {
        //
    }
}
