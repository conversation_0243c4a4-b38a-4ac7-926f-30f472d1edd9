<?php

namespace App\Strategies\ProductAssignment;

use App\Contracts\ProductAssignment\MultiProductAssignmentStrategyContract;
use App\Contracts\ProductAssignment\ProductAssignmentStrategyContract;
use App\Contracts\ProductAssignment\UnderSoldStrategyContract;
use App\DataModels\Campaigns\ConsumerProject;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\QualityTier;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\ProductRepository;
use App\Repositories\Odin\SaleTypeRepository;
use App\Services\Campaigns\ProductBiddingService;
use App\Services\ConsumerProductLifecycleTrackingService;
use App\Strategies\ProductAssignment\ProductAssignmentStrategies\DirectLeadsAssignmentStrategy;
use App\Strategies\ProductAssignment\ProductAssignmentStrategies\LeadAssignmentStrategy;
use App\Transformers\ProductAssignment\ProductAssignmentTransformer;
use Illuminate\Support\Collection;

class StandardMultiProductAssignmentStrategy implements MultiProductAssignmentStrategyContract
{
    /**
     * @param UnderSoldStrategyContract $underSoldStrategy
     * @param ProductAssignmentTransformer $transformer
     * @param ProductRepository $productRepository
     * @param LocationRepository $locationRepository
     * @param ProductBiddingService $productBiddingService
     * @param SaleTypeRepository $saleTypeRepository
     */
    public function __construct(
        protected UnderSoldStrategyContract    $underSoldStrategy,
        protected ProductAssignmentTransformer $transformer,
        protected ProductRepository            $productRepository,
        protected LocationRepository           $locationRepository,
        protected ProductBiddingService        $productBiddingService,
        protected SaleTypeRepository           $saleTypeRepository,
    ) {}

    /** @inheritDoc */
    public function calculate(
        ConsumerProject $project,
        Collection $campaigns,
        Collection $productTypes,
        array $payload,
        ?ConsumerProductLifecycleTrackingService $tracker = null,
    ): Collection
    {
        $proposedAssignments = collect();

        foreach ($productTypes as $productType) {
            $existingProductAssignments = $project->productAssignments()->where(ProductAssignment::FIELD_DELIVERED, true);

            // All current allocations must be passed through as assertions to make sure they are included in the 'BRS' outcome
            $assertedProposedAssignments = $proposedAssignments->merge(
                $this->transformer->transformAssignmentsToProposedAssignments($existingProductAssignments)
            );

            $assignmentStrategy = $this->getAssignmentStrategy($productType);
            $scopedCampaigns    = $campaigns->where(CompanyCampaign::FIELD_PRODUCT_ID, $productType->id);
            $proposedAssignments = $proposedAssignments->merge(
                $assignmentStrategy?->calculate($project, $scopedCampaigns, $assertedProposedAssignments, $tracker)
            );

            if (!$this->underSoldStrategy->shouldContinueAssigningNow($project, $proposedAssignments, $productType)) {
                break;
            }
            else {
                $proposedAssignments->each(fn(ProposedProductAssignment &$assignment) => $assignment->isAsserted = true);
            }
        }

        $this->updateProposesAssignmentsSaleTypeAndPrice($proposedAssignments, $project);

        return $proposedAssignments;
    }

    /**
     * @param Collection<ProposedProductAssignment> $proposedAssignments
     * @param ConsumerProject $project
     *
     * @return void
     */
    protected function updateProposesAssignmentsSaleTypeAndPrice(Collection $proposedAssignments, ConsumerProject $project): void
    {
        //Update sale type and price for Direct Leads Assignments if sale types don't match
        if ($this->hasDirectLeadsAssignment($proposedAssignments)) {
            $maxSaleType = $this->getMaxSaleType($proposedAssignments);
            $countyLocation = $this->locationRepository->getCountyFromZipcode($project->zipCode());
            $stateLocation = $this->locationRepository->getStateByStateAbbr($countyLocation->state_abbr);

            $proposedAssignments->each(function (ProposedProductAssignment $proposedAssignment) use ($maxSaleType, $countyLocation, $stateLocation, $project) {
                if ($proposedAssignment->salesTypeId !== $maxSaleType) {
                    $proposedAssignment->salesTypeId = $maxSaleType;
                    $proposedAssignment->price = $this->productBiddingService->getProductBid(
                        companyCampaign: $proposedAssignment->campaign(),
                        countyLocationId: $countyLocation->id,
                        stateLocationId: $stateLocation->id,
                        propertyTypeId: $project->propertyTypeId(),
                        qualityTierId: $proposedAssignment->qualityTier()?->id ?? QualityTier::STANDARD->model()->id,
                        salesTypeId: $this->saleTypeRepository->find($maxSaleType)->id,
                    );
                }
            });
        }
    }

    /**
     * @param Collection<ProposedProductAssignment> $proposedAssignments
     *
     * @return bool
     */
    protected function hasDirectLeadsAssignment(Collection $proposedAssignments): bool
    {
        $directLeadsProductId = ProductEnum::DIRECT_LEADS->model()->id;

        return $proposedAssignments->filter(fn(ProposedProductAssignment $proposedAssignment) => $proposedAssignment->productId === $directLeadsProductId)->isNotEmpty();
    }

    protected function getMaxSaleType(Collection $proposedAssignments)
    {
        return $proposedAssignments->max(fn(ProposedProductAssignment $proposedAssignment) => $proposedAssignment->salesTypeId);
    }

    /**
     * @param Product $productType
     * @return ProductAssignmentStrategyContract|null
     */
    private function getAssignmentStrategy(Product $productType): ?ProductAssignmentStrategyContract
    {
        return match ($productType->id) {
            $this->productRepository->getLeadProductId() => app(LeadAssignmentStrategy::class),
            $this->productRepository->getAppointmentProductId() => app(LeadAssignmentStrategy::class),
            $this->productRepository->getDirectLeadProductId() => app(DirectLeadsAssignmentStrategy::class),
            default => null,
        };
    }
}
