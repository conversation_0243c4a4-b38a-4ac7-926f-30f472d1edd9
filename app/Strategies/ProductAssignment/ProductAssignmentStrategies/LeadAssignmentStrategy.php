<?php

namespace App\Strategies\ProductAssignment\ProductAssignmentStrategies;

use App\DataModels\Campaigns\ConsumerProject;
use App\Enums\Odin\Product;
use App\Services\ConsumerProductLifecycleTrackingService;
use Illuminate\Support\Collection;

class LeadAssignmentStrategy extends BaseAssignmentStrategy
{

    /** @inheritDoc */
    public function calculate(
        ConsumerProject $project,
        Collection $campaigns,
        Collection $assertedProductAssignments,
        ?ConsumerProductLifecycleTrackingService $tracker = null,
    ): Collection
    {
        return $this->calculateForProduct(Product::LEAD, $project, $campaigns, $assertedProductAssignments, $tracker);
    }
}
