<?php

namespace App\Strategies\CampaignBudget;

use App\Contracts\Strategies\CampaignBudget\CalculateBudgetStrategy;
use Illuminate\Database\Eloquent\Collection;

class CalculateUnverifiedBudgetStrategy implements CalculateBudgetStrategy
{
    /**
     * @inheritDoc
     */
    public function calculateBudgetAvailable(Collection $campaigns): array
    {
        // TODO: Implement calculateBudgetAvailable() method.
    }

    /**
     * @inheritDoc
     */
    public function calculateHistoricalBudgetAvailable(Collection $campaigns): array
    {
        // TODO: Implement calculateBudgetAvailable() method.
    }
}
