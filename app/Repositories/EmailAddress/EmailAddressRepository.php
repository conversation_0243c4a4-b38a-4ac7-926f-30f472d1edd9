<?php

namespace App\Repositories\EmailAddress;

use App\Models\EmailAddress;

class EmailAddressRepository
{
    public function findByEmail(
        string $email,
    ): ?EmailAddress
    {
        return EmailAddress::query()->where(EmailAddress::FIELD_EMAIL, $email)->first();
    }

    /**
     * @param string $email
     * @param string|null $sanitizedEmail
     * @param bool|null $valid
     * @param bool|null $disposable
     * @param bool|null $frequentComplainer
     * @param string|null $spamTrapScore
     * @param float|null $fraudScore
     * @param array $payload
     * @return EmailAddress
     */
    public function create(
        string $email,
        ?string $sanitizedEmail = null,
        ?bool $valid = null,
        ?bool $disposable = null,
        ?bool $frequentComplainer = null,
        ?string $spamTrapScore = null,
        ?float $fraudScore = null,
        array $payload = [],
    ): EmailAddress
    {
        return EmailAddress::query()->create([
            EmailAddress::FIELD_EMAIL               => $email,
            EMailAddress::FIELD_SANITIZED_EMAIL     => $sanitizedEmail,
            EmailAddress::FIELD_VALID               => $valid ?? false,
            EmailAddress::FIELD_DISPOSABLE          => $disposable,
            EmailAddress::FIELD_FREQUENT_COMPLAINER => $frequentComplainer,
            EmailAddress::FIELD_SPAM_TRAP_SCORE     => $spamTrapScore,
            EmailAddress::FIELD_FRAUD_SCORE         => $fraudScore,
            EmailAddress::FIELD_PAYLOAD             => $payload,
        ]);
    }

}
