<?php

namespace App\Repositories\BundleManagement;

use App\Builders\BundleBuilder;
use App\Models\Bundle;
use App\Models\Odin\Industry;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class BundleRepository
{
    /**
     * @return Collection
     */
    public function all(): Collection
    {
        return Bundle::active()->get();
    }

    /**
     * @param string $name
     * @param string $title
     * @param string $description
     * @param float $cost
     * @param string $credit
     * @param string|null $note
     * @param User|null $user
     * @param bool|null $active
     * @param int|null $industryId
     * @return Bundle
     */
    public function createBundle(
        string $name,
        string $title,
        string $description,
        float $cost,
        string $credit,
        ?string $note = null,
        ?User $user = null,
        bool $active = false,
        ?int $industryId = null
    ): Bundle
    {
        /** @var Bundle $bundle */
        $bundle = Bundle::query()->create([
            Bundle::FIELD_NAME => $name,
            Bundle::FIELD_NOTE => $note,
            Bundle::FIELD_TITLE => $title,
            Bundle::FIELD_DESCRIPTION => $description,
            Bundle::FIELD_COST => $cost,
            Bundle::FIELD_CREDIT => $credit,
            Bundle::FIELD_CREATED_BY => $user?->{User::FIELD_ID},
            Bundle::FIELD_ACTIVATED_AT => $active ? Carbon::now() : null,
            Bundle::FIELD_INDUSTRY_ID => $industryId
        ]);

        return $bundle;
    }

    /**
     * @param Bundle $bundle
     * @param string $name
     * @param string $title
     * @param string $description
     * @param float $cost
     * @param string $credit
     * @param string|null $note
     * @param User|null $user
     * @param bool|null $active
     * @param int|null $industryId
     * @return Bundle
     */
    public function updateBundle(
        Bundle $bundle,
        string $name,
        string $title,
        string $description,
        float $cost,
        string $credit,
        ?string $note = null,
        ?User $user = null,
        bool $active = false,
        ?int $industryId = null
    ): Bundle
    {
        $bundle->update([
            Bundle::FIELD_NAME => $name,
            Bundle::FIELD_NOTE => $note,
            Bundle::FIELD_TITLE => $title,
            Bundle::FIELD_DESCRIPTION => $description,
            Bundle::FIELD_COST => $cost,
            Bundle::FIELD_CREDIT => $credit,
            Bundle::FIELD_CREATED_BY => $user?->{User::FIELD_ID},
            Bundle::FIELD_ACTIVATED_AT => $active ? Carbon::now() : null,
            Bundle::FIELD_INDUSTRY_ID => $industryId
        ]);

        return $bundle;
    }

    /**
     * @param Bundle $bundle
     * @return bool|null
     */
    public function deleteBundle(Bundle $bundle): ?bool
    {
        return $bundle->delete();
    }


    /**
     * @param string|null $bundleName
     * @param float|null $costFrom
     * @param float|null $costTo
     * @param float|null $creditFrom
     * @param float|null $creditTo
     * @param bool|null $active
     * @param bool|null $industryId
     * @param string|null $sortCol
     * @param string|null $sortDir
     * @param bool|null $autoApprovedOnly
     * @return Builder
     */
    public function getBundlesWithSearchFilters(
        ?string $bundleName = null,
        ?float $costFrom = null,
        ?float $costTo = null,
        ?float $creditFrom = null,
        ?float $creditTo = null,
        ?bool $active = null,
        ?bool $industryId = null,
        ?string $sortCol = null,
        ?string $sortDir = null,
        ?bool $autoApprovedOnly = null,
    ): Builder
    {
        return BundleBuilder::query()
            ->forBundleName($bundleName)
            ->forCostFrom($costFrom)
            ->forCostTo($costTo)
            ->forCreditFrom($creditFrom)
            ->forCreditTo($creditTo)
            ->forActive($active)
            ->forIndustry($industryId)
            ->forAutoApprovedOnly($autoApprovedOnly)
            ->sortDirection($sortDir)
            ->sortColumn($sortCol)
            ->getQuery();
    }

    /**
     * Gets only active bundles.
     *
     * @return Collection<Bundle>
     */
    public function getActiveNonIndustryBundles(): Collection
    {
        // Get active bundles that are not industry specific
        return Bundle::query()
            ->whereNotNull(Bundle::FIELD_ACTIVATED_AT)
            ->whereNULL(Bundle::FIELD_INDUSTRY_ID)
            ->orderBy(Bundle::FIELD_COST, 'asc')
            ->get();
    }

    /**
     * Returns active bundles belonging to an industry with the provided slug
     *
     * @param string $industrySlug
     * @return Collection<Bundle>
     */
    public function getBundlesByIndustry(string $industrySlug = ''): Collection
    {
        return Bundle::query()
            ->select([Bundle::TABLE . '.*'])
            ->join(Industry::TABLE, Industry::TABLE . '.' . Industry::FIELD_ID, '=', Bundle::TABLE . '.' . Bundle::FIELD_INDUSTRY_ID)
            ->whereNotNull(Bundle::TABLE . '.' . Bundle::FIELD_ACTIVATED_AT)
            ->where(Industry::TABLE . '.' . Industry::FIELD_SLUG, "=", $industrySlug)
            ->orderBy(Bundle::TABLE . '.' . Bundle::FIELD_COST, 'asc')
            ->get();
    }

}
