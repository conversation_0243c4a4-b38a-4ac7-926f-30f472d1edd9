<?php

namespace App\Repositories\MarketingCampaign;

use App\DTO\MarketingCampaign\CampaignMetrics;
use App\DTO\MarketingCampaign\EmailCampaignMetrics;
use App\Enums\MarketingCampaigns\MarketingCampaignStatus;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCancellation;
use App\Models\Odin\ProductRejection;
use App\Services\MarketingCampaign\CallbackPayload\CallbackPayloadType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class MarketingCampaignRepository
{
    /**
     * @param string|null $name
     * @param array<MarketingCampaignType>|null $types
     * @param array|null $statuses
     * @return Builder
     */
    public function getBaseQuery(
        ?string                  $name = null,
        ?array                   $types = null,
        ?array                   $statuses = null,
    ): Builder
    {
        return MarketingCampaign::query()
            ->when(filled($statuses), function (Builder $builder) use ($statuses) {
                $builder->whereIn(MarketingCampaign::FIELD_STATUS, $statuses);
            })
            ->when($name, function (Builder $builder) use ($name) {
                $builder->where(MarketingCampaign::FIELD_NAME, 'LIKE', '%' . $name . '%');
            })
            ->when($types, function (Builder $builder) use ($types) {
                $builder->whereIn(MarketingCampaign::FIELD_TYPE, $types);
            });
    }

    /**
     * @param MarketingCampaignStatus|null $status
     * @param string|null $name
     * @param array<MarketingCampaignType>|null $types
     * @return Builder
     */
    public function listMarketingCampaigns(
        ?MarketingCampaignStatus $status = null,
        ?string                  $name = null,
        ?array                   $types = null,
    ): Builder
    {
        $subQuery = MarketingCampaignConsumer::query()->select([
            MarketingCampaignConsumer::TABLE . '.' . MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_ID,
            DB::raw('COALESCE(SUM(' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COST . '), 0) as revenue'),
        ])->join(
            ProductAssignment::TABLE,
            ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
            MarketingCampaignConsumer::TABLE . '.' . MarketingCampaignConsumer::FIELD_CLONED_CONSUMER_PRODUCT_ID,
        )
            ->leftJoin(ProductRejection::TABLE, function ($join) {
                $join
                    ->on(
                        ProductRejection::TABLE . '.' . ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID,
                        '=',
                        ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID
                    )
                    ->whereNull(ProductRejection::TABLE . '.' . ProductRejection::FIELD_DELETED_AT);
            })->leftJoin(ProductCancellation::TABLE, function ($join) {
                $join
                    ->on(
                        ProductCancellation::TABLE . '.' . ProductCancellation::FIELD_PRODUCT_ASSIGNMENT_ID,
                        '=',
                        ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID
                    )
                    ->whereNull(ProductCancellation::TABLE . '.' . ProductCancellation::FIELD_DELETED_AT);
            })
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true)
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE, true)
            ->whereNull(ProductRejection::TABLE . '.' . ProductRejection::FIELD_ID)
            ->whereNull(ProductCancellation::TABLE . '.' . ProductCancellation::FIELD_ID)
            ->groupBy(MarketingCampaignConsumer::TABLE . '.' . MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_ID);

        return $this->getBaseQuery(
            name: $name,
            types: $types,
            statuses: array_filter([$status])
        )->leftJoinSub($subQuery, 'sub',
            'sub' . '.' . MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_ID,
            '=',
            MarketingCampaign::TABLE . '.' . MarketingCampaign::FIELD_ID
        )->orderByDesc(MarketingCampaign::FIELD_ID);
    }

    /**
     * @param array|null $statuses
     * @param array|null $type
     * @return Collection
     */
    public function getMarketingCampaignListOptions(
        ?array $statuses = null,
        ?array $type = null,
    ): Collection
    {
        $query = $this->getBaseQuery(types: $type, statuses: $statuses);

        return $query->select(
            MarketingCampaign::FIELD_ID,
            MarketingCampaign::FIELD_NAME,
            MarketingCampaign::FIELD_STATUS,
        )->get();

    }

    /**
     * @param int $id
     * @return MarketingCampaign|null
     */
    public function getMarketingCampaignById(int $id): ?MarketingCampaign
    {
        /** @var ?MarketingCampaign */
        return MarketingCampaign::query()->find($id);
    }

    /**
     * @param string $externalReference
     * @return MarketingCampaign|null
     */
    public function getMarketingCampaignByExternalReference(string $externalReference): ?MarketingCampaign
    {
        /** @var ?MarketingCampaign */
        return MarketingCampaign::query()->where(MarketingCampaign::FIELD_EXTERNAL_REFERENCE, '=', $externalReference)?->first();
    }

    /**
     * @param int $authorId
     * @param string $name
     * @param MarketingCampaignStatus $status
     * @param MarketingCampaignType $type
     * @param CallbackPayloadType $callbackPayloadType
     * @param string|null $description
     * @param string|null $externalReference
     * @param array|null $configuration
     * @return MarketingCampaign
     */
    public function createNewMarketingCampaign(
        int                     $authorId,
        string                  $name,
        MarketingCampaignStatus $status,
        MarketingCampaignType   $type,
        CallbackPayloadType     $callbackPayloadType,
        ?string                 $description = null,
        ?string                 $externalReference = null,
        ?array                  $configuration = [],
    ): MarketingCampaign
    {
        $sentAt = Arr::get($configuration, 'sent_at');

        if (!empty($sentAt)) {
            unset($configuration['sent_at']);
        }

        /** @var MarketingCampaign */
        return MarketingCampaign::query()->create([
            MarketingCampaign::FIELD_NAME               => $name,
            MarketingCampaign::FIELD_DESCRIPTION        => $description,
            MarketingCampaign::FIELD_CREATED_BY         => $authorId,
            MarketingCampaign::FIELD_UPDATED_BY         => $authorId,
            MarketingCampaign::FIELD_STATUS             => $status,
            MarketingCampaign::FIELD_TYPE               => $type,
            MarketingCampaign::FIELD_CALLBACK_PAYLOAD   => $callbackPayloadType->toProperArray(),
            MarketingCampaign::FIELD_EXTERNAL_REFERENCE => $externalReference,
            MarketingCampaign::FIELD_CONFIGURATION      => $configuration,
            MarketingCampaign::FIELD_SENT_AT            => $sentAt
        ]);
    }

    /**
     * @param MarketingCampaign $marketingCampaign
     * @param int|null $authorId
     * @param string|null $name
     * @param string|null $description
     * @param MarketingCampaignStatus|null $status
     * @param string|null $externalReference
     * @param string|null $externalCode
     * @param Carbon|null $sentAt
     * @param CallbackPayloadType|null $callbackPayload
     * @param EmailCampaignMetrics|null $metrics
     * @param array|null $configuration
     * @return bool
     */
    public function updateMarketingCampaign(
        MarketingCampaign        $marketingCampaign,
        ?int                     $authorId = null,
        ?string                  $name = null,
        ?string                  $description = null,
        ?MarketingCampaignStatus $status = null,
        ?string                  $externalReference = null,
        ?string                  $externalCode = null,
        ?Carbon                  $sentAt = null,
        ?CallbackPayloadType     $callbackPayload = null,
        ?CampaignMetrics         $metrics = null,
        ?array                   $configuration = null,
    ): bool
    {
        $metricsData = $marketingCampaign->{MarketingCampaign::FIELD_METRICS};

        if ($metrics) {
            $filledMetrics = array_filter($metrics->toArray(), function ($val) {
                return filled($val);
            });

            if (empty($metricsData)) {
                $metricsData = [];
            }

            $metricsData = array_merge($metricsData, $filledMetrics);
        }

        $configData = $marketingCampaign->{MarketingCampaign::FIELD_CONFIGURATION};

        if ($configuration) {
            $editableConfig = array_filter($configuration, function ($val) {
                return filled($val);
            });

            if (empty($configData)) {
                $configData = [];
            }

            $configData = array_merge($configData, $editableConfig);
        }

        if (!empty($sentAt)) {
            unset($configuration['sent_at']);
        }

        return $marketingCampaign->update([
            MarketingCampaign::FIELD_NAME               => $name ?? $marketingCampaign->{MarketingCampaign::FIELD_NAME},
            MarketingCampaign::FIELD_DESCRIPTION        => $description ?? $marketingCampaign->{MarketingCampaign::FIELD_DESCRIPTION},
            MarketingCampaign::FIELD_STATUS             => $status ?? $marketingCampaign->{MarketingCampaign::FIELD_STATUS},
            MarketingCampaign::FIELD_SENT_AT            => $sentAt ?? $marketingCampaign->{MarketingCampaign::FIELD_SENT_AT},
            MarketingCampaign::FIELD_EXTERNAL_REFERENCE => $externalReference ?? $marketingCampaign->{MarketingCampaign::FIELD_EXTERNAL_REFERENCE},
            MarketingCampaign::FIELD_CODE               => $externalCode ?? $marketingCampaign->{MarketingCampaign::FIELD_CODE},
            MarketingCampaign::FIELD_UPDATED_BY         => $authorId ?? $marketingCampaign->{MarketingCampaign::FIELD_UPDATED_BY},
            MarketingCampaign::FIELD_CALLBACK_PAYLOAD   => $callbackPayload ? $callbackPayload->toProperArray() : $marketingCampaign->{MarketingCampaign::FIELD_CALLBACK_PAYLOAD},
            MarketingCampaign::FIELD_METRICS            => $metricsData,
            MarketingCampaign::FIELD_CONFIGURATION      => $configData
        ]);
    }

    /**
     * @param MarketingCampaign $campaign
     * @return bool|null
     */
    public function deleteMarketingCampaign(
        MarketingCampaign $campaign
    ): ?bool
    {
        return $campaign->delete();
    }

}
