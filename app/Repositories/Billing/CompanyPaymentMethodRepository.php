<?php

namespace App\Repositories\Billing;

use App\Enums\Billing\PaymentMethodServices;
use App\Models\Billing\CompanyPaymentMethod;

class CompanyPaymentMethodRepository
{
    /**
     * @param int $companyId
     * @param PaymentMethodServices $type
     * @param string $addedByType
     * @param bool $isDefault
     * @param int|null $addedById
     * @param string|null $paymentGatewayPaymentMethodCode
     * @param string|null $paymentGatewayClientCode
     * @return CompanyPaymentMethod
     */
    public function create(
        int $companyId,
        PaymentMethodServices $type,
        string $addedByType,
        bool $isDefault,
        ?int $addedById = null,
        ?string $paymentGatewayPaymentMethodCode = null,
        ?string $paymentGatewayClientCode = null,
        ?string $expiryMonth = null,
        ?string $expiryYear = null,
        ?string $number = null,
    ): CompanyPaymentMethod
    {
        return CompanyPaymentMethod::query()
            ->create([
                CompanyPaymentMethod::FIELD_COMPANY_ID                          => $companyId,
                CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE => $paymentGatewayPaymentMethodCode,
                CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_CLIENT_CODE         => $paymentGatewayClientCode,
                CompanyPaymentMethod::FIELD_TYPE                                => $type->value,
                CompanyPaymentMethod::FIELD_ADDED_BY_TYPE                       => $addedByType,
                CompanyPaymentMethod::FIELD_ADDED_BY_ID                         => $addedById,
                CompanyPaymentMethod::FIELD_IS_DEFAULT                          => $isDefault,
                CompanyPaymentMethod::FIELD_EXPIRY_MONTH                        => $expiryMonth,
                CompanyPaymentMethod::FIELD_EXPIRY_YEAR                         => $expiryYear,
                CompanyPaymentMethod::FIELD_NUMBER                              => $number,
            ]);
    }

    /**
     * @param string $externalPaymentMethodId
     * @return ?CompanyPaymentMethod
     */
    public function getByExternalId(string $externalPaymentMethodId): ?CompanyPaymentMethod
    {
        return CompanyPaymentMethod::query()
            ->where(CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE, $externalPaymentMethodId)
            ->first();
    }

    /**
     * @param int $companyId
     * @return string|null
     */
    public function getStripeClientCodeByCompanyId(
        int $companyId
    ): ?string
    {
        return CompanyPaymentMethod::onlyStripe()
            ->where(CompanyPaymentMethod::FIELD_COMPANY_ID, $companyId)
            ->first()
            ?->{CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_CLIENT_CODE};
    }

    public function deleteCompanyPaymentMethod(int $companyPaymentMethodId): void
    {
        CompanyPaymentMethod::query()
            ->where(CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE, $companyPaymentMethodId)
            ->delete();
    }
}
