<?php

namespace App\Repositories\Odin;

use App\Builders\CompanyMediaAssetBuilder;
use App\Contracts\Repositories\Odin\CompanyMediaAssetRepositoryContract;
use App\Enums\CompanyMediaAssetType;
use App\Models\Odin\CompanyMediaAsset;
use Illuminate\Support\Collection;

class CompanyMediaAssetRepository implements CompanyMediaAssetRepositoryContract
{
    /**
     * @inheritDoc
     */
    public function getMediaFile(?int $company = null, ?string $url = null): ?CompanyMediaAsset
    {
        /** @var CompanyMediaAsset|null $mediaFile */
        $mediaFile = CompanyMediaAssetBuilder::query()
            ->forCompany($company)
            ->forUrl($url)
            ->forType(CompanyMediaAssetType::MEDIA)
            ->first();

        return $mediaFile;
    }

    /**
     * @inheritDoc
     */
    public function getYoutubeAsset(?int $company = null, ?string $url = null): ?CompanyMediaAsset
    {
        /** @var CompanyMediaAsset|null $youtubeAsset */
        $youtubeAsset = CompanyMediaAssetBuilder::query()
            ->forCompany($company)
            ->forUrl($url)
            ->forType(CompanyMediaAssetType::LINK)
            ->first();

        return $youtubeAsset;
    }

    /**
     * @inheritDoc
     */
    public function getAttachment(?int $company = null, ?string $url = null): ?CompanyMediaAsset
    {
        /** @var CompanyMediaAsset|null $attachment */
        $attachment = CompanyMediaAssetBuilder::query()
            ->forCompany($company)
            ->forUrl($url)
            ->forType(CompanyMediaAssetType::ATTACHMENT)
            ->first();

        return $attachment;
    }

    /**
     * @inheritDoc
     */
    public function getAllMediaAssets(?int    $company = null,
                                      ?array  $types   = null,
                                      ?string $sortCol = null,
                                      ?string $sortDir = null,
    ): Collection
    {
        /** @var CompanyMediaAssetBuilder $query */
        $query = CompanyMediaAssetBuilder::query()
            ->forCompany($company)
            ->forTypes($types)
            ->sortBy($sortCol, $sortDir);

        return $query->get();
    }
}
