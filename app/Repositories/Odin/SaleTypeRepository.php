<?php

namespace App\Repositories\Odin;

use App\Models\SaleType;

class SaleTypeRepository
{
    /**
     * @return array
     */
    public function all(): array
    {
        // TODO: do this properally, don't use a database, and don't use the name key here that I have to do temporarily.
        return SaleType::query()->pluck(SaleType::FIELD_ID, SaleType::FIELD_NAME)->toArray();
    }

    /**
     * @param string $salesType
     * @return SaleType|null
     */
    public function getSalesTypeByName(string $salesType): ?SaleType
    {
        return SaleType::query()
                       ->where(SaleType::FIELD_NAME, $salesType)
                       ->get()
                       ->first();
    }

    /**
     * @param string $salesType
     * @return SaleType|null
     */
    public function getSalesTypeByKey(string $salesType): ?SaleType
    {
        return SaleType::query()
            ->where(SaleType::FIELD_KEY, $salesType)
            ->get()
            ->first();
    }

    /**
     * @param int $saleTypeId
     *
     * @return SaleType|null
     */
    public function find(int $saleTypeId): SaleType|null
    {
        /** @var SaleType|null */
        return SaleType::query()->find($saleTypeId);
    }
}
