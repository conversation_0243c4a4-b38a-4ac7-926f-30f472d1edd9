<?php

namespace App\Repositories\Odin;

use App\Builders\CompanyUser\CompanyUserBuilder;
use App\Contracts\Repositories\Odin\CompanyUserRepositoryContract;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\CompanyUserData;
use App\Repositories\CompanyContactRepository;
use App\Repositories\Legacy\LegacyUserRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Ramsey\Uuid\Uuid;


class CompanyUserRepository implements CompanyUserRepositoryContract
{
    /**
     * @param LegacyUserRepository $legacyUserRepository
     * @param CompanyContactRepository $companyContactRepository
     */
    public function __construct(
        protected LegacyUserRepository     $legacyUserRepository,
        protected CompanyContactRepository $companyContactRepository
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function findCompanyUserByIdOrFail(int $id): CompanyUser|Model
    {
        return CompanyUser::query()->findOrFail($id);
    }

    /**
     * Find a CompanyUser based on the legacy_id, and whether the legacy origin was a Contact or User
     *
     * @param int $id
     * @param int $isContact
     * @return CompanyUser|Model
     * @throws ModelNotFoundException
     */
    public function findCompanyUserByLegacyIdAndModelOrFail(int $id, int $isContact): CompanyUser|Model
    {
        $companyUser = CompanyUser::query()
            ->where(CompanyUser::FIELD_LEGACY_ID, $id)
            ->where(CompanyUser::FIELD_IS_CONTACT, $isContact)
            ->first();

        if (!$companyUser) throw (new ModelNotFoundException)->setModel(CompanyUser::class);
        return $companyUser;
    }

    /**
     * @inheritDoc
     */
    public function getCompanyUsersAgainstCompanyId(int $company): Builder
    {
        return CompanyUser::query()
            ->where(CompanyUser::FIELD_COMPANY_ID, $company)
            ->where(CompanyUser::FIELD_IS_CONTACT, false)
            ->latest();
    }

    /**
     * @inheritDoc
     */
    public function getCompanyUser(int $company, int $user): CompanyUser
    {
        /** @var CompanyUser $companyUser */
        $companyUser = CompanyUser::query()
            ->where(CompanyUser::FIELD_IS_CONTACT, false)
            ->where(CompanyUser::FIELD_COMPANY_ID, $company)
            ->where(CompanyUser::FIELD_ID, $user)
            ->firstOrFail();
        return $companyUser;
    }

    public function getCompanyUserOrCompanyContact(int $company, int $user): CompanyUser
    {
        /** @var CompanyUser $companyUser */
        $companyUser = CompanyUser::query()
            ->where(CompanyUser::FIELD_COMPANY_ID, $company)
            ->where(CompanyUser::FIELD_ID, $user)
            ->firstOrFail();

        return $companyUser;
    }

    /**
     * @inheritDoc
     */
    public function getCompanyUserByReference(string $reference): CompanyUser|Model
    {
        return CompanyUser::query()->where(CompanyUser::FIELD_REFERENCE, $reference)->firstOrFail();
    }

    /**
     * @inheritDoc
     */
    public function createCompanyUser(int $companyId, array $data): ?CompanyUser
    {
        $company = Company::query()->findOrFail($companyId);
        $data[CompanyUser::FIELD_COMPANY_ID] = $data[CompanyUser::FIELD_COMPANY_ID] ?? $companyId;

        $newUser = $this->createCompanyUserFromAttributes($data);

        if(array_key_exists('user_fields', $data))
            $this->updateUserFields($newUser, $data);

        $this->legacyUserRepository->createCompanyUser($company->{Company::FIELD_REFERENCE}, $newUser->toArray());

        return $newUser;
    }

    /**
     * @inheritDoc
     */
    public function updateCompanyUser(string $companyReference, CompanyUser $user, array $data, ?bool $returnModel = false): bool|CompanyUser
    {
        $this->updateModel($user, $data);

        if (!$companyReference || !$user->{CompanyUser::FIELD_LEGACY_ID}) return false;

        $legacySync = $this->legacyUserRepository->updateCompanyUser($companyReference, $user->{CompanyUser::FIELD_LEGACY_ID}, $data);

        return $returnModel
            ? $user->refresh()
            : $legacySync ?? true;
    }

    /**
     * @inheritDoc
     */
    public function updateModel(CompanyUser $user, array $data): bool
    {
        $user->fill(collect($data)->except(['user_data'])->toArray());
        $result = $user->save();

        if(!$result)
            return $result;

        $this->updateUserFields($user, $data);

        return $result;
    }

    protected function updateUserFields(CompanyUser $user, array $data): void
    {
        if(array_key_exists('user_fields', $data)
            && array_key_exists('global', $data['user_fields']))
        {
            $data = collect($data['user_fields']["global"])->pluck('value', 'key');
            $user->data()->updateOrCreate(
                [CompanyUserData::FIELD_COMPANY_USER_ID => $user->id],
                [
                    CompanyUserData::FIELD_COMPANY_USER_ID => $user->id,
                    CompanyUserData::FIELD_PAYLOAD => $data->toArray(),
                ]
            );
        }

    }

    /**
     * @inheritDoc
     */
    public function deleteCompanyUser(string $companyReference, CompanyUser $user): bool
    {
        $legacyUserId = $user->{CompanyUser::FIELD_LEGACY_ID};
        $isContact = $user->{CompanyUser::FIELD_IS_CONTACT};

        $user->delete();

        if (!$companyReference || !$legacyUserId) return false;
        else {
            $legacySync = $isContact
                ? $this->companyContactRepository->deleteCompanyContact($companyReference, $legacyUserId)
                : $this->legacyUserRepository->deleteCompanyUser($companyReference, $legacyUserId);
            return $legacySync ?? true;
        }

    }

    /**
     * @inheritDoc
     */
    public function getCompanyContactsAgainstCompanyId(int $company): Collection
    {
        return CompanyUser::query()
            ->where(CompanyUser::FIELD_COMPANY_ID, $company)
            ->where(CompanyUser::FIELD_IS_CONTACT, true)
            ->orderBy(CompanyUser::FIELD_PINNED, 'desc')
            ->latest()
            ->withCount(['office_phone_calls', 'cell_phone_calls'])
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getCompanyContact(int $company, int $contact): CompanyUser|Model
    {
        return CompanyUser::query()
            ->where(CompanyUser::FIELD_IS_CONTACT, true)
            ->where(CompanyUser::FIELD_COMPANY_ID, $company)
            ->where(CompanyUser::FIELD_ID, $contact)
            ->firstOrFail();
    }

    /**
     * @param int $legacyId
     * @return CompanyUser|Model
     */
    public function getCompanyContactByLegacyIdWithTrashedOrFail(int $legacyId): CompanyUser|Model
    {
        return CompanyUser::query()
            ->where(CompanyUser::FIELD_IS_CONTACT,CompanyUser::USER_IS_CONTACT)
            ->where(CompanyUser::FIELD_LEGACY_ID, $legacyId)
            ->withTrashed()
            ->firstOrFail();
    }

    /**
     * @inheritDoc
     */
    public function createCompanyContact(Company $company, array $data, ?bool $returnModel = false): array
    {
        $data[CompanyUser::FIELD_COMPANY_ID] = $company->{Company::FIELD_ID};
        $data[CompanyUser::FIELD_REFERENCE]  = Uuid::uuid4()->toString();
        $data[CompanyUser::FIELD_IS_CONTACT] = true;
        $data[CompanyUser::FIELD_LAST_NAME]  = $data[CompanyUser::FIELD_LAST_NAME] ?? "";

        $newUser = $this->createCompanyUserFromAttributes($data);

        $this->updateUserFields($newUser, $data);

        $status = false;

        if (config('services.google.pubsub.service_account')) {
            $status = $this->companyContactRepository->addCompanyContact($company->{Company::FIELD_REFERENCE}, $data);
        }

        return [
            'legacyStatus' => $status,
            ...$returnModel ? ['contact' => $newUser] : []
        ];
    }

    /**
     * Handles creating a new company user
     *
     * @param array $data
     * @return CompanyUser
     */
    public function createCompanyUserFromAttributes(array $data = []): CompanyUser
    {
        $user = new CompanyUser();
        $user->fill($data);
        $user->save();

        return $user;
    }

    /**
     * @inheritDoc
     */
    public function updateCompanyContact(string $companyReference, CompanyUser $contact, array $data, ?bool $returnModel = false): bool|CompanyUser
    {
        $this->updateModel($contact, $data);

        if (!$companyReference || !$contact->{CompanyUser::FIELD_LEGACY_ID}) return false;

        if (config('services.google.pubsub.service_account')) {
            $legacySync = $this->companyContactRepository->updateCompanyContact($companyReference, $contact->{CompanyUser::FIELD_LEGACY_ID}, $data);
            return $returnModel
                ? $contact->refresh()
                : $legacySync;
        }
        else {
            return false;
        }
    }

    /**
     * @param int $legacyId
     * @param int $companyId
     *
     * @return CompanyUser
     */
    public function getCompanyUserByLegacyIdAndCompanyIdOrFail(int $legacyId, int $companyId): CompanyUser
    {
        /** @var CompanyUser $companyUser */
        $companyUser = CompanyUser::query()->where(CompanyUser::FIELD_LEGACY_ID, $legacyId)
            ->where(CompanyUser::FIELD_COMPANY_ID, $companyId)
            ->where(CompanyUser::FIELD_CAN_LOG_IN, true)
            ->firstOrFail();

        return $companyUser;
    }

    /**
     * @param int $legacyId
     * @param int $companyId
     *
     * @return CompanyUser|null
     */
    public function  getCompanyUserByLegacyIdAndCompany(int $legacyId, int $companyId): ?CompanyUser
    {
        /** @var CompanyUser|null */
        return CompanyUser::query()
            ->where(CompanyUser::FIELD_LEGACY_ID, $legacyId)
            ->where(CompanyUser::FIELD_COMPANY_ID, $companyId)
            ->first();
    }

    /**
     * @param string $email
     *
     * @return CompanyUser|null
     */
    public function getCompanyUserByEmailAndLegacyId(string $email, int $legacyId): ?CompanyUser
    {
        /** @var CompanyUser|null */
        return CompanyUser::query()
            ->where(CompanyUser::FIELD_EMAIL, $email)
            ->where(CompanyUser::FIELD_LEGACY_ID, $legacyId)
            ->first();
    }

    /**
     * @param string $email
     * @return CompanyUser|null
     */
    public function findCompanyUserByEmailOrFail(string $email): ?CompanyUser
    {
        /** @var CompanyUser $companyUser */
        $companyUser = CompanyUser::query()
            ->where(CompanyUser::FIELD_EMAIL, $email)
            ->firstOrFail();
        return $companyUser;
    }

    /**
     * @param array $data
     * @return void
     */
    protected function getOfficePhone(array &$data): void
    {
        if ($data[CompanyUser::FIELD_OFFICE_PHONE] === null
            || !strlen(trim($data[CompanyUser::FIELD_OFFICE_PHONE])))
            $data[CompanyUser::FIELD_OFFICE_PHONE] = $data[CompanyUser::FIELD_CELL_PHONE];
    }


    /**
     * Get company users by phone
     * @param string $phone
     * @return Collection
     */
    public function getCompanyUsersByPhone(string $phone): Collection
    {
        $trimPhone = trim($phone);

        //laravel tries to cast string to int on formatted numbers, can result in false matches
        $castable = filter_var($phone, FILTER_VALIDATE_INT) !== false;

        return CompanyUser::query()
            ->select([
                Company::TABLE . '.' . Company::FIELD_ID . ' as company_id',
                Company::TABLE . '.' . Company::FIELD_NAME . ' as company_name',
                CompanyUser::TABLE . '.' . '*'
            ])
            ->where(DB::raw('TRIM('.CompanyUser::FIELD_CELL_PHONE.')'),$trimPhone)
            ->orWhere(DB::raw('TRIM('.CompanyUser::FIELD_OFFICE_PHONE.')'),$trimPhone)
            ->when($castable, function (Builder $builder) use ($phone){
                $builder->orWhere(CompanyUser::FIELD_FORMATTED_CELL_PHONE, $phone)
                    ->orWhere(CompanyUser::FIELD_FORMATTED_OFFICE_PHONE, $phone);
            })
            ->join(Company::TABLE, Company::TABLE . '.' . Company::FIELD_ID, '=', CompanyUser::TABLE . '.' . CompanyUser::FIELD_COMPANY_ID)
            ->get();
    }


    /**
     * Get company user by id
     * @param string $companyUserId
     * @return Model
     */
    public function getCompanyUserById(string $companyUserId): Model
    {
        return CompanyUser::query()->find($companyUserId);
    }

    public function getCompanyUserDataByKey(CompanyUser $user, string $key, mixed $default = null): mixed
    {
        if (!$user->data) return $default;

        return $user->data->payload[$key] ?? $default;
    }

    /**
     * @param array $companyIds
     * @return Collection
     */
    public function getCompanyActiveCompanyUsersFromCompanyIds(array $companyIds): Collection
    {
        return CompanyUser::query()
            ->whereIn(CompanyUser::FIELD_COMPANY_ID, $companyIds)
            ->where(CompanyUser::FIELD_IS_CONTACT, false)
            ->where(CompanyUser::FIELD_STATUS, true)
            ->get();
    }

    /**
     * @param string $phone
     * @param int $companyId
     * @return Model|null
     */
    public function getCompanyUserByPhoneAndCompanyId(string $phone, int $companyId): ?CompanyUser
    {
        return CompanyUser::query()
            ->select([
                Company::TABLE . '.' . Company::FIELD_ID . ' as company_id',
                Company::TABLE . '.' . Company::FIELD_NAME . ' as company_name',
                CompanyUser::TABLE . '.' . '*'
            ])
            ->where(function ($query) use ($phone) {
                $query
                    ->where(CompanyUser::FIELD_CELL_PHONE, $phone)
                    ->orWhere(CompanyUser::FIELD_OFFICE_PHONE, $phone);
            })
            ->where(CompanyUser::FIELD_COMPANY_ID, $companyId)
            ->join(Company::TABLE, Company::TABLE . '.' . Company::FIELD_ID, '=', CompanyUser::TABLE . '.' . CompanyUser::FIELD_COMPANY_ID)
            ->first();
    }

    /**
     * @param int|null $companyId
     * @return Builder
     */
    public function list(
        ?int $companyId = null,
    ): Builder
    {
        return CompanyUserBuilder::query()
            ->forCompany(companyId: $companyId)
            ->getQuery();
    }
}
