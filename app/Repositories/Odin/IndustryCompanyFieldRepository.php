<?php

namespace App\Repositories\Odin;

use App\Contracts\Repositories\Odin\IndustryCompanyFieldRepositoryContract;
use App\DataModels\Odin\ConfigurableFieldDataModel;
use App\Enums\Odin\CompanyConfigurableFieldCategory;
use App\Models\Odin\IndustryCompanyField;
use Illuminate\Support\Collection;

class IndustryCompanyFieldRepository implements IndustryCompanyFieldRepositoryContract
{
    /**
     * @inheritDoc
     */
    public function getIndustryCompanyFields(int $industry): Collection
    {
        return IndustryCompanyField::query()->where(IndustryCompanyField::FIELD_INDUSTRY_ID, $industry)->latest()->get();
    }

    /**
     * @inheritDoc
     */
    public function updateOrCreateIndustryCompanyField(
        int    $industry,
        string $name,
        string $key,
        int    $type,
        int    $showOnProfile,
        int    $showOnDashboard,
        ConfigurableFieldDataModel $payload,
        CompanyConfigurableFieldCategory $category,
        ?int   $id = null
    ): bool
    {
        return IndustryCompanyField::query()->updateOrCreate(
                [
                    IndustryCompanyField::FIELD_ID => $id
                ],
                [
                    IndustryCompanyField::FIELD_INDUSTRY_ID         => $industry,
                    IndustryCompanyField::FIELD_NAME                => $name,
                    IndustryCompanyField::FIELD_KEY                 => $key,
                    IndustryCompanyField::FIELD_TYPE                => $type,
                    IndustryCompanyField::FIELD_SHOW_ON_PROFILE     => $showOnProfile === 1 ?? 0,
                    IndustryCompanyField::FIELD_SHOW_ON_DASHBOARD   => $showOnDashboard === 1 ?? 0,
                    IndustryCompanyField::FIELD_PAYLOAD             => $payload,
                    IndustryCompanyField::FIELD_CATEGORY            => $category
                ]
            ) !== null;
    }

    /**
     * @inheritDoc
     */
    public function deleteIndustryCompanyField(int $id): bool
    {
        return IndustryCompanyField::query()->where(IndustryCompanyField::FIELD_ID, $id)->delete();
    }
}
