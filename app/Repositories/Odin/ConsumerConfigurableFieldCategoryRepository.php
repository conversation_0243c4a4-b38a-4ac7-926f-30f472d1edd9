<?php

namespace App\Repositories\Odin;

use App\Contracts\Repositories\Odin\ConsumerConfigurableFieldCategoriesRepositoryContract;
use App\Models\Odin\ConsumerConfigurableFieldCategory;
use Illuminate\Support\Collection;

class ConsumerConfigurableFieldCategoryRepository implements ConsumerConfigurableFieldCategoriesRepositoryContract
{
    /**
     * Returns all consumer configurable field categories from database
     *
     * @return Collection<ConsumerConfigurableFieldCategory>
     */
    public function getAll(): Collection
    {
        return ConsumerConfigurableFieldCategory::all();
    }

    /**
     * Returns consumer configurable field categories that should be displayed on the frontend
     *
     * @return Collection<ConsumerConfigurableFieldCategory>
     */
    public function getLeadDisplayCategories(): Collection
    {
        return ConsumerConfigurableFieldCategory::query()
            ->where('slug', '<>', 'uncategorized')
            ->get();
    }
}
