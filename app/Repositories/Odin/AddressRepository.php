<?php

namespace App\Repositories\Odin;

use App\Contracts\Repositories\Odin\AddressRepositoryContract;
use App\Models\Odin\Address;
use App\Repositories\Legacy\ZipCodeSetRepository;
use App\Services\AddressIdentification\AddressIdentificationServiceFactory;
use Illuminate\Support\Arr;

class AddressRepository implements AddressRepositoryContract
{
    /**
     * @param ZipCodeSetRepository $zipCodeSetRepository
     */
    public function __construct(protected ZipCodeSetRepository $zipCodeSetRepository)
    {}

    /**
     * @inheritDoc
     */
    public function createAddressFromAttributes(array $data = []): Address
    {
        $address = new Address();

        $zipCode = Arr::get($data, Address::FIELD_ZIP_CODE);

        if (gettype($zipCode) === 'string') {
            $data[Address::FIELD_UTC] = $this->zipCodeSetRepository->getUtc($zipCode);
        }

        $address->fill(collect($data)->filter(fn($value) => $value !== null)->toArray());
        $address->save();

        return $address;
    }

    /**
     * @inheritDoc
     */
    public function findByLegacyIdOrFail(int $legacyId): Address
    {
        /** @var Address $address */
        $address = Address::query()->where(Address::FIELD_LEGACY_ID, $legacyId)->firstOrFail();

        return $address;
    }

    /**
     * @inheritDoc
     */
    public function findByLegacyId(int $legacyId): ?Address
    {
        /** @var Address $address */
        $address = Address::query()->where(Address::FIELD_LEGACY_ID, $legacyId)->first();

        return $address;
    }

    /**
     * @inheritDoc
     */
    public function findByIdOrFail(int $id): Address
    {
        /** @var Address $address */
        $address = Address::query()->findOrFail($id);

        return $address;
    }

    /**
     * @inheritDoc
     */
    public function updateAddress(Address $address, array $data): bool
    {
        if(array_key_exists(Address::FIELD_ZIP_CODE, $data))
            $data[Address::FIELD_UTC] = $this->zipCodeSetRepository->getUtc($data[Address::FIELD_ZIP_CODE]);

        return $address->update($data);
    }

    /**
     * Fetch Google place-id via Google ID service
     * @param Address $address
     * @return bool
     */
    public function getGooglePlaceId(Address $address): string
    {
        $addressIdentificationService = AddressIdentificationServiceFactory::make();
        return $addressIdentificationService->getIdFromAddressComponents(
            $address->{Address::FIELD_ADDRESS_1},
            $address->{Address::FIELD_CITY},
            $address->{Address::FIELD_STATE},
            $address->{Address::FIELD_ZIP_CODE},
            $address->{Address::FIELD_ADDRESS_2},
        );
    }

    /**
     * Fetch place-id via Google ID service then update the Address model
     * @param Address $address
     * @return bool
     */
    public function updateGooglePlaceId(Address $address): bool
    {
        $newPlaceId = $this->getGooglePlaceId($address);
        if ($newPlaceId) {
            $address->update([ Address::FIELD_PLACE_ID => $newPlaceId ]);
            return true;
        }
        else return false;
    }
}
