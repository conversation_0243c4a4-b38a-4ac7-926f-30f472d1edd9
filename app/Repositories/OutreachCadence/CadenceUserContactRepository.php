<?php

namespace App\Repositories\OutreachCadence;

use App\Models\Cadence\BaseModel;
use App\Models\Cadence\CadenceUserContactExclusion;
use App\Models\Cadence\CompanyCadenceRoutine;
use App\Models\Cadence\CompanyCadenceScheduledGroupAction;
use App\Models\Odin\CompanyUser;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class CadenceUserContactRepository
{
    const ROUTINE_EXCLUSION_STATUS              = 'cadence_routine_exclusion_status';
    const ROUTINE_EXCLUSION_STATUS_EXCLUDE_ALL  = 'exclude_all';
    const ROUTINE_EXCLUSION_STATUS_EXCLUDE_THIS = 'exclude_this';
    const ROUTINE_EXCLUSION_STATUS_ALLOW        = 'allow';
    const UPDATE_CONFIG_USER_ID                 = 'userId';
    const UPDATE_CONFIG_NEW_STATUS              = 'newStatus';


    /**
     * @param CompanyCadenceRoutine $routine
     * @return Collection<int, CompanyUser>
     */
    public function getExcludedCompanyUsers(CompanyCadenceRoutine $routine): Collection
    {
        return $this->getCompanyUserJoinQuery($routine->company_id)
                    ->whereNotNull(CadenceUserContactExclusion::TABLE . '.' . BaseModel::FIELD_ID)
                    ->whereNull(CadenceUserContactExclusion::TABLE . '.' . CadenceUserContactExclusion::FIELD_COMPANY_CADENCE_ROUTINE_ID)
                    ->orWhere(CadenceUserContactExclusion::TABLE . '.' . CadenceUserContactExclusion::FIELD_COMPANY_CADENCE_ROUTINE_ID, $routine->id)
                    ->get();
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @return Collection<int, CompanyUser>
     */
    public function getContactableCompanyUsersForAction(CompanyCadenceScheduledGroupAction $action): Collection
    {
        $routine   = $action->group->routine;
        $companyId = $routine->company_id;
        $query     = $this->getCompanyUserJoinQuery($companyId)
                          ->where(function ($q) use ($routine) {
                              $q->whereNull(CadenceUserContactExclusion::TABLE . '.' . BaseModel::FIELD_ID)
                                ->orWhere(
                                    CadenceUserContactExclusion::TABLE . '.' . CadenceUserContactExclusion::FIELD_COMPANY_CADENCE_ROUTINE_ID,
                                    $routine->id
                                );
                          });

        if ($routine->contact_decision_makers_only)
            $query->where(CompanyUser::TABLE . '.' . CompanyUser::FIELD_IS_DECISION_MAKER, true);

        return $query->get();
    }

    /**
     * @param int $routineId
     * @return Collection<int, CompanyUser>
     */
    public function getRoutineContacts(int $routineId): Collection
    {
        /** @var CompanyCadenceRoutine $routine */
        $routine         = CompanyCadenceRoutine::find($routineId);
        $companyContacts = CompanyUser::query()
                                      ->where(CompanyUser::FIELD_COMPANY_ID, $routine->company_id)
                                      ->where(CompanyUser::FIELD_STATUS, CompanyUser::STATUS_ACTIVE)
                                      ->get();
        $exclusions      = CadenceUserContactExclusion::query()->whereIn(CadenceUserContactExclusion::FIELD_USER_ID, $companyContacts->pluck(CompanyUser::FIELD_ID)->toArray())->get();
        return $companyContacts->map(function ($user) use ($exclusions, $routineId) {
            /** @var CompanyUser $user */
            /** @var CadenceUserContactExclusion $exclusion */
            $exclusion = $exclusions->where(CadenceUserContactExclusion::FIELD_USER_ID, $user->id)->first();

            // allow by default
            $user[self::ROUTINE_EXCLUSION_STATUS] = self::ROUTINE_EXCLUSION_STATUS_ALLOW;

            // if routine id is null - always exclude
            if ($exclusion && !$exclusion->company_cadence_routine_id)
                $user[self::ROUTINE_EXCLUSION_STATUS] = self::ROUTINE_EXCLUSION_STATUS_EXCLUDE_ALL;

            // if routine id matches - exclude from this routine
            elseif ($exclusion && $exclusion->company_cadence_routine_id === $routineId)
                $user[self::ROUTINE_EXCLUSION_STATUS] = self::ROUTINE_EXCLUSION_STATUS_EXCLUDE_THIS;

            // else delete exclusion because it's from an old routine
            elseif ($exclusion)
                $exclusion->delete();

            return $user;
        });
    }

    /**
     * @param $companyId
     * @return Builder
     */
    private function getCompanyUserJoinQuery($companyId): Builder
    {
        return CompanyUser::query()
                          ->select(CompanyUser::TABLE . '.*')
                          ->leftJoin(
                              CadenceUserContactExclusion::TABLE,
                              CompanyUser::TABLE . '.' . CompanyUser::FIELD_ID,
                              '=',
                              CadenceUserContactExclusion::TABLE . '.' . CadenceUserContactExclusion::FIELD_USER_ID
                          )
                          ->where(CompanyUser::TABLE . '.' . CompanyUser::FIELD_COMPANY_ID, $companyId)
                          ->where(CompanyUser::FIELD_STATUS, CompanyUser::STATUS_ACTIVE);
    }

    /**
     * @param int $routineId
     * @param array $contactUpdates
     * @return void
     */
    public function updateRoutineContacts(int $routineId, array $contactUpdates): void
    {
        $contactUpdates = collect($contactUpdates);
        $companyUsers   = CompanyUser::query()->whereIn(CompanyUser::FIELD_ID, $contactUpdates->pluck(self::UPDATE_CONFIG_USER_ID)->toArray())->get();
        /** @var CompanyUser $user */
        foreach ($companyUsers as $user) {
            $updateConfig = $contactUpdates->where(self::UPDATE_CONFIG_USER_ID, $user->id)->first();

            switch ($updateConfig[self::UPDATE_CONFIG_NEW_STATUS]) {
                case self::ROUTINE_EXCLUSION_STATUS_ALLOW:
                    CadenceUserContactExclusion::query()->where(CadenceUserContactExclusion::FIELD_USER_ID, $user->id)->delete();
                    break;
                case self::ROUTINE_EXCLUSION_STATUS_EXCLUDE_THIS:
                    CadenceUserContactExclusion::query()->updateOrCreate(
                        [CadenceUserContactExclusion::FIELD_USER_ID => $user->id],
                        [CadenceUserContactExclusion::FIELD_COMPANY_CADENCE_ROUTINE_ID => $routineId]
                    );
                    break;
                case self::ROUTINE_EXCLUSION_STATUS_EXCLUDE_ALL:
                    CadenceUserContactExclusion::query()->updateOrCreate(
                        [CadenceUserContactExclusion::FIELD_USER_ID => $user->id],
                        [CadenceUserContactExclusion::FIELD_COMPANY_CADENCE_ROUTINE_ID => null]
                    );
            }

        }
    }
}
