<?php

namespace App\Repositories\Conference;

use App\Models\Conference\ConferenceRecording;

class ConferenceRecordingRepository
{
    /**
     * @param string $conferenceId
     * @param string $externalId
     * @param string|null $externalDestinationFileId
     * @param string|null $startTime
     * @param string|null $endTime
     * @param int|null $durationInSeconds
     * @param string|null $bucketPath
     * @return ConferenceRecording
     */
    public function updateOrCreate(
        string $conferenceId,
        string $externalId,
        ?string $externalDestinationFileId = null,
        ?string $startTime = null,
        ?string $endTime = null,
        ?int $durationInSeconds = null,
        ?string $bucketPath = null,
    ): ConferenceRecording
    {
        return ConferenceRecording::query()
            ->updateOrCreate([
                ConferenceRecording::FIELD_EXTERNAL_ID   => $externalId,
                ConferenceRecording::FIELD_CONFERENCE_ID => $conferenceId,
            ],
                [
                    ConferenceRecording::FIELD_EXTERNAL_DESTINATION_FILE_ID => $externalDestinationFileId,
                    ConferenceRecording::FIELD_BUCKET_PATH                  => $bucketPath,
                    ConferenceRecording::FIELD_START_TIME                   => $startTime,
                    ConferenceRecording::FIELD_END_TIME                     => $endTime,
                    ConferenceRecording::FIELD_DURATION_IN_SECONDS          => $durationInSeconds,
                ]);
    }
}
