<?php

namespace App\Repositories\Legacy;


use App\Models\Legacy\SolarCalculatorEngine\GeneralCostModifier;
use App\Models\Legacy\SolarCalculatorEngine\InstallerSystemOffer;
use App\Models\Legacy\SolarCalculatorEngine\ProductCostModifier;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;

class SystemCostRepository
{
    const DEFAULT_CUSTOMER_SECTOR = InstallerSystemOffer::CUSTOMER_SECTOR_TYPE_RESIDENTIAL;
    const DEFAULT_SYSTEM_TYPE = InstallerSystemOffer::SYSTEM_TYPE_GRID_TIED;
    const DEFAULT_IS_DIY = false;
    const USER_SYSTEM_COUNT_MINIMUM = 10;
    const MIN_COST_PER_WATT = 'min(cost_per_watt)';

    // This is a fallback in the event a generated system cannot be found
    const DEFAULT_GENERATED_COST = 3.2;

    /**
     * @param string $stateAbbr
     * @param float $systemSize
     * @param string $customerSector
     * @param string $systemType
     * @param bool $isDiy
     * @param string|null $panelManufacturerReference
     * @param string|null $inverterManufacturerReference
     * @param string|null $batteryManufacturerReference
     * @return float|int
     */
    public function getCostInState(
        string $stateAbbr,
        float $systemSize,
        string $customerSector = self::DEFAULT_CUSTOMER_SECTOR,
        string $systemType = self::DEFAULT_SYSTEM_TYPE,
        bool $isDiy = self::DEFAULT_IS_DIY,
        string $panelManufacturerReference = null,
        string $inverterManufacturerReference = null,
        string $batteryManufacturerReference = null
    ): float|int
    {
        // todo: lookup manufacturers in user systems
        $query = InstallerSystemOffer::query()->selectRaw(self::MIN_COST_PER_WATT)
            ->whereNested(function(\Illuminate\Database\Query\Builder $query) {
                $query->where(InstallerSystemOffer::FIELD_IS_GENERATED,false)
                    ->orWhere(InstallerSystemOffer::FIELD_IS_GENERATED, null);
            })
            ->where(InstallerSystemOffer::FIELD_STATE_ABBR, $stateAbbr)
            ->where(InstallerSystemOffer::FIELD_MIN_SIZE, '<=', $systemSize)
            ->where(InstallerSystemOffer::FIELD_MAX_SIZE, '>=', $systemSize)
            ->where(InstallerSystemOffer::FIELD_CUSTOMER_SECTOR_TYPE, $customerSector)
            ->where(InstallerSystemOffer::FIELD_SYSTEM_TYPE, $systemType)
            ->where(InstallerSystemOffer::FIELD_IS_DIY, $isDiy)
            ->where(InstallerSystemOffer::FIELD_IS_ACTIVE, true)
            ->orderBy(InstallerSystemOffer::FIELD_COST_PER_WATT)
            ->groupBy(InstallerSystemOffer::FIELD_INSTALLER_REFERENCE);

        /** @var InstallerSystemOffer[]|Collection $offers */
        $offers = $query->get();

        if($offers->count() < self::USER_SYSTEM_COUNT_MINIMUM) {
            $generatedSystemCost = $this->getGeneratedSystemCostInState($stateAbbr, $systemSize);
            $multiplier = $this->getTotalMultiplier($customerSector, $systemType, $isDiy, $panelManufacturerReference, $inverterManufacturerReference, $batteryManufacturerReference);

            return $this->getModifiedCost($multiplier, $offers, $generatedSystemCost);
        } else {
            return Arr::get($query->skip($offers->count()/2)->first(),self::MIN_COST_PER_WATT);
        }
    }

    /**
     * @param string $stateAbbr
     * @param float|int $systemSize
     * @return float
     */
    private function getGeneratedSystemCostInState(string $stateAbbr, float|int $systemSize): float
    {
        /** @var InstallerSystemOffer $generatedSystem */
        $generatedSystem = InstallerSystemOffer::query()
            ->where(InstallerSystemOffer::FIELD_IS_GENERATED, true)
            ->where(InstallerSystemOffer::FIELD_STATE_ABBR, $stateAbbr)
            ->where(InstallerSystemOffer::FIELD_MIN_SIZE, '<=', $systemSize)
            ->where(InstallerSystemOffer::FIELD_MAX_SIZE, '>=', $systemSize)
            ->first();

        if($generatedSystem) {
            return $generatedSystem->cost_per_watt;
        }

        return self::DEFAULT_GENERATED_COST;
    }

    /**
     * @param string|null $customerSector
     * @param string|null $systemType
     * @param bool|null $isDiy
     * @param string|null $panelManufacturerReference
     * @param string|null $inverterManufacturerReference
     * @param string|null $batteryManufacturerReference
     * @return float|int
     */
    private function getTotalMultiplier(
        ?string $customerSector = self::DEFAULT_CUSTOMER_SECTOR,
        ?string $systemType = self::DEFAULT_SYSTEM_TYPE,
        ?bool   $isDiy = self::DEFAULT_IS_DIY,
        string  $panelManufacturerReference = null,
        string  $inverterManufacturerReference = null,
        string $batteryManufacturerReference = null
    ): float|int
    {
        $generalModifiers = GeneralCostModifier::query()
            ->where(function($query) use ($customerSector) {
                $query->where(GeneralCostModifier::FIELD_FIELD_NAME, GeneralCostModifier::FIELD_NAME_CUSTOMER_SECTOR_TYPE)
                    ->where(GeneralCostModifier::FIELD_FIELD_VALUE, $customerSector);
            })->orWhere(function($query) use ($systemType) {
                $query->where(GeneralCostModifier::FIELD_FIELD_NAME, GeneralCostModifier::FIELD_NAME_SYSTEM_TYPE)
                    ->where(GeneralCostModifier::FIELD_FIELD_VALUE, $systemType);
            })->orWhere(function($query) use ($isDiy) {
                $query->where(GeneralCostModifier::FIELD_FIELD_NAME, GeneralCostModifier::FIELD_NAME_IS_DIY)
                    ->where(GeneralCostModifier::FIELD_FIELD_VALUE, $isDiy);
            })->get();

        $generalModifierValue = $generalModifiers->reduce(function ($a, $c) {
            return $a * $c->multiplier;
        }, 1);

        $manufacturerModifiers = ProductCostModifier::query()
            ->where(function ($query) use ($panelManufacturerReference) {
                $query->where(ProductCostModifier::FIELD_PRODUCT_TYPE, ProductCostModifier::PRODUCT_TYPE_PANELS)
                    ->where(ProductCostModifier::FIELD_MANUFACTURER_REFERENCE, $panelManufacturerReference);
            })->orWhere(function ($query) use ($inverterManufacturerReference) {
                $query->where(ProductCostModifier::FIELD_PRODUCT_TYPE, ProductCostModifier::PRODUCT_TYPE_INVERTERS)
                    ->where(ProductCostModifier::FIELD_MANUFACTURER_REFERENCE, $inverterManufacturerReference);
            })->orWhere(function ($query) use ($batteryManufacturerReference) {
                $query->where(ProductCostModifier::FIELD_PRODUCT_TYPE, ProductCostModifier::PRODUCT_TYPE_BATTERIES)
                    ->where(ProductCostModifier::FIELD_MANUFACTURER_REFERENCE, $batteryManufacturerReference);
            })->get();

        $manufacturerModifierValue = $manufacturerModifiers->reduce(function ($a, $c) {
            return $a * $c->multiplier;
        }, 1);

        return $generalModifierValue * $manufacturerModifierValue;
    }

    /**
     * @param float|int $multiplier
     * @param Collection|InstallerSystemOffer[]|null $userOffers
     * @param float|int $generatedSystemCost
     * @return float|int
     */
    private function getModifiedCost(float|int $multiplier, array|Collection|null $userOffers, float|int $generatedSystemCost): float|int
    {
        $weightedGeneratedCost = ($generatedSystemCost * $multiplier) * (self::USER_SYSTEM_COUNT_MINIMUM - $userOffers->count());
        $weightedUserCost = $userOffers->sum(self::MIN_COST_PER_WATT);

        return ($weightedGeneratedCost + $weightedUserCost) / self::USER_SYSTEM_COUNT_MINIMUM;
    }
}
