<?php

namespace App\Repositories\Legacy;

use App\Models\Legacy\LeadSalesType;
use Illuminate\Database\Eloquent\Model;

class LeadSaleTypeRepository
{
    /**
     * @param string $leadSaleTypeKeyValue
     * @return LeadSalesType|null
     */
    public function getByKeyValue(string $leadSaleTypeKeyValue): ?LeadSalesType
    {
        $leadSaleTypeKeyValue = str_replace('-', '_', strtoupper(trim($leadSaleTypeKeyValue)));

        /** @var LeadSalesType|null $model */
        $model = LeadSalesType::query()
            ->where(LeadSalesType::KEY_VALUE, $leadSaleTypeKeyValue)
            ->first();

        return $model;
    }
}
