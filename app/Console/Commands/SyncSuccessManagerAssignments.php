<?php

namespace App\Console\Commands;

use App\Enums\ActivityLog\ActivityLogDescription;
use App\Models\SuccessManagerClient;
use App\Models\Odin\Company;
use App\Models\User;
use App\Services\SuccessManagerService;
use Illuminate\Console\Command;

class SyncSuccessManagerAssignments extends Command
{
    const string CHOICE_YES = 'Yes';
    const string CHOICE_NO  = 'No';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:legacy-success-manager-assignments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync legacy company success managers';

    public function __construct(
        protected SuccessManagerService $successManagerService,
    )
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $choice = $this->choice('Do you want to specify Company Legacy IDs to sync?', [self::CHOICE_NO, self::CHOICE_YES]);

        if ($choice === self::CHOICE_YES) {
            $this->handleSpecificCompanies();
        } else {
            $this->handleAllCompanies();
        }
    }

    private function handleAllCompanies(): void
    {
        $count = Company::query()
            ->whereNotNull(Company::FIELD_LEGACY_ID)
            ->count();

        $bar = $this->output->createProgressBar($count);
        $bar->start();

        Company::query()
            ->whereNotNull(Company::FIELD_LEGACY_ID)
            ->with(Company::RELATION_LEGACY_COMPANY)
            ->chunk(500, function($companies) use ($bar) {
                foreach ($companies as $company) {
                    $status = $this->syncSuccessManagerAssignment($company);

                    if ($status !== true) $this->alert($status);
                    $bar->advance();
                }
            });

        $bar->finish();
    }

    /**
     * @return void
     */

    private function handleSpecificCompanies(): void
    {
        $legacyIds = $this->ask('Enter comma separated Company Legacy IDs');
        $legacyIds = explode(",", trim($legacyIds));

        $count = Company::query()
            ->whereIn(Company::FIELD_LEGACY_ID, $legacyIds)
            ->count();

        $bar = $this->output->createProgressBar($count);
        $bar->start();

        Company::query()
            ->whereIn(Company::FIELD_LEGACY_ID, $legacyIds)
            ->with(Company::RELATION_LEGACY_COMPANY)
            ->chunk(500, function($companies) use ($bar) {
                foreach ($companies as $company) {
                    $status = $this->syncSuccessManagerAssignment($company);

                    if ($status !== true) $this->alert($status);
                    $bar->advance();
                }
            });

        $bar->finish();
    }

    /**
     * @param Company $company
     * @return bool|string
     */
    private function syncSuccessManagerAssignment(Company $company): bool|string
    {
        $legacyCompany = $company->legacyCompany;

        if (!$legacyCompany) return "Legacy company does not exist for company ID: {$company->id}";

        $legacyUserId = $legacyCompany->salesconsultantid;

        if($legacyUserId !== 0) {
            $successManagerId = $this->getSuccessManagerIdByLegacyUserId($legacyUserId);

            if (!$successManagerId) return "Success manager does not exist for legacy user ID: {$legacyUserId}, (company {$company->id})";

            $currentSuccessManagerId = $this->getSuccessManagerIdByCompanyReference($company->reference);

            if($successManagerId !== $currentSuccessManagerId) {
                $this->successManagerService->updateCompanySuccessManager(
                    ActivityLogDescription::SYSTEM_UPDATED,
                    $company,
                    $successManagerId
                );
            }
        } else {
            $this->successManagerService->updateCompanySuccessManager(
                ActivityLogDescription::SYSTEM_UPDATED,
                $company,
            );
        }

        return true;
    }

    /**
     * @param string $companyReference
     * @return int|null
     */
    private function getSuccessManagerIdByCompanyReference(string $companyReference): ?int
    {
        /** @var SuccessManagerClient|null $successManagerClient */
        $successManagerClient = SuccessManagerClient::query()
            ->where(SuccessManagerClient::FIELD_STATUS, SuccessManagerClient::STATUS_ACTIVE)
            ->where(SuccessManagerClient::FIELD_COMPANY_REFERENCE, $companyReference)
            ->first();

        return $successManagerClient?->success_manager_id;
    }

    /**
     * @param int $legacyUserId
     * @return int|null
     */
    private function getSuccessManagerIdByLegacyUserId(int $legacyUserId): ?int
    {
        /** @var User|null $user */
        $user = User::query()
            ->where(User::FIELD_LEGACY_USER_ID, $legacyUserId)
            ->first();

        return $user?->successManager?->id;
    }
}
