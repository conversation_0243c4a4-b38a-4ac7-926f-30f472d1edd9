<?php

namespace App\Console\Commands;

use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyReview;
use App\Models\Odin\CompanyReviewData;
use App\Models\Odin\CompanyService;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\CompanyUserLog;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductRejection;
use Illuminate\Console\Command;
use function app;

class TruncateAllLegacyMigrationModels extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:truncate-all';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Truncate all data from legacy migration commands';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {

        $modelsToTruncate = collect([
            ProductRejection::class,
            ProductAssignment::class,
            ConsumerProductData::class,
            ConsumerProductTracking::class,
            ConsumerProduct::class,
            Consumer::class,
            CompanyReviewData::class,
            CompanyReview::class,
            CompanyUserLog::class,
            CompanyUser::class,
            CompanyData::class,
            CompanyLocation::class,
            CompanyService::class,
            CompanyIndustry::class,
            Company::class,
            Address::class,
        ]);

        $this->warn('This command will destroy all previously migrated legacy data in Models:');
        $modelsToTruncate->each(fn($model) => $this->info("  - {$model}"));

        if ($this->confirm('Are you sure you wish to continue?')) {
            $modelsToTruncate->each(fn($model) => app($model)::truncate());

            $this->warn('Data destroyed. Pew pew pew.');
            return 1;
        }

        $this->line('Operation aborted, no data destroyed. You coward.');
        return 0;
    }

}
