<?php

namespace App\Console\Commands;

use App\Models\Sales\Task;
use App\Repositories\TaskRepository;
use Illuminate\Console\Command;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Symfony\Component\Console\Helper\ProgressBar;

class CalculateExistingTaskTimezones extends Command
{
    /** Chunk count to curve any potential memory issues */
    const DEFAULT_CHUNK_COUNT = 100;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "calculate:task-timezones";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles calculating existing task timezones.';

    /**
     * Execute the console command.
     *
     * @return int
     * @throws BindingResolutionException
     */
    public function handle(): int
    {
        $repository = $this->getRepository();
        $total = $this->getQuery()->count();
        $this->info("Calculating timezones for $total task(s).");

        $progress = $this->getProgressBar($total);
        $progress->start();

        $this->getQuery()->chunk(self::DEFAULT_CHUNK_COUNT, function($tasks) use (&$progress, $repository) {
            /** @var Task $task */
            foreach($tasks as $task)
                $this->calculateTimezoneForTask($task, $repository);

            $progress->advance(count($tasks));
        });

        $progress->finish();

        return 0;
    }

    protected function calculateTimezoneForTask(Task $task, TaskRepository $repository): void
    {
        $repository->storeTimezoneForTask($task);
    }

    /**
     * Gets the query for retrieving tasks without a timezone.
     *
     * @return Builder
     */
    protected function getQuery(): Builder
    {
        return Task::query()
            ->where(Task::FIELD_COMPLETED, false)
            ->where(Task::FIELD_TIMEZONE, null);
    }

    /**
     * Helper function that returns the task repository.
     *
     * @return TaskRepository
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    protected function getRepository(): TaskRepository
    {
        /** @var TaskRepository $repository */
        $repository = app()->make(TaskRepository::class);

        return $repository;
    }

    /**
     * Helper function for creating a progress bar.
     *
     * @param int $count
     * @return ProgressBar
     */
    protected function getProgressBar(int $count = 0): ProgressBar
    {
        return $this->output->createProgressBar($count);
    }
}
