<?php

namespace App\Console\Commands;

use App\Jobs\LegacyMigrations\MigrateLegacyCompaniesJob;
use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Services\DatabaseHelperService;
use App\Services\BatchHelperService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Batch;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Throwable;

class MigrateLegacyCompanies extends Command
{
    const BATCH_COUNT = 'batch-count';
    const NEW_ID = 'new-id';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:companies {--batch-count=20 : The number of batches/jobs to create} {--ids=* : Pass in specific ids to migrate } {--new-id : Whether to give the company a new Admin 2 ID or use the old legacy ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles the migration of legacy companies to their new schema.';

    /**
     * Execute the console command.
     *
     * @return int
     * @throws Throwable
     */
    public function handle()
    {
        $this->line("Migrating companies");

        $syncLegacyId = !$this->option(self::NEW_ID);

        // Performance
        DB::disableQueryLog();

        $industries = Industry::all()->pluck(Industry::FIELD_ID, Industry::FIELD_NAME)->toArray();
        $industryServices = IndustryService::all()->pluck(IndustryService::FIELD_ID, IndustryService::FIELD_SLUG)->toArray();

        $suppliedCompanyIds = $this->option('ids');
        if ($suppliedCompanyIds) {
            $migrationJob = new MigrateLegacyCompaniesJob(collect($suppliedCompanyIds), $industries, $industryServices, $syncLegacyId);

            Bus::dispatchSync($migrationJob);

            return count($suppliedCompanyIds);
        }

        $companyIdCol = EloquentCompany::ID;
        $countCol = 'count';

        $total = EloquentCompany::query()
                    ->leftJoin(DatabaseHelperService::database().'.'.Company::TABLE, function($join) {
                        $join->on(
                            DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_LEGACY_ID,
                            '=',
                            DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::ID
                        );
                    })
                    ->whereNull(Company::TABLE.'.'.Company::FIELD_ID)
                    ->selectRaw("COUNT($companyIdCol) AS $countCol")
                    ->first()
                    ->{$countCol};

        if($total > 0) {
            $this->line("$total companies total");

            // unguard to allow manual primary key insert for ID matching
            Company::unguard();

            $batchCount = (int) $this->option(self::BATCH_COUNT);

            $batchJobs = [];

            $companyIds = EloquentCompany::query()
                ->leftJoin(DatabaseHelperService::database().'.'.Company::TABLE, function($join) {
                    $join->on(
                        DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_LEGACY_ID,
                        '=',
                        DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::ID
                    );
                })
                ->whereNull(Company::TABLE.'.'.Company::FIELD_ID)
                ->select(EloquentCompany::ID)
                ->pluck(EloquentCompany::ID);

            $chunkedCompanyIds = app(BatchHelperService::class)->chunkByBatchCount($batchCount, $companyIds->toArray());

            foreach($chunkedCompanyIds as $idsChunk) {
                $batchJobs[] = new MigrateLegacyCompaniesJob(collect($idsChunk), $industries, $industryServices, $syncLegacyId);
            }

            $batch = Bus::batch($batchJobs)
                        ->catch(function(Batch $batch, Throwable $throwable) {
                            logger()->error("Legacy companies migration error: ".$throwable->getMessage());
                        })
                        ->allowFailures()
                        ->name("Legacy Companies Migration")
                        ->onConnection(QueueHelperService::QUEUE_CONNECTION)
                        ->onQueue(QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION)
                        ->dispatch();

            $bar = $this->output->createProgressBar($batch->totalJobs);

            $bar->setFormat("%current%/%max% migration jobs done. Elapsed: %elapsed%");
            $bar->setRedrawFrequency(1);

            $bar->start();

            while(!$batch->finished() && !$batch->cancelled()) {
                $batch = $batch->fresh();
                $bar->setProgress($batch->processedJobs());
            }

            $bar->finish();

            $nextCompanyId = Company::query()->orderBy(Company::FIELD_ID, 'desc')->first()->{Company::FIELD_ID} + 1;

            DB::statement("ALTER TABLE ".Company::TABLE." AUTO_INCREMENT = ".$nextCompanyId);

            $this->newLine();
            $this->info("Companies table auto_increment set to {$nextCompanyId}.");

            Company::reguard();

            $this->newLine();
            $this->line("Companies migrated");
        }
        else {
            $this->line("No companies to migrate");
        }

        return 0;
    }
}
