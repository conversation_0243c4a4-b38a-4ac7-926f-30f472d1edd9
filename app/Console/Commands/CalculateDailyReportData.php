<?php

namespace App\Console\Commands;

use App\Jobs\Reports\CalculateDailyReportDataJob;
use Illuminate\Console\Command;

class CalculateDailyReportData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate:daily-report-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate daily report data.';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        CalculateDailyReportDataJob::dispatch();
        $this->info("Daily report data job dispatched.");
    }
}
