<?php

namespace App\Campaigns\Delivery\CRM\Pipedrive;

use App\Campaigns\Delivery\CRM\BaseInteractableCRMDeliverer;
use App\Campaigns\Delivery\CRM\Enums\CRMFieldType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use App\Models\Odin\ConsumerProduct;
use App\Services\Campaigns\CRMIntegrations\Pipedrive\PipedriveCRMService;
use App\Services\Campaigns\CRMIntegrations\Pipedrive\PipedriveCRMServiceFactory;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Validation\UnauthorizedException;
use Pipedrive\ApiException;

class PipedriveCRMDeliverer extends BaseInteractableCRMDeliverer
{
    const AUTH_TOKEN               = 'auth_token';
    const PIPEDRIVE_TYPE           = 'field_type';
    const PIPEDRIVE_OPTIONS        = 'options';
    const PIPEDRIVE_KEY            = 'key';
    const PIPEDRIVE_NAME           = 'name';
    const PIPEDRIVE_OPTION_LABEL   = 'label';
    const PIPEDRIVE_VALUE          = 'value';
    const PIPEDRIVE_NOTE_FIELD     = 'deal_note';
    const PIPEDRIVE_REQUIRED_FIELD = 'mandatory_flag';

    const string RESPONSE_STATUS = 'status';

    /**
     * A temporary store for interactable fields, used for configuring a CRM
     *
     * @var array
     */
    protected array $editableFields;

    /**
     * @inheritDoc
     */
    protected function getDefaultSystemFields(): array
    {
        return [
            $this->formatField(self::AUTH_TOKEN, "Auth Token", required: true)
        ];
    }

    /**
     * @inheritDoc
     */
    protected function getDefaultAdditionalFields(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     * @throws GuzzleException
     */
    public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        $deliveryService = new PipedriveDeliveryService($this->getApiKey($campaign));

        $parsedPerson = $this->parseManyFieldValues($this->getInteractableFieldGroup($campaign, PipedriveCRMService::FIELD_CATEGORY_PERSON), $product, $campaign);
        $this->setMissingRequiredPersonFields($parsedPerson, $product, $campaign);
        $parsedDeal = $this->parseManyFieldValues($this->getInteractableFieldGroup($campaign, PipedriveCRMService::FIELD_CATEGORY_DEAL), $product, $campaign);
        $this->setMissingRequiredDealFields($parsedDeal, $product, $campaign);
        $parsedDealNoteValue = $this->getParsedNoteFieldValue($product, $campaign);
        $filteredPerson = array_filter($parsedPerson, fn($v) => $v);
        $filteredDeal = array_filter($parsedDeal, fn($v) => $v);

        return $this->getDeliveryProxy(
            $deliveryService
                ->makePerson($filteredPerson)
                ->makeDeal($filteredDeal)
                ->makeNote($parsedDealNoteValue),
            $product,
            $campaign
        )->prepareDelivery($campaign)->success;
    }

    /**
     * @inheritDoc
     */
    public function getInteractables(CompanyCampaignDeliveryModuleCRM|CompanyCRMTemplate|null $crm = null): array
    {
        return [
            $this->formatInteractable('getFieldsFromAPI', true, "Get Fields")
        ];
    }

    /**
     * @param array $payload
     * @return array
     * @throws GuzzleException
     * @throws ApiException
     */
    protected function getFieldsFromAPI(array $payload = []): array
    {
        $systemFields = $payload[self::SYSTEM_FIELDS_KEY] ?? [];
        $apiKeyField = array_filter($systemFields, fn($field) => $field[self::PIPEDRIVE_KEY] === self::AUTH_TOKEN)[0] ?? null;

        if (!$apiKeyField || !$apiKeyField[self::PIPEDRIVE_VALUE])
            throw new UnauthorizedException("No Auth key was provided for the CRM Integration.");

        $service = PipedriveCRMServiceFactory::make($apiKeyField[self::PIPEDRIVE_VALUE]);

        $fields = $service->getFields();

        $this->transformPipedriveFields($fields);

        return [
            self::RESPONSE_STATUS                                 => true,
            BaseInteractableCRMDeliverer::INTERACTABLE_FIELDS_KEY => $this->getEditableFields(),
        ];
    }

    /**
     * @param array $interactableFieldGroups
     * @return void
     */
    protected function transformPipedriveFields(array $interactableFieldGroups): void
    {
        foreach ($interactableFieldGroups as $groupKey => $fieldGroup) {
            $currentValues = $this->getEditableFieldGroup($groupKey);

            $transform = array_map(function ($field) use ($currentValues) {
                $existingField = array_values(array_filter($currentValues, fn($currentField) => $currentField[self::PIPEDRIVE_KEY] === $field[self::PIPEDRIVE_KEY]))[0] ?? null;
                $existingValue = $existingField ? $existingField[self::PIPEDRIVE_VALUE] : null;

                if ($field[self::PIPEDRIVE_TYPE] === 'enum') {
                    return $this->formatField(
                        key: $field[self::PIPEDRIVE_KEY],
                        displayName: $field[self::PIPEDRIVE_NAME],
                        value: $existingValue,
                        type: CRMFieldType::DROPDOWN,
                        payload: ['options' => [
                            ...array_map(fn($option) => $option[self::PIPEDRIVE_OPTION_LABEL], $field[self::PIPEDRIVE_OPTIONS]),
                        ]],
                        required: !!($field[self::PIPEDRIVE_REQUIRED_FIELD] ?? false),
                    );
                } else {
                    return $this->formatField(
                        key: $field[self::PIPEDRIVE_KEY],
                        displayName: $field[self::PIPEDRIVE_NAME],
                        value: $existingValue,
                        required: !!($field[self::PIPEDRIVE_REQUIRED_FIELD] ?? false),
                    );
                }
            }, $fieldGroup->toArray());

            $this->setEditableFieldGroup($groupKey, $transform);
        }
    }

    /**
     * @return array
     */
    protected function getEditableFields(): array
    {
        $this->editableFields = $this->editableFields
            ?? $this->crm->payload[self::INTERACTABLE_FIELDS_KEY]
            ?? [];

        return $this->editableFields;
    }

    /**
     * @param string $groupKey
     * @return array
     */
    protected function getEditableFieldGroup(string $groupKey): array
    {
        return $this->getEditableFields()[$groupKey] ?? [];
    }

    /**
     * @param string $groupKey
     * @param array $values
     * @return void
     */
    protected function setEditableFieldGroup(string $groupKey, array $values): void
    {
        $this->getEditableFields();
        $this->editableFields[$groupKey] = $values;
    }

    /**
     * @param CompanyCampaign $campaign
     * @return string
     */
    private function getApiKey(CompanyCampaign $campaign): string
    {
        return $this->getFieldValue($campaign, self::AUTH_TOKEN);
    }

    /**
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     * @return string|null
     */
    private function getParsedNoteFieldValue(ConsumerProduct $product, CompanyCampaign $campaign): ?string
    {
        $otherFields = $this->getInteractableFieldGroup($campaign, PipedriveCRMService::FIELD_CATEGORY_OTHER);
        $noteField = array_filter($otherFields, fn($field) => $field[self::PIPEDRIVE_KEY] = self::PIPEDRIVE_NOTE_FIELD)[0] ?? null;

        $noteFieldValue = $noteField
            ? $noteField[self::PIPEDRIVE_VALUE]
            : null;

        return $noteFieldValue
            ? $this->parseFieldValue($noteField[self::PIPEDRIVE_VALUE], $product, $campaign)
            : null;
    }

    /**
     * @param array $parsedDeal
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     * @return void
     */
    public function setMissingRequiredDealFields(array &$parsedDeal, ConsumerProduct $product, CompanyCampaign $campaign): void
    {
        $requiredDealFields = ['title' => fn() => $product->consumer->getFullName() . " Residence"];
        $this->setMissingRequiredFields($parsedDeal, $requiredDealFields, $product, $campaign);
    }

    /**
     * @param array $parsedPerson
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     * @return void
     */
    public function setMissingRequiredPersonFields(array &$parsedPerson, ConsumerProduct $product, CompanyCampaign $campaign): void
    {
        $requiredPersonFields = ['name' => fn() => $product->consumer->getFullName()];
        $this->setMissingRequiredFields($parsedPerson, $requiredPersonFields, $product, $campaign);
    }

    /**
     * @param array $parsedFields
     * @param array $requiredFields
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     * @return void
     */
    private function setMissingRequiredFields(array &$parsedFields, array $requiredFields, ConsumerProduct $product, CompanyCampaign $campaign): void
    {
        foreach ($requiredFields as $requiredKey => $valueFunction) {
            if (!array_key_exists($requiredKey, $parsedFields) || !$parsedFields[$requiredKey]) {
                $parsedFields[$requiredKey] = $valueFunction();
            }
        }
    }
}
