<?php

namespace App\Campaigns\Delivery\CRM\WebForm;

use App\Campaigns\Delivery\CRM\BaseCRMDeliveryService;
use App\Campaigns\Delivery\CRM\CRMDeliveryResponse;
use Exception;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class StandardWebFormDeliveryService extends BaseCRMDeliveryService
{
    const string ACCULYNX_DOMAIN = 'leads.acculynx.com';
    const string TEST_KEY = 'webform';
    const string FORMAT_JSON = 'JSON';
    const string FORMAT_POST = 'POST';

    protected string $url;
    protected string $sendFormat;
    protected string $successResponse;
    protected string $errorResponse;
    protected array  $body    = [];
    protected array  $headers = [];

    /**
     * @inheritDoc
     * @throws ConnectionException
     */
    public function deliver(): CRMDeliveryResponse
    {
        if (!$this->url) {
            return new CRMDeliveryResponse(false, ['error' => 'Bad StandardWebform CRM configuration, no URL field.']);
        }

        $request = Http::withHeaders($this->headers);

        if (str_contains($this->url, self::ACCULYNX_DOMAIN) || $this->sendFormat === self::FORMAT_POST) {
            $response = $request->asForm()
                ->withoutRedirecting()
                ->post($this->url, $this->body);
        } else {
            $response = $request->withBody(json_encode($this->body), 'application/json')
                ->withoutRedirecting()
                ->post($this->url);
        }

        return new CRMDeliveryResponse($this->responseSuccessful($response), ['body' => $response->body()]);
    }

    /**
     * @inheritDoc
     */
    public function debugDelivery(): CRMDeliveryResponse
    {
        $this->url = $this->getDebugUrl(self::TEST_KEY);
        $this->setTestHeaders($this->headers);

        return $this->deliver();
    }

    /**
     * @param string|null $url
     * @return StandardWebFormDeliveryService
     */
    public function setUrl(?string $url): StandardWebFormDeliveryService
    {
        $this->url = $url ?? "";

        return $this;
    }

    /**
     * @param string|null $sendFormat
     * @return StandardWebFormDeliveryService
     */
    public function setSendFormat(?string $sendFormat): StandardWebFormDeliveryService
    {
        $this->sendFormat = strtoupper($sendFormat ?? "");

        return $this;
    }

    /**
     * @param string|null $successResponse
     * @return StandardWebFormDeliveryService
     */
    public function setSuccessResponse(?string $successResponse): StandardWebFormDeliveryService
    {
        $this->successResponse = trim($successResponse ?? "");

        return $this;
    }

    /**
     * @param string|null $errorResponse
     * @return StandardWebFormDeliveryService
     */
    public function setErrorResponse(?string $errorResponse): StandardWebFormDeliveryService
    {
        $this->errorResponse = trim($errorResponse ?? "");

        return $this;
    }

    /**
     * @param array $body
     * @return StandardWebFormDeliveryService
     */
    public function setBody(array $body): StandardWebFormDeliveryService
    {
        // Allow forcing surrounding whitespace in key names to support existing integrations
        // expecting it, by surrounding with backticks e.g. ` First Name` will preserve the leading space
        foreach($body as $key => $value) {
            if (preg_match("/(^\s*`|`\s*$)/", $key)) {
                $newKey = preg_replace("/(^\s*`|`\s*$)/", "", $key);
                $this->body[$newKey] = $value;
            }
            else
                $this->body[$key] = $value;
        }

        return $this;
    }

    /**
     * @return array
     */
    public function getBody(): array
    {
        return $this->body;
    }

    /**
     * @param array $headers
     * @return StandardWebFormDeliveryService
     */
    public function setHeaders(array $headers): StandardWebFormDeliveryService
    {
        $this->headers = $headers;

        return $this;
    }

    /**
     * @param Response $response
     * @return bool
     */
    protected function responseSuccessful(Response $response): bool
    {
        if ($this->errorResponse) {
            if ($this->regexCheck([strval($response->status()), strval($response->body())], $this->errorResponse))
                return false;
        }
        if ($this->successResponse) {
            if ($this->regexCheck([strval($response->status()), strval($response->body())], $this->successResponse))
                return true;
        }

        return $response->successful();
    }

    /**
     * @param string[] $haystacks
     * @param string $keyword
     * @return bool
     */
    private function regexCheck(array $haystacks, string $keyword): bool
    {
        try {
            $regex = '/' . preg_quote($keyword, '/') . '/';

            foreach ($haystacks as $haystack) {
                if (preg_match($regex, $haystack))
                    return true;
            }
        }
        catch(Exception $e) {}

        return false;
    }
}
