<?php

namespace App\Campaigns\Delivery\CRM\Insightly;

use App\Campaigns\Delivery\CRM\BaseCRMTestDeliverer;
use App\Campaigns\Delivery\CRM\CRMTestDeliveryResponse;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;
use Override;

class InsightlyCRMTestDeliverer extends BaseCRMTestDeliverer
{
    /**
     * @inheritDoc
     */
    #[Override] protected function getDefaultSystemFields(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    #[Override] protected function getDefaultAdditionalFields(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    #[Override] public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    #[Override] public function deliverTestLead(): CRMTestDeliveryResponse
    {
        /** @var InsightlyCRMDeliveryService $deliveryService */
        $deliveryService = app(InsightlyCRMDeliveryService::class);

        $campaign = $this->crm->module->campaign;
        $additionalFields = $this->parseManyFieldValuesForFakeData($this->getManyFieldGroups($campaign, [self::ADDITIONAL_FIELDS_KEY]), $campaign);
        $customFields = $this->parseManyFieldValuesForFakeData($this->getManyFieldGroups($campaign, [self::CUSTOM_FIELDS_KEY]), $campaign);

        $deliveryService->setApiVersion($this->getFieldValue($campaign, InsightlyCRMDeliveryService::FIELD_API_VERSION))
            ->setApiKey($this->getFieldValue($campaign, InsightlyCRMDeliveryService::FIELD_API_KEY))
            ->setBody($additionalFields, $customFields);

        $requestBody = $deliveryService->getBody();
        $response = $deliveryService->deliver();
        $headers = $deliveryService->getHeaders();
        $url = $deliveryService->getUrl();

        return new CRMTestDeliveryResponse(
            url: $url,
            requestBody: $requestBody,
            headers: $headers,
            success: $response->success,
            responseBody: $response->payload
        );
    }
}
