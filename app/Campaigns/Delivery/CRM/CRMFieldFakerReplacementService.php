<?php

namespace App\Campaigns\Delivery\CRM;

use App\Enums\Campaigns\CRMFieldReplacerKey;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\StateAbbreviation;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\CompanyUser;
use App\Services\CompanyOptInNameService;
use Faker\Generator;

class CRMFieldFakerReplacementService
{
    public function __construct(protected Generator $faker)
    {
    }

    /**
     * Handles replacing shortcodes in a CRM field value.
     *
     * @param string $value
     * @param CompanyCampaign $campaign
     *
     * @return string
     */
    public function replaceField(string $value, CompanyCampaign $campaign): string
    {
        return preg_replace_callback("/\[[0-z-]+?]/", function ($match) use ($campaign) {
            $key = preg_replace("/[\[\]]/", "", $match[0]);

            return $this->getReplacement($key, $campaign);
        }, $value);
    }

    /**
     * @param string $key
     * @param CompanyCampaign $campaign
     *
     * @return string
     */
    protected function getReplacement(string $key, CompanyCampaign $campaign): string
    {
        $keyEnum = CRMFieldReplacerKey::tryFromWithAliases($key);

        return $keyEnum
            ? match ($keyEnum) {
                CRMFieldReplacerKey::BRAND                      => $this->getBrandName($campaign),
                CRMFieldReplacerKey::DATE                       => now()->format('m/d/Y'),
                CRMFieldReplacerKey::LEAD_SOURCE                => 'Fixr',
                CRMFieldReplacerKey::LEAD_INDUSTRY              => $campaign->service->industry->name,
                CRMFieldReplacerKey::LEAD_SERVICE               => $campaign->service->name,
                CRMFieldReplacerKey::FIRST_NAME                 => $this->faker->firstName,
                CRMFieldReplacerKey::LAST_NAME                  => $this->faker->lastName,
                CRMFieldReplacerKey::FULL_NAME                  => "{$this->faker->firstName} {$this->faker->lastName}",
                CRMFieldReplacerKey::ACCOUNT_NAME               => "{$this->faker->firstName} {$this->faker->lastName} Residence",
                CRMFieldReplacerKey::EMAIL                      => $this->faker->email,
                CRMFieldReplacerKey::FULL_ADDRESS               => $this->faker->address,
                CRMFieldReplacerKey::ADDRESS                    => preg_replace("/\n/", ', ', $this->faker->address . ', ' . $this->faker->secondaryAddress()),
                CRMFieldReplacerKey::ADDRESS_1                  => $this->faker->streetAddress,
                CRMFieldReplacerKey::ADDRESS_2                  => $this->faker->secondaryAddress(),
                CRMFieldReplacerKey::CITY                       => $this->faker->city,
                CRMFieldReplacerKey::STATE_ABBR                 => $this->faker->stateAbbr(),
                CRMFieldReplacerKey::STATE                      => $this->getFullStateName($this->faker->stateAbbr()),
                CRMFieldReplacerKey::ZIP_CODE                   => '90210',//$this->faker->postcode,
                CRMFieldReplacerKey::COUNTRY_ABBR               => 'US',
                CRMFieldReplacerKey::COUNTRY                    => 'United States',
                CRMFieldReplacerKey::COMBINED_COMMENTS          => $this->fakeCombinedComments($campaign),
                CRMFieldReplacerKey::ELECTRIC_SPEND             => $this->faker->numberBetween(50, 650),
                CRMFieldReplacerKey::UTILITY_NAME               => 'Others',
                CRMFieldReplacerKey::ROOF_ESTIMATE_LOW          => $this->faker->numberBetween(50, 650),
                CRMFieldReplacerKey::ROOF_ESTIMATE_MEDIAN       => $this->faker->numberBetween(500, 1000),
                CRMFieldReplacerKey::ROOF_ESTIMATE_HIGH         => $this->faker->numberBetween(1000, 2000),
                CRMFieldReplacerKey::LEAD_ID                    => $this->faker->numberBetween(1000, 10000),
                CRMFieldReplacerKey::ASSIGNMENT_ID              => $this->faker->numberBetween(1000, 10000),
                CRMFieldReplacerKey::UNIVERSAL_LEAD_ID          => $this->faker->uuid,
                CRMFieldReplacerKey::LEAD_PRICE                 => 1,
                CRMFieldReplacerKey::NUMBER_OF_QUOTES_REQUESTED => $this->faker->numberBetween(1, 4),
                CRMFieldReplacerKey::PHONE                      => '5556665655',//$this->faker->phoneNumber,
                CRMFieldReplacerKey::SYSTEM_SIZE                => $this->faker->numberBetween(6, 24),
                CRMFieldReplacerKey::ORIGIN_URL                 => $this->faker->url,
                CRMFieldReplacerKey::CAMPAIGN_NAME              => $campaign->name,
                CRMFieldReplacerKey::LEAD_SALE_TYPE             => $this->faker->randomElement(['Exclusive', 'Duo', 'Trio', 'Quad']),
                CRMFieldReplacerKey::LEAD_SALE_COUNT            => 1,
                CRMFieldReplacerKey::PROPERTY_TYPE              => 'Residential',
                CRMFieldReplacerKey::LEAD_TYPE                  => 'Standard',
                CRMFieldReplacerKey::COMPANY_NAME               => $campaign->company->name,
                CRMFieldReplacerKey::COMPANY_USER_NAME          => $campaign->company->users()->where(CompanyUser::FIELD_CAN_LOG_IN, true)->first()?->completeName() ?? '',
                CRMFieldReplacerKey::PRODUCT                    => $campaign->product->name,
                CRMFieldReplacerKey::SHADE                      => $this->faker->randomElement(['None', 'A little']),
                CRMFieldReplacerKey::BEST_TIME_TO_CALL          => $this->getContactTime(),
                CRMFieldReplacerKey::TRUSTED_FORM_URL           => "https://cert.trustedform.com/abc123",
                CRMFieldReplacerKey::WATCHDOG_VIDEO_LINK        => $this->faker->url(),
                CRMFieldReplacerKey::OPT_IN_NAME                => CompanyOptInNameService::getOptInNameByCampaign($campaign) ?? $campaign->company->name,
                CRMFieldReplacerKey::PUBLIC_COMMENTS            => $this->faker->realText,
                default                                         => $this->handleMissingKey($keyEnum),
            }
            : $key;
    }

    /**
     * @param string $stateAbbr
     *
     * @return string
     */
    protected function getFullStateName(string $stateAbbr): string
    {
        return array_flip(StateAbbreviation::getAsKeyValueSelectArray())[strtoupper($stateAbbr)] ?? $stateAbbr;
    }

    /**
     * @param CompanyCampaign $campaign
     * @return string
     */
    protected function fakeCombinedComments(CompanyCampaign $campaign): string
    {
        return implode("\n", [
            $campaign->service->industry->name . " " . $campaign->product->name,
            'Contact Time: ' . $this->faker->time('H:00'),
            'Credit Score: ' . $this->faker->numberBetween(50, 150),
            'Origin: ' . $this->faker->url,
        ]);
    }

    /**
     * @param CompanyCampaign $campaign
     * @return string
     */
    protected function getBrandName(CompanyCampaign $campaign): string
    {
        return match ($campaign->service->industry->name) {
            IndustryEnum::SOLAR->value   => 'SolarReviews',
            IndustryEnum::ROOFING->value => 'Roofing Calculator',
            default                      => 'Fixr',
        };
    }

    /**
     * @param CRMFieldReplacerKey $key
     * @return null
     */
    private function handleMissingKey(CRMFieldReplacerKey $key): null
    {
        logger()->warning(self::class . ': failed to implement replacer for CRMFieldReplacerKey ' . $key->name);

        return null;
    }

    private function getContactTime(): string
    {
        $from = $this->faker->numberBetween(7, 11);
        $to   = $this->faker->numberBetween(12, 6);

        return "{$from}am to {$to}pm";
    }
}
