<?php

namespace App\Campaigns\Delivery\Contacts\Strategies\SMS;

use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Enums\Odin\Industry as IndustryEnum;

class SMSTemplateStrategy
{
    /**
     * Handles returning the SMSTemplate for a given product.
     *
     * @param ConsumerProduct $product
     * @return SMSTemplateDataModel
     */
    public function getSMSTemplateForProduct(ConsumerProduct $product): SMSTemplateDataModel
    {
        if($this->industryHasTemplateOverride($product->industryService->industry))
            return $this->getIndustryTemplateOverride($product->industryService->industry);

        return $this->getDefaultSMSTemplate();
    }

    /**
     * Returns whether an industry has a template override.
     *
     * @param Industry $industry
     * @return bool
     */
    protected function industryHasTemplateOverride(Industry $industry): bool
    {
        return IndustryEnum::tryFromSlug($industry->slug) === IndustryEnum::SOLAR;
    }

    /**
     * Returns the industry template override for an industry.
     *
     * @param Industry $industry
     * @return SMSTemplateDataModel|null
     */
    protected function getIndustryTemplateOverride(Industry $industry): ?SMSTemplateDataModel
    {
        return match (IndustryEnum::tryFromSlug($industry->slug)) {
            IndustryEnum::SOLAR => SMSTemplateDataModel::getSolarTemplate(),
            default => null,
        };
    }

    /**
     * Returns the default SMS Template.
     *
     * @return SMSTemplateDataModel
     */
    protected function getDefaultSMSTemplate(): SMSTemplateDataModel
    {
        return SMSTemplateDataModel::getDefaultTemplate();
    }
}
