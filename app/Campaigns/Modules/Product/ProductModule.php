<?php

namespace App\Campaigns\Modules\Product;

use App\Campaigns\Modules\BaseModule;
use App\DataModels\Campaigns\ConsumerProject;
use App\Enums\Odin\Product;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\OptInCompany;
use Illuminate\Database\Eloquent\Model;

class ProductModule extends BaseModule
{

    /**
     * @inheritDoc
     */
    #[\Override] protected function getModel(CompanyCampaign $campaign): Model
    {
        return $campaign;
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function preFilter(CompanyCampaign $campaign, ConsumerProject $project): bool
    {
        return match ($campaign->product->name) {
            Product::DIRECT_LEADS->value => $this->isOptInCompany(campaign: $campaign, project: $project),
            default => true
        };
    }

    /**
     * @param CompanyCampaign $campaign
     * @param ConsumerProject $project
     *
     * @return bool
     */
    private function isOptInCompany(CompanyCampaign $campaign, ConsumerProject $project): bool
    {
        return in_array(
            $campaign->company_id,
            $project->leadConsumerProduct()->optInCompanies->pluck(OptInCompany::FIELD_COMPANY_ID)->toArray()
        );
    }
}
