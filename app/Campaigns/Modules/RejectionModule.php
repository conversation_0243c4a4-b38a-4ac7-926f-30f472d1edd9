<?php

namespace App\Campaigns\Modules;

use App\DataModels\Campaigns\ConsumerProject;
use App\Enums\Campaigns\CampaignType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\ComputedRejectionStatistic;
use Illuminate\Database\Eloquent\Model;

class RejectionModule extends BaseModule
{
    /**
     * @var float
     */
    private float $leadRejectionThreshold;

    /**
     * @var float
     */
    private float $appointmentRejectionThreshold;

    public function __construct()
    {
        $this->leadRejectionThreshold = floatval(config('sales.leads.crm_rejection_percentage_threshold'));
        $this->appointmentRejectionThreshold = floatval(config('sales.appointments.crm_rejection_percentage_threshold'));
    }

    /**
     * @inheritDoc
     */
    protected function getModel(CompanyCampaign $campaign): Model
    {
        return $campaign;
    }

    /**
     * @inheritDoc
     */
    public function filter(CompanyCampaign $campaign, ConsumerProject $project): bool
    {
        $rejectionPercentage = $this->getRejectionForCompany($campaign);

        return match($campaign->type) {
            CampaignType::APPOINTMENT_CAMPAIGN => $rejectionPercentage < $this->appointmentRejectionThreshold,
            default                            => $rejectionPercentage < $this->leadRejectionThreshold,
        };
    }

    /**
     * @param CompanyCampaign $campaign
     * @return float
     */
    private function getRejectionForCompany(CompanyCampaign $campaign): float
    {
        /** @var ComputedRejectionStatistic $stats */
        $stats = $campaign->company->rejectionStatistics()
            ->where(ComputedRejectionStatistic::FIELD_PRODUCT_ID, $campaign->product_id)
            ->first();

        return $stats?->crm_rejection_percentage ?? 0.0;
    }
}
