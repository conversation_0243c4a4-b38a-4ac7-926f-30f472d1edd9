<?php

namespace App\Builders\Billing;

use App\Enums\Billing\ApprovalStatus;
use App\Helpers\CarbonHelper;
use App\Models\Billing\ActionApproval;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use \Illuminate\Database\Query\Builder as QueryBuilder;


class InvoiceBuilder extends BillingBuilder
{
    protected array $sortColumnsMap = [
        'id'           => Invoice::TABLE . '.' . Invoice::FIELD_ID,
        'company_id'   => Invoice::TABLE . '.' . Invoice::FIELD_COMPANY_ID,
        'company_name' => Company::TABLE . '.' . Company::FIELD_NAME,
    ];

    /**
     * @param Builder|QueryBuilder $query
     */
    public function __construct(protected Builder|QueryBuilder $query)
    {
        parent::__construct($query);
    }

    /**
     * @return self
     */
    public static function query(): InvoiceBuilder
    {
        $query = Invoice::query()
            ->from(DatabaseHelperService::database() . '.' . Invoice::TABLE);

        return new self($query);
    }

    /**
     * @param int|null $invoiceId
     * @return $this
     */
    public function forInvoiceId(?int $invoiceId = null): static
    {
        if (filled($invoiceId)) {
            $this->query->where(DatabaseHelperService::database() . '.' . Invoice::TABLE . '.' . Invoice::FIELD_ID, $invoiceId);
        }
        return $this;
    }

    /**
     * @param string|null $invoiceUuid
     * @return $this
     */
    public function forInvoiceUuid(?string $invoiceUuid = null): static
    {
        if (filled($invoiceUuid)) {
            $this->query->where(DatabaseHelperService::database() . '.' . Invoice::TABLE . '.' . Invoice::FIELD_UUID, $invoiceUuid);
        }
        return $this;
    }

    /**
     * @param array|null $statuses
     * @param bool $not
     * @return $this
     */
    public function inStatuses(?array $statuses = null, bool $not = false): static
    {
        if (filled($statuses)) {
            $this->query->whereIn(
                column: DatabaseHelperService::database() . '.' . Invoice::TABLE . '.' . Invoice::FIELD_STATUS,
                values: $statuses,
                not   : $not
            );
        }
        return $this;
    }

    /**
     * @param array|null $tags
     * @return $this
     */
    public function withAnyTags(?array $tags): static
    {
        if (filled($tags)) {
            $this->query->withAnyTags($tags);
        }

        return $this;
    }

    /**
     * @return $this
     */
    public function joinBillingProfile(): self
    {
        $this->safeJoin('billing_profile', fn() => $this->query->join(DatabaseHelperService::database() . '.' . BillingProfile::TABLE,
            DatabaseHelperService::database() . '.' . Invoice::TABLE . '.' . Invoice::FIELD_BILLING_PROFILE_ID,
            '=',
            DatabaseHelperService::database() . '.' . BillingProfile::TABLE . '.' . BillingProfile::FIELD_ID
        ));

        return $this;
    }

    /**
     * @return $this
     */
    public function joinMostRecentInvoiceSnapshot(): self
    {
        $this->safeJoin('most_recent_invoice_snapshot', fn() => $this->query->leftJoinSub(InvoiceSnapshot::mostRecentByInvoice(), 'latest_snapshot', function ($join) {
            $join->on(
                DatabaseHelperService::database() . '.' . Invoice::TABLE . '.' . Invoice::FIELD_ID,
                '=',
                "latest_snapshot.invoice_id"
            );
        }));

        return $this;
    }

    /**
     * @return $this
     */
    public function leftJoinApprovalsInProcessingStatus(): self
    {
        $this->safeJoin('invoice_processing_approvals', function () {
            $invoicePendingApprovalQuery = ActionApproval::query()
                ->from(DatabaseHelperService::database() . '.' . ActionApproval::TABLE)
                ->where(ActionApproval::FIELD_APPROVABLE_TYPE, Invoice::class)
                ->where(function ($query) {
                    $query->where(ActionApproval::FIELD_STATUS, ApprovalStatus::PENDING->value)
                        ->orWhere(ActionApproval::FIELD_IS_PROCESSING, true);
                });

            $this->query->leftJoinSub($invoicePendingApprovalQuery, 'pending_action', function ($join) {
                $join->on(
                    DatabaseHelperService::database() . '.' . Invoice::TABLE . '.' . Invoice::FIELD_ID,
                    '=',
                    "pending_action.approvable_id"
                );
            });
        });

        return $this;
    }

    /**
     * @return $this
     */
    public function joinItemItemTotals(): self
    {
        $this->safeJoin('invoice_item_totals', function () {
            $invoiceItemQuery = InvoiceItem::query()
                ->from(DatabaseHelperService::database() . '.' . InvoiceItem::TABLE)
                ->select([
                    DB::raw('SUM(' . InvoiceItem::FIELD_QUANTITY . ' * ' . InvoiceItem::FIELD_UNIT_PRICE . ') as total_items_price'),
                    InvoiceItem::FIELD_INVOICE_ID
                ])
                ->groupBy(InvoiceItem::FIELD_INVOICE_ID);

            $this->query->joinSub(
                $invoiceItemQuery,
                'invoice_items_total',
                'invoice_items_total.' . InvoiceItem::FIELD_INVOICE_ID,
                Invoice::TABLE . '.' . Invoice::FIELD_ID
            );

            $this->select([
                'invoice_items_total.total_items_price'
            ]);
        });

        return $this;
    }

    /**
     * @param string|null $startDate
     * @param string|null $endDate
     * @return $this
     */
    public function betweenDates(
        ?string $startDate = null,
        ?string $endDate = null,
    ): static
    {
        if (filled($startDate)) {
            $this->query->where(
                DatabaseHelperService::database() . '.' . Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT,
                '>=',
                CarbonHelper::parseWithTimezone(str_replace('"', '', $startDate))->startOfDayUTC()
            );
        }

        if (filled($endDate)) {
            $this->query->where(
                DatabaseHelperService::database() . '.' . Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT,
                '<=',
                CarbonHelper::parseWithTimezone(str_replace('"', '', $endDate))->endOfDayUTC()
            );
        }

        return $this;
    }
}
