<?php

namespace App\Builders\Odin;

use App\Models\Billing\InvoiceItem;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\CompanyOptInName;
use App\Models\Legacy\EloquentInvoiceItem;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\IndustryConsumerField;
use App\Models\Odin\IndustryService;
use App\Models\Odin\OptInCompany;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCancellation;
use App\Models\Odin\ProductRejection;
use App\Models\Odin\ServiceConsumerField;
use App\Models\Odin\ServiceProduct;
use App\Models\Odin\SingleProductSale;
use App\Models\Odin\Website;
use App\Models\SaleType;
use App\Services\DatabaseHelperService;
use App\Services\Odin\ConfigurableFieldsService;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class ProductAssignmentBuilder
{
    public function __construct
    (
        protected ConfigurableFieldsService $configurableFieldsService,
        protected ?int $companyId = null,
        protected ?int $campaignId = null,
        protected ?string $companyCampaignReference = null,
        protected ?string $status = null,
        protected ?int $productId = null,
        protected ?int $parentProductId = null,
        protected ?int $serviceProductId = null,
        protected ?int $leadId = null,
        protected ?string $name = null,
        protected mixed $forStateOrZip = null,
        protected ?Carbon $fromDate = null,
        protected ?Carbon $toDate = null,
        protected ?int $invoiceId = null,
        protected ?bool $qualifiedOverBudget = null,
        protected ?bool $withTracking = false,
        protected ?bool $withAddress = false,
        protected ?bool $withAppointment = false,
        protected ?Collection $dashboardFields = null,
        protected ?int $industryId = null,
        protected ?int $industryServiceId = null,
        protected ?int $missedOnly = null,
        protected ?bool $forCompaniesColumnId = false,
        protected ?bool $delivered = null,
        protected ?bool $removePreviewLeads = null
    ) {}

    /**
     * Creates an instance of builder.
     * @return static
     */
    public static function query(): self
    {
        return new self(app()->make(ConfigurableFieldsService::class));
    }

    /**
     * @param bool $remove
     *
     * @return $this
     */
    public function removeLegacyPreviewLeads(bool $remove): static
    {
        $this->removePreviewLeads = $remove;

        return $this;
    }

    /**
     * Allows querying via company ID - Note this is the Odin ID
     * @param int|null $id
     * @return $this
     */
    public function forCompanyId(?int $id = null): self
    {
        $this->companyId = $id;
        return $this;
    }

    /**
     * @param bool $delivered
     *
     * @return $this
     */
    public function delivered(bool $delivered): self
    {
        $this->delivered = $delivered;
        return $this;
    }

    /**
     * Allows querying via companies id column - Note - Usually used as sub query
     * @return $this
     */
    public function forCompaniesColumnId(): self
    {
        $this->forCompaniesColumnId = true;
        return $this;
    }

    /**
     * Allows querying via campaign ID - Note this is the Legacy ID
     * @param int|null $id
     * @return $this
     */
    public function forCampaignId(?int $id = null): self
    {
        $this->campaignId = $id;
        return $this;
    }

    /**
     * @param string|null $reference
     * @return self
     */
    public function forCompanyCampaignReference(?string $reference = null): self
    {
        $this->companyCampaignReference = $reference;

        return $this;
    }

    /**
     * Allows querying for Consumer Product Statuses - purchased, non_chargeable, rejected
     * @param string|null $status
     * @return $this
     */
    public function forStatus(?string $status = null): self
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @param int|null $productId
     * @return $this
     */
    public function forProductId(?int $productId = null): self
    {
        $this->productId = $productId;
        return $this;
    }

    /**
     * @param int|null $parentProductId
     * @param int|null $industryServiceId
     * @return self
     */
    public function includeParentProductAndService(?int $parentProductId = null, ?int $industryServiceId = null): self
    {
        if ($parentProductId && $industryServiceId) {
            $this->parentProductId   = $parentProductId;
            $this->industryServiceId = $industryServiceId;
        }

        return $this;
    }

    /**
     * Limit query to IndustryService
     * @param int|null $service
     * @return $this
     */
    public function forServiceProduct(?int $service = null): self
    {
        $this->serviceProductId = $service;
        return $this;
    }

    /**
     * Allows querying via ProductAssignment ID
     * @param int|null $id
     * @return $this
     */
    public function forLeadId(?int $id = null): self
    {
        $this->leadId = $id;
        return $this;
    }

    /**
     * Query for name of Consumer - includes first_name, last_name and email
     * @param string|null $name
     * @return $this
     */
    public function forName(?string $name = null): self
    {
        $this->name = $name;
        return $this;
    }

    /**
     * Query for state and zipcode on ConsumerProduct->Address
     * @param string|int|null $forStateOrZip
     * @return $this
     */
    public function forStateOrZip(mixed $forStateOrZip = null): self
    {
        $this->forStateOrZip = $forStateOrZip;
        return $this;
    }

    /**
     * @param ?Carbon $fromDate
     * @return $this
     */
    public function forFromDate(?Carbon $fromDate = null): self
    {
        $this->fromDate = $fromDate;
        return $this;
    }

    /**
     * @param ?Carbon $toDate
     * @return $this
     */
    public function forToDate(?Carbon $toDate = null): self
    {
        $this->toDate = $toDate;
        return $this;
    }

    /**
     * @param int|null $invoiceId
     * @return $this
     */
    public function forInvoiceId(?int $invoiceId = null): self
    {
        $this->invoiceId = $invoiceId;
        return $this;
    }

    /**
     * @param int|null $industryId
     * @return $this
     */
    public function forIndustryId(int $industryId = null): self
    {
        $this->industryId = $industryId;
        return $this;
    }

    /**
     * @param int|null $missedOnly
     * @return $this
     */
    public function missedOnly(?int $missedOnly = null): self
    {
        $this->missedOnly = $missedOnly;
        return $this;
    }

    /**
     * @param bool|null $qualifiedOverBudget
     * @return $this
     */
    public function qualifiedOverBudget(?bool $qualifiedOverBudget = null): self
    {
        $this->qualifiedOverBudget = $qualifiedOverBudget;
        return $this;
    }

    /**
     * Eager load ConsumerProduct->ConsumerProductTracking
     * @param bool|null $withTracking
     * @return $this
     */
    public function withTracking(?bool $withTracking = false): self
    {
        $this->withTracking = $withTracking;
        return $this;
    }

    /**
     * Eager load ConsumerProduct->Address
     * @param bool|null $withAddress
     * @return $this
     */
    public function withAddress(?bool $withAddress = false): self
    {
        $this->withAddress = $withAddress;
        return $this;
    }

    /**
     * @param bool|null $withAppointment
     * @return self
     */
    public function withAppointment(?bool $withAppointment = false): self
    {
        $this->withAppointment = $withAppointment;
        return $this;
    }

    /**
     * Eager load configurable fields on data->payload for dashboard
     * Pass in IndustryServiceId to retrieve those Consumer configurables
     * @param int|null $withDashboardFields
     * @return $this
     * @throws Exception
     */
    public function withDashboardFieldsForServiceId(?int $withDashboardFields = null): self
    {
        $serviceFields = $withDashboardFields
            ? $this->configurableFieldsService->getFields('consumer', 'service', $withDashboardFields)
                ->filter(fn(ServiceConsumerField $field) => $field->show_on_dashboard)
                ->pluck('key')
            : null;

        $this->dashboardFields = isset($this->dashboardFields)
            ? $this->dashboardFields->merge($serviceFields)->unique()
            : $serviceFields;

        return $this;
    }

    /**
     * @param int|null $withDashboardFields
     * @return $this
     * @throws Exception
     */
    public function withDashboardFieldsForIndustryId(?int $withDashboardFields = null): self
    {
        $industryFields = $withDashboardFields
            ? $this->configurableFieldsService->getFields('consumer', 'industry', $withDashboardFields)
                ->filter(fn(IndustryConsumerField $field) => $field->show_on_dashboard)
                ->pluck('key')
            : null;

        $this->dashboardFields = isset($this->dashboardFields)
            ? $this->dashboardFields->merge($industryFields)->unique()
            : $industryFields;

        return $this;
    }

    /**
     * Returns a final collection by running the query builder.
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()->get();
    }

    /**
     * Returns a paginated instance of the query.
     * @param int|null $perPage
     * @param array $columns
     * @param string $pageName
     * @param int|null $page
     * @return LengthAwarePaginator
     */
    public function paginate(?int $perPage = null, array $columns = ['*'], string $pageName = 'page', ?int $page = null): LengthAwarePaginator
    {
        return $this->getQuery()->paginate($perPage, $columns, $pageName, $page);
    }

    /**
     * @return int|mixed
     */
    public function getSpend(): mixed
    {
        return $this->getQuery(false)->sum(ProductAssignment::FIELD_COST);
    }

    /**
     * Returns a query for this instance.
     *
     * @param bool $select
     *
     * @return Builder
     */
    public function getQuery(bool $select = true): Builder
    {
        $query = ProductAssignment::query();

        if ($select)
            $query->select(
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COST,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED_AT,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_REJECTION_EXPIRY,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_LEGACY_ID,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_SALE_TYPE_ID,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_EXCLUDE_BUDGET,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_PARENT_PRODUCT_ID,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_OPT_IN_ID,
            );

        $query
            ->with([
                ProductAssignment::RELATION_CONSUMER_PRODUCT,
                ProductAssignment::RELATION_CONSUMER_PRODUCT .'.'. ConsumerProduct::RELATION_ADDRESS => function ($with) {
                    $with->select([
                          Address::FIELD_ID,
                          Address::FIELD_ADDRESS_1,
                          Address::FIELD_ADDRESS_2,
                          Address::FIELD_CITY,
                          Address::FIELD_STATE,
                          Address::FIELD_ZIP_CODE,
                      ]);
                },
                ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER => function ($with) {
                    $with->select([
                          Consumer::FIELD_ID,
                          Consumer::FIELD_FIRST_NAME,
                          Consumer::FIELD_LAST_NAME,
                          Consumer::FIELD_EMAIL,
                          Consumer::FIELD_PHONE,
                    ]);
                },
                ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_SERVICE_PRODUCT,
                ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_PRODUCT,
                ProductAssignment::RELATION_APPOINTMENT => function ($with) {
                    $with->select([
                          ProductAppointment::CONSUMER_PRODUCT_ID,
                          ProductAppointment::APPOINTMENT_DATE,
                          ProductAppointment::APPOINTMENT_TIME,
                          ProductAppointment::APPOINTMENT_TYPE
                      ]);
                },
                ProductAssignment::RELATION_PRODUCT_REJECTIONS => function ($with) {
                    $with->select([
                          ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID,
                          ProductRejection::FIELD_REASON,
                          ProductRejection::FIELD_DELETED_AT,
                          ProductRejection::CREATED_AT
                      ]);
                },
                ProductAssignment::RELATION_PRODUCT_CANCELLATIONS => function ($with) {
                    $with->select([
                          ProductCancellation::FIELD_PRODUCT_ASSIGNMENT_ID,
                          ProductCancellation::FIELD_REASON
                      ]);
                },
                ProductAssignment::RELATION_SALE_TYPE => function ($with) {
                    $with->select([
                        SaleType::FIELD_ID,
                        SaleType::FIELD_NAME
                    ]);
                },
                ProductAssignment::RELATION_LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION => function ($with) {
                    $with->select([
                        LeadCampaignSalesTypeConfiguration::ID,
                        LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID
                    ]);
                },
                ProductAssignment::RELATION_LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION.'.'.LeadCampaignSalesTypeConfiguration::RELATION_LEAD_CAMPAIGN => function ($with) {
                    $with->select([
                        LeadCampaign::ID,
                        LeadCampaign::NAME
                    ]);
                },
                ProductAssignment::RELATION_INVOICE_ITEM => function ($with) {
                    $with->select([
                          EloquentInvoiceItem::TABLE.'.'.EloquentInvoiceItem::ID,
                          EloquentInvoiceItem::INVOICE_ID,
                      ]);
                },
                ProductAssignment::RELATION_OPT_IN_COMPANY => function ($with) {
                    $with->select([
                          OptInCompany::TABLE.'.'.OptInCompany::FIELD_ID,
                          OptInCompany::TABLE.'.'.OptInCompany::FIELD_COMPANY_ID,
                          OptInCompany::TABLE.'.'.OptInCompany::FIELD_COMPANY_OPT_IN_NAME_ID,
                      ]);
                },
                ProductAssignment::RELATION_OPT_IN_COMPANY.'.'.OptInCompany::RELATION_COMPANY_OPT_IN_NAME => function ($with) {
                    $with->select([
                          CompanyOptInName::TABLE.'.'.CompanyOptInName::FIELD_ID,
                          CompanyOptInName::TABLE.'.'.CompanyOptInName::FIELD_NAME,
                      ]);
                },
                ProductAssignment::RELATION_OPT_IN_COMPANY.'.'.OptInCompany::RELATION_COMPANY => function ($with) {
                    $with->select([
                          Company::TABLE.'.'.Company::FIELD_ID,
                          Company::TABLE.'.'.Company::FIELD_NAME,
                      ]);
                },
            ])
            ->withCount(ProductAssignment::RELATION_PRODUCT_CANCELLATIONS)
            ->withCount(ProductAssignment::RELATION_PRODUCT_REJECTIONS);

        $query->when($this->withTracking, fn(Builder $subQuery) =>
            $subQuery->with([
                ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER_PRODUCT_TRACKING => function ($with) {
                    $with->select([
                        ConsumerProductTracking::ID,
                        ConsumerProductTracking::WEBSITE_ID
                    ]);
                },
                ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER_PRODUCT_TRACKING .'.'. ConsumerProductTracking::RELATION_WEBSITE => function ($with) {
                    $with->select([
                        Website::FIELD_ID,
                        Website::FIELD_NAME
                    ]);
                }])
        );

        if ($this->companyId) {
            $query->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COMPANY_ID, $this->companyId);
        }

        if ($this->forCompaniesColumnId) {
            $query->whereRaw(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COMPANY_ID . ' = ' . Company::TABLE . '.' . Company::FIELD_ID);
        }

        if ($this->campaignId || $this->invoiceId || $this->removePreviewLeads !== null) {
            $query->leftJoin(
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE,
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::ID,
                '=',
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_LEGACY_ID);
            if ($this->campaignId) {
                $query->leftJoin(
                    DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignSalesTypeConfiguration::TABLE,
                    DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignSalesTypeConfiguration::TABLE . '.' . LeadCampaignSalesTypeConfiguration::ID
                )->where(DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignSalesTypeConfiguration::TABLE . '.' . LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID, $this->campaignId);
            }

            if ($this->invoiceId) {
                $query->leftJoin(
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentInvoiceItem::TABLE,
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentInvoiceItem::TABLE.'.'.EloquentInvoiceItem::ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::INVOICE_ITEM_ID
                )
                    ->where(function ($query) {
                        $query->where(DatabaseHelperService::readOnlyDatabase().'.'.EloquentInvoiceItem::TABLE.'.'.EloquentInvoiceItem::INVOICE_ID, $this->invoiceId)
                            ->orWhereHas(ProductAssignment::RELATION_ODIN_INVOICE_ITEM, function ($query) {
                                $query->where(InvoiceItem::FIELD_INVOICE_ID, $this->invoiceId);
                            });
                    });
            }

            if ($this->removePreviewLeads) {
                $query->whereHas(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE, function ($query) {
                    $query->where(
                        DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::SOLD_STATUS,
                        '!=',
                        EloquentQuoteCompany::VALUE_SOLD_STATUS_PREVIEW
                    );
                });
            }
        }

        if ($this->companyCampaignReference) {
            $query->join(Budget::TABLE, Budget::TABLE .'.'. Budget::FIELD_ID, '=', ProductAssignment::FIELD_BUDGET_ID)
                ->join(BudgetContainer::TABLE, BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_ID, '=', Budget::FIELD_BUDGET_CONTAINER_ID)
                ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, '=', BudgetContainer::FIELD_CAMPAIGN_ID)
                ->where(CompanyCampaign::FIELD_REFERENCE, $this->companyCampaignReference);
        }

        if ($this->delivered !== null) $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, $this->delivered);

        if ($this->industryId) {
            $query
                ->join(ConsumerProduct::TABLE, ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID, '=', ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
                ->join(ServiceProduct::TABLE, ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID, '=', ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
                ->join(IndustryService::TABLE, IndustryService::TABLE . '.' . IndustryService::FIELD_ID, '=', ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
                ->where(IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID, $this->industryId);
        }

        if ($this->status) {
            $query = match ($this->status) {
                'purchased'      => $query->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CHARGEABLE, true)
                    ->having('product_rejections_count', '<', 1),
                'not_chargeable' => $query->where('chargeable', '!=', true),
                'rejected'       => $query->having('product_rejections_count', '>', 0),
                'appointment_purchased' => $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE, true)
                    ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true)
                    ->having('product_cancellations_count', '<', 1)
                    ->having('product_rejections_count', '<', 1),
                default     => $query
            };
        }

        if ($this->parentProductId) {
            if ($this->serviceProductId) {
                $query->whereHas(ProductAssignment::RELATION_CONSUMER_PRODUCT, fn(Builder $subQuery) =>
                    $subQuery->whereHas(ConsumerProduct::RELATION_SERVICE_PRODUCT, fn(Builder $subQuery) =>
                        $subQuery->where(ServiceProduct::FIELD_ID, $this->serviceProductId)
                            ->orWhere(fn(Builder $query) =>
                                $query->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $this->industryServiceId)
                                    ->where(ProductAssignment::FIELD_PARENT_PRODUCT_ID, $this->parentProductId))
                ));
            }
            else {
                $query->whereHas(ProductAssignment::RELATION_CONSUMER_PRODUCT, fn(Builder $subQuery) =>
                    $subQuery->whereHas(ConsumerProduct::RELATION_SERVICE_PRODUCT, fn(Builder $subQuery) =>
                        $subQuery->where(ServiceProduct::FIELD_PRODUCT_ID, $this->productId)
                            ->orWhere(ProductAssignment::FIELD_PARENT_PRODUCT_ID, $this->parentProductId)
                ));
            }
        }
        else if ($this->serviceProductId) {
            $query->whereHas(ProductAssignment::RELATION_CONSUMER_PRODUCT, fn(Builder $subQuery) =>
                $subQuery->where(ConsumerProduct::FIELD_SERVICE_PRODUCT_ID, $this->serviceProductId));
        }
        else if ($this->productId) {
            $query->whereHas(ProductAssignment::RELATION_CONSUMER_PRODUCT, fn(Builder $subQuery) =>
                $subQuery->whereHas(ConsumerProduct::RELATION_SERVICE_PRODUCT, fn(Builder $subQuery) =>
                    $subQuery->where(ServiceProduct::FIELD_PRODUCT_ID, $this->productId)
            ));
        }

        if ($this->leadId) {
            $query->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, $this->leadId);
        }

        if ($this->name) {
            $query->whereHas(ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER, fn(Builder $subQuery) =>
                $subQuery->where(Consumer::FIELD_FIRST_NAME, 'like', '%'.$this->name.'%')
                    ->orWhere(Consumer::FIELD_LAST_NAME, 'like', '%'.$this->name.'%')
                    ->orwhere(Consumer::FIELD_EMAIL,'like',  '%'.$this->name.'%'));
        }

        if ($this->forStateOrZip) {
            $query->whereHas(ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_ADDRESS, fn(Builder $subQuery) =>
                $subQuery->where(Address::FIELD_STATE, $this->forStateOrZip)
                    ->orWhere(Address::FIELD_ZIP_CODE, $this->forStateOrZip));
        }

        if ($this->toDate) {
            $query->where(ProductAssignment::FIELD_DELIVERED_AT, '<=', $this->toDate);
        }
        if ($this->fromDate) {
            $query->where(ProductAssignment::FIELD_DELIVERED_AT, '>=', $this->fromDate);
        }


        if ($this->qualifiedOverBudget !== null) {
            if ($this->qualifiedOverBudget) {
                $query->where(ProductAssignment::FIELD_EXCLUDE_BUDGET, '>', 0);
            } else {
                $query->where(ProductAssignment::FIELD_EXCLUDE_BUDGET, '<', 1);
            }
        }

        if ($this->dashboardFields) {
            $selectMap = $this->dashboardFields->map(fn($keyName) => "payload->$keyName as $keyName")->join(',');
            $queryString = $selectMap
                ? ":id,$selectMap"
                : ":id";

            $query->with(ProductAssignment::RELATION_CONSUMER_PRODUCT .'.'. ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA . $queryString);
        }

        if (isset($this->missedOnly)) {
            $query->leftJoin(SingleProductSale::TABLE, SingleProductSale::TABLE . '.' . SingleProductSale::FIELD_PRODUCT_ASSIGNMENT_ID, ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID);

            if ($this->missedOnly == 1) $query->whereNotNull(SingleProductSale::TABLE . '.' . SingleProductSale::FIELD_PRODUCT_ASSIGNMENT_ID);
            else $query->whereNull(SingleProductSale::TABLE . '.' . SingleProductSale::FIELD_PRODUCT_ASSIGNMENT_ID);
        }

        $query->orderBy(ProductAssignment::FIELD_DELIVERED_AT, 'DESC');

        return $query;

    }
}
