<?php

namespace App\Builders\Appointments;

use App\Models\AppointmentProcessingAllocation;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Odin\Address;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductAssignment;
use App\Services\DatabaseHelperService;
use App\Services\Odin\Appointments\AppointmentService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;

class UnsoldLeadConsumerProductsBuilder
{
    const PROCESSING_ALLOCATION_FAILED_COL = 'processing_allocation_failed';
    const APPOINTMENT_DATETIME_COL = 'appointment_datetime';
    const APPOINTMENT_SOLD_COL = 'appointment_sold';
    const APPOINTMENT_CANCELLED_REJECTED = 'appointment_cancelled_rejected';
    const TIMEZONE_COL = 'timezone';
    const LEGACY_COMPANY_ID_COL = 'legacy_company_id';

    private Builder $query;

    public function __construct()
    {
        $this->query = ProductAppointment::query();
    }

    /**
     * Returns a new instance of this builder.
     *
     * @return self
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * @param array $leadConsumerProductIds
     * @return $this
     */
    public function whereLeadConsumerProductIdIsIn(array $leadConsumerProductIds): self
    {
        $this->query->whereIn(ProductAppointment::TABLE.'.'.ProductAppointment::LEAD_CONSUMER_PRODUCT_ID, $leadConsumerProductIds);

        return $this;
    }

    /**
     * @return $this
     */
    public function whereIsNotAllocatedAsLead(): self
    {
        $this->query
            ->where(ProductAppointment::TABLE.'.'.ProductAppointment::ALLOCATED_AS_LEAD, false)
            ->where(AppointmentProcessingAllocation::TABLE.'.'.AppointmentProcessingAllocation::FIELD_ALLOCATED_AS_LEAD, false);

        return $this;
    }

    /**
     * @return $this
     */
    public function joinAddress(): self
    {
        $this->query
            ->join(ConsumerProduct::TABLE, function ($join) {
                $join->on(
                    ProductAppointment::TABLE . '.' . ProductAppointment::CONSUMER_PRODUCT_ID,
                    '=',
                    ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID
                );
            })
            ->join(Address::TABLE, function ($join) {
                $join->on(
                    ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ADDRESS_ID,
                    '=',
                    Address::TABLE . '.' . Address::FIELD_ID
                );
            });

        return $this;
    }

    /**
     * @return $this
     */
    public function joinProductAssignments(): self
    {
        $this->query
            ->leftJoin( DatabaseHelperService::database().'.'.ProductAssignment::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::database().'.'.ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
                    '=',
                    DatabaseHelperService::database().'.'.ProductAppointment::TABLE.'.'.ProductAppointment::CONSUMER_PRODUCT_ID
                );
            })
            ->leftJoin(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::ID,
                    '=',
                    DatabaseHelperService::database().'.'.ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_LEGACY_ID
                );
            });

        return $this;
    }

    /**
     * @return $this
     */
    public function joinAppointmentProcessingAllocations(): self
    {
        $this->query
            ->join(AppointmentProcessingAllocation::TABLE, function($join) {
                $join->on(
                    AppointmentProcessingAllocation::TABLE.'.'.AppointmentProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID,
                    '=',
                    ProductAppointment::TABLE.'.'.ProductAppointment::CONSUMER_PRODUCT_ID
                );
            })
            ->whereNull( AppointmentProcessingAllocation::TABLE.'.'.AppointmentProcessingAllocation::FIELD_DELETED_AT);

        return $this;
    }

    /**
     * @return Builder
     */
    public function getQuery(): Builder
    {
        return $this->query;
    }

    /**
     * @return Collection
     */
    public function getLeadConsumerProducts(): Collection
    {
        return $this->query
            ->groupBy([
                ProductAppointment::TABLE . '.' . ProductAppointment::LEAD_CONSUMER_PRODUCT_ID,
                ProductAppointment::TABLE . '.' . ProductAppointment::CONSUMER_PRODUCT_ID
            ])
            ->selectRaw(implode(',', [
                ProductAppointment::TABLE . '.' . ProductAppointment::LEAD_CONSUMER_PRODUCT_ID,
                ProductAppointment::TABLE . '.' . ProductAppointment::CONSUMER_PRODUCT_ID,
                "MAX(" . AppointmentProcessingAllocation::TABLE . '.' . AppointmentProcessingAllocation::FIELD_FAILED_APPT_ALLOCATION . ") AS ".self::PROCESSING_ALLOCATION_FAILED_COL,
                AppointmentProcessingAllocation::TABLE.'.'.AppointmentProcessingAllocation::FIELD_ALLOCATED,
                "CONCAT(".ProductAppointment::TABLE.'.'.ProductAppointment::APPOINTMENT_DATE.", ' ', ".ProductAppointment::TABLE.'.'.ProductAppointment::APPOINTMENT_TIME.") AS ".self::APPOINTMENT_DATETIME_COL,
                Address::TABLE.'.'.Address::FIELD_UTC." AS ".self::TIMEZONE_COL,
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID." IS NOT NULL AS ".self::APPOINTMENT_SOLD_COL,
                AppointmentProcessingAllocation::TABLE.'.'.AppointmentProcessingAllocation::FIELD_CANCELLED_REJECTED." AS ".self::APPOINTMENT_CANCELLED_REJECTED,
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COMPANY_ID." AS ".self::LEGACY_COMPANY_ID_COL
            ]))
            ->get()
            ->groupBy(ProductAppointment::LEAD_CONSUMER_PRODUCT_ID);
    }

    /**
     * @return array
     */
    public function get(): array
    {
        $lcp = $this->getLeadConsumerProducts();

        if(!App::isProduction()) {
            AppointmentService::writeAppointmentLog(
                __CLASS__.': Lead Consumer Products',
                [
                    'lead_consumer_products' => $lcp->toArray()
                ]
            );
        }

        return $lcp->filter(function($leadConsumerProductData) {
            foreach($leadConsumerProductData as $consumerProduct) {
                if(empty($consumerProduct->{self::PROCESSING_ALLOCATION_FAILED_COL})
                && empty($consumerProduct->{AppointmentProcessingAllocation::FIELD_ALLOCATED})
                && empty($consumerProduct->{self::APPOINTMENT_CANCELLED_REJECTED})) {
                    return false;
                }
            }

            return true;
        })
        ->toArray();
    }
}
