<?php

namespace App\Builders;

use App\Enums\CompanyMediaAssetType;
use App\Models\Odin\CompanyMediaAsset;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Model;

class CompanyMediaAssetBuilder
{
    /**
     * @param int|null                   $companyId
     * @param string|null                $url
     * @param CompanyMediaAssetType|null $type
     * @param array|null                 $typesList
     * @param Carbon|null                $createdAt
     * @param Carbon|null                $updatedAt
     * @param string|null                $sortByColumn
     * @param string|null                $sortDirection
     */
    public function __construct(
        protected ?int                   $companyId     = null,
        protected ?string                $url           = null,
        protected ?CompanyMediaAssetType $type          = null,
        protected ?array                 $typesList     = null,
        protected ?Carbon                $createdAt     = null,
        protected ?Carbon                $updatedAt     = null,
        protected ?string                $sortByColumn  = null,
        protected ?string                $sortDirection = 'desc',
    ) {}

    /**
     * Handles applying company filter on media assets.
     *
     * @param int|null $companyId
     * @return $this
     */
    public function forCompany(?int $companyId = null): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    /**
     * Handles applying url filter on media assets.
     *
     * @param string|null $url
     * @return $this
     */
    public function forUrl(?string $url = null): self
    {
        $this->url = $url;

        return $this;
    }

    /**
     * Handles applying asset type filter on media assets.
     *
     * @param CompanyMediaAssetType|null $type
     * @return $this
     */
    public function forType(?CompanyMediaAssetType $type = null): self
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Handles applying set of asset types to filter media assets.
     *
     * @param array|null $types
     * @return $this
     */
    public function forTypes(?array $types = null): self
    {
        collect($types)->each(function ($type) {
            if($type instanceof CompanyMediaAssetType) {
                $this->typesList[] = $type->value;
            }
        });

        return $this;
    }

    /**
     * Handles applying date filter for querying media assets on/after the given date.
     *
     * @param Carbon|null $date
     * @return $this
     */
    public function mediaCreatedAtOrAfterTheDate(?Carbon $date): self
    {
        $this->createdAt = $date;

        return $this;
    }

    /**
     * Handles applying date filter for querying media assets on/before the given date.
     *
     * @param Carbon|null $date
     * @return $this
     */
    public function mediaUpdatedAtOrBeforeTheDate(?Carbon $date): self
    {
        $this->updatedAt = $date;

        return $this;
    }

    /**
     * Handles the sort order of the resulting query.
     *
     * @param string|null $column
     * @param string|null $direction
     * @return $this
     */
    public function sortBy(?string $column = null, ?string $direction = 'desc'): self
    {
        $this->sortByColumn  = $column;
        $this->sortDirection = $direction;

        return $this;
    }

    /**
     * Creates an instance of the company media asset builder.
     *
     * @return $this
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * Runs the query builder, and returns a list of media assets.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()
                    ->get();
    }

    /**
     * Runs the query builder, and returns the first media item.
     *
     * @return CompanyMediaAsset|Model|null
     */
    public function first(): CompanyMediaAsset|Model|null
    {
        return $this->getQuery()
                    ->first();
    }

    /**
     * Runs the query builder, and return the count of the queried items.
     *
     * @return int
     */
    public function count(): int
    {
        return $this->getQuery()
                    ->count();
    }

    /**
     * Returns the query for this instance.
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = CompanyMediaAsset::query();

        if(!is_null($this->companyId) && $this->companyId > 0) {
            $query->where(CompanyMediaAsset::FIELD_COMPANY_ID, $this->companyId);
        }

        if(!is_null($this->url) && strlen(trim($this->url)) > 0 ) {
            $query->where(CompanyMediaAsset::FIELD_URL, $this->url);
        }

        if(!is_null($this->typesList)) {
            $query->whereIn(CompanyMediaAsset::FIELD_TYPE, $this->typesList);
        }
         else if(!is_null($this->type) && in_array($this->type->value, CompanyMediaAssetType::allTypes())) {
            $query->where(CompanyMediaAsset::FIELD_TYPE, $this->type->value);
        }

        if($this->createdAt instanceof Carbon) {
            $query->where(CompanyMediaAsset::FIELD_CREATED_AT, '>=', $this->createdAt);
        }

        if($this->updatedAt instanceof Carbon) {
            $query->where(CompanyMediaAsset::FIELD_UPDATED_AT, '<=', $this->updatedAt);
        }

        if (!is_null($this->sortByColumn)) {
            $query->orderBy($this->sortByColumn, $this->sortDirection ?? 'DESC');
        } else {
            $query->latest();
        }

        return $query;
    }
}
