<?php

namespace App\Models\Billing;

use App\Enums\Billing\InvoiceTransactionScenario;
use App\Enums\Billing\InvoiceTransactionScope;
use App\Enums\Billing\InvoiceTransactionType;
use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int id
 * @property string external_reference
 * @property string invoice_uuid
 * @property float amount
 * @property string currency
 * @property InvoiceTransactionType type
 * @property InvoiceTransactionScenario scenario
 * @property InvoiceTransactionScope scope
 * @property string origin
 * @property array payload
 * @property Carbon updated_at
 * @property Carbon created_at
 *
 * @property-read Invoice $invoice
 */
class InvoiceTransaction extends BaseModel
{
    use HasFactory, SoftDeletes;

    const string TABLE = 'invoice_transactions';

    const string FIELD_ID                 = 'id';
    const string FIELD_UUID               = 'uuid';
    const string FIELD_INVOICE_UUID       = 'invoice_uuid';
    const string FIELD_EXTERNAL_REFERENCE = 'external_reference';
    const string FIELD_AMOUNT             = 'amount';
    const string FIELD_CURRENCY           = 'currency';
    const string FIELD_TYPE               = 'type';
    const string FIELD_ORIGIN             = 'origin';
    const string FIELD_PAYLOAD            = 'payload';
    const string FIELD_SCENARIO           = 'scenario';
    const string FIELD_SCOPE              = 'scope';
    const string FIELD_UPDATED_AT         = 'updated_at';
    const string FIELD_CREATED_AT         = 'created_at';
    const string FIELD_DELETED_AT         = 'deleted_at';
    const string FIELD_DATE               = 'date';
    const string RELATION_INVOICE         = 'invoice';
    const string RELATION_REFUNDS         = 'refunds';

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_PAYLOAD  => 'array',
        self::FIELD_TYPE     => InvoiceTransactionType::class,
        self::FIELD_SCENARIO => InvoiceTransactionScenario::class,
        self::FIELD_SCOPE    => InvoiceTransactionScope::class,
    ];

    /**
     * @return BelongsTo
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, self::FIELD_INVOICE_UUID, Invoice::FIELD_UUID);
    }

    /**
     * @return HasMany
     */
    public function refunds(): HasMany
    {
        return $this->hasMany(InvoiceRefundCharge::class, InvoiceRefundCharge::FIELD_REFUNDED_PAYMENT_ID, self::FIELD_ID);
    }

    /**
     * @return float
     */
    public function totalRefunded(): float
    {
        return $this->refunds()->sum(InvoiceRefundCharge::FIELD_AMOUNT) ?? 0;
    }

    /**
     * @return float
     */
    public function totalRefundable(): float
    {
        if ($this->type != InvoiceTransactionType::PAYMENT) {
            return 0;
        }

        return $this->{InvoiceTransaction::FIELD_AMOUNT} - $this->totalRefunded();
    }

    public function getConsolidatedTitle(): string
    {
        return collect([
            $this->scope?->getTitle(),
            $this->type->getTitle(),
            $this->scenario?->getTitle(),
        ])->filter()->join(' ');
    }
}
