<?php

namespace App\Models\Billing;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int id
 * @property int invoice_item_id
 * @property float value
 * @property int invoice_refund_id
 */
class InvoiceRefundItem extends BaseModel
{
    use HasFactory;

    const string TABLE = 'invoice_refund_items';

    const string FIELD_ID                = 'id';
    const string FIELD_INVOICE_ITEM_ID   = 'invoice_item_id';
    const string FIELD_VALUE             = 'value';
    const string FIELD_INVOICE_REFUND_ID = 'invoice_refund_id';
    const string RELATION_INVOICE_ITEM   = 'invoiceItem';
    const string RELATION_INVOICE_REFUND = 'invoiceRefund';

    protected $guarded = [
        self::FIELD_ID
    ];

    public function invoiceItem(): BelongsTo
    {
        return $this->belongsTo(InvoiceItem::class, self::FIELD_INVOICE_ITEM_ID, InvoiceItem::FIELD_ID);
    }

    public function invoiceRefund(): BelongsTo
    {
        return $this->belongsTo(InvoiceRefund::class, self::FIELD_INVOICE_REFUND_ID, InvoiceRefund::FIELD_ID);
    }
}
