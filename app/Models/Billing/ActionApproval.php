<?php

namespace App\Models\Billing;

use App\Enums\Billing\ApprovalStatus;
use App\Enums\Billing\ApprovableActionType;
use App\Models\User;
use App\Traits\Uuid;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Arr;

/**
 * @property int $id
 *
 * @property int approvable_id
 * @property string $uuid
 * @property string approvable_type
 * @property int $requested_by
 * @property ApprovalStatus $status
 * @property ApprovableActionType $requested_action
 * @property string $reason
 * @property array $payload
 *
 * @property-read User $reviewedBy
 * @property-read User $requestedBy
 *
 * @property Carbon $reviewed_at
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class ActionApproval extends Model
{
    use Uuid;

    const string TABLE = 'action_approvals';

    const string FIELD_ID               = 'id';
    const string FIELD_UUID             = 'uuid';
    const string FIELD_REQUESTED_BY     = 'requested_by';
    const string FIELD_REVIEWED_BY      = 'reviewed_by';
    const string FIELD_REQUESTED_ACTION = 'requested_action';
    const string FIELD_PAYLOAD          = 'payload';
    const string FIELD_STATUS           = 'status';
    const string FIELD_REASON           = 'reason';
    const string FIELD_NOTE             = 'note';
    const string FIELD_IS_PROCESSING    = 'is_processing';
    const string FIELD_APPROVABLE_ID    = 'approvable_id';
    const string FIELD_APPROVABLE_TYPE  = 'approvable_type';
    const string FIELD_REVIEWED_AT      = 'reviewed_at';
    const string FIELD_CREATED_AT       = 'created_at';
    const string FIELD_UPDATED_AT       = 'updated_at';
    const string RELATION_APPROVABLE    = 'approvable';
    const string PAYLOAD_ARGUMENTS      = 'arguments';

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_PAYLOAD          => 'array',
        self::FIELD_STATUS           => ApprovalStatus::class,
        self::FIELD_REQUESTED_ACTION => ApprovableActionType::class,
    ];

    /**
     * @return BelongsTo
     */
    public function requestedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_REQUESTED_BY, User::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function reviewedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_REVIEWED_BY, User::FIELD_ID);
    }

    /**
     * @return MorphTo
     */
    public function approvable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * @return array
     */
    public function getActionArguments(): array
    {
        return Arr::get($this->{ActionApproval::FIELD_PAYLOAD} ?? [], self::PAYLOAD_ARGUMENTS, []);
    }
}
