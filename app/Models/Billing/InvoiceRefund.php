<?php

namespace App\Models\Billing;

use App\Enums\InvoiceRefundStatus;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int id
 * @property int invoice_id
 * @property int transaction_id
 * @property float total
 * @property string external_charge_id
 * @property InvoiceRefundStatus status
 * @property string reason
 */
class InvoiceRefund extends BaseModel
{
    use HasFactory;

    const string TABLE = 'invoice_refunds';

    const string FIELD_ID                 = 'id';
    const string FIELD_UUID               = 'uuid';
    const string FIELD_INVOICE_ID         = 'invoice_id';
    const string FIELD_TOTAL              = 'total';
    const string FIELD_STATUS             = 'status';
    const string FIELD_REASON             = 'reason';
    const string FIELD_CREATED_AT         = 'created_at';
    const string FIELD_UPDATED_AT         = 'updated_at';
    const string RELATION_INVOICE         = 'invoice';
    const string RELATION_REFUND_ITEMS    = 'refundItems';
    const string RELATION_REFUND_CHARGES  = 'refundCharges';

    protected $casts = [
        self::FIELD_STATUS => InvoiceRefundStatus::class,
    ];

    protected $guarded = [
        self::FIELD_ID
    ];

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, self::FIELD_INVOICE_ID, Invoice::FIELD_ID);
    }

    public function refundCharges(): hasMany
    {
        return $this->hasMany(InvoiceRefundCharge::class, InvoiceRefundCharge::FIELD_INVOICE_REFUND_ID, self::FIELD_ID);
    }

    public function refundItems(): HasMany
    {
        return $this->hasMany(InvoiceRefundItem::class, InvoiceRefundItem::FIELD_INVOICE_REFUND_ID, self::FIELD_ID);
    }

}
