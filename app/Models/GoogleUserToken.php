<?php

namespace App\Models;

use App\Enums\GoogleServiceType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 *
 * @property int $user_id
 * @property string $token
 * @property string $refresh_token
 * @property GoogleServiceType $service
 *
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 *
 * @property-read User $user
 */
class GoogleUserToken extends BaseModel
{
    use SoftDeletes;

    const string TABLE = 'google_user_tokens';

    const string FIELD_ID            = 'id';
    const string FIELD_USER_ID       = 'user_id';
    const string FIELD_TOKEN         = 'token';
    const string FIELD_REFRESH_TOKEN = 'refresh_token';
    const string FIELD_CREATED_AT    = 'created_at';
    const string FIELD_UPDATED_AT    = 'updated_at';
    const string FIELD_DELETED_AT    = 'deleted_at';
    const string FIELD_SERVICE       = 'service';
    const string FIELD_SCOPES        = 'scopes';

    const string RELATION_USER = 'user';

    protected $casts = [
        self::FIELD_SERVICE    => GoogleServiceType::class,
    ];

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }
}
