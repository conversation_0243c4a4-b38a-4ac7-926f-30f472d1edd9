<?php

namespace App\Models\Affiliates;

use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property int $affiliate_id
 * @property PayoutStrategyTypeEnum $type
 * @property int $value
 * @property int $author_id
 * @property Carbon $active_from
 * @property Carbon $active_to
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Affiliate $affiliate
 * @property-read Collection<Payout> $payouts
 * @property-read User $author
 *
 * @method static Builder active()
 */
class PayoutStrategy extends Model
{
    use HasFactory;

    const string TABLE = 'affiliate_payout_strategies';

    const string FIELD_ID = 'id';
    const string FIELD_AFFILIATE_ID = 'affiliate_id';
    const string FIELD_AUTHOR_ID = 'author_id';
    const string FIELD_TYPE = 'type';
    const string FIELD_VALUE = 'value';
    const string FIELD_ACTIVE_FROM = 'active_from';
    const string FIELD_ACTIVE_TO = 'active_to';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    const string RELATION_AFFILIATE = 'affiliate';
    const string RELATION_PAYOUTS = 'payouts';
    const string RELATION_AUTHOR = 'author';

    const int DEFAULT_REVENUE_PERCENTAGE = 50;

    protected $guarded = [self::FIELD_ID];
    protected $casts = [self::FIELD_TYPE => PayoutStrategyTypeEnum::class];
    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function affiliate(): BelongsTo
    {
        return $this->belongsTo(Affiliate::class);
    }

    /**
     * @return HasMany
     */
    public function payouts(): HasMany
    {
        return $this->hasMany(Payout::class, Payout::FIELD_PAYOUT_STRATEGY_ID, self::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_AUTHOR_ID, User::FIELD_ID);
    }

    public function scopeActive(Builder $builder)
    {
        return $builder
            ->where(self::FIELD_ACTIVE_FROM, '<=', now())
            ->where(function (Builder $builder) {
                $builder->whereNull(self::FIELD_ACTIVE_TO)
                    ->orWhere(self::FIELD_ACTIVE_TO, '>=', now());
            });
    }
}
