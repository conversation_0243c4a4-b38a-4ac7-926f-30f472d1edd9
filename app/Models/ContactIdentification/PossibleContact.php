<?php

namespace App\Models\ContactIdentification;

use App\Models\Odin\CompanyUser;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $relation_id
 * @property string $relation_type
 * @property int $identified_contact_id
 */
class PossibleContact extends Model
{
    use SoftDeletes;

    const string TABLE = 'possible_contacts';

    const string FIELD_ID                    = 'id';
    const string FIELD_RELATION_ID           = 'relation_id';
    const string FIELD_RELATION_TYPE         = 'relation_type';
    const string FIELD_IDENTIFIED_CONTACT_ID = 'identified_contact_id';
    const string FIELD_CREATED_AT            = 'created_at';
    const string FIELD_UPDATED_AT            = 'updated_at';
    const string FIELD_DELETED_AT            = 'deleted_at';

    const string RELATION_IDENTIFIED_CONTACT = 'identifiedContact';
    const string RELATION_IDENTIFIABLE       = 'identifiable';

    protected $guarded = [self::FIELD_ID];

    public function isCompanyUser(): bool
    {
        return $this->relation_type === CompanyUser::class;
    }

    /**
     * @return MorphTo
     */
    public function identifiable(): MorphTo
    {
        return $this->morphTo(__FUNCTION__, self::FIELD_RELATION_TYPE, self::FIELD_RELATION_ID);
    }

    /**
     * @return BelongsTo
     */
    public function identifiedContact(): BelongsTo
    {
        return $this->belongsTo(IdentifiedContact::class, self::FIELD_IDENTIFIED_CONTACT_ID, IdentifiedContact::FIELD_ID);
    }
}
