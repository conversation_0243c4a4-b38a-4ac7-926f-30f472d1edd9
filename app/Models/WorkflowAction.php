<?php

namespace App\Models;

use App\Enums\ActionType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property int $previous_node_id
 * @property string $previous_node_type
 * @property string $display_name
 * @property int $workflow_id
 * @property ActionType $action_type
 * @property array $payload
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Collection<WorkflowAction> $children
 * @property-read Collection<WorkflowAction> $siblings
 */
class WorkflowAction extends Model
{
    use HasFactory;

    const TABLE = "workflow_actions";

    const FIELD_ID                 = 'id';
    const FIELD_PREVIOUS_NODE_ID   = 'previous_node_id';
    const FIELD_PREVIOUS_NODE_TYPE = 'previous_node_type';
    const FIELD_DISPLAY_NAME       = 'display_name';
    const FIELD_WORKFLOW_ID        = 'workflow_id';
    const FIELD_ACTION_TYPE        = 'action_type';
    const FIELD_PAYLOAD            = 'payload';
    const FIELD_CREATED_AT         = 'created_at';
    const FIELD_UPDATED_AT         = 'updated_at';

    const PARENT_TYPE_PARENT  = 'parent';
    const PARENT_TYPE_SIBLING = 'sibling';

    const RELATION_CHILDREN = 'children';
    const RELATION_SIBLINGS = 'siblings';

    protected $table   = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts   = [
        self::FIELD_ACTION_TYPE => ActionType::class,
        self::FIELD_PAYLOAD     => 'array',
    ];

    /**
     * Get Action children.
     *
     * @return HasMany
     */
    public function children(): HasMany
    {
        return $this->hasMany(WorkflowAction::class, self::FIELD_PREVIOUS_NODE_ID, self::FIELD_ID)
                    ->where(self::FIELD_PREVIOUS_NODE_TYPE, self::PARENT_TYPE_PARENT);
    }

    /**
     * Relation to siblings.
     *
     * @return HasMany
     */
    public function siblings(): HasMany
    {
        return $this->hasMany(WorkflowAction::class, self::FIELD_PREVIOUS_NODE_ID, self::FIELD_ID)
                    ->where(self::FIELD_PREVIOUS_NODE_TYPE, self::PARENT_TYPE_SIBLING);
    }
}
