<?php

namespace App\Models\Conference;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 *
 * @property int $conference_id
 * @property string $external_id
 * @property string $external_destination_file_id
 * @property string $bucket_path
 * @property string $start_time
 * @property string $end_time
 * @property string $expire_time
 * @property int $duration_in_seconds
 *
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Conference $conference
 */
class ConferenceRecording extends BaseModel
{
    const string TABLE = 'conference_recordings';

    const string FIELD_ID = 'id';

    const string FIELD_CONFERENCE_ID                = 'conference_id';
    const string FIELD_EXTERNAL_ID                  = 'external_id';
    const string FIELD_EXTERNAL_DESTINATION_FILE_ID = 'external_destination_file_id';
    const string FIELD_BUCKET_PATH                  = 'bucket_path';
    const string FIELD_START_TIME                   = 'start_time';
    const string FIELD_END_TIME                     = 'end_time';
    const string FIELD_EXPIRE_TIME                  = 'expire_time';
    const string FIELD_DURATION_IN_SECONDS          = 'duration_in_seconds';

    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_START_TIME => 'datetime',
        self::FIELD_END_TIME   => 'datetime',
    ];

    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function conference(): BelongsTo
    {
        return $this->belongsTo(Conference::class, self::FIELD_CONFERENCE_ID, self::FIELD_ID);
    }
}
