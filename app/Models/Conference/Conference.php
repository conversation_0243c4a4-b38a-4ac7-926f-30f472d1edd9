<?php

namespace App\Models\Conference;

use App\Models\BaseModel;
use App\Models\Calendar\CalendarEvent;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * @property int $id
 *
 * @property int $calendar_event_id
 * @property int $user_id
 * @property int $external_id
 * @property string $start_time
 * @property string $end_time
 * @property string $expire_time
 * @property int $duration_in_seconds
 * @property string $status
 *
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read CalendarEvent $calendarEvent
 * @property-read Collection<ConferenceParticipant> $participants
 */
class Conference extends BaseModel
{
    use HasFactory;

    const string TABLE = 'conferences';

    const string FIELD_ID = 'id';

    const string FIELD_CALENDAR_EVENT_ID   = 'calendar_event_id';
    const string FIELD_EXTERNAL_ID         = 'external_id';
    const string FIELD_USER_ID             = 'user_id';
    const string FIELD_START_TIME          = 'start_time';
    const string FIELD_END_TIME            = 'end_time';
    const string FIELD_EXPIRE_TIME         = 'expire_time';
    const string FIELD_DURATION_IN_SECONDS = 'duration_in_seconds';
    const string FIELD_STATUS              = 'status';

    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';

    const string RELATION_PARTICIPANTS   = 'participants';
    const string RELATION_TRANSCRIPTS    = 'transcripts';
    const string RELATION_CALENDAR_EVENT = 'calendarEvent';

    protected $casts = [
        self::FIELD_START_TIME => 'datetime',
        self::FIELD_END_TIME   => 'datetime',
    ];

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function calendarEvent(): BelongsTo
    {
        return $this->belongsTo(CalendarEvent::class, self::FIELD_CALENDAR_EVENT_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function participants(): HasMany
    {
        return $this->hasMany(ConferenceParticipant::class, ConferenceParticipant::FIELD_CONFERENCE_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function transcripts(): HasMany
    {
        return $this->hasMany(ConferenceTranscript::class, ConferenceTranscript::FIELD_CONFERENCE_ID, self::FIELD_ID);
    }
}
