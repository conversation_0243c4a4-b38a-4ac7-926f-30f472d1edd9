<?php

namespace App\Models\SalesIntel;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserImportRecord extends Model
{
    /** @use HasFactory<\Database\Factories\SalesIntel\UserImportRecordFactory> */
    use HasFactory;
    const string TABLE = 'sales_intel_user_import_records';

    protected $table = self::TABLE;

    protected $guarded = [
        'id'
    ];

    function company() {
        return $this->belongsTo(Company::class);
    }

    function user() {
        return $this->belongsTo(CompanyUser::class);
    }
}
