<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TieredAdvertisingLog extends Model
{
    const string TABLE = 'tiered_advertising_logs';

    const string FIELD_ID = 'id';
    const string FIELD_PLATFORM = 'platform';
    const string FIELD_INDUSTRY_ID = 'industry_id';
    const string FIELD_LOCATION_ID = 'location_id';
    const string FIELD_ACCOUNT_ID = 'tiered_advertising_account_id';
    const string FIELD_CAMPAIGN_ID = 'tiered_advertising_campaign_id';
    const string FIELD_USER_ID = 'user_id';
    const string FIELD_TYPE = 'type';
    const string FIELD_MESSAGE = 'message';
    const string FIELD_DATA = 'data';

    const string TYPE_GOOGLE_LOCATION_REMOVE = 'gr';
    const string TYPE_GOOGLE_LOCATION_ADD = 'ga';

    const string DATA_TCPA_BID = 'bid';
    const string DATA_NEGATIVE_ZIPS = 'nZips';
    const string DATA_LOWER_BOUND = 'lBound';
    const string DATA_UPPER_BOUND = 'uBound';
    const string DATA_TIER = 'tier';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_DATA => 'array',
    ];
}
