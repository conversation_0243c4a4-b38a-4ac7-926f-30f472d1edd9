<?php

namespace App\Models;

use App\Models\Odin\Industry;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class AvailableBudget
 * @package App\Models
 * @property integer $id
 * @property integer $location_id
 * @property integer $county_location_id
 * @property float $budget_available_dollars
 * @property integer $budget_available_volume
 * @property integer $available_campaign_count
 * @property integer $unlimited_campaign_count
 * @property float $potential_queued_revenue
 * @property integer $budget_type
 * @property string $industry_type
 * @property int $industry_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class AvailableBudget extends Model
{
    const string TABLE                          = 'available_budgets';
    const string FIELD_ID                       = 'id';
    const string FIELD_LOCATION_ID              = 'location_id';
    const string FIELD_COUNTY_LOCATION_ID       = 'county_location_id';
    const string FIELD_BUDGET_AVAILABLE_DOLLARS = 'budget_available_dollars';
    const string FIELD_BUDGET_AVAILABLE_VOLUME  = 'budget_available_volume';
    const string FIELD_AVAILABLE_CAMPAIGN_COUNT = 'available_campaign_count';
    const string FIELD_UNLIMITED_BUDGET_COUNT   = 'unlimited_budget_count';
    const string FIELD_POTENTIAL_QUEUED_REVENUE = 'potential_queued_revenue';
    const string FIELD_INDUSTRY_TYPE            = 'industry_type';
    const string FIELD_INDUSTRY_ID              = 'industry_id';
    const string FIELD_BUDGET_TYPE              = 'budget_type';
    const string FIELD_CREATED_AT               = 'created_at';
    const string FIELD_UPDATED_AT               = 'updated_at';

    const int BUDGET_TYPE_VERIFIED   = 1;
    const int BUDGET_TYPE_UNVERIFIED = 0;

    const string INDUSTRY_TYPE_SOLAR   = 'solar';
    const string INDUSTRY_TYPE_ROOFING = 'roofer';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

}
