<?php

namespace App\Models;

/**
 * Class USZipCode
 *
 * @property int $id
 * @property string $zip_code
 * @property string $zip_type
 * @property string $city_name
 * @property string $city_type
 * @property string $county_name
 * @property string $county
 * @property string $county_fips
 * @property string $state_name
 * @property string $state_abbr
 * @property string $state_fips
 * @property string $msa_code
 * @property string $area_code
 * @property string $time_zone
 * @property integer $utc
 * @property boolean $dst
 * @property float $latitude
 * @property float $longitude
 */
class USZipCode extends BaseModel
{
    // Data for this table is imported from https://zipcodedownload.com/Products/Product/Z5Commercial/Standard/Overview#schema

    const TABLE = 'us_zip_codes';

    protected $table = self::TABLE;

    const FIELD_ID          = 'id';
    const FIELD_ZIP_CODE    = 'zip_code';
    const FIELD_ZIP_TYPE    = 'zip_type';
    const FIELD_CITY_NAME   = 'city_name';
    const FIELD_CITY_TYPE   = 'city_type';
    const FIELD_COUNTY_NAME = 'county_name';
    const FIELD_COUNTY_FIPS = 'county_fips'; // Each county in the United States is assigned a unique number for identification purposes
    const FIELD_STATE_NAME  = 'state_name';
    const FIELD_STATE_ABBR  = 'state_abbr';
    const FIELD_STATE_FIPS  = 'state_fips';
    const FIELD_MSA_CODE    = 'msa_code';
    const FIELD_AREA_CODE   = 'area_code'; // If multiple area codes belong to one postal code, all area codes are listed and delimited by a slash "/" character.
    const FIELD_TIME_ZONE   = 'time_zone';
    const FIELD_UTC         = 'utc'; // The UTC offset is the number of hours the particular ZIP Code is from Universal Time Co-ordinated (UTC), the international time standard, also known as Greenwich Meridian Time (GMT).
    const FIELD_DST         = 'dst'; // The daylight savings time flag indicates if a particular ZIP Code obeys, or, in other words adjusts their clocks forward and back with the seasons.
    const FIELD_LATITUDE    = 'latitude';
    const FIELD_LONGITUDE   = 'longitude';
    const FIELD_POPULATION  = 'population';
    const FIELD_DENSITY     = 'density'; // The estimated population per square kilometer. Only exists if zcta is TRUE, otherwise the zip code is not associated with a physical area
    const FIELD_COUNTY_WEIGHTS = 'county_weights'; // Some zip codes cover multiple counties, this contains a JSON object of County Fips => Weight of county within that zip code

    const ZIP_TYPES = [
        'S', // Standard - A "standard" ZIP Code is what most people think of when they talk about ZIP Codes - essentially a town, city, or a division of a city that has mail service.
        'P', // PO Box Only - Rural towns, groups of towns, or even high-growth areas of cities are given a "PO Box Only" ZIP Code type.
        'U', // Unique - Companies, organizations, and institutions that receive large quantities of mail are given a "unique" ZIP Code type.
        'M' // Military - Military bases overseas - and often vessels and ships - are given a "military" ZIP Code type.
    ];

    const CITY_TYPES = [
        'D', // Default - This is the "preferred" name - by the USPS - for a city. Each ZIP Code has one - and only one - "default" name. In most cases, this is what people who live in that area call the city as well.
        'A', // Acceptable - This name can be used for mailing purposes. Often times alternative names are large neighborhoods or sections of the city/town. In some cases a ZIP Code may have several "acceptable" names which is used to group towns under one ZIP Code.
        'N', // Not Acceptable - A "not acceptable" name is, in many cases, a nickname that residents give that location. According to the USPS, you should NOT send mail to that ZIP Code using the "not acceptable" name when mailing.
    ];

    const DEFAULT_ZIP_TYPE  = 'S';
    const DEFAULT_CITY_TYPE = 'D';

    protected $guarded = [self::FIELD_ID];

    public    $timestamps = false;
}
