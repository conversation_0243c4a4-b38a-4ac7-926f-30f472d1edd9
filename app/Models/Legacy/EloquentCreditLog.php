<?php

namespace App\Models\Legacy;

/**
 * Class EloquentCreditLog
 *
 * @package App
 *
 * @property int $id
 * @property int $logdate
 * @property int $companyid
 * @property int $invoiceid
 * @property string $status
 * @property float $amount
 * @property string $type
 * @property int $userid
 * @property int $manual
 * @property int $reverted
 * @property int $revertedid
 *
 */
class EloquentCreditLog extends LegacyModel
{
    protected $table = 'tbl_credit_log';
    public $timestamps = true;
    CONST TABLE = 'tbl_credit_log';
    const ID = 'id';
    const LOG_DATE = 'logdate';
    const COMPANY_ID = 'companyid';
    const INVOICE_ID = 'invoiceid';
    const APPLIED_INVOICE_ID = 'appliedinvoiceid';
    const STATUS = 'status';
    const AMOUNT = 'amount';
    const TYPE = 'type';
    const USERID = 'userid';
    const MANUAL = 'manual';
    const REVERTED = 'reverted';
    const REVERTED_ID = 'revertedid';
    const CREATED_AT = 'created_at';
    const COMMENT = 'comment';

    const TYPE_CREDIT = 'credit';
    const TYPE_VOUCHER = 'voucher';
    const TYPE_SIGNUP_BONUS = 'signup_bonus';
    const STATUS_ISSUED = 'issued';
    const STATUS_APPLIED = 'applied';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REVERTED = 'reverted';
}
