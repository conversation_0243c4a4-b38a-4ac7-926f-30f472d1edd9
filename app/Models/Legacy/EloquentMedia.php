<?php

namespace App\Models\Legacy;

/**
 * @property int    $mediaid
 * @property string $reltype
 * @property int    $relid
 * @property string $realname
 * @property string $type
 * @property string $displayname
 * @property string $comments
 * @property int    $timestampupdated
 * @property int    $timestampadded
 * @property int    $share
 * @property int    $random
 * @property int    $secure
 *
 */
class EloquentMedia extends LegacyModel
{
    const TABLE = 'tblmedia';

    const MEDIA_ID          = 'mediaid';
    const ID                = self::MEDIA_ID;
    const REL_TYPE          = 'reltype';
    const REL_ID            = 'relid';
    const REAL_NAME         = 'realname';
    const TYPE              = 'type';
    const DISPLAY_NAME      = 'displayname';
    const COMMENTS          = 'comments';
    const TIMESTAMP_UPDATED = 'timestampupdated';
    const TIMESTAMP_ADDED   = 'timestampadded';
    const SHARE             = 'share';
    const RANDOM            = 'random';
    const SECURE            = 'secure';

    const REL_TYPE_OPTION  = 'option';
    const REL_TYPE_COMPANY = 'company';

    const SHARE_VALUE    = 1;
    const UNSHARE_VALUE  = 0;
    const SECURE_VALUE   = 1;
    const UNSECURE_VALUE = 0;

    protected $table = self::TABLE;
    protected $guarded = [
        self::ID
    ];

    protected $fillable = [
        self::REL_TYPE,
        self::REL_ID,
        self::REAL_NAME,
        self::TYPE,
        self::DISPLAY_NAME
    ];
}
