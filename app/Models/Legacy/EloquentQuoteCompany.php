<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class EloquentQuoteCompany
 *
 * @property EloquentQuote $quote
 * @property int $invoiceitemid
 * @property int $timestampdelivered
 * @property int $timestamp_rejected_at
 * @property EloquentCompany $company
 * @property float $cost
 * @property boolean $delivered
 * @property boolean $chargeable
 * @property boolean $offHourSale
 * @property string $leadcosttype
 * @method static Builder|EloquentQuoteCompany chargeableAndDelivered()
 * @method static Builder|EloquentQuoteCompany filterByDeliveredTimestamp(?int $days = null)
 */
class EloquentQuoteCompany extends LegacyModel
{
    const TABLE = 'tblquotecompany';

    const ID                                        = self::QUOTE_COMPANY_ID;
    const QUOTE_ID                                  = 'quoteid';
    const QUOTE_COMPANY_ID                          = 'quotecompanyid';
    const COMPANY_ID                                = 'companyid';
    const DELIVERED                                 = 'delivered';
    const CHARGEABLE                                = 'chargeable';
    const COST                                      = 'cost';
    const TIMESTAMP_DELIVERED                       = 'timestampdelivered';
    const CHARGE_STATUS                             = 'chargestatus';
    const TIMESTAMP_INITIAL_DELIVERY                = 'timestampinitialdelivery';
    const TIMESTAMP_REJECTION_EXPIRY                = 'timestamprejectionexpiry';
    const TIMESTAMP_REJECTED_AT                     = 'timestamp_rejected_at';
    const SOLD_STATUS                               = 'soldstatus';
    const VALUE_SOLD_STATUS_SOLD                    = 'sold';
    const LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID = 'lead_campaign_sales_type_configuration_id';
    const INVOICE_ITEM_ID                           = 'invoiceitemid';
    const INCLUDE_IN_BUDGET                         = 'incl_in_budget';
    const REJECT_NOTES                              = 'rejectnotes';
    const NON_BUDGET_PREMIUM_LEAD                   = 'non_budget_premium_lead';
    const TIMESTAMP_ADDED                           = 'timestampadded';
    const OFF_HOUR_SALE                             = 'off_hour_sale';
    const LEAD_COST_TYPE                            = 'leadcosttype';
    const SALE_TYPE                                 = 'saletype';
    const BILLING_VERSION                           = 'billing_version';


    const VALUE_CHARGE_STATUS_REJECTED = 'rejected';
    const VALUE_CHARGE_STATUS_INITIAL = 'initial';
    const VALUE_UNINVOICE_ID = 0;

    const IS_DELIVERED  = 1;
    const NOT_DELIVERED = 0;

    const IS_CHARGEABLE = 1;
    const NOT_CHARGEABLE = 0;

    const VALUE_LEAD_COST_TYPE_PAYPERLEAD = 'payperlead';
    const VALUE_LEAD_COST_TYPE_APPOINTMENT = 'appointment';
    const VALUE_LEAD_COST_TYPE_REJECTED_APPOINTMENT_LEAD = 'rejapptlead';
    const VALUE_LEAD_COST_TYPE_PREMIUM = 'premium';
    const VALUE_SOLD_STATUS_PREVIEW = 'preview';

    const LEGACY_SALE_TYPE_MULTIPLE  = 'multiple';
    const LEGACY_SALE_TYPE_EXCLUSIVE = 'exclusive';

    const RELATION_LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION = 'leadCampaignSalesTypeConfiguration';
    const RELATION_QUOTE                                  = 'quote';

    protected $primaryKey = self::ID;
    protected $table      = self::TABLE;
    public    $timestamps = false;

    protected $casts = [
        self::QUOTE_ID  => 'integer',
        self::DELIVERED => 'boolean'
    ];

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeDelivered(Builder $query): Builder
    {
        return $query->where(self::DELIVERED, true);
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeChargeableAndDelivered(Builder $query): Builder
    {
        $query = $this->scopeChargeable($query);
        return $this->scopeDelivered($query);
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeChargeable(Builder $query): Builder
    {
        return $query->where(self::CHARGEABLE, true);
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeChargeStatusRejected(Builder $query): Builder
    {
        return $query->where(self::CHARGE_STATUS, self::VALUE_CHARGE_STATUS_REJECTED);
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeChargeStatusNotRejected(Builder $query): Builder
    {
        return $query->whereNot(self::CHARGE_STATUS, self::VALUE_CHARGE_STATUS_REJECTED);
    }

    /**
     * @return BelongsTo
     */
    public function quote(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, EloquentQuote::ID, self::QUOTE_ID);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(EloquentCompany::class, EloquentCompany::ID, self::COMPANY_ID);
    }

    /**
     * @return BelongsTo
     */
    public function leadCampaignSalesTypeConfiguration(): BelongsTo
    {
        return $this->belongsTo(LeadCampaignSalesTypeConfiguration::class, self::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID, LeadCampaignSalesTypeConfiguration::ID);
    }

    /**
     * @param  Builder  $query
     * @param  int|null  $days
     * @return Builder
     * @see filterByDeliveredTimestamp
     */
    public function scopeFilterByDeliveredTimestamp(Builder $query, ?int $days = null): Builder
    {
        $days = $days ?? config('models.EloquentQuoteCompany.range_by_delivered_timestamp_in_days');
        $query->where(self::TIMESTAMP_DELIVERED, '>=', now()->subDays($days)->timestamp);
        return $query;
    }
}
