<?php

namespace App\Models\Legacy;

/**
 * Class LeadSalesType
 *
 * @property int $id
 *
 * @property-read string $name
 */
class LeadSalesType extends LegacyModel
{
    const TABLE = 'tbl_lead_sales_types';

    const ID        = 'id';
    const KEY_VALUE = 'key_value';
    const NAME      = 'name';
    const STATUS = 'status';
    const SALE_LIMIT = 'sale_limit';

    const KEY_VALUE_EXCLUSIVE  = "EXCLUSIVE";
    const KEY_VALUE_DUO        = "DUO";
    const KEY_VALUE_TRIO       = "TRIO";
    const KEY_VALUE_QUAD       = "QUAD";
    const KEY_VALUE_UNVERIFIED = "UNVERIFIED";
    const KEY_VALUE_EMAIL_ONLY = "EMAIL_ONLY";

    const LEAD_SALE_TYPE_EXCLUSIVE_ID = '1';
    const LEAD_SALE_TYPE_DUO_ID = '2';
    const LEAD_SALE_TYPE_TRIO_ID = '3';
    const LEAD_SALE_TYPE_QUAD_ID = '4';
    const LEAD_SALE_TYPE_UNVERIFIED_ID = '5';
    const LEAD_SALE_TYPE_EMAIL_ONLY_ID = '6';

    const VERIFIED_SALES_TYPES = [
        self::LEAD_SALE_TYPE_EXCLUSIVE_ID,
        self::LEAD_SALE_TYPE_DUO_ID,
        self::LEAD_SALE_TYPE_TRIO_ID,
        self::LEAD_SALE_TYPE_QUAD_ID
    ];

    const UNVERIFIED_SALES_TYPES = [
        self::LEAD_SALE_TYPE_UNVERIFIED_ID,
    ];

    protected $table      = self::TABLE;
    protected $guarded    = [self::ID];
    public    $timestamps = false;
}
