<?php

namespace App\Models;

use App\Models\Sales\Task;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property int $task_id
 * @property int $user_id
 * @property string $note
 * @property Carbon $created_at
 * @property-read Task $task
 * @property-read User $user
 */
class TaskNote extends Model
{
    const TABLE = 'task_notes';

    const FIELD_ID = 'id';
    const FIELD_TASK_ID = 'task_id';
    const FIELD_USER_ID = 'user_id';
    const FIELD_NOTE = 'note';
    const FIELD_CREATED_AT = 'created_at';

    const RELATION_TASK = 'task';
    const RELATION_USER = 'user';

    protected $guarded = [self::FIELD_ID];

    /**
     * Defines relationship to the tasks that use this task type.
     *
     * @return HasOne
     */
    public function task(): HasOne
    {
        return $this->hasOne(Task::class, Task::FIELD_ID, self::FIELD_TASK_ID);
    }

    /**
     * @return HasOne
     */
    public function user(): hasOne
    {
        return $this->hasOne(User::class, User::FIELD_ID, self::FIELD_USER_ID);
    }
}
