<?php

namespace App\Models;

use Carbon\Carbon;

/**
 * Class HistoricalAvailableCampaignBudget
 * @package App\Models
 * @property integer $id
 * @property integer $location_id
 * @property integer $campaign_id
 * @property float $budget_available_dollars
 * @property integer $budget_available_volume
 * @property float $budget_available_dollars_omit_rejection
 * @property integer $budget_available_volume_omit_rejection
 * @property integer $available_campaign_count
 * @property bool $unlimited
 * @property integer $budget_type
 * @property string $industry_type
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class HistoricalAvailableCampaignBudget extends BaseModel
{
    const TABLE = 'historical_available_campaign_budgets';

    const FIELD_ID                                      = 'id';
    const FIELD_BUDGET_AVAILABLE_DOLLARS                = 'budget_available_dollars';
    const FIELD_BUDGET_AVAILABLE_VOLUME                 = 'budget_available_volume';
    const FIELD_BUDGET_AVAILABLE_DOLLARS_OMIT_REJECTION = 'budget_available_dollars_omit_rejection';
    const FIELD_BUDGET_AVAILABLE_VOLUME_OMIT_REJECTION  = 'budget_available_volume_omit_rejection';
    const FIELD_INDUSTRY_TYPE                           = 'industry_type';
    const FIELD_BUDGET_TYPE                             = 'budget_type';
    const FIELD_UNLIMITED                               = 'unlimited';
    const FIELD_LOCATION_ID                             = 'location_id';
    const FIELD_CAMPAIGN_ID                             = 'campaign_id';
    const FIELD_CREATED_AT                              = 'created_at';
    const FIELD_UPDATED_AT                              = 'updated_at';

    const BUDGET_TYPE_VERIFIED   = 1;
    const BUDGET_TYPE_UNVERIFIED = 0;

    const INDUSTRY_TYPE_SOLAR   = 'solar';
    const INDUSTRY_TYPE_ROOFING = 'roofing';

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

}
