<?php

namespace App\Models;

use App\Models\Odin\Industry;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Support\Collection;


/**
 * @property int $id
 * @property string $name
 * @property int $primary_queue_configuration_id
 * @property int $primary_utc_offset
 * @property string $industry_name
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Collection|LeadProcessor[] $leadProcessors
 * @property-read User[] $leadProcessorUsers
 * @property-read LeadProcessingQueueConfiguration $primaryQueue
 * @property-read Industry $industry
 * @property-read LeadProcessingTeamIndustry[] $leadProcessingTeamIndustries
 * @property-read Industry[]|Collection $industries
 */
class LeadProcessingTeam extends BaseModel
{
    use HasFactory;

    const TABLE = 'lead_processing_teams';

    const FIELD_ID                             = 'id';
    const FIELD_NAME                           = 'name';
    const FIELD_PRIMARY_QUEUE_CONFIGURATION_ID = 'primary_queue_configuration_id';
    const FIELD_PRIMARY_UTC_OFFSET             = 'primary_utc_offset';
    const FIELD_INDUSTRY_ID                    = 'industry_id';
    const FIELD_INDUSTRY                       = 'industry_name';

    const RELATION_LEAD_PROCESSORS                 = 'leadProcessors';
    const RELATION_LEAD_PROCESSOR_USERS            = 'leadProcessorUsers';
    const RELATION_PRIMARY_QUEUE                   = 'primaryQueue';
    const RELATION_INDUSTRY                        = 'industry';
    const RELATION_LEAD_PROCESSING_TEAM_INDUSTRIES = 'leadProcessingTeamIndustries';
    const RELATION_INDUSTRIES                      = 'industries';

    const NO_TEAM_ASSIGNED_ID = 1;

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * Defines the has many relationship to the lead processors.
     *
     * @return HasMany
     */
    public function leadProcessors(): HasMany
    {
        return $this->hasMany(LeadProcessor::class, LeadProcessor::FIELD_LEAD_PROCESSING_TEAM_ID, self::FIELD_ID);
    }

    /**
     * Defines the relationship to the users table through the lead processor table.
     *
     * @return BelongsToMany
     */
    public function leadProcessorUsers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, LeadProcessor::TABLE, LeadProcessor::FIELD_LEAD_PROCESSING_TEAM_ID, LeadProcessor::FIELD_USER_ID);
    }

    /**
     * Defines the relationship with the primary queue.
     *
     * @return BelongsTo
     */
    public function primaryQueue(): BelongsTo
    {
        return $this->belongsTo(LeadProcessingQueueConfiguration::class, self::FIELD_PRIMARY_QUEUE_CONFIGURATION_ID, LeadProcessingQueueConfiguration::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class, self::FIELD_INDUSTRY_ID, Industry::FIELD_ID);
    }

    /**
     * Defines the relationship to the lead processing team industries pivot table.
     *
     * @return HasMany
     */
    public function leadProcessingTeamIndustries(): HasMany
    {
        return $this->hasMany(LeadProcessingTeamIndustry::class, LeadProcessingTeamIndustry::FIELD_LEAD_PROCESSING_TEAM_ID, self::FIELD_ID);
    }

    /**
     * Defines the relationship to the industries.
     *
     * @return HasManyThrough
     */
    public function industries(): HasManyThrough
    {
        return $this->hasManyThrough(
            Industry::class,
            LeadProcessingTeamIndustry::class,
            LeadProcessingTeamIndustry::FIELD_LEAD_PROCESSING_TEAM_ID,
            Industry::FIELD_ID,
            self::FIELD_ID,
            LeadProcessingTeamIndustry::FIELD_INDUSTRY_ID
        );
    }
}
