<?php

namespace App\Models\Campaigns;

use App\Enums\Campaigns\CampaignExternalRelationType;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property int $id
 * @property int $relation_id
 * @property string $relation_type
 * @property int $company_campaign_id
 *
 * @property-read $companyCampaignExternalRelation
 */
class CompanyCampaignRelation extends BaseModel
{
    use HasFactory;

    const TABLE = 'company_campaign_relations';

    const FIELD_ID = 'id';
    const FIELD_COMPANY_CAMPAIGN_ID = 'company_campaign_id';
    const FIELD_RELATION_ID = 'relation_id';
    const FIELD_RELATION_TYPE = 'relation_type';

    const RELATION_COMPANY_CAMPAIGN_EXTERNAL_RELATION = 'companyCampaignExternalRelation';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_RELATION_TYPE => CampaignExternalRelationType::class
    ];

    /**
     * @return MorphTo
     */
    public function companyCampaignExternalRelation(): MorphTo
    {
        return $this->morphTo(__FUNCTION__, self::FIELD_RELATION_TYPE, self::FIELD_RELATION_ID);
    }
}
