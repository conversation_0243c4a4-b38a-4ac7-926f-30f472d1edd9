<?php

namespace App\Models\Campaigns;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $campaign_id
 * @property Carbon|null $reactivate_at
 * @property string $reason
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 *
 * @property-read CompanyCampaign $campaign
 */
class CampaignReactivation extends BaseModel
{
    use SoftDeletes, HasFactory;

    const TABLE = 'campaign_reactivations';

    const string FIELD_ID            = 'id';
    const string FIELD_CAMPAIGN_ID   = 'campaign_id';
    const string FIELD_REACTIVATE_AT = 'reactivate_at';
    const string FIELD_REASON        = 'reason';
    const string FIELD_DELETED_AT    = 'deleted_at';
    const string FIELD_CREATED_AT    = 'created_at';

    const RELATION_CAMPAIGN = 'campaign';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_REACTIVATE_AT => 'datetime'
    ];

    /**
     * Defines the model for this reactivation to a company campaign.
     *
     * @return BelongsTo
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(CompanyCampaign::class, self::FIELD_CAMPAIGN_ID, CompanyCampaign::FIELD_ID);
    }
}
