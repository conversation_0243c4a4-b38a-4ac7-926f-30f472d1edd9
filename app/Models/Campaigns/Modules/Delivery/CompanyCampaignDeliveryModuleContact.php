<?php

namespace App\Models\Campaigns\Modules\Delivery;

use App\Models\BaseModel;
use App\Models\Odin\CompanyUser;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $module_id
 * @property int $contact_id
 * @property bool $active
 * @property bool $email_active
 * @property bool $sms_active
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read CompanyCampaignDeliveryModule $module
 * @property-read CompanyUser $contact
 */
class CompanyCampaignDeliveryModuleContact extends BaseModel
{
    use HasFactory;

    const TABLE = 'company_campaign_delivery_module_contacts';

    const FIELD_ID           = 'id';
    const FIELD_MODULE_ID    = 'module_id';
    const FIELD_CONTACT_ID   = 'contact_id';
    const FIELD_ACTIVE       = 'active';
    const FIELD_EMAIL_ACTIVE = 'email_active';
    const FIELD_SMS_ACTIVE   = 'sms_active';

    const RELATION_MODULE  = 'module';
    const RELATION_CONTACT = 'contact';

    protected $guarded = [self::FIELD_ID];
    protected $table   = self::TABLE;

    /**
     * Defines the relationship to the delivery module
     *
     * @return BelongsTo
     */
    public function module(): BelongsTo
    {
        return $this->belongsTo(
            CompanyCampaignDeliveryModule::class,
            self::FIELD_MODULE_ID,
            CompanyCampaignDeliveryModule::FIELD_ID
        );
    }

    /**
     * Defines the relationship to the contact that this delivery method is for.
     *
     * @return BelongsTo
     */
    public function contact(): BelongsTo
    {
        return $this->belongsTo(
            CompanyUser::class,
            self::FIELD_CONTACT_ID,
            CompanyUser::FIELD_ID
        );
    }
}
