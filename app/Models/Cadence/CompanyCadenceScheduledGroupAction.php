<?php

namespace App\Models\Cadence;

use App\Models\Sales\Task;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Actions triggered by CompanyCadenceScheduledGroup. Templated by CadenceScheduledGroupAction.
 *
 * @property int $company_cadence_scheduled_group_id
 * @property bool $skip
 * @property string $status
 * @property array $resolution_notes
 * @property string $action_type
 * @property int|null $task_template_id
 * @property int|null $sms_template_id
 * @property int|null $email_template_id
 * @property string|null $preview
 * @property int|null $task_id
 *
 * @property-read CompanyCadenceScheduledGroup $group
 * @property-read CadenceEmailTemplate $emailTemplate
 * @property-read CadenceSmsTemplate $smsTemplate
 * @property-read CadenceTaskTemplate $taskTemplate
 * @property-read Task $task
 */
class CompanyCadenceScheduledGroupAction extends BaseModel
{
    const TABLE = 'company_cadence_scheduled_group_actions';

    const FIELD_COMPANY_CADENCE_SCHEDULED_GROUP_ID = 'company_cadence_scheduled_group_id';
    const FIELD_SKIP                               = 'skip';
    const FIELD_STATUS                             = 'status';
    const FIELD_RESOLUTION_NOTES                   = 'resolution_notes';
    const FIELD_ACTION_TYPE                        = 'action_type';
    const FIELD_TASK_TEMPLATE_ID                   = 'task_template_id';
    const FIELD_SMS_TEMPLATE_ID                    = 'sms_template_id';
    const FIELD_EMAIL_TEMPLATE_ID                  = 'email_template_id';
    const FIELD_PREVIEW                            = 'preview';
    const FIELD_TASK_ID                            = 'task_id';

    const RELATION_GROUP          = 'group';
    const RELATION_EMAIL_TEMPLATE = 'emailTemplate';
    const RELATION_SMS_TEMPLATE   = 'smsTemplate';
    const RELATION_TASK_TEMPLATE  = 'taskTemplate';
    const RELATION_TASK           = 'task';

    const STATUS_PENDING   = 'pending';
    const STATUS_CONCLUDED = 'concluded';

    const ACTION_TYPE_TASK  = CadenceScheduledGroupAction::ACTION_TYPE_TASK;
    const ACTION_TYPE_SMS   = CadenceScheduledGroupAction::ACTION_TYPE_SMS;
    const ACTION_TYPE_EMAIL = CadenceScheduledGroupAction::ACTION_TYPE_EMAIL;

    protected $table = self::TABLE;

    protected $casts = [self::FIELD_RESOLUTION_NOTES => 'array'];

    /**
     * @return BelongsTo
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(CompanyCadenceScheduledGroup::class, self::FIELD_COMPANY_CADENCE_SCHEDULED_GROUP_ID);
    }

    /**
     * @return BelongsTo
     */
    public function emailTemplate(): BelongsTo
    {
        return $this->belongsTo(CadenceEmailTemplate::class, self::FIELD_EMAIL_TEMPLATE_ID)->withTrashed();
    }

    /**
     * @return BelongsTo
     */
    public function smsTemplate(): BelongsTo
    {
        return $this->belongsTo(CadenceSmsTemplate::class, self::FIELD_SMS_TEMPLATE_ID)->withTrashed();
    }

    /**
     * @return BelongsTo
     */
    public function taskTemplate(): BelongsTo
    {
        return $this->belongsTo(CadenceTaskTemplate::class, self::FIELD_TASK_TEMPLATE_ID)->withTrashed();
    }

    /**
     * @param string $note
     * @param array $data
     * @return void
     */
    public function addResolutionNote(string $note, array $data = []): void
    {
        if ($this->resolution_notes === null)
            $this->resolution_notes = [];
        $notes                  = $this->resolution_notes;
        $notes[]                = [
            'message' => $note,
            'data'    => $data
        ];
        $this->resolution_notes = $notes;
        $this->save();
        $this->group->addResolutionNote($note);
    }

    /**
     * @return bool
     */
    public function isDeliveryIssueAlertTask(): bool
    {
        return $this->action_type === self::ACTION_TYPE_TASK && $this->task_template_id === null;
    }

    /**
     * @return BelongsTo
     */
    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }
}
