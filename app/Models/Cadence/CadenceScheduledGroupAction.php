<?php

namespace App\Models\Cadence;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 *
 * Template used to create CompanyScheduledGroupAction's
 *
 * @property int $cadence_scheduled_group_id
 * @property string $action_type
 * @property int|null $task_template_id
 * @property int|null $sms_template_id
 * @property int|null $email_template_id
 *
 * @property-read CadenceEmailTemplate $cadenceEmailTemplate
 * @property-read CadenceSmsTemplate $cadenceSmsTemplate
 * @property-read CadenceTaskTemplate $cadenceTaskTemplate
 */
class CadenceScheduledGroupAction extends BaseModel
{
    const TABLE = 'cadence_scheduled_group_actions';

    const FIELD_CADENCE_SCHEDULED_GROUP_ID = 'cadence_scheduled_group_id';
    const FIELD_ACTION_TYPE                = 'action_type';
    const FIELD_TASK_TEMPLATE_ID           = 'task_template_id';
    const FIELD_SMS_TEMPLATE_ID            = 'sms_template_id';
    const FIELD_EMAIL_TEMPLATE_ID          = 'email_template_id';

    const RELATION_EMAIL_TEMPLATE = 'cadenceEmailTemplate';
    const RELATION_SMS_TEMPLATE   = 'cadenceSmsTemplate';
    const RELATION_TASK_TEMPLATE  = 'cadenceTaskTemplate';

    const ACTION_TYPE_TASK  = 'task';
    const ACTION_TYPE_SMS   = 'sms';
    const ACTION_TYPE_EMAIL = 'email';

    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function cadenceEmailTemplate(): BelongsTo
    {
        return $this->belongsTo(CadenceEmailTemplate::class, self::FIELD_EMAIL_TEMPLATE_ID);
    }

    /**
     * @return BelongsTo
     */
    public function cadenceSmsTemplate(): BelongsTo
    {
        return $this->belongsTo(CadenceSmsTemplate::class, self::FIELD_SMS_TEMPLATE_ID);
    }

    /**
     * @return BelongsTo
     */
    public function cadenceTaskTemplate(): BelongsTo
    {
        return $this->belongsTo(CadenceTaskTemplate::class, self::FIELD_TASK_TEMPLATE_ID);
    }
}
