<?php

namespace App\Models\Cadence;

use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property string $name
 * @property string $body
 * @property int $user_id
 * @property bool $global
 *
 * @property-read User $user
 */
class CadenceSmsTemplate extends BaseModel
{
    use SoftDeletes;

    const TABLE = 'cadence_sms_templates';

    const FIELD_NAME        = 'name';
    const FIELD_BODY        = 'body';
    const FIELD_USER_ID     = 'user_id';
    const FIELD_GLOBAL      = 'global';

    const RELATIONSHIP_USER = 'user';

    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
