<?php

namespace App\Models;

use App\Models\Legacy\Location;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property string $industry
 * @property int $type
 * @property int|null $location_id
 * @property bool $enabled
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Location|null $location
 */
class SalesBaitConfiguration extends BaseModel
{
    const TABLE = 'sales_bait_configurations';

    const FIELD_ID          = 'id';
    const FIELD_INDUSTRY    = 'industry';
    const FIELD_TYPE        = 'type';
    const FIELD_LOCATION_ID = 'location_id';
    const FIELD_ENABLED     = 'enabled';

    const TYPE_NATIONAL = 0;
    const TYPE_STATE    = 1;
    const TYPE_COUNTY   = 2;

    const INDUSTRY_SOLAR   = 'solar';
    const INDUSTRY_ROOFING = 'roofing';

    const RELATION_LOCATION = 'location';

    protected $guarded = [self::FIELD_ID];
    protected $table   = self::TABLE;

    protected $casts = [
        self::FIELD_ENABLED => 'boolean'
    ];

    /**
     * Defines relationship to location.
     *
     * @return BelongsTo
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class, self::FIELD_LOCATION_ID, Location::ID);
    }


}
