<?php

namespace App\Models\Mailbox;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int id
 * @property int user_id
 * @property int history_id
 * @property Carbon created_at
 * @property Carbon updated_at
 */
class MailboxUserEmailHistory extends Model
{
    const TABLE             = 'mailbox_user_email_histories';

    const FIELD_ID          = 'id';
    const FIELD_USER_ID     = 'user_id';
    const FIELD_HISTORY_ID  = 'history_id';
    const FIELD_CREATED_AT  = 'created_at';
    const FIELD_UPDATED_AT  = 'updated_at';

    protected $fillable = [
        self::FIELD_USER_ID,
        self::FIELD_HISTORY_ID,
        self::FIELD_CREATED_AT,
        self::FIELD_UPDATED_AT,
    ];
}
