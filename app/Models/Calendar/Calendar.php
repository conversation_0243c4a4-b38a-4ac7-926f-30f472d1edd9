<?php

namespace App\Models\Calendar;

use App\Enums\Calendar\CalendarProviderType;
use App\Models\BaseModel;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $user_id
 * @property string $external_id
 * @property CalendarProviderType $provider
 * @property string $name
 * @property string $timezone
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 *
 * @property-read User $user
 */
class Calendar extends BaseModel
{
    use SoftDeletes, HasFactory;

    const string TABLE = 'calendars';

    const string FIELD_ID          = 'id';
    const string FIELD_USER_ID     = 'user_id';
    const string FIELD_EXTERNAL_ID = 'external_id';
    const string FIELD_PROVIDER    = 'provider';
    const string FIELD_SYNC_TOKEN  = 'sync_token';
    const string FIELD_NAME        = 'name';
    const string FIELD_TIMEZONE    = 'timezone';
    const string FIELD_CREATED_AT  = 'created_at';
    const string FIELD_UPDATED_AT  = 'updated_at';
    const string FIELD_DELETED_AT  = 'deleted_at';

    const string RELATION_USER = 'user';

    protected $casts = [
        self::FIELD_PROVIDER   => CalendarProviderType::class,
    ];

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $table = self::TABLE;

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }
}
