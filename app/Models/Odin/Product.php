<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string $name
 * @property string $slug
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Industry[] $industries
 * @property-read Collection<IndustryService> $services
 * @property-read Consumer[] $consumers
 */
class Product extends BaseModel
{
    use HasFactory, SoftDeletes;

    const string TABLE = 'products';

    const string FIELD_ID   = 'id';
    const string FIELD_NAME = 'name';
    const string FIELD_SLUG = 'slug';

    const string RELATION_INDUSTRIES = 'industries';
    const string RELATION_SERVICES   = 'services';
    const string RELATION_CONSUMERS  = 'consumers';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsToMany
     */
    public function services(): BelongsToMany
    {
        return $this->belongsToMany(IndustryService::class, ServiceProduct::TABLE, ServiceProduct::FIELD_PRODUCT_ID, ServiceProduct::FIELD_INDUSTRY_SERVICE_ID);
    }

    /**
     * @return array
     */
    public static function getAsKeyValueSelectArray(): array
    {
        $result = [];

        /**
         * @var self $product
         */
        foreach (self::all() as $product) {
            $result[$product->name] = $product->id;
        }

        return $result;
    }

    // todo: industries()
    // todo: consumers()
}
