<?php

namespace App\Models\Odin;

use App\Enums\Odin\ConsumerFieldType;
use App\Enums\Odin\SystemModule;
use App\Models\BaseModel;
use App\Models\ConsumerConfigurableFieldCategory;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property int $industry_id
 * @property string $name
 * @property string $key
 * @property int $type
 * @property boolean $show_on_profile
 * @property boolean $show_on_dashboard
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read IndustryService $service
 * @method static Builder forFeatureModule(SystemModule $module, ?string $feature = null, ?bool $isVisible = null)
 */
class ServiceConsumerField extends BaseModel
{
    const TABLE = 'service_consumer_fields';

    const FIELD_ID                  = 'id';
    const FIELD_INDUSTRY_SERVICE_ID = 'industry_service_id';
    const FIELD_NAME                = 'name';
    const FIELD_KEY                 = 'key';
    const FIELD_TYPE                = 'type';
    const FIELD_SHOW_ON_PROFILE     = 'show_on_profile';
    const FIELD_SHOW_ON_DASHBOARD   = 'show_on_dashboard';
    const FIELD_CATEGORY_ID         = 'category_id';
    const RELATION_MODULES_VISIBILITIES = 'modulesVisibilities';



    const RELATION_SERVICE = 'service';


    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    // todo: define type values

    public function scopeForFeatureModule(Builder $query, SystemModule $module, ?string $feature = null, bool $visibility = null): Builder
    {
        return $query
            ->whereHas(self::RELATION_MODULES_VISIBILITIES, function (Builder $query) use($visibility, $module, $feature) {
                $query->where(ConsumerFieldModuleVisibility::FIELD_MODULE_TYPE, $module->value);

                if (isset($feature)) {
                    $query->where(ConsumerFieldModuleVisibility::FIELD_FEATURE_TYPE, $feature);
                }

                if (isset($visibility)) {
                    $query->where(ConsumerFieldModuleVisibility::FIELD_IS_VISIBLE, $visibility);
                }
            });
    }

    /**
     * @return BelongsTo
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(IndustryService::class, self::FIELD_INDUSTRY_SERVICE_ID, IndustryService::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ConsumerConfigurableFieldCategory::class, self::FIELD_CATEGORY_ID, ConsumerConfigurableFieldCategory::FIELD_ID);
    }

    public function modulesVisibilities(): HasMany
    {
        return $this
            ->hasMany(
                ConsumerFieldModuleVisibility::class,
                ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_ID,
                self::FIELD_ID,
            )
            ->where(ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_CATEGORY, ConsumerFieldType::INDUSTRY->value);
    }
}
