<?php

namespace App\Models\Odin;

use App\Enums\ConsumerProductChannel;
use App\Enums\MarketingStrategyType;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\PropertyType;
use App\Events\ConsumerProductSaved;
use App\Models\Affiliates\Payout;
use App\Models\AppointmentProcessingAllocation;
use App\Models\BaseModel;
use App\Models\ConsumerProcessingActivity;
use App\Models\LeadProcessingPendingReview;
use App\Models\LeadProcessingReservedLead;
use App\Models\LeadProcessingUnderReview;
use App\Models\MarketingCampaignConsumer;
use App\Models\PingPostAffiliates\PingPostAffiliate;
use App\Models\PingPostAffiliates\PingPostAffiliateLead;
use App\Models\RecycledLeads\RecycledLead;
use App\Models\TestProduct;
use App\Transformers\Odin\v2\EloquentQuoteTransformer;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Support\Collection;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property int $id
 * @property int $service_product_id
 * @property int $consumer_id
 * @property int $address_id
 * @property boolean $good_to_sell
 * @property int $consumer_product_data_id
 * @property int $status
 * @property int $contact_requests
 * @property int $consumer_product_tcpa_record_id
 * @property int $consumer_product_tracking_id
 * @property int $consumer_product_affiliate_record_id
 * @property int $property_type_id
 * @property string $own_property
 * @property string $ip_address
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property int|null $cloned_from_id
 * @property bool $is_secondary_service
 * @property ConsumerProductChannel $channel
 *
 * @property-read ServiceProduct $serviceProduct
 * @property-read Consumer $consumer
 * @property-read Collection<ProductAssignment> $productAssignment
 * @property-read Address $address
 * @property-read ConsumerProductData $consumerProductData
 * @property-read ConsumerProductTcpaRecord|null $consumerProductTcpaRecord
 * @property-read ConsumerProductTracking|null $consumerProductTracking
 * @property-read ConsumerProductAffiliateRecord|null $consumerProductAffiliateRecord
 * @property-read LeadProcessingPendingReview|null $pendingReview
 * @property-read LeadProcessingUnderReview|null $underReview
 * @property-read IndustryService $industryService
 * @property-read Collection<ProductAppointment> $leadAppointments
 * @property-read ProductAppointment $appointment
 * @property-read PropertyType $propertyType
 * @property-read AppointmentProcessingAllocation[] $appointmentProcessingAllocations
 * @property-read TestProduct|null $testProduct
 * @property-read Collection<OptInCompany> $optInCompanies
 * @property-read self|null $clonedFrom
 * @property-read Collection<self> $clones
 * @property-read Collection|RecycledLead[] $recycledLeads
 * @property-read Payout|null $affiliatePayout
 * @property-read PingPostAffiliate|null $pingPostAffiliate
 * @property-read MarketingCampaignConsumer|null $fromMarketingCampaignConsumer
 * @property-read Collection<ConsumerProcessingActivity> $processingActivities
 */
class ConsumerProduct extends BaseModel
{
    use HasFactory, LogsActivity;

    const string TABLE = 'consumer_products';

    const string FIELD_ID                                   = 'id';
    const string FIELD_CONSUMER_ID                          = 'consumer_id';
    const string FIELD_SERVICE_PRODUCT_ID                   = 'service_product_id';
    const string FIELD_ADDRESS_ID                           = 'address_id';
    const string FIELD_GOOD_TO_SELL                         = 'good_to_sell';
    const string FIELD_STATUS                               = 'status';
    const string FIELD_CONTACT_REQUESTS                     = 'contact_requests';
    const string FIELD_CREATED_AT                           = 'created_at';
    const string FIELD_UPDATED_AT                           = 'updated_at';
    const string FIELD_CONSUMER_PRODUCT_DATA_ID             = 'consumer_product_data_id';
    const string FIELD_CONSUMER_PRODUCT_TCPA_RECORD_ID      = 'consumer_product_tcpa_record_id';
    const string FIELD_CONSUMER_PRODUCT_TRACKING_ID         = 'consumer_product_tracking_id';
    const string FIELD_CONSUMER_PRODUCT_AFFILIATE_RECORD_ID = 'consumer_product_affiliate_record_id';
    const string FIELD_PROPERTY_TYPE_ID                     = 'property_type_id';
    const string FIELD_IP_ADDRESS                           = 'ip_address';
    const string FIELD_CLONED_FROM_ID                       = 'cloned_from_id';
    const string FIELD_IS_SECONDARY_SERVICE                 = 'is_secondary_service';
    const string FIELD_CHANNEL                              = 'channel';

    const string ATTRIBUTE_OWN_PROPERTY                     =  'own_property';

    const string RELATION_SERVICE_PRODUCT                    = 'serviceProduct';
    const string RELATION_CONSUMER                           = 'consumer';
    const string RELATION_PRODUCT_ASSIGNMENT                 = 'productAssignment';
    const string RELATION_ADDRESS                            = 'address';
    const string RELATION_CONSUMER_PRODUCT_DATA              = 'consumerProductData';
    const string RELATION_CONSUMER_PRODUCT_TCPA_RECORD       = 'consumerProductTcpaRecord';
    const string RELATION_CONSUMER_PRODUCT_TRACKING          = 'consumerProductTracking';
    const string RELATION_CONSUMER_PRODUCT_AFFILIATE_RECORD  = 'consumerProductAffiliateRecord';
    const string RELATION_LEAD_APPOINTMENTS                  = 'leadAppointments';
    const string RELATION_APPOINTMENT                        = 'appointment';
    const string RELATION_INDUSTRY_SERVICE                   = 'industryService';
    const string RELATION_PROPERTY_TYPE                      = 'propertyType';
    const string RELATION_APPOINTMENT_PROCESSING_ALLOCATIONS = 'appointmentProcessingAllocations';
    const string RELATION_TEST_PRODUCT                       = 'testProduct';
    const string RELATION_UNDER_REVIEW                       = 'underReview';
    const string RELATION_PENDING_REVIEW                     = 'pendingReview';
    const string RELATION_CLONED_FROM                        = 'clonedFrom';
    const string RELATION_CLONES                             = 'clones';
    const string RELATION_RECYCLED_LEADS                     = 'recycledLeads';
    const string RELATION_RESERVED                           = 'reserved';
    const string RELATION_AFFILIATE_PAYOUT                   = 'affiliatePayout';
    const string RELATION_PING_POST_AFFILIATE                = 'pingPostAffiliate';
    const string RELATION_FROM_MARKETING_CAMPAIGN_CONSUMER   = 'fromMarketingCampaignConsumer';
    const string RELATION_PROCESSING_ACTIVITIES          = 'processingActivities';

    const string RELATION_OPT_IN_COMPANIES = 'optInCompanies';

    /**
     * @IMPORTANT:
     *      Whilst we still sync to legacy, when adding a new status we need to ensure there's
     *      a valid mapping in legacy for this new status. Please make sure to update the
     *      entry in EloquentQuoteTransformer.
     *
     * @see EloquentQuoteTransformer::getLegacyStatus()
     */
    const int STATUS_CANCELLED          = -1;
    const int STATUS_INITIAL            = 0;
    const int STATUS_PENDING_REVIEW     = 1;
    const int STATUS_UNDER_REVIEW       = 2;
    const int STATUS_ALLOCATED          = 3;
    const int STATUS_UNSOLD             = 4;
    const int STATUS_PENDING_ALLOCATION = 5;

    const int DEFAULT_CONTACT_REQUEST_FOR_MULTI_INDUSTRY_LEAD = 3;
    const int DEFAULT_CONTACT_REQUEST_FOR_SOLAR_LEAD = 4;

    const array STATUS_TEXT = [
        -1 => 'Cancelled',
        0  => 'Initial',
        1  => 'Pending Review',
        2  => 'Under Review',
        3  => 'Allocated',
        4  => 'Unsold',
        5  => 'Pending Allocation',
    ];

    const array STATUSES = [
        self::STATUS_CANCELLED,
        self::STATUS_INITIAL,
        self::STATUS_PENDING_REVIEW,
        self::STATUS_UNDER_REVIEW,
        self::STATUS_ALLOCATED,
        self::STATUS_UNSOLD,
        self::STATUS_PENDING_ALLOCATION,
    ];

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [self::FIELD_CHANNEL => ConsumerProductChannel::class];

    protected static array $recordEvents = ['updated'];
    protected $dispatchesEvents = [
        'saved' => ConsumerProductSaved::class,
    ];

    /**
     * @return BelongsTo
     */
    public function serviceProduct(): BelongsTo
    {
        return $this->belongsTo(ServiceProduct::class, self::FIELD_SERVICE_PRODUCT_ID, ServiceProduct::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function consumer(): BelongsTo
    {
        return $this->belongsTo(Consumer::class, self::FIELD_CONSUMER_ID, Consumer::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function productAssignment(): HasMany
    {
        return $this->hasMany(ProductAssignment::class, ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function address(): HasOne
    {
        return $this->HasOne(Address::class, Address::FIELD_ID, self::FIELD_ADDRESS_ID);
    }

    /**
     * @return BelongsTo
     */
    public function consumerProductData(): BelongsTo
    {
        return $this->belongsTo(ConsumerProductData::class, self::FIELD_CONSUMER_PRODUCT_DATA_ID, ConsumerProductData::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function consumerProductTcpaRecord(): BelongsTo
    {
        return $this->belongsTo(ConsumerProductTcpaRecord::class);
    }

    /**
     * @return BelongsTo
     */
    public function consumerProductTracking(): BelongsTo
    {
        return $this->belongsTo(ConsumerProductTracking::class, self::FIELD_CONSUMER_PRODUCT_TRACKING_ID, ConsumerProductTracking::ID);
    }

    /**
     * @return BelongsTo
     */
    public function consumerProductAffiliateRecord(): BelongsTo
    {
        return $this->belongsTo(ConsumerProductAffiliateRecord::class, self::FIELD_CONSUMER_PRODUCT_AFFILIATE_RECORD_ID, ConsumerProductAffiliateRecord::FIELD_ID);
    }

    /**
     * Defines the relationship with pending review.
     *
     * @return HasOne
     */
    public function pendingReview(): HasOne
    {
        return $this->hasOne(LeadProcessingPendingReview::class, LeadProcessingPendingReview::FIELD_CONSUMER_PRODUCT_ID, self::FIELD_ID);
    }

    /**
     * Defines the relationship with under review.
     *
     * @return HasOne
     */
    public function underReview(): HasOne
    {
        return $this->hasOne(LeadProcessingUnderReview::class, LeadProcessingUnderReview::FIELD_CONSUMER_PRODUCT_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function reserved(): HasOne
    {
        return $this->hasOne(LeadProcessingReservedLead::class, LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function leadAppointments(): HasMany
    {
        return $this->hasMany(ProductAppointment::class, ProductAppointment::LEAD_CONSUMER_PRODUCT_ID, self::FIELD_ID);
    }


    /**
     * @return HasOne
     */
    public function appointment(): HasOne
    {
        return $this->hasOne(ProductAppointment::class, ProductAppointment::CONSUMER_PRODUCT_ID, self::FIELD_ID);
    }

    /**
     * @return HasOneThrough
     */
    public function industryService(): HasOneThrough
    {
        return $this->hasOneThrough(
            IndustryService::class,
            ServiceProduct::class,
            ServiceProduct::FIELD_ID,
            IndustryService::FIELD_ID,
            ConsumerProduct::FIELD_SERVICE_PRODUCT_ID,
            ServiceProduct::FIELD_INDUSTRY_SERVICE_ID
        );
    }

    /**
     * @return BelongsTo
     */
    public function propertyType(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Odin\PropertyType::class, self::FIELD_PROPERTY_TYPE_ID, \App\Models\Odin\PropertyType::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function appointmentProcessingAllocations(): HasMany
    {
        return $this->hasMany(AppointmentProcessingAllocation::class, AppointmentProcessingAllocation::FIELD_LEAD_CONSUMER_PRODUCT_ID, self::FIELD_ID);
    }

    /**
     * Returns static map image of this lead.
     *
     * @return string
     */
    public function getAddressImage(): string
    {
        $params = [
            "center" => $this->address->latitude.','.$this->address->longitude ,
            "zoom" => 19,
            "size" => "650x400",
            "maptype" => "satellite",
            "key" => config('services.google.maps.static_maps_key')
        ];

        return "https://maps.googleapis.com/maps/api/staticmap?" . http_build_query($params);
    }


    /**
     * Return a bool value to indicate whether the product came from a missed lead or not
     * @return bool
     */
    public function isMissedProduct(): bool
    {
        // TODO - Check if product is a missed lead
        //  Perhaps create a table to store all missed leads purchased
        return false;
    }

    /**
     * @return HasOne
     */
    public function testProduct(): HasOne
    {
        return $this->hasOne(TestProduct::class, TestProduct::FIELD_PRODUCT_ID, self::FIELD_ID);
    }

    public function getOwnPropertyAttribute()
    {
        return $this->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}->{ConsumerProductData::FIELD_PAYLOAD}['own_property'] ?? null;
    }

    /**
     * @return bool
     */
    public function isAppointment(): bool
    {
        return $this->serviceProduct?->product?->name === ProductEnum::APPOINTMENT->value;
    }

    /**
     * @return HasMany
     */
    public function optInCompanies(): HasMany
    {
        return $this->hasMany(OptInCompany::class);
    }

    /**
     * @return MarketingStrategyType
     */
    public function marketingStrategyType(): MarketingStrategyType
    {
        if ($this->consumerProductAffiliateRecord()
            ->where(ConsumerProductAffiliateRecord::TABLE .'.'. ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID, '>', 0)
            ->where(ConsumerProductAffiliateRecord::TABLE .'.'. ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID, '>', 0)
            ->exists()) {
                return MarketingStrategyType::AFFILIATE;
        }

        if ($this->pingPostAffiliate)
            return MarketingStrategyType::PING_POST_AFFILIATE;

        $hasTracking = $this->consumerProductTracking()
            ->whereNotNull(ConsumerProductTracking::TABLE . '.' . ConsumerProductTracking::AD_TRACK_TYPE)
            ->whereNotNull(ConsumerProductTracking::TABLE . '.' . ConsumerProductTracking::AD_TRACK_CODE)
            ->exists();

        return $hasTracking ? MarketingStrategyType::PAID : MarketingStrategyType::ORGANIC;
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                self::FIELD_STATUS
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Checks to see if lead is locked by "system"
     * @return bool
     */
    public function isLeadReservedBySystem(): bool
    {
        return LeadProcessingReservedLead::query()
                ->where(LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID, $this->{ConsumerProduct::FIELD_ID})
                ->where(LeadProcessingReservedLead::FIELD_PROCESSOR_ID, LeadProcessingReservedLead::SYSTEM_ID)
                ->first() !== null;
    }

    /**
     * @return BelongsTo
     */
    public function clonedFrom(): BelongsTo
    {
        return $this->belongsTo(self::class);
    }

    /**
     * @return HasMany
     */
    public function clones(): HasMany
    {
        return $this->hasMany(
            self::class,
            self::FIELD_CLONED_FROM_ID,
            self::FIELD_ID
        );
    }

    /**
     * @return HasMany
     */
    public function recycledLeads(): HasMany
    {
        return $this->hasMany(RecycledLead::class);
    }

    /**
     * @return string
     */
    public function getAffiliateStatus(): string
    {
        return match($this->{ConsumerProduct::FIELD_STATUS}) {
            ConsumerProduct::STATUS_INITIAL, ConsumerProduct::STATUS_UNSOLD, ConsumerProduct::STATUS_PENDING_ALLOCATION, ConsumerProduct::STATUS_UNDER_REVIEW, ConsumerProduct::STATUS_PENDING_REVIEW => "Unsold",
            ConsumerProduct::STATUS_ALLOCATED => "Sold",
            ConsumerProduct::STATUS_CANCELLED => "Cancelled"
        };
    }

    /**
     * @return HasOne
     */
    public function affiliatePayout(): HasOne
    {
        return $this->hasOne(Payout::class, Payout::FIELD_CONSUMER_PRODUCT_ID, self::FIELD_ID);
    }

    /**
     * @return HasOneThrough
     */
    public function pingPostAffiliate(): HasOneThrough
    {
        return $this->hasOneThrough(
            PingPostAffiliate::class,
            PingPostAffiliateLead::class,
            PingPostAffiliateLead::FIELD_CONSUMER_PRODUCT_ID,
            PingPostAffiliate::FIELD_ID,
            ConsumerProduct::FIELD_ID,
            PingPostAffiliateLead::FIELD_PING_POST_AFFILIATE_ID,
        );
    }

    /**
     * @return HasOne
     */
    public function fromMarketingCampaignConsumer(): HasOne
    {
        return $this->hasOne(MarketingCampaignConsumer::class, MarketingCampaignConsumer::FIELD_CLONED_CONSUMER_PRODUCT_ID, self::FIELD_ID);
    }

    /**
     * For now, this returns all comments attached to the parent Consumer
     * consumer_processing_activities also has consumer_product_id,
     *   so we can filter or sort to the specific product if ever needed
     *
     * @return HasMany
     */
    public function processingActivities(): HasMany
    {
        return $this->hasMany(ConsumerProcessingActivity::class, ConsumerProcessingActivity::FIELD_CONSUMER_ID, self::FIELD_CONSUMER_ID);
    }
}
