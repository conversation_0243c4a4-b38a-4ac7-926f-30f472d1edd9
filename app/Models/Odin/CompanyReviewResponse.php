<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $review_id
 * @property int $user_id
 * @property string $body
 * @property string $first_name
 * @property string $last_name
 * @property string $email
 * @property string $ip_address
 * @property int $status
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read User $user
 * @property-read CompanyReview $review
 */
class CompanyReviewResponse extends BaseModel
{
    use HasFactory;

    const TABLE = 'company_review_responses';

    const FIELD_ID         = 'id';
    const FIELD_REVIEW_ID  = 'review_id';
    const FIELD_USER_ID    = 'user_id';
    const FIELD_BODY       = 'body';
    const FIELD_FIRST_NAME = 'first_name';
    const FIELD_LAST_NAME  = 'last_name';
    const FIELD_EMAIL      = 'email';
    const FIELD_IP_ADDRESS = 'ip_address';
    const FIELD_STATUS     = 'status';

    const RELATION_USER = 'user';
    const RELATION_REVIEW = 'review';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function review(): BelongsTo
    {
        return $this->belongsTo(CompanyReview::class, self::FIELD_REVIEW_ID, CompanyReview::FIELD_ID);
    }
}
