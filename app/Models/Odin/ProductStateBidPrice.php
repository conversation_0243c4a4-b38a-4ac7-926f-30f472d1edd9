<?php

namespace App\Models\Odin;

use App\Models\Legacy\Location;
use App\Models\SaleType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property-read int $id
 * @property-read int $state_location_id
 * @property-read int $product_campaign_id
 * @property-read int $service_product_id
 * @property-read int $sale_type_id
 * @property-read int $quality_tier_id
 * @property-read int $property_type_id
 * @property-read float $price
 *
 * @property-read ServiceProduct $serviceProduct
 * @property-read Location $location
 * @property-read QualityTier $qualityTier
 * @property-read SaleType $saleType
 * @property-read PropertyType $propertyType
 * @property-read Location $stateLocation
 */
class ProductStateBidPrice extends Model
{
    use HasFactory, LogsActivity;

    const TABLE = 'product_state_bid_prices';

    const FIELD_ID = 'id';
    const FIELD_STATE_LOCATION_ID = 'state_location_id';
    const FIELD_PRODUCT_CAMPAIGN_ID = 'product_campaign_id';
    const FIELD_SERVICE_PRODUCT_ID = 'service_product_id';
    const FIELD_SALE_TYPE_ID = 'sale_type_id';
    const FIELD_QUALITY_TIER_ID = 'quality_tier_id';
    const FIELD_PROPERTY_TYPE_ID = 'property_type_id';
    const FIELD_PRICE = 'price';
    const FIELD_MODULE_ID = 'module_id';

    const RELATION_SERVICE_PRODUCT = 'serviceProduct';
    const RELATION_LOCATION = 'location';
    const RELATION_QUALITY_TIER = 'qualityTier';
    const RELATION_SALE_TYPE = 'saleType';
    const RELATION_PROPERTY_TYPE = 'propertyType';
    const RELATION_STATE_LOCATION = 'stateLocation';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return BelongsTo
     */
    public function serviceProduct(): BelongsTo
    {
        return $this->belongsTo(ServiceProduct::class, self::FIELD_SERVICE_PRODUCT_ID, ServiceProduct::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class, self::FIELD_STATE_LOCATION_ID, Location::ID);
    }

    /**
     * @return BelongsTo
     */
    public function stateLocation(): BelongsTo
    {
        return $this->belongsTo(Location::class, self::FIELD_STATE_LOCATION_ID, Location::ID);
    }

    /**
     * @return BelongsTo
     */
    public function qualityTier(): BelongsTo
    {
        return $this->belongsTo(QualityTier::class, self::FIELD_QUALITY_TIER_ID, QualityTier::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function saleType(): BelongsTo
    {
        return $this->belongsTo(SaleType::class, self::FIELD_SALE_TYPE_ID, SaleType::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function propertyType(): BelongsTo
    {
        return $this->belongsTo(PropertyType::class, self::FIELD_PROPERTY_TYPE_ID, PropertyType::FIELD_ID);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([self::FIELD_PRICE])
            ->useLogName('campaign_state_bid_price')
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
