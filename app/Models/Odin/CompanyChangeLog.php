<?php

namespace App\Models\Odin;

use App\Enums\Odin\CompanyChangeLogType;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CompanyChangeLog extends BaseModel
{
    use HasFactory;
    const TABLE = 'company_change_logs';

    const FIELD_ID = 'id';
    const FIELD_COMPANY_ID = 'company_id';
    const FIELD_LOG = 'log';
    const FIELD_PAYLOAD = 'payload';

    const RELATION_COMPANY = 'company';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_PAYLOAD => 'array',
        self::FIELD_LOG => CompanyChangeLogType::class
    ];

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }
}
