<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $company_review_id
 * @property string $payload
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read CompanyUser $user
 */
class CompanyReviewData extends BaseModel
{
    use HasFactory;

    const TABLE = 'company_review_data';

    const FIELD_ID                = 'id';
    const FIELD_COMPANY_REVIEW_ID = 'company_review_id';
    const FIELD_PAYLOAD           = 'payload';

    const RELATION_REVIEW = 'review';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_PAYLOAD => 'array'
    ];

    /**
     * @return BelongsTo
     */
    public function review(): BelongsTo
    {
        return $this->belongsTo(CompanyReview::class, self::FIELD_COMPANY_REVIEW_ID, CompanyReview::FIELD_ID);
    }
}
