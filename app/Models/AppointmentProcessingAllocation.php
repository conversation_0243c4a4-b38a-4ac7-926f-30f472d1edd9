<?php

namespace App\Models;

use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductAssignment;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class AppointmentProcessingAllocation extends BaseModel
{
    use HasFactory, SoftDeletes;

    const TABLE = 'appointment_processing_allocations';

    const FIELD_ID = 'id';
    const FIELD_LEAD_CONSUMER_PRODUCT_ID = 'lead_consumer_product_id';
    const FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const FIELD_LEAD_PROCESSOR_ID = 'lead_processor_id';
    const FIELD_PROCESSING_SCENARIO = 'processing_scenario';
    const FIELD_ALLOCATED = 'allocated';
    const FIELD_DELIVERED = 'delivered';
    const FIELD_ALLOCATE_AT = 'allocate_at';
    const FIELD_FAILED_APPT_ALLOCATION = 'failed_appt_allocation';
    const FIELD_CANCELLED_REJECTED = 'cancelled_rejected';
    const FIELD_ERROR = 'error';
    const FIELD_ALLOCATED_AS_LEAD = 'allocated_as_lead';
    const FIELD_RAN_LEAD_ALLOCATION = 'ran_lead_allocation';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';
    const FIELD_DELETED_AT = 'deleted_at';

    const RELATION_CONSUMER_PRODUCT = 'consumerProduct';
    const RELATION_PRODUCT_APPOINTMENT = 'productAppointment';
    const RELATION_LEAD_PROCESSOR = 'leadProcessor';
    const RELATION_LEAD_PRODUCT_ASSIGNMENTS = 'leadProductAssignments';
    const RELATION_LEAD_CONSUMER_PRODUCT = 'leadConsumerProduct';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [self::FIELD_ALLOCATE_AT => 'datetime'];

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function productAppointment(): BelongsTo
    {
        return $this->belongsTo(ProductAppointment::class, self::FIELD_CONSUMER_PRODUCT_ID, ProductAppointment::CONSUMER_PRODUCT_ID);
    }

    /**
     * @return BelongsTo
     */
    public function leadProcessor(): BelongsTo
    {
        return $this->belongsTo(LeadProcessor::class, self::FIELD_LEAD_PROCESSOR_ID, LeadProcessor::FIELD_ID)->withTrashed();
    }

    /**
     * @return HasMany
     */
    public function leadProductAssignments(): HasMany
    {
        return $this->hasMany(ProductAssignment::class, ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, self::FIELD_LEAD_CONSUMER_PRODUCT_ID);
    }

    /**
     * @return BelongsTo
     */
    public function leadConsumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::FIELD_LEAD_CONSUMER_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }
}
