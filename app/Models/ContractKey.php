<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property string $key
 * @property string $name
 *
 * @property-read Contract $contract
 */
class ContractKey extends BaseModel
{
    const string TABLE = 'contract_keys';

    const string FIELD_ID      = 'id';
    const string FIELD_KEY     = 'key';
    const string FIELD_NAME    = 'name';

    const string RELATION_CONTRACT = 'contract';

    /**
     * @return HasMany
     */
    public function contract(): HasMany
    {
        return $this->hasMany(Contract::class, Contract::FIELD_CONTRACT_KEY_ID, self::FIELD_ID);
    }

}