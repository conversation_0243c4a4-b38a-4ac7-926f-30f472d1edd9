<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TieredAdvertisingConfiguration extends Model
{
    const string TABLE = 'tiered_advertising_configurations';

    const string FIELD_ID = 'id';
    const string FIELD_INDUSTRY_ID = 'industry_id';
    const string FIELD_PLATFORM = 'platform';
    const string FIELD_ENABLED = 'enabled';
    const string FIELD_LAST_LOCATION_UPDATE = 'last_location_update';
    const string FIELD_UPDATE_FREQUENCY_MINUTES = 'update_frequency_minutes';
    const string FIELD_ROAS = 'roas';
    const string FIELD_CONFIGS = 'configs';
    const string FIELD_INSTANCE_ID = 'instance_id';
    const string FIELD_ADVERTISER = 'advertiser';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_CONFIGS => 'array',
    ];
}
