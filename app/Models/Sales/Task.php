<?php

namespace App\Models\Sales;

use App\Database\Casts\AsTaskResultPayload;
use App\DataModels\Workflows\TaskResultDataModel;
use App\Enums\SupportedTimezones;
use App\Models\ActivityFeed;
use App\Models\Cadence\CompanyCadenceScheduledGroupAction;
use App\Models\CompletedWorkflow;
use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Models\RunningWorkflow;
use App\Models\TaskCategory;
use App\Models\TaskNote;
use App\Models\TaskType;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property int $assigned_user_id
 * @property int $running_workflow_id
 * @property int $reschedule_count
 * @property Carbon $available_at
 * @property Carbon $completed_at
 * @property int $task_type_id
 * @property string $subject
 * @property Collection<TaskResultDataModel> $results
 * @property int $priority
 * @property bool $completed
 * @property bool $allowsRescheduling
 * @property bool $manual
 * @property array $payload
 * @property bool $hasOdinId
 * @property SupportedTimezones $timezone
 * @property int|null $county_location_id
 * @property int|null $completed_by_user_id
 * @property int|null $completed_by_impersonator_user_id
 *
 * @property int|null $manual_company_id
 * @property int $task_category_id
 *
 * @property boolean $dynamic_priority
 * @property int|null $dynamic_priority_type
 *
 * @property-read RunningWorkflow $runningWorkflow
 * @property-read CompletedWorkflow $completedWorkflow
 * @property-read TaskType $taskType
 * @property-read Collection<TaskNote> $taskNotes
 * @property-read TaskCategory $taskCategory
 * @property-read User $assignedUser
 * @property-read CompanyCadenceScheduledGroupAction $companyCadenceAction
 * @property-read Company $company
 */
class Task extends Model
{
    use HasFactory;

    const TABLE = 'tasks';

    const FIELD_ID                                = 'id';
    const FIELD_ASSIGNED_USER_ID                  = 'assigned_user_id';
    const FIELD_RUNNING_WORKFLOW_ID               = 'running_workflow_id';
    const FIELD_AVAILABLE_AT                      = 'available_at';
    const FIELD_TASK_TYPE_ID                      = 'task_type_id';
    const FIELD_SUBJECT                           = 'subject';
    const FIELD_RESULTS                           = 'results';
    const FIELD_CREATED_AT                        = 'created_at';
    const FIELD_UPDATED_AT                        = 'updated_at';
    const FIELD_COMPLETED_AT                      = 'completed_at';
    const FIELD_PRIORITY                          = 'priority';
    const FIELD_COMPLETED                         = 'completed';
    const FIELD_ALLOWS_RESCHEDULING               = 'allows_rescheduling';
    const FIELD_RESCHEDULE_COUNT                  = 'reschedule_count';
    const FIELD_MANUAL                            = 'manual';
    const FIELD_PAYLOAD                           = 'payload';
    const FIELD_TASK_CATEGORY_ID                  = 'task_category_id';
    const FIELD_USES_ODIN_ID                      = 'uses_odin_id';
    const FIELD_COMPLETED_BY_USER_ID              = 'completed_by_user_id';
    const FIELD_COMPLETED_BY_IMPERSONATOR_USER_ID = 'completed_by_impersonator_user_id';
    const FIELD_MUTED                             = 'muted';

    const FIELD_DYNAMIC_PRIORITY      = 'dynamic_priority';
    const FIELD_DYNAMIC_PRIORITY_TYPE = 'dynamic_priority_type';
    const FIELD_TIMEZONE              = 'timezone';
    const FIELD_COUNTY_LOCATION_ID    = 'county_location_id';

    const VIRTUAL_FIELD_MANUAL_COMPANY_ID = 'manual_company_id';

    const RELATION_ASSIGNED_USER          = 'assignedUser';
    const RELATION_RUNNING_WORKFLOW       = 'runningWorkflow';
    const RELATION_TASK_TYPE              = 'taskType';
    const RELATION_TASK_NOTES             = 'taskNotes';
    const RELATION_COMPLETED_WORKFLOW     = "completedWorkflow";
    const RELATION_COMPANY_CADENCE_ACTION = 'companyCadenceAction';

    const RELATION_TASK_CATEGORY = "taskCategory";
    const RELATION_COMPANY       = "company";

    const TASK_COMPLETED     = 1;
    const TASK_NOT_COMPLETED = 0;
    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_AVAILABLE_AT => 'datetime',
        self::FIELD_COMPLETED_AT => 'datetime',
        self::FIELD_RESULTS      => AsTaskResultPayload::class,
        self::FIELD_PAYLOAD      => 'array',
        self::FIELD_MANUAL       => 'boolean',
        self::FIELD_TIMEZONE     => SupportedTimezones::class,
    ];

    /**
     * @return BelongsTo
     */
    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_ASSIGNED_USER_ID, User::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::VIRTUAL_FIELD_MANUAL_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function runningWorkflow(): HasOne
    {
        return $this->hasOne(RunningWorkflow::class, RunningWorkflow::FIELD_ID, self::FIELD_RUNNING_WORKFLOW_ID);
    }

    /**
     * Defines relationship to completed workflow.
     *
     * @return HasOne
     */
    public function completedWorkflow(): HasOne
    {
        return $this->hasOne(CompletedWorkflow::class, CompletedWorkflow::FIELD_RUNNING_WORKFLOW_ID, self::FIELD_RUNNING_WORKFLOW_ID);
    }

    public function taskType(): HasOne
    {
        return $this->hasOne(TaskType::class, TaskType::FIELD_ID, self::FIELD_TASK_TYPE_ID);
    }

    /**
     * @return HasMany
     */
    public function taskNotes(): HasMany
    {
        return $this->hasMany(TaskNote::class, TaskNote::FIELD_TASK_ID, self::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function taskCategory(): BelongsTo
    {
        return $this->belongsTo(TaskCategory::class);
    }

    /**
     * @return MorphOne
     */
    public function activity(): MorphOne
    {
        return $this->morphOne(ActivityFeed::class, 'item');
    }

    /**
     * @return HasOne
     */
    public function companyCadenceAction(): HasOne
    {
        return $this->hasOne(CompanyCadenceScheduledGroupAction::class, CompanyCadenceScheduledGroupAction::FIELD_TASK_ID);
    }
}
