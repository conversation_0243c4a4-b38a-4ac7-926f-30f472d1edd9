<?php

namespace App\Enums;

use App\Enums\Odin\Industry;
use App\Enums\Odin\Product;

enum RejectionReasons: string
{
    case BAD_PHONE                = 'ph';
    case HEAVY_SHADE              = 'sh';
    case NOT_HOME_OWNER           = 'no';
    case SCHOOL_PROJECT           = 'sc';
    case DUPLICATE                = 'du';
    case ALREADY_HAS_SOLAR        = 'ah';
    case NO_SHOW                  = 'ns';
    case OTHER                    = 'ot';
    case ALREADY_KNOWN            = "ak";
    case BAD_EMAIL                = "em";
    case BAD_PHONE_OR_EMAIL       = "bp";
    case COULD_NO_CONTACT         = "nc";
    case DUPLICATE_GENERIC        = "dug";
    case NOT_INTERESTED           = "ni";
    case OUTSIDE_OF_SERVICE_AREAS = "oa";
    case SELF_INSTALL             = "si";
    case SPAM                     = "sp";
    case TIME_WASTER              = "tw";
    case REPAIR_ONLY              = "ro";

    /**
     * @return string[]
     */
    public static function getSolarReasons(Product $product = Product::LEAD): array
    {
        if ($product === Product::LEAD) {
            return [
                ...self::getDefaultReasons($product),
                self::HEAVY_SHADE->value       => self::HEAVY_SHADE->getText(),
                self::SCHOOL_PROJECT->value    => self::SCHOOL_PROJECT->getText(),
                self::ALREADY_HAS_SOLAR->value => self::ALREADY_HAS_SOLAR->getText(),
            ];
        } else if ($product === Product::APPOINTMENT)
            return self::getDefaultReasons($product);

        return [];
    }

    /**
     * @return string[]
     */
    public static function getDefaultReasons(Product $product = Product::LEAD): array
    {
        if ($product === Product::LEAD) {
            return [
                self::BAD_PHONE->value      => self::BAD_PHONE->getText(),
                self::NOT_HOME_OWNER->value => self::NOT_HOME_OWNER->getText(),
                self::DUPLICATE->value      => self::DUPLICATE->getText(),
                self::OTHER->value          => self::OTHER->getText(),
            ];
        } else if ($product === Product::APPOINTMENT) {
            return [
                self::NO_SHOW->value => self::NO_SHOW->getText(),
                self::OTHER->value   => self::OTHER->getText(),
            ];
        }

        return [];
    }

    /**
     * @param ?Industry $industry
     * @param Product $product
     * @return string[]
     */
    public static function getIndustryReasons(?Industry $industry = null, Product $product = Product::LEAD): array
    {
        return match ($industry) {
            Industry::SOLAR => self::getSolarReasons($product),
            default         => self::getDefaultReasons($product),
        };
    }

    public function getText(): string
    {
        return match ($this) {
            self::BAD_PHONE                          => "Bad Phone",
            self::HEAVY_SHADE                        => "Heavy shade (no possibility of installing system bigger than 2kw)",
            self::NOT_HOME_OWNER                     => "Renter or Not Home Owner",
            self::SCHOOL_PROJECT                     => "School Project",
            self::DUPLICATE, self::DUPLICATE_GENERIC => "Duplicate of another lead in the last 3 months",
            self::ALREADY_HAS_SOLAR                  => "Already has solar",
            self::NO_SHOW                            => "No Show",
            self::ALREADY_KNOWN                      => "Already Known",
            self::BAD_EMAIL                          => "Bad Email",
            self::BAD_PHONE_OR_EMAIL                 => "Bad Phone or Email",
            self::COULD_NO_CONTACT                   => "Could Not Contact",
            self::NOT_INTERESTED                     => "Not Interested",
            self::OUTSIDE_OF_SERVICE_AREAS           => "Outside of Service Areas",
            self::SELF_INSTALL                       => "Wants to Self Install",
            self::SPAM                               => "Spam",
            self::TIME_WASTER                        => "Time Waster",
            self::REPAIR_ONLY                        => "Repair only",
            default                                  => 'Other',
        };
    }
}
