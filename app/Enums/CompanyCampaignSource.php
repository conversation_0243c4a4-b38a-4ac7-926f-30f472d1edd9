<?php

namespace App\Enums;

use Illuminate\Support\Collection;

enum CompanyCampaignSource: string
{
    case PRODUCT_CAMPAIGN = 'product_campaign'; // A20
    case COMPANY_CAMPAIGN = 'company_campaign'; // Future campaigns
    case LEAD_CAMPAIGN    = 'lead_campaign';    // Legacy

    public static function getValues(): Collection
    {
        return collect(self::cases())->map(fn($c) => $c->value);
    }

    public function getDescription(): string
    {
        return match($this) {
            self::PRODUCT_CAMPAIGN  => 'A20',
            self::COMPANY_CAMPAIGN  => 'Future',
            self::LEAD_CAMPAIGN     => 'Legacy',
            default                 => '',
        };
    }
}
