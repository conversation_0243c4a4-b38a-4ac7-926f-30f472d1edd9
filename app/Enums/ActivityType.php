<?php

namespace App\Enums;

enum ActivityType: string
{
    case ALL           = 'all';
    case ACTION        = 'action';
    case CALL          = 'call';
    case TEXT          = 'text';
    case EMAIL         = 'email';
    case MAILBOX_EMAIL = 'mailbox_email';
    case TASK          = 'task';

    /**
     * Handles returning a list of all activity-types.
     *
     * @return array
     */
    public static function allTypes(): array
    {
        return array_column(self::cases(), 'value');
    }
}
