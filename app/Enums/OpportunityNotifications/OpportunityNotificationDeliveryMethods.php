<?php

namespace App\Enums\OpportunityNotifications;

enum OpportunityNotificationDeliveryMethods: int {

    case UNSENT = 0;
    case EMAIL = 1;
    case SMS = 2;
    case BOTH = 3;

    /**
     * @return string
     */
    public function getDeliveryMethodString(): string
    {
        return match ($this) {
            self::UNSENT         => 'Unsent',
            self::EMAIL          => 'Email',
            self::SMS            => 'SMS',
            self::BOTH           => 'Both',
        };
    }

    /**
     * @param string $display
     * @return OpportunityNotificationDeliveryMethods
     */
    public static function fromDisplayString(string $display): OpportunityNotificationDeliveryMethods
    {
        return match ($display) {
            'Unsent' => self::UNSENT,
            'Email' => self::EMAIL,
            'SMS' => self::SMS,
            'Both' => self::BOTH,
            default => throw new \RuntimeException("{$display} is not a valid Delivery Method")
        };
    }

    /**
     * @param string $display
     * @return OpportunityNotificationDeliveryMethods|null
     */
    public static function tryFromDisplayString(string $display): ?OpportunityNotificationDeliveryMethods
    {
        try {
            return OpportunityNotificationDeliveryMethods::fromDisplayString($display);
        } catch(\RuntimeException $e) {
            return null;
        }
    }

}
