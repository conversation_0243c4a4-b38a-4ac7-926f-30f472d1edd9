<?php

namespace App\Enums\Billing;

use Illuminate\Support\Carbon;

enum GraphGroupByOptions: string
{
    case HOUR  = 'hour';
    case DAY   = 'day';
    case WEEK  = 'week';
    case MONTH = 'month';
    case YEAR  = 'year';

    /**
     * @return array
     */
    public static function allTypes(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getCarbonFormat(): string
    {
        return match ($this) {
            self::HOUR  => 'Y-m-d H:00:00',
            self::DAY   => 'Y-m-d',
            self::WEEK  => 'Y-W',
            self::MONTH => 'Y-m',
            self::YEAR  => 'Y',
            default     => null,
        };
    }

    public function getCarbonTimeGrouped(Carbon $time): Carbon
    {
        return match ($this) {
            self::HOUR  => $time->startOfHour(),
            self::DAY   => $time->startOfDay(),
            self::WEEK  => $time->startOfWeek(),
            self::MONTH => $time->startOfMonth(),
            self::YEAR  => $time->startOfYear(),
            default     => null,
        };
    }

    public function getUnit(): ?string
    {
        return match ($this) {
            self::HOUR  => 'hour',
            self::DAY   => 'day',
            self::WEEK  => 'week',
            self::MONTH => 'month',
            self::YEAR  => 'year',
            default     => null,
        };
    }

    public static function getStep(Carbon $start, Carbon $end): GraphGroupByOptions
    {
        $diffInDays = $start->diffInDays($end);

        return match (true) {
            $diffInDays > 1 && $diffInDays <= 14           => GraphGroupByOptions::DAY,
            $diffInDays > 14 && $diffInDays <= 28 * 3      => GraphGroupByOptions::WEEK,
            $diffInDays > 28 * 3 && $diffInDays <= 365 * 2 => GraphGroupByOptions::MONTH,
            $diffInDays > 365 * 2                          => GraphGroupByOptions::YEAR,
            default                                        => GraphGroupByOptions::HOUR,
        };


    }

}
