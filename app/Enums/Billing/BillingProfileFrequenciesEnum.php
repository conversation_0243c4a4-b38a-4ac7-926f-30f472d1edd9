<?php

namespace App\Enums\Billing;
enum BillingProfileFrequenciesEnum: string
{
    case MONTHLY = 'monthly';
    case DAILY   = 'daily';
    case WEEKLY  = 'weekly';

    /**
     * @return array
     */
    public static function getAllValue(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function cronData(): array
    {
        return match ($this) {
            self::MONTHLY => [
                'month_day' => 3,
            ],
            self::DAILY   => [
                'day' => 14
            ],
            self::WEEKLY  => [
                'week_day' => 'friday'
            ],
            default       => []
        };

    }
}
