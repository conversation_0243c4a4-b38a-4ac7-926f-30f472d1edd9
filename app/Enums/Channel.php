<?php

namespace App\Enums;

use App\Models\Notification;

enum Channel:string
{
    case DEFAULT = "default";
    case LEGACY = "legacy";
    case ACTIONS = "actions";
    case EMAIL = "email";

    public function toNotificationMapping(): int
    {
        return match($this) {
            self::LEGACY => Notification::TYPE_LEGACY,
            self::ACTIONS => Notification::TYPE_ACTIONS,
            default => Notification::TYPE_DEFAULT
        };
    }
}
