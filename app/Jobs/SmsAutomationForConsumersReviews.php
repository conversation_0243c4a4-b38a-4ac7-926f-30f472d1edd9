<?php

namespace App\Jobs;

use App\Models\Phone;
use App\Models\Text;
use App\Repositories\Odin\ConsumerRepository;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Twilio\Exceptions\TwilioException;
use Twilio\Rest\Client as TwilioClient;
use Carbon\Carbon;

class SmsAutomationForConsumersReviews implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;

    const TIMEZONE = 'timezone';
    const WEBSITE = 'website';
    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->onQueue(config('queue.named_queues.default'));
    }

    /**
     * Execute the job.
     * @throws TwilioException
     * @param ConsumerRepository $consumerRepository
     */

    public function handle(ConsumerRepository $consumerRepository): void
    {
        $twilioSid = config('services.twilio.sid');
        $twilioAuthToken = config('services.twilio.token');
        $twilioReviewRequest = config('services.twilio.review_requests');
        $twilioClient = new TwilioClient($twilioSid, $twilioAuthToken);
        $reviewPhoneModel = null;

        $consumers = $consumerRepository->getConsumersProducts();

        if($twilioReviewRequest) {
            $reviewPhoneModel = \App\Models\Phone::query()->where(Phone::FIELD_PHONE, $twilioReviewRequest)->first();
            logger()->info('$reviewPhoneModel: ', [
                $reviewPhoneModel
            ]);
            if(is_null($reviewPhoneModel)) {
                logger()->info('null $reviewPhoneModel');
                $reviewPhoneModel = \App\Models\Phone::query()->create([
                    Phone::FIELD_PHONE => $twilioReviewRequest,
                    Phone::FIELD_STATUS => Phone::STATUS_ACTIVE
                ]);
            }
        }

        logger()->info('How many consumers totals', [$consumers->count()]);

        $consumers->chunk(10, function($consumers_chunk) use($twilioClient, $reviewPhoneModel) {
            $data = [];
            $bulkDataToSave = [];
            logger()->info('$consumers_chunk: ', [
                $consumers_chunk
            ]);
            foreach ($consumers_chunk as $chunk) {
                $data[$chunk->phone] = [
                    Phone::FIELD_PHONE => $chunk->phone,
                    Model::CREATED_AT  => $chunk->created_at,
                    self::WEBSITE      => $chunk->website_name,
                    self::TIMEZONE     => $chunk->timezone_val,
                ];
            }

            $recentInteracted = Text::query()
                ->select(TEXT::FIELD_OTHER_NUMBER, TEXT::FIELD_DIRECTION, Model::CREATED_AT)
                ->whereIn(TEXT::FIELD_OTHER_NUMBER, array_keys($data))
                ->where(Model::CREATED_AT, '>=', Carbon::now()->subDays(14))
                ->where(Text::FIELD_PHONE_ID, $reviewPhoneModel?->id)
                ->where(function ($subQuery) {
                    $subQuery->where(TEXT::FIELD_DIRECTION, TEXT::DIRECTION_INBOUND)
                        ->orWhere(TEXT::FIELD_DIRECTION, TEXT::DIRECTION_OUTBOUND);
                })
                ->get()
                ->pluck(TEXT::FIELD_OTHER_NUMBER)
                ->toArray();
            logger()->info('$recentInteracted: ', [
                $recentInteracted
            ]);


            logger()->info('List of numbers with no interaction in the last 14days ', [array_diff(array_keys($data), $recentInteracted)]);
            if(count(array_diff(array_keys($data), $recentInteracted)) > 0) {
                $newNumbersVal = array_diff_key($data, array_flip($recentInteracted));
                logger()->info('List of numbers to send text to ', [$newNumbersVal]);
                foreach($newNumbersVal as $val) {

                    logger()->info('Check if within business hours ', [$this->isWithinBusinessHours($val[self::TIMEZONE])]);
                    if ($this->isWithinBusinessHours($val[self::TIMEZONE])) {
                        logger()->info('Within business hours');

                        $createdAt = Carbon::parse($val[Model::CREATED_AT])->timezone($val[self::TIMEZONE])->format('l');

                        $message = "Hey there! I’m Geoffrey (quality assurance) with {$val[self::WEBSITE]}! I see that you used our online estimation tool on {$createdAt} and I wanted to know if you found it to be easy to use and generally helpful. Please answer YES if our service was helpful or NO if it was not helpful. Additional comments are welcome!";

                        if(!is_null($val[PHONE::FIELD_PHONE])) {
                            if ($reviewPhoneModel) {
                                $bulkDataToSave[] = [
                                    Text::FIELD_PHONE_ID     => $reviewPhoneModel?->id,
                                    Text::FIELD_OTHER_NUMBER => $val[PHONE::FIELD_PHONE],
                                    Text::FIELD_MESSAGE_BODY => $message,
                                    Text::FIELD_DIRECTION    => Text::DIRECTION_OUTBOUND,
                                    Model::CREATED_AT        => Carbon::now('UTC'),
                                    Model::UPDATED_AT        => Carbon::now('UTC')
                                ];
                            }

                            logger()->info('Prepare to send text, check if consumer value is not null ', [!is_null($val[PHONE::FIELD_PHONE])]);

                            try {
                                $twilioClient->messages->create($val[PHONE::FIELD_PHONE], [
                                    'from' => $reviewPhoneModel?->phone,
                                    'body' => $message,
                                ]);
                                logger()->info('text sent!');
                            } catch (TwilioException $exception) {
                                $phoneNumber = $val[Phone::FIELD_PHONE];
                                logger()->error("text failed for $phoneNumber:" . $exception->getMessage());
                            }
                        }
                    }
                }

                try {
                    if(count($bulkDataToSave) > 0) {
                        Text::query()->insert($bulkDataToSave);
                    }
                }
                catch(Exception $e) {
                    logger()->error($e->getMessage());
                }
            }

        });
    }


    private function isWithinBusinessHours($timezoneOffset = null): bool
    {
        // Default to UTC if no timezone offset is provided
        if (is_null($timezoneOffset)) {
            $timezone = 'UTC';
        } else {
            // Generate the timezone string from the offset
            $timezone = timezone_name_from_abbr('', $timezoneOffset * 3600, 0);
            if ($timezone === false) {
                // If timezone_name_from_abbr fails, fallback to manually constructing the offset
                $timezone = sprintf('Etc/GMT%+d', -$timezoneOffset);
            }
        }

        // Get current time in the specified timezone
        $now = Carbon::now($timezone);

        // Define the start and end times (9 AM and 7 PM)
        $startOfBusiness = Carbon::createFromTime(9, 0, 0, $timezone);
        $endOfBusiness = Carbon::createFromTime(19, 0, 0, $timezone);

        // Check if the current time is within the business hours
        return $now->between($startOfBusiness, $endOfBusiness);
    }
}
