<?php

namespace App\Jobs;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Services\CompanyDiscoveryService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

/**
 * Class SyncCompanyLocationToLegacyJob
 * @package App\Jobs
 */
class SyncCompanyLocationToLegacyJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /** @var Company $company */
    private Company $company;

    /** @var CompanyLocation $companyLocation */
    private CompanyLocation $companyLocation;

    /**
     * @param Company $company
     * @param CompanyLocation $companyLocation
     */
    public function __construct(Company $company, CompanyLocation $companyLocation)
    {
        $this->onQueue(config('queue.named_queues.long_running'));

        $this->company = $company;
        $this->companyLocation = $companyLocation;
    }

    /**
     * @param CompanyDiscoveryService $companyDiscoveryService
     * @return void
     */
    public function handle(CompanyDiscoveryService $companyDiscoveryService)
    {
        $this->company->refresh();
        if($this->company->legacy_id) {
            $companyDiscoveryService->syncCompanyLocationToLegacy($this->company, $this->companyLocation);
        }
    }
}
