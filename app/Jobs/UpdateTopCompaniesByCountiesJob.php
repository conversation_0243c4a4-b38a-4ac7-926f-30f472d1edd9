<?php

namespace App\Jobs;

use App\Enums\Odin\IndustryServiceSlug;
use App\Models\Legacy\CompanyRanking;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Models\TopCompanyByCounty;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class UpdateTopCompaniesByCountiesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        $serviceId = IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, IndustryServiceSlug::SOLAR_INSTALLATION)
            ->first()
            ->{IndustryService::FIELD_ID};

        $data = [];

        Location::query()
            ->select(Location::ID)
            ->where(Location::TYPE, Location::TYPE_COUNTY)
            ->each(function (Location $location) use ($serviceId, &$data) {
                $companies = $this->getCompaniesByCountyLocationId($location->id, $serviceId);

                if($companies->count() > 0) {
                    foreach($companies as $company) {
                        $data[] = [
                            TopCompanyByCounty::FIELD_INDUSTRY_SERVICE_ID       => $serviceId,
                            TopCompanyByCounty::FIELD_COUNTY_LOCATION_ID        => $location->id,
                            TopCompanyByCounty::FIELD_COMPANY_ID                => $company->id,
                            TopCompanyByCounty::FIELD_COMPANY_NAME              => $company->name,
                            TopCompanyByCounty::FIELD_COMPANY_REFERENCE         => $company->reference,
                            TopCompanyByCounty::FIELD_COMPANY_REVIEW_COUNT      => $company->review_count,
                            TopCompanyByCounty::FIELD_COMPANY_BAYESIAN_ALL_TIME => $company->bayesian_all_time,
                        ];
                    }
                }
            });

        DB::transaction(function () use ($data) {
            TopCompanyByCounty::query()->delete();
            foreach (array_chunk($data, 2000) as $chunkInsert) {
                TopCompanyByCounty::query()->insert($chunkInsert);
            }
        });
    }

    /**
     * @param int $locationId
     * @param int $serviceId
     * @return Collection
     */
    private function getCompaniesByCountyLocationId(int $locationId, int $serviceId): Collection
    {
        return DB::table(ProductAssignment::TABLE)
            ->select([
                         Company::TABLE .'.'. Company::FIELD_NAME,
                         Company::TABLE .'.'. Company::FIELD_REFERENCE,
                         Company::TABLE .'.'. Company::FIELD_ID,
                         CompanyRanking::TABLE .'.'. CompanyRanking::REVIEW_COUNT,
                         CompanyRanking::TABLE .'.'. CompanyRanking::BAYESIAN_ALL_TIME,
                     ])
            ->distinct(Company::TABLE .'.'. Company::FIELD_ID)
            ->join(
                ConsumerProduct::TABLE,
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID,
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID
            )
            ->join(
                ServiceProduct::TABLE,
                ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_ID,
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_SERVICE_PRODUCT_ID
            )
            ->join(
                Address::TABLE,
                Address::TABLE.'.'.Address::FIELD_ID,
                ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ADDRESS_ID
            )
            ->join(
                Company::TABLE,
                Company::TABLE.'.'.Company::FIELD_ID,
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COMPANY_ID
            )
            ->join(
                DatabaseHelperService::readOnlyDatabase() .'.'. EloquentCompany::TABLE,
                EloquentCompany::TABLE .'.'. EloquentCompany::ID,
                Company::TABLE .'.'. Company::FIELD_LEGACY_ID
            )
            ->join(
                DatabaseHelperService::readOnlyDatabase() .'.'. CompanyRanking::TABLE,
                CompanyRanking::TABLE .'.'. CompanyRanking::COMPANY_ID,
                Company::TABLE .'.'. Company::FIELD_LEGACY_ID
            )
            ->where(Address::FIELD_COUNTY_LOCATION_ID, $locationId)
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $serviceId)
            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::CREATED_AT, '>=', Carbon::today()->subWeeks(2))
            ->orderBy(CompanyRanking::TABLE .'.'. CompanyRanking::BAYESIAN_ALL_TIME, 'DESC')
            ->limit(4)
            ->get();
    }
}
