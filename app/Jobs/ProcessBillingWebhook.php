<?php

namespace App\Jobs;

use App\Services\Billing\BillingLogService;
use App\Services\Billing\BillingWebhookService;
use App\Services\Billing\PaymentGateway\Events\PaymentGatewayEvent;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessBillingWebhook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(protected PaymentGatewayEvent $event)
    {
        $this->onQueue('billing');
    }

    /**
     * Execute the job.
     * @throws Exception
     */
    public function handle(BillingWebhookService $billingWebhookService): void
    {
        BillingLogService::log(
            message  : 'Processing webhook event ' . $this->event::class,
            namespace: 'process_billing_webhook',
            context  : [
                'event' => $this->event
            ]
        );

        $billingWebhookService->handleEvent($this->event);
    }
}
