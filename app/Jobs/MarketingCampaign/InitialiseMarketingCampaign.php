<?php

namespace App\Jobs\MarketingCampaign;

use App\Contracts\Services\MarketingCampaign\EmailMarketingServiceFactory;
use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Enums\MarketingCampaigns\MarketingCampaignStatus;
use App\Models\MarketingCampaign;
use App\Repositories\MarketingCampaign\MarketingCampaignRepository;
use App\Services\MarketingCampaign\MarketingCampaignService;
use App\Services\MarketingCampaign\MarketingLogService;
use App\Services\QueueHelperService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InitialiseMarketingCampaign implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const int CHUNK_SIZE = 200; //mailchimp max batch upload is 500
    const int INITIAL_BATCH_SIZE = 100;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected int    $campaignId,
        protected string $campaignName,
        protected array  $consumerFilters,
        protected int    $authorId,
    )
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_MARKETING);
    }

    /**
     * Execute the job.
     * @throws Exception
     */
    public function handle(
        MarketingCampaignRepository         $repository,
        MarketingCampaignService            $marketingCampaignService,
        EmailMarketingServiceFactory        $emailMarketingServiceFactory,
    ): void
    {
        /** @var ?MarketingCampaign $campaign */
        $campaign = MarketingCampaign::query()->find($this->campaignId);

        if (empty($campaign)) {
            logger()->error("Could not find marketing campaign of id: $this->campaignId");
            return;
        }

        $emailMarketingService = $emailMarketingServiceFactory::make($campaign->{MarketingCampaign::FIELD_TYPE});

        $campaign->{MarketingCampaign::FIELD_PROCESSING} = true;
        $campaign->save();

        $data = $marketingCampaignService->initialiseMarketingCampaignConsumers($this->consumerFilters, $this->campaignId);

        $count = $data->count();

        if($data->isEmpty()) {
            MarketingLogService::log(
                message: "Didn't identify any consumers",
                namespace: MarketingLogType::ADD_USERS,
                level: LogLevel::ALERT,
                context: [
                    'campaign_id' => $campaign->id,
                    'consumer_filters' => $this->consumerFilters,
                ],
                relations: [$campaign],
            );

            $marketingCampaignService->syncCampaignProcessing(campaign: $campaign);

            return;
        }

        $initialConsumers = $data->splice(0,self::INITIAL_BATCH_SIZE);

        $response = $emailMarketingService->createMarketingCampaign($this->campaignName, $initialConsumers);

        if (empty($response)) {
            throw new Exception("Failed to Initialise Marketing Campaign Id: $this->campaignId");
        }

        $metrics = $marketingCampaignService->getMarketingCampaignMetrics($campaign);

        $metrics->setTargets($count);

        $repository->updateMarketingCampaign(
            marketingCampaign: $campaign,
            authorId: $this->authorId,
            status: MarketingCampaignStatus::ACTIVE,
            externalReference: $response->getExternalReference(),
            externalCode: $response->getExternalCode(),
            metrics: $metrics,
        );

        $marketingCampaignService->updateMarketingCampaignConsumers(
            $response->getAddedUsers(),
            $this->campaignId
        );

        $data->chunk(self::CHUNK_SIZE)->each(function ($chunk) {
            ChunkUploadEmailMarketingConsumers::dispatch($this->campaignId, $chunk);
        });

        $marketingCampaignService->syncCampaignProcessing($campaign);
    }
}
