<?php

namespace App\Jobs\MarketingCampaign;

use App\Contracts\Services\MarketingCampaign\EmailMarketingServiceFactory;
use App\Enums\MarketingCampaigns\MarketingCampaignStatus;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Repositories\MarketingCampaign\MarketingCampaignRepository;
use App\Services\MarketingCampaign\MarketingCampaignService;
use App\Services\QueueHelperService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class UpdateEmailMarketingCampaignMetrics implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected int $marketingCampaignId
    )
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_MARKETING);
    }

    /**
     * Execute the job.
     */
    public function handle(
        MarketingCampaignRepository $marketingCampaignRepository,
        MarketingCampaignService $marketingCampaignService,
    ): void
    {
        /** @var ?MarketingCampaign $campaign */
        $campaign = MarketingCampaign::query()->find($this->marketingCampaignId);

        if(empty($campaign)) {
            logger()->error("Could not identify marketing campaign with id: $this->marketingCampaignId");
            return;
        }

        $emailMarketingService = EmailMarketingServiceFactory::make($campaign->{MarketingCampaign::FIELD_TYPE});

        $campaignMetrics = $emailMarketingService->getEmailMarketingCampaignMetrics($campaign);

        $marketingCampaignRepository->updateMarketingCampaign(
            marketingCampaign: $campaign,
            metrics: $campaignMetrics,
        );
    }
}
