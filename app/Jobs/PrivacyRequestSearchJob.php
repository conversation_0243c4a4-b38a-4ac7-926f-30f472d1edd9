<?php

namespace App\Jobs;

use App\Enums\PrivacyManagement\PrivacyRequestStatuses;
use App\Models\PrivacyRequest;
use App\Services\QueueHelperService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Tests\Testable;
use function filled;

class PrivacyRequestSearchJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    use Testable;

    protected array $searchParams;
    protected array $domains;
    public int  $maxExceptions = 1;
    public bool $failOnTimeout = true;

    public int $timeout = Carbon::SECONDS_PER_MINUTE * 3;

    public int $tries = 1;
    public function __construct(public PrivacyRequest $privacyRequest)
    {
        $this->getSearchParams();

        $this->onQueue(QueueHelperService::QUEUE_NAME_PRIVACY);
        $this->onConnection(QueueHelperService::QUEUE_CONNECTION);

//        todo set up flow builder, solar estimate, fixr
        $this->domains = [
            config('app.url') => config('app.url') . config('privacy-request.uri-prefix') . '/search',
        ];
    }
    
    /**
     * @throws ConnectionException
     */
    public function handle(): void
    {
        $responseArray = array();

        foreach($this->domains as $key => $value) {
            $response = Http::timeout(180)
                ->withToken(config('privacy-request.access-token'))
                ->get($value, $this->searchParams);

            if($response->getStatusCode() >= 200 && $response->getStatusCode() < 300)
                $responseArray[$key] = json_decode($response->getBody(), true)['data'];
        }

        $this->privacyRequest->scan_response = $responseArray;
        $this->privacyRequest->status = PrivacyRequestStatuses::SCANNED;
        $this->privacyRequest->save();
    }

    protected function getSearchParams(): void
    {
        $searchParams = [];

        if (isset($this->privacyRequest->payload['first_name']) && filled($this->privacyRequest->payload['first_name'])) {
            $searchParams['first_name'] = $this->privacyRequest->payload['first_name'];
            $searchParams['firstname'] = $this->privacyRequest->payload['first_name'];
        }


        if (isset($this->privacyRequest->payload['last_name']) && filled($this->privacyRequest->payload['last_name'])) {
            $searchParams['last_name'] = $this->privacyRequest->payload['last_name'];
            $searchParams['lastname'] = $this->privacyRequest->payload['last_name'];
        }

        if (isset($this->privacyRequest->payload['email']) && filled($this->privacyRequest->payload['email'])) {
            $searchParams['email'] = $this->privacyRequest->payload['email'];
            $searchParams['useremail'] = $this->privacyRequest->payload['email'];
        }

        if (isset($this->privacyRequest->payload['phone']) && filled($this->privacyRequest->payload['phone'])) {
            $searchParams['phone'] = $this->privacyRequest->payload['phone'];
        }

        $this->searchParams = ['search' => $searchParams];
    }
}
