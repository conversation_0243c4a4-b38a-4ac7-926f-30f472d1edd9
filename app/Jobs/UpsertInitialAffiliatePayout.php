<?php

namespace App\Jobs;

use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Payout;
use App\Models\Affiliates\PayoutStrategy;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Repositories\Affiliate\PayoutRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class UpsertInitialAffiliatePayout implements ShouldQueue
{
    use Queueable;
    const int CHUNK_SIZE = 500;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected PayoutStrategy $strategy,
        protected array $consumerProductIds,
    )
    {
        $this->onQueue(config('queue.named_queues.long_running'));
    }

    /**
     * Execute the job.
     */
    public function handle(PayoutRepository $repository): void
    {
        $strategyClass = $this->strategy->type->getClass();

        ConsumerProduct::query()
            ->with(ConsumerProduct::RELATION_CONSUMER_PRODUCT_AFFILIATE_RECORD .'.'. ConsumerProductAffiliateRecord::RELATION_AFFILIATE .'.'. Affiliate::RELATION_STRATEGY)
            ->whereIntegerInRaw(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID, $this->consumerProductIds)
            ->chunk(self::CHUNK_SIZE, function ($consumerProducts) use ($strategyClass, $repository) {
                $mappedProducts = $consumerProducts->map(function (ConsumerProduct $item)  use ($strategyClass) {
                    $value = $strategyClass->calculate($item, $this->strategy);

                    return [
                        Payout::FIELD_AFFILIATE_ID => $this->strategy->affiliate_id,
                        Payout::FIELD_PAYOUT_STRATEGY_ID => $this->strategy->id,
                        Payout::FIELD_CENT_VALUE => $value,
                        Payout::FIELD_CONSUMER_PRODUCT_ID => $item->id,
                    ];
                });

                $repository->upsert(
                    $mappedProducts->toArray()
                );
            });
    }
}
