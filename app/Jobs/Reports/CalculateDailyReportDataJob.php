<?php

namespace App\Jobs\Reports;

use App\Services\QueueHelperService;
use App\Services\Reports\ReportDataService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CalculateDailyReportDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->queue = QueueHelperService::QUEUE_NAME_LONG_RUNNING;
    }

    /**
     * @param ReportDataService $reportDataService
     * @return void
     */
    public function handle(ReportDataService $reportDataService): void
    {
        $reportDataService->calculateDailyReportData();
    }
}
