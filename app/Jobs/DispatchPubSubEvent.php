<?php

namespace App\Jobs;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Models\SalesBaitLead;
use App\Services\PubSub\PubSubService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DispatchPubSubEvent implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public EventCategory $category, public EventName $name, public array $data = [])
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(PubSubService $pubSubService)
    {
        $pubSubService->handle(
            $this->category->value,
            $this->name->value,
            $this->data
        );
    }
}
