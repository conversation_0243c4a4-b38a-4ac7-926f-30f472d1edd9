<?php

namespace App\Jobs\Billing\LeadImport;

use App\Enums\RejectionReasons;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Services\Billing\BillingLogService;
use App\Services\Odin\ProductAssignmentSystemRejectionService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SystemRejectProductAssignmentsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        protected array $productAssignmentIds,
        protected int $companyId,
        protected string $reasonNote
    )
    {
        $this->onQueue('billing');
    }

    /**
     * @param ProductAssignmentSystemRejectionService $productAssignmentSystemRejectionService
     * @return void
     * @throws BindingResolutionException
     */
    public function handle(
        ProductAssignmentSystemRejectionService $productAssignmentSystemRejectionService
    ): void
    {
        if (empty($this->productAssignmentIds)) {
            return;
        }

        $productAssignments = ProductAssignment::query()
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_REJECTIONS)
            ->where(ProductAssignment::FIELD_COMPANY_ID, $this->companyId)
            ->whereIn(ProductAssignment::FIELD_ID, $this->productAssignmentIds)
            ->get();

        $company = Company::query()->findOrFail($this->companyId);

        foreach ($productAssignments as $assigment) {
            try {
                $productAssignmentSystemRejectionService->rejectProductAssignment(
                    company                        : $company,
                    productAssignment              : $assigment,
                    rejectionReason                : RejectionReasons::OTHER,
                    rejectionReasonNote            : $this->reasonNote,
                    shouldAffectRejectionPercentage: false,
                );
            } catch (Exception $exception) {
                BillingLogService::logException(
                    exception  : $exception,
                    namespace  : 'system_reject_product_assignments',
                    relatedType: ProductAssignment::class,
                    relatedId  : $assigment->id,
                    context    : [
                        'company_id' => $company->id,
                    ],
                );
            }
        }
    }
}
