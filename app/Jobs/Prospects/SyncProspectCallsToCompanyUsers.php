<?php

namespace App\Jobs\Prospects;

use App\Models\Call;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncProspectCallsToCompanyUsers implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $companyId) {}

    public function handle(): void
    {
        /** @var Company $company */
        $company     = Company::findOrFail($this->companyId);
        $phoneToUser = $company->users->pluck(CompanyUser::FIELD_ID, CompanyUser::FIELD_FORMATTED_CELL_PHONE);

        $calls = Call::query()
            ->whereNull(Call::FIELD_RELATION_TYPE)
            ->where(Call::FIELD_DIRECTION, Call::DIRECTION_OUTBOUND)
            ->whereIn(Call::FIELD_FORMATTED_OTHER_NUMBER, $phoneToUser->keys()->toArray())
            ->get();

        $calls->each(function (Call $call) use ($phoneToUser) {
            $companyUserId = $phoneToUser[$call->formatted_other_number] ?? null;
            if ($companyUserId) {
                $call->update([
                    Call::FIELD_RELATION_TYPE => 'company_user',
                    Call::FIELD_RELATION_ID   => $companyUserId,
                ]);
            }
        });
    }
}
