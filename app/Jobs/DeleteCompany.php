<?php

namespace App\Jobs;

use App\Models\Odin\Company;
use App\Services\Companies\Delete\CompanyDeleteService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class DeleteCompany implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected int $companyId
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $company = Company::query()->find($this->companyId);

        if (empty($company)) {
            logger()->error("Company with ID: $this->companyId not found, exiting.");

            return;
        }

        app(CompanyDeleteService::class, compact('company'))->delete();
    }
}
