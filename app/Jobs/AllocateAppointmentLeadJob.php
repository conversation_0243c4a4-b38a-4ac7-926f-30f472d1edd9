<?php

namespace App\Jobs;

use App\Models\AppointmentProcessingAllocation;
use App\Models\LeadProcessingAllocation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class AllocateAppointment<PERSON>eadJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /** @var LeadProcessingAllocation $leadProcessingAllocation */
    protected LeadProcessingAllocation $leadProcessingAllocation;

    // If true, this allocation attempt should only sell to 'Unverified' budgets
    protected bool $unverified;

    protected ?int $remainingLegs;

    protected array $ignoreCompanyIds;

    /**
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @param bool $unverified
     * @param array $ignoreCompanyIds
     * @param int|null $remainingLegs
     */
    public function __construct(
        LeadProcessingAllocation $leadProcessingAllocation,
        bool $unverified = false,
        array $ignoreCompanyIds = [],
        ?int $remainingLegs = null
    )
    {
        $this->queue = 'lead_allocation_queue';
        $this->leadProcessingAllocation = $leadProcessingAllocation;
        $this->unverified = $unverified;
        $this->remainingLegs = $remainingLegs;
        $this->ignoreCompanyIds = array_filter(array_unique($ignoreCompanyIds));;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if($this->appointmentLeadAllocationAlreadyRan()) {
            logger()->error("This appointment lead allocation attempt has failed because there was already a prior attempt: {$this->leadProcessingAllocation->lead_id}");

            return;
        }

        return AllocateLeadJob::dispatchSync(
            $this->leadProcessingAllocation,
            $this->unverified,
            $this->ignoreCompanyIds,
            $this->remainingLegs
        );
    }

    /**
     * @return bool
     */
    public function appointmentLeadAllocationAlreadyRan(): bool
    {
        $alreadyRan = AppointmentProcessingAllocation::query()
            ->where(AppointmentProcessingAllocation::FIELD_LEAD_CONSUMER_PRODUCT_ID, $this->leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID})
            ->where(AppointmentProcessingAllocation::FIELD_RAN_LEAD_ALLOCATION, true)
            ->exists();

        AppointmentProcessingAllocation::query()
            ->where(AppointmentProcessingAllocation::FIELD_LEAD_CONSUMER_PRODUCT_ID, $this->leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID})
            ->where(AppointmentProcessingAllocation::FIELD_ALLOCATED_AS_LEAD, true)
            ->update([
                AppointmentProcessingAllocation::FIELD_RAN_LEAD_ALLOCATION => true
            ]);

        return $alreadyRan;
    }
}
