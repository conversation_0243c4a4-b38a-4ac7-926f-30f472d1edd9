<?php

namespace App\Registries;

use App\Campaigns\Definitions\Appointment\AppointmentCampaign;
use App\Campaigns\Definitions\Appointment\SolarAppointmentCampaign;
use App\Campaigns\Definitions\DirectLeads\DirectLeadsCampaign;
use App\Campaigns\Definitions\Lead\ExclusiveOnlyLeadCampaign;
use App\Campaigns\Definitions\Lead\LeadCampaign;
use App\Campaigns\Definitions\Lead\SolarLeadCampaign;
use App\Campaigns\Definitions\Lead\UnverifiedOnlyLeadCampaign;
use App\Contracts\Campaigns\CampaignDefinitionContract;
use App\Enums\Campaigns\CampaignType;
use App\Enums\Campaigns\CustomCampaignBudgetType;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\Product as ProductEnum;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Arr;

class CampaignRegistry
{
    /**
     * Service level campaign type overrides.
     *
     * @var array
     */
    const array SERVICE_OVERRIDES = [];

    /**
     * Industry level campaign type overrides.
     *
     * @var array|array[]
     */
    const array INDUSTRY_OVERRIDES = [
        IndustryEnum::SOLAR->value => [
            ProductEnum::LEAD->value        => CampaignType::SOLAR_LEAD_CAMPAIGN,
            ProductEnum::APPOINTMENT->value => CampaignType::APPOINTMENT_CAMPAIGN
        ]
    ];

    /**
     * Custom budget override by product
     */
    const array BUDGET_OVERRIDES = [
        ProductEnum::LEAD->value => [
            CustomCampaignBudgetType::EXCLUSIVE_ONLY_LEADS->value  => CampaignType::EXCLUSIVE_ONLY_LEAD_CAMPAIGN,
            CustomCampaignBudgetType::UNVERIFIED_ONLY_LEADS->value => CampaignType::UNVERIFIED_ONLY_LEAD_CAMPAIGN,
        ]
    ];

    /**
     * Mappings for products -> campaign types.
     *
     * @var array
     */
    const array PRODUCT_MAPPINGS = [
        ProductEnum::LEAD->value         => CampaignType::LEAD_CAMPAIGN,
        ProductEnum::APPOINTMENT->value  => CampaignType::APPOINTMENT_CAMPAIGN,
        ProductEnum::DIRECT_LEADS->value => CampaignType::DIRECT_LEADS
    ];

    /**
     * Returns the corresponding campaign definition for a given campaign type.
     *
     * @param CampaignType $type
     * @return CampaignDefinitionContract|null
     * @throws BindingResolutionException
     */
    public function getCampaignDefinition(CampaignType $type): ?CampaignDefinitionContract
    {
        return match ($type) {
            CampaignType::LEAD_CAMPAIGN                 => app()->make(LeadCampaign::class),
            CampaignType::APPOINTMENT_CAMPAIGN          => app()->make(AppointmentCampaign::class),
            CampaignType::SOLAR_LEAD_CAMPAIGN           => app()->make(SolarLeadCampaign::class),
            CampaignType::SOLAR_APPOINTMENT_CAMPAIGN    => app()->make(SolarAppointmentCampaign::class),
            CampaignType::DIRECT_LEADS                  => app()->make(DirectLeadsCampaign::class),
            CampaignType::EXCLUSIVE_ONLY_LEAD_CAMPAIGN  => app()->make(ExclusiveOnlyLeadCampaign::class),
            CampaignType::UNVERIFIED_ONLY_LEAD_CAMPAIGN => app()->make(UnverifiedOnlyLeadCampaign::class),
            default                                     => null
        };
    }

    /**
     * Returns the mapping for a given product, optionally filtered by the industry key and the service key.
     *
     * @param ProductEnum $product
     * @param IndustryEnum|null $industry
     * @param string|null $serviceKey
     * @param CustomCampaignBudgetType|null $customCampaignBudgetType
     * @return CampaignDefinitionContract|null
     * @throws BindingResolutionException
     */
    public function getCampaignMapping(
        ProductEnum               $product,
        ?IndustryEnum             $industry = null,
        ?string                   $serviceKey = null,
        ?CustomCampaignBudgetType $customCampaignBudgetType = null,
    ): ?CampaignDefinitionContract
    {
        $campaignType = $this->getCampaignMappingType($product, $industry, $serviceKey, $customCampaignBudgetType);

        return $campaignType ? $this->getCampaignDefinition($campaignType) : null;
    }

    /**
     * Returns the mapping type for a product, optionally filtered by industry and service.
     *
     * @param ProductEnum $product
     * @param IndustryEnum|null $industry
     * @param string|null $serviceKey
     * @param CustomCampaignBudgetType|null $budgetType
     * @return CampaignType|null
     */
    public function getCampaignMappingType(
        ProductEnum               $product,
        ?IndustryEnum             $industry = null,
        ?string                   $serviceKey = null,
        ?CustomCampaignBudgetType $budgetType = null,
    ): ?CampaignType
    {
        $campaignType = self::PRODUCT_MAPPINGS[$product->value] ?? null;

        if ($budgetType !== null && Arr::has(self::BUDGET_OVERRIDES, "{$product->value}.{$budgetType->value}"))
            $campaignType = self::BUDGET_OVERRIDES[$product->value][$budgetType->value];
        else if ($industry !== null && $serviceKey !== null && Arr::has(self::SERVICE_OVERRIDES, "{$industry->value}.{$serviceKey}.{$product->value}'"))
            $campaignType = self::SERVICE_OVERRIDES[$industry->value][$serviceKey][$product->value];
        else if ($industry !== null && Arr::has(self::INDUSTRY_OVERRIDES, "{$industry->value}.{$product->value}"))
            $campaignType = self::INDUSTRY_OVERRIDES[$industry->value][$product->value];

        return $campaignType;
    }
}
