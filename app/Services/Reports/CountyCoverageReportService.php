<?php

namespace App\Services\Reports;

use App\Builders\Odin\LeadsReportCostBuilder;
use App\Builders\Odin\LeadsReportLegsBuilder;
use App\Contracts\Services\Reports\ReportDataServiceInterface;
use App\Enums\LeadsReport\LeadsReportColumnEnum;
use App\Enums\LeadsReport\LeadsReportGroupEnum;
use App\Enums\Odin\Industry;
use App\Models\Odin\Industry as IndustryModel;
use App\Enums\Reports\CountyCoverageReport\CountyCoverageReportColumnEnum;
use App\Models\Legacy\Location;
use App\Models\ReportData;
use App\Repositories\Odin\IndustryRepository;
use App\Repositories\Reports\CountyCoverageReportRepository;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class CountyCoverageReportService implements ReportDataServiceInterface
{
    const string LEADS_QUERY = 'leads_query';
    const string COST_QUERY = 'cost_query';

    /**
     * @param CountyCoverageReportRepository $countyCoverageReportRepository
     * @param IndustryRepository $industryRepository
     */
    public function __construct(
        protected CountyCoverageReportRepository    $countyCoverageReportRepository,
        protected IndustryRepository                $industryRepository
    ) {}

    /**
     * @param Industry $industry
     * @param mixed $filters
     * @param array $columns
     * @param int|null $companyId
     * @param array|null $campaigns
     * @return Collection
     */
    public function buildReport(
        Industry $industry,
        mixed $filters,
        array $columns,
        ?int $companyId,
        ?array $campaigns,
    ): Collection
    {
        $returnedColumns = $this->getDependentColumns($columns);

        $query = $this->countyCoverageReportRepository->getCountyQuery($industry, $filters, $returnedColumns, $companyId, $campaigns);

        return $query->get();
    }

    /**
     * Include column dependencies for calculated columns in search
     * @param array $columns
     * @return array
     */
    protected function getDependentColumns(array $columns): array
    {
        $returnedColumns = $columns;

        foreach ($columns as $column) {
            array_push($returnedColumns, ...CountyCoverageReportColumnEnum::getDependencies($column));
            $returnedColumns = array_values(array_unique(array_map(function ($col) {return $col->value;}, $returnedColumns)));
            $returnedColumns = array_map(function ($col) {return CountyCoverageReportColumnEnum::from($col);}, $returnedColumns);
        }
        return $returnedColumns;
    }

    /**
     * @return void
     */
    public function calculateReportData(): void
    {
        $calculatedData = collect();
        foreach (CountyCoverageReportColumnEnum::activeIndustries() as $industry) {
            $calculatedData = $calculatedData->merge($this->calculateReportDataForIndustry($industry));
        }

        // Clear existing data for county coverage report
        ReportData::query()
            ->where(ReportData::FIELD_REPORT, ReportData::REPORT_COUNTY_COVERAGE)
            ->delete();

        // Add new data
        $calculatedData->chunk(100)->each(function ($chunk) {
            ReportData::insert($chunk->toArray());
        });
    }

    /**
     * @param Industry $industry
     * @return Collection
     */
    public function calculateReportDataForIndustry(Industry $industry): Collection
    {
        $industryId = $industry->model()->{IndustryModel::FIELD_ID};

        $leadsQuery = LeadsReportLegsBuilder::query()
            ->forGroup(LeadsReportGroupEnum::COUNTY)
            ->fromDatabase(DatabaseHelperService::database())
            ->fromReadonlyDatabase(DatabaseHelperService::readOnlyDatabase())
            ->forIndustryId($industryId)
            ->forStartDate(Carbon::now()->subWeek())
            ->forEndDate(Carbon::now())
            ->forColumns([
                LeadsReportColumnEnum::TOTAL_LEADS,
                LeadsReportColumnEnum::TOTAL_GTS_LEADS,
                LeadsReportColumnEnum::TOTAL_SOLD_LEADS,
                LeadsReportColumnEnum::TOTAL_SOLD_LEGS,
                LeadsReportColumnEnum::TOTAL_REVENUE,
                LeadsReportColumnEnum::GOOGLE_PAID_GTS_LEADS,
                LeadsReportColumnEnum::GOOGLE_REVENUE,
                LeadsReportColumnEnum::GOOGLE_RAW_LEADS,
                LeadsReportColumnEnum::GOOGLE_GTS_LEADS,
                LeadsReportColumnEnum::GOOGLE_SOLD_LEADS,
                LeadsReportColumnEnum::GOOGLE_SOLD_LEGS,
                LeadsReportColumnEnum::GOOGLE_SMS_VERIFIED_REVENUE,
                LeadsReportColumnEnum::GOOGLE_SMS_VERIFIED_LEADS,
            ])
            ->getQuery()
            ->addSelect('l.'.Location::STATE_KEY)
            ->addSelect('l.'.Location::COUNTY_KEY);

        $costQuery = LeadsReportCostBuilder::query()
            ->forGroup(LeadsReportGroupEnum::COUNTY)
            ->fromDatabase(DatabaseHelperService::database())
            ->fromReadonlyDatabase(DatabaseHelperService::readOnlyDatabase())
            ->forIndustryId($industryId)
            ->forStartDate(Carbon::now()->subWeek())
            ->forEndDate(Carbon::now())
            ->forColumns([
                LeadsReportColumnEnum::GOOGLE_AD_COST,
                LeadsReportColumnEnum::TOTAL_AD_COST,
            ])
            ->getQuery()
            ->addSelect('l.'.Location::ID);

        $totalQuery = DB::table(DatabaseHelperService::readOnlyDatabase().'.'.Location::TABLE)
            ->select([
                Location::TABLE.'.'.Location::ID,
                Location::TABLE.'.'.Location::STATE_KEY,
                Location::TABLE.'.'.Location::COUNTY_KEY,
                DB::raw(self::LEADS_QUERY.'.*'),
                DB::raw(self::COST_QUERY.'.*'),
            ])
            ->joinSub($leadsQuery, self::LEADS_QUERY, function($query) {
                return $query->on(self::LEADS_QUERY.'.'.Location::STATE_KEY, '=', Location::TABLE.'.'.Location::STATE_KEY)
                    ->on(self::LEADS_QUERY.'.'.Location::COUNTY_KEY, '=', Location::TABLE.'.'.Location::COUNTY_KEY);
            })
            ->joinSub($costQuery, self::COST_QUERY, function($query) {
                return $query->on(self::COST_QUERY.'.'.Location::ID, '=', Location::TABLE.'.'.Location::ID);
            })
            ->where(Location::TYPE, Location::TYPE_COUNTY);

        $data = $totalQuery->get();

        $calculatedData = $data->map(function ($county) use ($industryId) {
            $data[ReportData::FIELD_RELATION_ID] = $county->{Location::ID};
            $data[ReportData::FIELD_RELATION_TYPE] = ReportData::RELATION_TYPE_COUNTY_LOCATION;
            $data[ReportData::FIELD_REPORT] = ReportData::REPORT_COUNTY_COVERAGE;
            $data[ReportData::FIELD_DATA] = json_encode([
                CountyCoverageReportColumnEnum::INDUSTRY_ID => $industryId,

                CountyCoverageReportColumnEnum::TOTAL_LEADS->value => $county->{LeadsReportColumnEnum::TOTAL_LEADS->keyValue()} ?: 0,
                CountyCoverageReportColumnEnum::TOTAL_REVENUE->value => $county->{LeadsReportColumnEnum::TOTAL_REVENUE->keyValue()} ?: 0,
                CountyCoverageReportColumnEnum::TOTAL_AD_COST->value => $county->{LeadsReportColumnEnum::TOTAL_AD_COST->keyValue()} ?: 0,
                CountyCoverageReportColumnEnum::GROSS_PROFIT->value => round($county->{LeadsReportColumnEnum::TOTAL_REVENUE->keyValue()} - $county->{LeadsReportColumnEnum::TOTAL_AD_COST->keyValue()}, 2),
                CountyCoverageReportColumnEnum::GOOGLE_REVENUE->value => $county->{LeadsReportColumnEnum::GOOGLE_REVENUE->keyValue()} ?: 0,


                CountyCoverageReportColumnEnum::SOLD_LEGS_PER_SOLD_LEAD->value => $county->{LeadsReportColumnEnum::TOTAL_SOLD_LEADS->keyValue()} ?
                    round($county->{LeadsReportColumnEnum::TOTAL_SOLD_LEGS->keyValue()} / $county->{LeadsReportColumnEnum::TOTAL_SOLD_LEADS->keyValue()}, 2) : 0,
                CountyCoverageReportColumnEnum::RAW_TO_GTS_RATIO->value => $county->{LeadsReportColumnEnum::TOTAL_LEADS->keyValue()} ?
                    round($county->{LeadsReportColumnEnum::TOTAL_GTS_LEADS->keyValue()} / $county->{LeadsReportColumnEnum::TOTAL_LEADS->keyValue()}, 2) : 0,
                CountyCoverageReportColumnEnum::GTS_TO_SOLD_RATIO->value => $county->{LeadsReportColumnEnum::TOTAL_GTS_LEADS->keyValue()} ?
                    round($county->{LeadsReportColumnEnum::TOTAL_SOLD_LEADS->keyValue()} / $county->{LeadsReportColumnEnum::TOTAL_GTS_LEADS->keyValue()}, 2) : 0,

                CountyCoverageReportColumnEnum::REVENUE_PER_RAW_LEAD->value => $county->{LeadsReportColumnEnum::TOTAL_LEADS->keyValue()} ?
                    round($county->{LeadsReportColumnEnum::TOTAL_REVENUE->keyValue()} / $county->{LeadsReportColumnEnum::TOTAL_LEADS->keyValue()}, 2) : 0,
                CountyCoverageReportColumnEnum::REVENUE_PER_GTS_LEAD->value => $county->{LeadsReportColumnEnum::TOTAL_GTS_LEADS->keyValue()} ?
                    round($county->{LeadsReportColumnEnum::TOTAL_REVENUE->keyValue()} / $county->{LeadsReportColumnEnum::TOTAL_GTS_LEADS->keyValue()}, 2) : 0,
                CountyCoverageReportColumnEnum::REVENUE_PER_SOLD_LEAD->value => $county->{LeadsReportColumnEnum::TOTAL_SOLD_LEADS->keyValue()} ?
                    round($county->{LeadsReportColumnEnum::TOTAL_REVENUE->keyValue()} / $county->{LeadsReportColumnEnum::TOTAL_SOLD_LEADS->keyValue()}, 2) : 0,
                CountyCoverageReportColumnEnum::REVENUE_PER_SOLD_LEG->value => $county->{LeadsReportColumnEnum::TOTAL_SOLD_LEGS->keyValue()} ?
                    round($county->{LeadsReportColumnEnum::TOTAL_REVENUE->keyValue()} / $county->{LeadsReportColumnEnum::TOTAL_SOLD_LEGS->keyValue()}, 2) : 0,

                CountyCoverageReportColumnEnum::GOOGLE_AD_COST_PER_RAW_LEAD->value => $county->{LeadsReportColumnEnum::GOOGLE_RAW_LEADS->keyValue()} ?
                    round($county->{LeadsReportColumnEnum::GOOGLE_AD_COST->keyValue()} / $county->{LeadsReportColumnEnum::GOOGLE_RAW_LEADS->keyValue()}, 2) : 0,
                CountyCoverageReportColumnEnum::GOOGLE_AD_COST_PER_GTS_LEAD->value => $county->{LeadsReportColumnEnum::GOOGLE_GTS_LEADS->keyValue()} ?
                    round($county->{LeadsReportColumnEnum::GOOGLE_AD_COST->keyValue()} / $county->{LeadsReportColumnEnum::GOOGLE_GTS_LEADS->keyValue()}, 2) : 0,
                CountyCoverageReportColumnEnum::GOOGLE_AD_COST_PER_SOLD_LEAD->value => $county->{LeadsReportColumnEnum::GOOGLE_SOLD_LEADS->keyValue()} ?
                    round($county->{LeadsReportColumnEnum::GOOGLE_AD_COST->keyValue()} / $county->{LeadsReportColumnEnum::GOOGLE_SOLD_LEADS->keyValue()}, 2) : 0,
                CountyCoverageReportColumnEnum::GOOGLE_AD_COST_PER_SOLD_LEG->value => $county->{LeadsReportColumnEnum::GOOGLE_SOLD_LEGS->keyValue()} ?
                    round($county->{LeadsReportColumnEnum::GOOGLE_AD_COST->keyValue()} / $county->{LeadsReportColumnEnum::GOOGLE_SOLD_LEGS->keyValue()}, 2) : 0,
                CountyCoverageReportColumnEnum::GOOGLE_REVENUE_PER_RAW_LEAD->value => $county->{LeadsReportColumnEnum::GOOGLE_RAW_LEADS->keyValue()} ?
                    round($county->{LeadsReportColumnEnum::GOOGLE_REVENUE->keyValue()} / $county->{LeadsReportColumnEnum::GOOGLE_RAW_LEADS->keyValue()}, 2) : 0,
                CountyCoverageReportColumnEnum::GOOGLE_REVENUE_PER_SMS_VERIFIED_LEAD->value => $county->{LeadsReportColumnEnum::GOOGLE_SMS_VERIFIED_LEADS->keyValue()} ?
                    round($county->{LeadsReportColumnEnum::GOOGLE_SMS_VERIFIED_REVENUE->keyValue()} / $county->{LeadsReportColumnEnum::GOOGLE_SMS_VERIFIED_LEADS->keyValue()}, 2) : 0,
            ]);
            $data[ReportData::CREATED_AT] = Carbon::now();
            $data[ReportData::UPDATED_AT] = Carbon::now();
            return $data;
        });

        return $calculatedData;
    }
}
