<?php

namespace App\Services\Search\Searchables;

use App\Contracts\Search\SearchableContract;
use App\DataModels\Search\SearchResultDataModel;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\PermissionType;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Company;
use App\Models\Odin\Address;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CompanySearchableService implements SearchableContract
{

    const COMPANY_RELATION_ADDRESSES = Company::RELATION_LOCATIONS.'.'.CompanyLocation::RELATION_ADDRESS;

    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::COMPANY->value);
    }

    public function getCategory(): string
    {
        return 'Companies';
    }
    public function search(string $query): Collection
    {
        return Company::query()
            ->with([Company::RELATION_USERS, Company::RELATION_LOCATIONS])
            ->where(Company::FIELD_ID, '=', $query)
            ->orWhere(Company::FIELD_NAME, 'LIKE', "%{$query}%")
            ->orWhereHas(
                self::COMPANY_RELATION_ADDRESSES,
                fn(Builder $builder) => $builder
                    ->where(Address::FIELD_ADDRESS_1, 'LIKE', "%{$query}%")
                    ->orWhere(Address::FIELD_ADDRESS_2, 'LIKE', "%{$query}%")
                    ->orWhere(Address::FIELD_CITY, 'LIKE', "{$query}%")
            )
            ->orWhereHas(
                Company::RELATION_USERS,
                fn(Builder $builder) => $builder
                    // Clean up phone numbers to remove leading country code and non-digits
                    ->where(CompanyUser::FIELD_EMAIL, 'LIKE', "{$query}%")
                    ->orWhere(DB::raw('REGEXP_REPLACE(TRIM('.CompanyUser::FIELD_CELL_PHONE.'), "^\+1|^1|[^0-9]*", "")'), $query)
                    ->orWhere(DB::raw('REGEXP_REPLACE(TRIM('.CompanyUser::FIELD_OFFICE_PHONE.'), "^\+1|^1|[^0-9]*", "")'), $query)
            )
            ->limit(5)
            ->get()
            ->map(fn(Company $company) => new SearchResultDataModel(
                "{$company->{Company::FIELD_ID}}",
                "{$company->{Company::FIELD_ID}}: {$company->{Company::FIELD_NAME}} ("
                    . (CompanyConsolidatedStatus::label($company->consolidated_status))
                    . "/" . ($company->accountManager?->name ?? 'No Acc. Manager')
                    . ")",
                '/companies/' . $company->{Company::FIELD_ID},
                false
            ));
    }
}
