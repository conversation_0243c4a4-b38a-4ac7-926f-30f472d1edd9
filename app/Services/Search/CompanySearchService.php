<?php

namespace App\Services\Search;

use App\Contracts\Search\SearchableContract;
use App\DataModels\Search\SearchCategoryDataModel;
use App\DataModels\Search\SearchResultDataModel;
use Illuminate\Support\Collection;

class CompanySearchService
{
    /**
     * @param Collection<SearchableContract> $searchables
     */
    public function __construct(protected Collection $searchables) {}

    /**
     * @param string $query
     * @return Collection<Collection<SearchResultDataModel>>
     */
    public function search(string $query): Collection
    {
        return $this->searchables
            ->map(fn(SearchableContract $searchable) => new SearchCategoryDataModel($searchable->getCategory(), $searchable->search($query)));
    }
}
