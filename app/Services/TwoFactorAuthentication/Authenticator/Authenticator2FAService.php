<?php

namespace App\Services\TwoFactorAuthentication\Authenticator;

use App\Contracts\TwoFactorAuthentication\AuthenticatorTwoFactorAuthenticationContract;
use App\Models\User;
use PragmaRX\Google2FA\Exceptions\IncompatibleWithGoogleAuthenticatorException;
use PragmaRX\Google2FA\Exceptions\InvalidCharactersException;
use PragmaRX\Google2FA\Exceptions\SecretKeyTooShortException;
use PragmaRX\Google2FA\Google2FA;

class Authenticator2FAService implements AuthenticatorTwoFactorAuthenticationContract
{
    public function __construct(protected Google2FA $authenticator) {}

    /**
     * Returns the URL for signing this 2FA. Used to generate a QR Code.
     *
     * @param User $user
     * @return string
     */
    public function getSigningUrl(User $user): string
    {
        if($user->two_factor_auth_secret_key === null)
            throw new \RuntimeException("User '{$user->name}' does not have a 2FA signing key.");

        return $this->authenticator->getQRCodeUrl(
            "SolarReviews - Admin 2",
            $user->name,
            $user->two_factor_auth_secret_key
        );
    }

    /**
     * Handles generating a secret key for the user authenticating.
     *
     * @param User $user
     * @return string
     * @throws IncompatibleWithGoogleAuthenticatorException
     * @throws InvalidCharactersException
     * @throws SecretKeyTooShortException
     */
    public function generateSecretKey(User $user): string
    {
        return $this->authenticator->generateSecretKey();
    }

    /**
     * Verifies if the provided code matches the internal service.
     *
     * @param User $user
     * @param string $key
     * @return bool
     * @throws IncompatibleWithGoogleAuthenticatorException
     * @throws InvalidCharactersException
     * @throws SecretKeyTooShortException
     */
    public function verifyKey(User $user, string $key): bool
    {
        if($user->two_factor_auth_secret_key === null)
            throw new \RuntimeException("User '{$user->name}' does not have a 2FA signing key.");

        return $this->authenticator->verifyKey($user->two_factor_auth_secret_key, $key);
    }
}
