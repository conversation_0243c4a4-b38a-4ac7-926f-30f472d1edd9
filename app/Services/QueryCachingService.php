<?php

namespace App\Services;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class QueryCachingService
{

    /**
     * @param Builder $query
     * @param int $ttlMinutes
     * @param bool $serveStale
     * @return Collection
     */
    public static function getCachedResults(Builder $query, int $ttlMinutes = 60, bool $serveStale = true): Collection
    {
        $key = base64_encode(md5($query->toSql() . implode('', $query->getBindings()), true));

        if ($serveStale) {
            return cache()->flexible($key, [$ttlMinutes * 60, $ttlMinutes * 60 * 10], function () use ($query) {
                return $query->get();
            });
        }else{
            return cache()->remember($key, now()->addMinutes($ttlMinutes), function () use ($query) {
                return $query->get();
            });
        }
    }
}
