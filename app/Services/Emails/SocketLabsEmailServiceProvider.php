<?php

namespace App\Services\Emails;


use App\DTO\EmailService\DomainDTO;
use App\DTO\EmailService\OutgoingEmailDTO;
use App\Enums\Emails\DomainStatus;
use App\Exceptions\MarketingCampaign\EmailBatchSendFailedException;
use App\Mail\EmailTemplateRenderer;
use App\Services\EmailTemplates\EmailTemplateImageService;
use App\Services\Shortcode\ShortcodeImplementation\EmailMarketingShortcodeUseCase;
use App\Services\Shortcode\ShortcodeReplacerService;
use App\Services\Shortcode\Shortcodes\DynamicMarketingCampaignConsumerCallbackUrlShortcode;
use Carbon\Carbon;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Socketlabs\AddressResult;
use Socketlabs\Message\BasicMessage;
use Socketlabs\Message\BulkMessage;
use Socketlabs\Message\EmailAddress;
use Socketlabs\SendResult;
use Socketlabs\SocketLabsClient;
use Throwable;

class SocketLabsEmailServiceProvider implements EmailServiceProviderContract
{
    const string DOMAIN_SOLAR_ESTIMATE_ORG = 'solar-estimate.org';
    const string DOMAIN_BATHROOM_ESTIMATE_ORG = 'bathroom-estimate.org';
    const string DOMAIN_KITCHEN_ESTIMATE_ORG = 'kitchen-estimate.org';
    const string DOMAIN_ROOFINGCALCULATOR_COM = 'roofingcalculator.com';
    const string DOMAIN_SOLARREVIEWS_COM = 'solarreviews.com';
    const array HARDCODED_DOMAINS = [
        self::DOMAIN_SOLAR_ESTIMATE_ORG,
        self::DOMAIN_BATHROOM_ESTIMATE_ORG,
        self::DOMAIN_KITCHEN_ESTIMATE_ORG,
        self::DOMAIN_ROOFINGCALCULATOR_COM,
        self::DOMAIN_SOLARREVIEWS_COM,
    ];

    const array OTHER_SERVER_DOMAINS = [
        self::DOMAIN_SOLAR_ESTIMATE_ORG,
        self::DOMAIN_BATHROOM_ESTIMATE_ORG,
        self::DOMAIN_KITCHEN_ESTIMATE_ORG,
        self::DOMAIN_ROOFINGCALCULATOR_COM,
        self::DOMAIN_SOLARREVIEWS_COM,
    ];
    const string UNSUBSCRIBE = "
        <span style='font-size: 0.75em;'>
            <HsUnsubscribe>Unsubscribe</HsUnsubscribe>
        </span>";

    const string SOCKET_LABS_UNSUBSCRIBE_FOOTER = "\n<!-- SOCKET_LABS_UNSUBSCRIBE_FOOTER -->";
    protected SocketLabsClient $socketLabsClient;
    protected string           $apiKey;
    protected string           $server;

    public function __construct(
        protected Client $client,
        protected ShortcodeReplacerService $replacerService,
        protected EmailTemplateImageService $emailTemplateImageService,
    )
    {
        $this->apiKey = config('services.marketing.email.socket_labs.api_key');

        $this->server = config('services.marketing.email.socket_labs.server');

        $this->socketLabsClient = new SocketLabsClient($this->server, $this->apiKey);
    }

    public function send(
        string $toEmail,
        string $fromEmail,
        string $subject,
        string $body,
        string $fromUserName,
    ): void
    {
        $message = new BasicMessage();

        $message->subject  = $subject;
        $message->htmlBody = $body;
        $message->plainTextBody = $body;

        $message->from     = new EmailAddress($fromEmail);
        $message->addToAddress($toEmail);

        $response = $this->socketLabsClient->send($message);
    }

    /**
     * @inheritDoc
     */
    public function batchSend(
        Collection $outgoingEmails,
        string $subject,
        string $body,
        string $fromEmail,
        string $fromName
    ): Collection
    {
        return collect();
    }


    /**
     * @param Collection<OutgoingEmailDTO> $outgoingEmails
     * @param string $subject
     * @param string $body
     * @param string $fromEmail
     * @param string $fromName
     * @param string|null $header
     * @param string|null $footer
     * @param int|null $templateId
     * @param int|null $backgroundId
     * @return bool
     * @throws BindingResolutionException|EmailBatchSendFailedException|Throwable
     */
    public function rawBatchSend(
        Collection $outgoingEmails,
        string $subject,
        string $body,
        string     $fromEmail,
        string     $fromName,
        ?string $header = null,
        ?string $footer = null,
        ?int $templateId = null,
        ?int $backgroundId = null,
    ): bool
    {
        $body = Str::markdown($body);
        $header = Str::markdown($header);

        $newShortcodeFormatMapped = collect([
            ...$this->replacerService->getAllShortcodes($subject),
            ...$this->replacerService->getAllShortcodes($body),
            ...$this->replacerService->getAllShortcodes($header ?? ''),
            ...$this->replacerService->getAllShortcodes($footer ?? ''),
        ])->mapWithKeys(function (string $shortcode) {
            return [$shortcode => '%%' . str_replace(' ', '', ucwords(str_replace(['.', '_'], ' ', $shortcode))) . '%%'];
        });

        $message = new BulkMessage();

        $message->from = new EmailAddress($fromEmail, $fromName);

        foreach ($outgoingEmails as $outgoingEmail) {
            $shortcodes = collect($outgoingEmail->getShortcodes());

            $recipient = $message->addToAddress($outgoingEmail->getToEmail());

            if ($newShortcodeFormatMapped->isNotEmpty()) {
                $socketShortcodes = $shortcodes->mapWithKeys(function ($value, $key) use ($newShortcodeFormatMapped) {
                    return [$newShortcodeFormatMapped[$key] => $value];
                });

                foreach ($socketShortcodes as $key => $value) {
                    $recipient->addMergeData(str_replace('%', '', $key), $value);
                }
            }
        }

        $processedSubject = $this->replacerService->process($subject, $newShortcodeFormatMapped->toArray());

        $processedBody = $this->replacerService->process($body, $newShortcodeFormatMapped->toArray());

        $processedHeader = $this->replacerService->process($header, $newShortcodeFormatMapped->toArray());

        $processedFooter = $this->replacerService->process($footer, $newShortcodeFormatMapped->toArray()) . self::SOCKET_LABS_UNSUBSCRIBE_FOOTER;

        $processedHeader  = filled($backgroundId) ? $this->emailTemplateImageService->shortcodesToUrlImageTags($processedHeader, $backgroundId) : $processedHeader;
        $processedBody = filled($templateId) ? $this->emailTemplateImageService->shortcodesToUrlImageTags($processedBody, $templateId) : $processedBody;

        $emailBody = app()->makeWith(EmailTemplateRenderer::class, [
            'header'  => $processedHeader,
            'footer' => $processedFooter,
            'content' => $processedBody,
        ])->preview();

        $message->subject  = $processedSubject;

        $message->htmlBody = str_replace(self::SOCKET_LABS_UNSUBSCRIBE_FOOTER,self::UNSUBSCRIBE, $emailBody);
        $message->plainTextBody = $body;

        $response = $this->socketLabsClient->send($message);

        if ($response->result === SendResult::Success) {
            return true;
        } else {
            $addressResults = collect($response->addressResults);

            $data = $addressResults->map(function (AddressResult $result) {
                return [
                    'email_address' => $result->emailAddress,
                    'error' => $result->errorCode,
                ];
            })->toArray();

            throw new EmailBatchSendFailedException(
                $response->responseMessage,
                $data
            );
        }
    }

    /**
     * @param Collection|null $statuses
     * @param array|null $excludedDomains
     * @inheritDoc
     * @throws GuzzleException
     */
    public function listSendingDomains(
        ?Collection $statuses = null,
        ?array $excludedDomains = []
    ): Collection
    {
        $res = $this->client->get("https://api.socketlabs.com/v2/servers/$this->server/sending-domains", [
            'headers' => [
                'Authorization' => "Bearer $this->apiKey"
            ],
        ]);

        $response = json_decode($res->getBody()->getContents(), true);

        return collect(Arr::get($response, 'data'))->map(function (array $domain) use ($excludedDomains) {
            if (in_array(Arr::get($domain, 'domain'), $excludedDomains)) {
                return null;
            }

            return new DomainDTO(
                name: Arr::get($domain, 'domain'),
                status: DomainStatus::VERIFIED,
                createdAt: Carbon::parse(Arr::get($domain, 'createdOn')),
            );
        })->merge(collect(self::HARDCODED_DOMAINS)->map(fn(string $domain) => new DomainDTO(name: $domain, status: DomainStatus::VERIFIED)))->filter();
    }

    public function rawSend(
        OutgoingEmailDTO $outgoingEmail,
        string $subject,
        string $body,
        string $fromEmail,
        string $fromName,
        ?string $header = null,
        ?string $footer = null,
        ?int $templateId = null,
        ?int $backgroundId = null,
    ): bool
    {
        $body = Str::markdown($body);
        $header = Str::markdown($header);

        $newShortcodeFormatMapped = collect([
            ...$this->replacerService->getAllShortcodes($subject),
            ...$this->replacerService->getAllShortcodes($body),
            ...$this->replacerService->getAllShortcodes($header ?? ''),
            ...$this->replacerService->getAllShortcodes($footer ?? ''),
        ])->mapWithKeys(function (string $shortcode) {
            return [$shortcode => '%%' . str_replace(' ', '', ucwords(str_replace(['.', '_'], ' ', $shortcode))) . '%%'];
        });

        $message = new BulkMessage();

        $message->from = new EmailAddress($fromEmail, $fromName);

        $shortcodes = collect($outgoingEmail->getShortcodes());

        $dynamicCallback = new DynamicMarketingCampaignConsumerCallbackUrlShortcode();

        $dynamicShortcodeKey = EmailMarketingShortcodeUseCase::MARKETING_CAMPAIGN_CONSUMER .'.'. $dynamicCallback->getKey();

        if ($shortcodes->has($dynamicShortcodeKey)) {
            $this->matchLinkDomainToEmail($shortcodes, $dynamicShortcodeKey, $outgoingEmail);
        }

        $recipient = $message->addToAddress($outgoingEmail->getToEmail());

        if ($newShortcodeFormatMapped->isNotEmpty()) {
            $socketShortcodes = $shortcodes->mapWithKeys(function ($value, $key) use ($newShortcodeFormatMapped) {
                return [$newShortcodeFormatMapped[$key] => $value];
            });

            foreach ($socketShortcodes as $key => $value) {
                $recipient->addMergeData(str_replace('%', '', $key), $value);
            }
        }

        $processedSubject = $this->replacerService->process($subject, $newShortcodeFormatMapped->toArray());

        $processedBody = $this->replacerService->process($body, $newShortcodeFormatMapped->toArray());

        $processedHeader = $this->replacerService->process($header, $newShortcodeFormatMapped->toArray());

        $processedFooter = $this->replacerService->process($footer, $newShortcodeFormatMapped->toArray()) . self::SOCKET_LABS_UNSUBSCRIBE_FOOTER;

        $processedHeader  = filled($backgroundId) ? $this->emailTemplateImageService->shortcodesToUrlImageTags($processedHeader, $backgroundId) : $processedHeader;
        $processedBody = filled($templateId) ? $this->emailTemplateImageService->shortcodesToUrlImageTags($processedBody, $templateId) : $processedBody;

        $emailBody = app()->makeWith(EmailTemplateRenderer::class, [
            'header'  => $processedHeader,
            'footer' => $processedFooter,
            'content' => $processedBody,
        ])->preview();

        $message->subject  = $processedSubject;

        $meta = $outgoingEmail->getEmailMeta();

        if (filled($meta)) {
            foreach ($meta as $key => $value) {
                $message->addMetadata($key, (string)$value);
            }
        }

        $message->htmlBody = str_replace(self::SOCKET_LABS_UNSUBSCRIBE_FOOTER,self::UNSUBSCRIBE, $emailBody);
        $message->plainTextBody = $body;
        $domain = Str::of($outgoingEmail->getFromEmail())->after('@');

        $serverId = $this->server;
        $apiKey = $this->apiKey;

        if(in_array($domain, self::OTHER_SERVER_DOMAINS)) {
            $serverId = config('services.marketing.email.socket_labs_alt.server');
            $apiKey = config('services.marketing.email.socket_labs_alt.api_key');
        }

        $client = new SocketLabsClient($serverId, $apiKey);

        $response = $client->send($message);

        if ($response->result === SendResult::Success) {
            return true;
        } else {
            $addressResults = collect($response->addressResults);

            $data = $addressResults->map(function (AddressResult $result) {
                return [
                    'email_address' => $result->emailAddress,
                    'error' => $result->errorCode,
                ];
            })->toArray();

            throw new EmailBatchSendFailedException(
                $response->responseMessage,
                $data
            );
        }
    }

    protected function matchLinkDomainToEmail(Collection $shortcodes, string $targetShortcodeKey, OutgoingEmailDTO $emailDTO)
    {
        // Parse the original URL
        $parsed = parse_url($shortcodes->get($targetShortcodeKey));

        // Rebuild path + query
        $path = $parsed['path'] ?? '';
        $query = isset($parsed['query']) ? '?' . $parsed['query'] : '';

        $fromEmail = $emailDTO->getFromEmail();

        $fromEmailDomain = Str::of($fromEmail)->after('@');

        // Replace with new base
        $newUrl = 'https://www.' . $fromEmailDomain . $path . $query;

        // Update the collection
        $shortcodes->put($targetShortcodeKey, $newUrl);
    }
}

