<?php

namespace App\Services\MissedProducts\Reasons;

use App\Enums\MissedProducts\MissedProductCategory;
use App\Enums\MissedProducts\MissedProductReasonEventType;

class OutbidReason extends BaseMissedProductReason
{
    const string KEY = 'outbid';
    const string SUMMARY = 'competitors bid higher';

    public function getKey(): string
    {
        return self::KEY;
    }

    public function getMissedProductEventType(): ?MissedProductReasonEventType
    {
        return null;
    }

    public function doesImpact(): bool
    {
        return false;
    }

    public function getTitle(): string
    {
        return 'Getting outbid';
    }

    public function getSummaryText(): string
    {
        return self::SUMMARY;
    }

    public function getMessage(): string
    {
        return "Other contractors have added higher bids for specific lead types (exclusive, duo) in your service areas.";
    }

    public function getCategory(): MissedProductCategory
    {
        return MissedProductCategory::BIDDING;
    }
}
