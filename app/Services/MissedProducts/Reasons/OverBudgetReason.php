<?php

namespace App\Services\MissedProducts\Reasons;

use App\Contracts\MissedProducts\MissedProductReasonContract;
use App\Enums\MissedProducts\MissedProductCategory;
use App\Enums\MissedProducts\MissedProductReasonEventType;
use App\Models\MissedProducts\MissedProductReasonEvent;
use Carbon\Carbon;

class OverBudgetReason extends BaseMissedProductReason
{
    protected ?int $daysOverBudget = null;

    public function getKey(): string
    {
        return 'over_budget';
    }

    public function getMissedProductEventType(): ?MissedProductReasonEventType
    {
        return MissedProductReasonEventType::OVER_BUDGET;
    }

    public function doesImpact(): bool
    {
        return $this->getDaysOverBudget() > 0;
    }

    public function getTitle(): string
    {
        return 'Budget too low';
    }

    public function getSummaryText(): string
    {
        return 'the campaign was over budget';
    }

    public function getMessage(): string
    {
        $days = $this->getDaysOverBudget() != 1 ? 'days' : 'day';

        return "Your budget has restricted the amount of leads you received on {$this->getDaysOverBudget()} {$days} in the last {$this->getRangeInDays()}.";
    }

    public function getCategory(): MissedProductCategory
    {
        return MissedProductCategory::CAMPAIGNS;
    }

    public function setFromDate(Carbon $date): MissedProductReasonContract
    {
        $this->daysOverBudget = null;

        return parent::setFromDate($date);
    }

    protected function getDaysOverBudget(): int
    {
        if ($this->daysOverBudget === null) {
            $overBudgetTimeline = $this->getTimeline();
            $days = $overBudgetTimeline->reduce(function (int $output, MissedProductReasonEvent $event) {
                $fromDate = $event->started_at
                    ? max($event->started_at, $this->fromDate)
                    : $this->fromDate;
                $toDate = $event->ended_at ?? now();
                return $output + round($toDate->diffInHours($fromDate, true) / 24);
            }, 0);

            $this->daysOverBudget = min($this->getRangeInDays(), $days);
        }

        return $this->daysOverBudget;
    }
}
