<?php

namespace App\Services\EmailTemplates\TemplateOptions;

use App\Enums\EmailTemplateType;

class EmailTemplateOptionsFactory
{
    /**
     * @param EmailTemplateType $type
     * @return InvoicingEmailTemplateOptions|null
     */
    public static function make(EmailTemplateType $type): ?InvoicingEmailTemplateOptions
    {
        return match ($type) {
            InvoicingEmailTemplateOptions::getType() => new InvoicingEmailTemplateOptions(),
            default                                  => null
        };
    }

}
