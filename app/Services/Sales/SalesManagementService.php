<?php

namespace App\Services\Sales;

use App\Enums\RoleType;
use App\Enums\RoundRobinType;
use App\Models\RoundRobin;
use App\Models\RoundRobinParticipant;
use App\Models\User;
use App\Repositories\RoundRobins\RoundRobinRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class SalesManagementService
{
    /**
     * @return Collection
     */
    public function getAccountAssignmentParticipantsQuery(): Collection
    {
        $roundRobin = $this->getAccountAssignmentRoundRobin();
        $selectColumns = [
            User::TABLE .'.'. User::FIELD_ID,
            User::FIELD_NAME,
            RoundRobinParticipant::FIELD_ROUND_ROBIN_ID,
        ];

        $participants = User::query()
            ->select($selectColumns)
            ->leftJoin(RoundRobinParticipant::TABLE, fn(JoinClause $join) =>
                $join->on(RoundRobinParticipant::TABLE .'.'. RoundRobinParticipant::FIELD_USER_ID, User::TABLE .'.'. User::FIELD_ID)
                    ->where(RoundRobinParticipant::FIELD_ROUND_ROBIN_ID, $roundRobin->id)
            )->where(function ($query) {
                $query->whereHas(User::RELATION_ROLES, fn(Builder $query) => $query->where('name', RoleType::ACCOUNT_MANAGER->value))
                    ->orWhereNotNull(RoundRobinParticipant::FIELD_ROUND_ROBIN_ID);
            })->get();

        return $participants->sortBy([RoundRobinParticipant::FIELD_ROUND_ROBIN_ID, User::FIELD_NAME]);
    }

    /**
     * @param array $addUserIds
     * @param array $removeUserIds
     * @return bool
     */
    public function updateAccountAssignmentParticipants(array $addUserIds, array $removeUserIds): bool
    {
        try {
            DB::beginTransaction();
            /** @var RoundRobinRepository $repository */
            $repository = app(RoundRobinRepository::class);
            if ($addUserIds)
                $repository->addParticipants(RoundRobinType::ACCOUNT_ASSIGNMENT, $addUserIds);
            if ($removeUserIds)
                $repository->removeParticipants(RoundRobinType::ACCOUNT_ASSIGNMENT, $removeUserIds);

            DB::commit();
            return true;
        }
        catch (Throwable $e) {
            logger()->error("Error creating RoundRobinParticipants: " . $e->getMessage());
            DB::rollBack();

            return false;
        }
    }

    /**
     * @return RoundRobin
     */
    protected function getAccountAssignmentRoundRobin(): RoundRobin
    {
        $roundRobinRepository = app(RoundRobinRepository::class);

        return $roundRobinRepository->getRoundRobin(RoundRobinType::ACCOUNT_ASSIGNMENT);
    }
}