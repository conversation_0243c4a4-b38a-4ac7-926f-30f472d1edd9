<?php

namespace App\Services\OutreachCadence;

use App\Models\Cadence\CompanyCadenceScheduledGroup;
use App\Models\Cadence\CompanyCadenceScheduledGroupAction;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use App\Repositories\OutreachCadence\CadenceUserContactRepository;
use Exception;
use Illuminate\Support\Collection;

class ActionLogger
{

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @return string|null
     */
    private static function getCommonIssues(CompanyCadenceScheduledGroupAction $action): ?string
    {
        $routine = $action->group?->routine;

        if ($routine === null) {
            return 'routine no longer exists';
        }

        if (empty($routine->company)) {
            return 'no company assigned to routine';
        }

        if (empty($routine->company->accountManager)) {
            return 'no account manager assigned to company';
        }

        if($routine->company->industries->isEmpty()) {
            return 'no industries assigned for company - assign an industry';
        }

        return null;
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @param string $message
     * @param array $data
     * @return void
     */
    private static function addFailedResolutionNote(CompanyCadenceScheduledGroupAction $action, string $message, array $data = []): void
    {
        $action->addResolutionNote("{$action->action_type} failed - {$message}", $data);
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @param string $message
     * @param array $data
     * @return void
     */
    private static function addSuccessResolutionNote(CompanyCadenceScheduledGroupAction $action, string $message, array $data = []): void
    {
        $action->addResolutionNote("{$action->action_type} success - {$message}", $data);
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @return User|null
     */
    private static function getCommunicationUser(CompanyCadenceScheduledGroupAction $action): ?User
    {
        /** @var CadenceUserService $userService */
        $userService = app(CadenceUserService::class);
        return $userService->getCommunicationUser($action->group->routine);
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @return void
     */
    public static function noManagerEmail(CompanyCadenceScheduledGroupAction $action): void
    {
        $commonIssue = self::getCommonIssues($action);
        if ($commonIssue) {
            self::addFailedResolutionNote($action, $commonIssue);
            return;
        }

        $user = self::getCommunicationUser($action);
        self::addFailedResolutionNote($action, "specified manager's user email not found - add valid email address for user: {$user?->name}($user?->id)");
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @return void
     */
    public static function noAvailableContacts(CompanyCadenceScheduledGroupAction $action): void
    {
        $message = "no valid contacts found" . self::getAdditionalNoContactsDetails($action);
        self::addFailedResolutionNote($action, $message);
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @return string
     */
    private static function getAdditionalNoContactsDetails(CompanyCadenceScheduledGroupAction $action): string
    {
        $company = $action->group->routine->company;

        // no contacts
        $allCompanyUsers = self::getAllCompanyUsers($company);
        if ($allCompanyUsers->count() < 1)
            return " - company has no users or contacts - create some contacts";

        // no active contacts
        $activeUsers = $allCompanyUsers->where(CompanyUser::FIELD_STATUS, CompanyUser::STATUS_ACTIVE);
        if ($activeUsers->count() < 1)
            return " - all users and contacts are inactive - set contact status to 'enabled'";

        // all active contacts are excluded from cadence
        /** @var CadenceUserContactRepository $cadenceContactRepository */
        $cadenceContactRepository = app(CadenceUserContactRepository::class);
        $excludedUserIds          = $cadenceContactRepository->getExcludedCompanyUsers($action->group->routine)->pluck(CompanyUser::FIELD_ID)->toArray();
        $nonExcludedActiveUsers   = $activeUsers->filter(function (CompanyUser $user) use ($excludedUserIds) {
            return !in_array($user->id, $excludedUserIds);
        });
        if ($nonExcludedActiveUsers->count() < 1)
            return " - all available contacts have been explicitly excluded from this cadence routine - update cadence contact configurations";

        // decision makers only
        if ($action->group->routine->contact_decision_makers_only) {
            // none of the available contacts are decision makers
            $decisionMakers = $nonExcludedActiveUsers->where(CompanyUser::FIELD_IS_DECISION_MAKER, true);
            if ($decisionMakers->count() < 1)
                return " - none of the available contacts are decision makers - promote an active contact to decision maker";
        }

        return "";
    }

    /**
     * @param Company $company
     * @return Collection<int, CompanyUser>
     */
    private static function getAllCompanyUsers(Company $company): Collection
    {
        return CompanyUser::query()->where(CompanyUser::FIELD_COMPANY_ID, $company->id)->get();
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @param CompanyUser $companyUser
     * @return void
     */
    public static function noContactEmail(CompanyCadenceScheduledGroupAction $action, CompanyUser $companyUser): void
    {
        self::addFailedResolutionNote($action, "no email found for {$companyUser->first_name} {$companyUser->last_name}({$companyUser->id}) - add a valid email address to contact");
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @param string $message
     * @param array $data
     * @return void
     */
    public static function success(CompanyCadenceScheduledGroupAction $action, string $message, array $data = []): void
    {
        self::addSuccessResolutionNote($action, $message, $data);
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @param string $message
     * @param Exception $exception
     * @return void
     */
    public static function unexpectedError(CompanyCadenceScheduledGroupAction $action, string $message, Exception $exception): void
    {
        self::addFailedResolutionNote($action, "{$message} - unhandled error - contact development", [
            'message' => $exception->getMessage(),
            'trace'   => $exception->getTrace(),
        ]);
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @return void
     */
    public static function noManagerPhone(CompanyCadenceScheduledGroupAction $action): void
    {
        $commonIssue = self::getCommonIssues($action);
        if ($commonIssue) {
            self::addFailedResolutionNote($action, $commonIssue);
            return;
        }

        $user = self::getCommunicationUser($action);
        self::addFailedResolutionNote($action, "specified manager's user phone not found - assign a phone number to user: {$user?->name}($user?->id)");
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @param CompanyUser $companyUser
     * @return void
     */
    public static function noContactPhone(CompanyCadenceScheduledGroupAction $action, CompanyUser $companyUser): void
    {
        self::addFailedResolutionNote($action, "no phone found for {$companyUser->first_name} {$companyUser->last_name}({$companyUser->id}) - add a valid phone number to contact");
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @param string $message
     * @param array $data
     * @return void
     */
    public static function genericLog(CompanyCadenceScheduledGroupAction $action, string $message, array $data = []): void
    {
        $action->addResolutionNote($message, $data);
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @return void
     */
    public static function routineDeleted(CompanyCadenceScheduledGroupAction $action): void
    {
        self::genericLog($action, "routine no longer exists");
    }

    /**
     * @param CompanyCadenceScheduledGroup $group
     * @return void
     */
    public static function groupSkipped(CompanyCadenceScheduledGroup $group): void
    {
        $group->addResolutionNote("group manually skipped by user");
    }

    public static function logCommonDeliveryIssues(CompanyCadenceScheduledGroupAction $action): bool
    {
        $commonIssue = self::getCommonIssues($action);
        if ($commonIssue) {
            self::addFailedResolutionNote($action, $commonIssue);
            return true;
        }
        return false;
    }

}
