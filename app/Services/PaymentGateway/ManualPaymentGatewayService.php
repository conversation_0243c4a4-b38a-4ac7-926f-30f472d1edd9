<?php

namespace App\Services\PaymentGateway;

use App\Contracts\Services\PaymentGatewayServiceContract;
use App\DTO\Billing\PaymentMethods\PaymentMethodDTO;
use App\Enums\Billing\PaymentMethodServices;
use App\Enums\InvoiceRefundStatus;
use App\Jobs\ProcessBillingWebhook;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceRefund;
use App\Models\Billing\InvoiceRefundCharge;
use App\Models\Odin\Company;
use App\Services\Billing\PaymentGateway\Events\ChargeRefunded;
use App\Services\Billing\PaymentGateway\Events\ChargeSucceeded;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Stripe\Exception\ApiErrorException;

class ManualPaymentGatewayService implements PaymentGatewayServiceContract
{
    /**
     * @return PaymentMethodServices
     */
    public function getPaymentServiceProviderCode(): PaymentMethodServices
    {
        return PaymentMethodServices::MANUAL;
    }

    /**
     * @param string $invoicePaymentChargeUuid
     * @param string $invoiceUuid
     * @param int $total
     * @param array $metadata
     * @param string|null $description
     * @param string|null $customerCode
     * @param string|null $paymentMethod
     * @return bool
     */
    public function makeChargeRequest(
        string $invoicePaymentChargeUuid,
        string $invoiceUuid,
        int $total,
        array $metadata,
        ?string $description,
        ?string $customerCode,
        ?string $paymentMethod
    ): bool
    {
        $event = new ChargeSucceeded(
            externalTransactionId   : Str::uuid()->toString(),
            invoiceUuid             : $invoiceUuid,
            amount                  : $total,
            currency                : 'usd',
            source                  : 'a20',
            date                    : Arr::get($metadata, 'date', now()->toISOString()),
            invoicePaymentChargeUuid: $invoicePaymentChargeUuid,
        );

        ProcessBillingWebhook::dispatch($event);

        return true;
    }

    /**
     * @param string $invoiceRefundChargeUuid
     * @param int $total
     * @param string $chargeId
     * @param string $invoiceUuid
     * @return bool
     */
    public function makeRefundRequest(
        string $invoiceRefundChargeUuid,
        int $total,
        string $chargeId,
        string $invoiceUuid
    ): bool
    {
        // We need to calculate this to send the same as stripe
        $alreadyRefunded = InvoiceRefundCharge::query()
            ->whereHas(InvoiceRefundCharge::RELATION_INVOICE_REFUND, function ($query) use ($invoiceUuid) {
                $query->whereHas(InvoiceRefund::RELATION_INVOICE, function ($query) use ($invoiceUuid) {
                    $query->where(Invoice::FIELD_UUID, $invoiceUuid);
                });
            })
            ->where(InvoiceRefundCharge::FIELD_REQUEST_STATUS, InvoiceRefundStatus::REFUNDED)
            ->sum(InvoiceRefundCharge::FIELD_AMOUNT);

        $event = new ChargeRefunded(
            externalTransactionId: $chargeId,
            amount               : $alreadyRefunded,
            currency             : 'usd',
            amountRefunded       : $alreadyRefunded + $total,
            amountCaptured       : $alreadyRefunded,
            fullyRefunded        : true,
            source               : 'a20',
            reason               : '',
            invoiceUuid          : $invoiceUuid,
        );

        ProcessBillingWebhook::dispatch($event);

        return true;
    }

    /**
     * @param Company $company
     * @param string $email
     * @return string
     */
    public function createCustomer(Company $company, string $email): string
    {
        return '';
    }

    /**
     * @param string $customerCode
     * @param string $paymentMethod
     * @return void
     */
    public function addPaymentMethod(string $customerCode, string $paymentMethod): void
    {

    }


    /**
     * @throws ApiErrorException
     */
    public function createNewPaymentMethod(string $token): string
    {
        return '';
    }

    /**
     * @param string $customerCode
     * @return Collection<PaymentMethodDTO>
     * @throws ApiErrorException
     */
    public function getPaymentMethods(string $customerCode): Collection
    {
        return collect();
    }

    /**
     * @param string $customerCode
     * @param string $paymentMethod
     * @return void
     */
    public function setPaymentMethodPrimary(string $customerCode, string $paymentMethod): void
    {

    }

    /**
     * @param string $paymentMethod
     * @return void
     */
    public function deletePaymentMethod(string $paymentMethod): void
    {

    }
}
