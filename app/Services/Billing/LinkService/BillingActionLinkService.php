<?php

namespace App\Services\Billing\LinkService;

use App\Models\Billing\ActionApproval;

class BillingActionLinkService
{
    /**
     * Returns either full url or just path to billing action request
     *
     * @param ActionApproval $actionApproval
     * @param bool $full
     * @return string
     */
    public function getActionRequestLink(
        ActionApproval $actionApproval,
        bool $full = true,
    ): string
    {
        $id = $actionApproval->id;

        $path = "/billing-management?tab=Action+Requests&id=$id";

        return $full ? config('app.url') . $path : $path;
    }

}
