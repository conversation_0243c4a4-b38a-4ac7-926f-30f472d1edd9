<?php

namespace App\Services\Billing;

use App\DTO\Billing\CompanyPaymentProfile;
use App\Enums\Company\CompanyCampaignStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Jobs\Billing\ValidateCompanyPaymentMethodsExpiryJob;
use App\Models\Billing\CompanyPaymentMethod;
use App\Models\Legacy\EloquentCompanyPaymentProfile;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class CompanyPaymentMethodValidationService
{
    /**
     * @return void
     */
    public function validate(): void
    {
        CompanyPaymentMethod::query()
            ->select(CompanyPaymentMethod::TABLE . '.*')
            ->join(Company::TABLE, Company::TABLE . '.' . Company::FIELD_ID, CompanyPaymentMethod::TABLE . '.' . CompanyPaymentMethod::FIELD_COMPANY_ID)
            ->whereNotNull(CompanyPaymentMethod::TABLE . '.' . CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_CLIENT_CODE)
            ->where(function (Builder $query) {
                $query->whereIn(Company::TABLE . '.' . Company::FIELD_CONSOLIDATED_STATUS, [
                    CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS
                ])->orWhereIn(Company::TABLE . '.' . Company::FIELD_CAMPAIGN_STATUS, [
                    CompanyCampaignStatus::CAMPAIGNS_PAUSED
                ]);
            })
            ->groupBy(CompanyPaymentMethod::TABLE . '.' . CompanyPaymentMethod::FIELD_COMPANY_ID)
            ->chunk(100, fn(Collection $paymentMethods) => ValidateCompanyPaymentMethodsExpiryJob::dispatch($paymentMethods));
    }
}
