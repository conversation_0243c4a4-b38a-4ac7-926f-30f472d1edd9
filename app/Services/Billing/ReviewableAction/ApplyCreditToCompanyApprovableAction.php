<?php

namespace App\Services\Billing\ReviewableAction;

use App\Models\Billing\ActionApproval;
use App\Services\Billing\CreditService;

class ApplyCreditToCompanyApprovableAction extends ReviewableAction
{
    public function __construct(protected CreditService $creditService)
    {

    }

    /**
     * @param array $arguments
     * @return void
     */
    public function onApproval(array $arguments): void
    {
        [
            "amount"           => $amount,
            "type"             => $type,
            "authorType"       => $authorType,
            "authorId"         => $authorId,
            "companyReference" => $companyReference,
            "uuid"             => $uuid,
        ] = $arguments;

        $this->creditService->addCredit(
            uuid            : $uuid,
            companyReference: $companyReference,
            amount          : $amount,
            type            : $type,
            authorType      : $authorType,
            authorId        : $authorId,
        );
    }
}
