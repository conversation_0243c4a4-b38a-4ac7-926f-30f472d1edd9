<?php

namespace App\Services\Billing\InvoiceTemplateRenderer;

use App\Enums\Billing\InvoiceTemplateComponent;

class InvoiceFoundationComponent extends BaseComponent
{
    const string FIELD_INVOICE_LOGO            = 'invoice_logo';
    const string FIELD_INVOICE_CONTACT_ADDRESS = 'invoice_contact_address';
    const string FIELD_SUPPORT_EMAIL           = 'support_email';
    const string FIELD_CURRENCY                = 'currency';
    const string FIELD_INVOICE_BILLING_ACCOUNT = 'billing_account';

    /**
     * @var string
     */
    protected string $id = InvoiceTemplateComponent::INVOICE_FOUNDATION->value;
    /**
     * @var string
     */
    protected string $invoiceLogo;
    /**
     * @var array
     */
    protected array $invoiceContactAddress;
    /**
     * @var string
     */
    protected string $supportEmail;
    /**
     * @var string
     */
    protected string $currency;
    /**
     * @var string
     */
    protected string $billingAccount;

    /**
     * @return string[]
     */
    public static function getValidationRules(): array
    {
        return [
            self::FIELD_INVOICE_LOGO            => 'required|string',
            self::FIELD_SUPPORT_EMAIL           => 'required|string',
            self::FIELD_CURRENCY                => 'required|string',
            self::FIELD_INVOICE_CONTACT_ADDRESS => 'required|array',
            self::FIELD_INVOICE_BILLING_ACCOUNT => 'required|string',
        ];
    }

    /**
     * @return string
     */
    public function getId(): string
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getInvoiceLogo(): string
    {
        return $this->invoiceLogo;
    }

    /**
     * @return array
     */
    public function getInvoiceContactAddress(): array
    {
        return $this->invoiceContactAddress;
    }

    /**
     * @return string
     */
    public function getSupportEmail(): string
    {
        return $this->supportEmail;
    }

    /**
     * @return string
     */
    public function getCurrency(): string
    {
        return $this->currency;
    }

    /**
     * @return string
     */
    public function getBillingAccount(): string
    {
        return $this->billingAccount;
    }

    /**
     * @return array
     */
    public function toViewData(): array
    {
        return [
            self::FIELD_INVOICE_LOGO            => $this->invoiceLogo,
            self::FIELD_INVOICE_CONTACT_ADDRESS => $this->invoiceContactAddress,
            self::FIELD_SUPPORT_EMAIL           => $this->supportEmail,
            self::FIELD_CURRENCY                => $this->currency,
            self::FIELD_INVOICE_BILLING_ACCOUNT => $this->billingAccount,
        ];
    }
}



