<?php

namespace App\Services\Billing\Notification;

use App\Enums\Billing\InvoiceEvents;
use App\Models\Billing\ActionApproval;
use App\Services\Billing\LinkService\BillingActionLinkService;
use App\Services\NotificationService;
use Illuminate\Support\Collection;

class BillingInternalNotificationService implements BillingNotificationContract
{
    public function __construct(protected NotificationService $notificationService)
    {}

    public function notify(
        Collection $recipients,
        ActionApproval $actionApproval,
        string $subject,
        string $message,
        InvoiceEvents $eventType,
    ): void
    {
        $notificationBody = collect([
            $message,
            !empty($actionApproval->reason) ? 'Reason: ' . $actionApproval->reason : ''
        ])->join(' ');

        foreach ($recipients as $recipient) {
            $this->notificationService
                ->createNotificationForUser(
                    userId : $recipient->id,
                    fromId : null,
                    subject: $subject,
                    body   : $notificationBody,
                    type   : null,
                    link   : app(BillingActionLinkService::class)->getActionRequestLink(actionApproval: $actionApproval, full: false),
                );
        }
    }
}
