<?php

namespace App\Services\Legacy;

use App\Models\Legacy\EloquentUtility;
use App\Models\Legacy\Location;
use App\Models\Legacy\SolarCalculatorEngine\IncentivesLookup;
use App\Repositories\Legacy\UtilityRepository;
use App\Repositories\LocationRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class IncentiveLookupService
 * @package App\Services
 */
class IncentiveLookupService
{
    /**
     * IncentiveLookupService constructor.
     *
     * @param LocationRepository $locationRepository
     * @param UtilityRepository $utilityRepository
     */
    public function __construct(
        protected LocationRepository $locationRepository,
        protected UtilityRepository  $utilityRepository
    )
    {
    }

    /**
     * @param Location|null $location
     * @return Collection
     */
    public function getLocationIncentives(?Location $location): Collection
    {
        $query =  IncentivesLookup::query()
            ->with(IncentivesLookup::RELATION_INCENTIVE)
            ->where(function (Builder $query) {
                $query->where(IncentivesLookup::FIELD_LOOKUP_TYPE, IncentivesLookup::LOOKUP_TYPE_COUNTRY)
                    ->where(IncentivesLookup::FIELD_LOOKUP_VALUE, IncentivesLookup::LOOKUP_COUNTRY_US);
            });

        if($location?->state_abbr) {
            $query->orWhere(function (Builder $query) use ($location) {
                $query->where(IncentivesLookup::FIELD_LOOKUP_TYPE, IncentivesLookup::LOOKUP_TYPE_STATE)
                    ->where(IncentivesLookup::FIELD_LOOKUP_VALUE, $location->state_abbr);
            });
        }

        $incentiveLookups = $query->orderBy(IncentivesLookup::FIELD_LOOKUP_TYPE)->get();

        return $this->processedData($incentiveLookups);
    }

    /**
     * Get all incentives available for a utility
     *
     * @param EloquentUtility $eloquentUtility
     *
     * @return \Illuminate\Support\Collection|IncentivesLookup[]
     */
    public function getUtilityIncentives(EloquentUtility $eloquentUtility): array|Collection|\Illuminate\Support\Collection
    {
        $incentiveLookups =  IncentivesLookup::query()
            ->with(IncentivesLookup::RELATION_INCENTIVE)
            ->where(function (Builder $query) {
                $query->where(IncentivesLookup::FIELD_LOOKUP_TYPE, IncentivesLookup::LOOKUP_TYPE_COUNTRY)
                    ->where(IncentivesLookup::FIELD_LOOKUP_VALUE, IncentivesLookup::LOOKUP_COUNTRY_US);
            })
            ->orWhere(function (Builder $query) use ($eloquentUtility) {
                $query->where(IncentivesLookup::FIELD_LOOKUP_TYPE, IncentivesLookup::LOOKUP_TYPE_STATE)
                    ->where(IncentivesLookup::FIELD_LOOKUP_VALUE, $eloquentUtility->state);
            })
            ->orWhere(function (Builder $query) use ($eloquentUtility) {
                $query->where(IncentivesLookup::FIELD_LOOKUP_TYPE, IncentivesLookup::LOOKUP_TYPE_UTILITY)
                    ->where(IncentivesLookup::FIELD_LOOKUP_VALUE, "{$eloquentUtility->state}|{$eloquentUtility->eia_id}");
            })
            ->orderBy(IncentivesLookup::FIELD_LOOKUP_TYPE)
            ->get();

        return $this->processedData($incentiveLookups);
    }

    /**
     * Remove incentive lookups which are duplicate or do not have any incentive linked
     *
     * @param Collection|IncentivesLookup[] $incentivesLookups
     *
     * @return Collection
     */
    protected function processedData(array|Collection $incentivesLookups): Collection
    {
        return $incentivesLookups->filter(function (IncentivesLookup $incentivesLookup) {
            return !empty($incentivesLookup->incentive);
        })->unique(function (IncentivesLookup $incentivesLookup) {
            return $incentivesLookup->incentive_reference;
        })->values();
    }
}
