<?php

namespace App\Services\Legacy\Payments\Gateways;

use App\Contracts\Legacy\Payments\PaymentGatewayAccountRepositoryContract;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyPaymentProfile;
use App\Services\Legacy\Exceptions\PaymentException;

class PaymentGatewayAccountRepositoryFactory
{
    public function __construct(protected EloquentCompany $company) {}

    public function get(string $gateway): PaymentGatewayAccountRepositoryContract
    {
        if(!PaymentGateways::exists($gateway))
            throw new PaymentException("Unsupported payment gateway: {$gateway}");

        /** @var EloquentCompanyPaymentProfile $profile */
        if(!($profile = $this->company->companyPaymentProfiles->where(EloquentCompanyPaymentProfile::PROVIDER_CODE, $gateway)->first())) {
            return new CompanyPaymentProfileGatewayAccountRepository($gateway, $this->company);
        }

        return new CompanyPaymentProfileGatewayAccountRepository($gateway, $this->company, $profile);
    }
}
