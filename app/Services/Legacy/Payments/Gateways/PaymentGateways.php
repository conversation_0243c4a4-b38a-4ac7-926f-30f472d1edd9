<?php

namespace App\Services\Legacy\Payments\Gateways;

class PaymentGateways
{
    const SHORT_CODE_STRIPE = "str";

    const DISPLAY_NAME_STRIPE = "Stripe";

    static function exists(string $code): bool
    {
        return in_array($code, self::all());
    }

    static function all(): array
    {
        return [self::SHORT_CODE_STRIPE];
    }

    static function preferred(): string
    {
        return self::SHORT_CODE_STRIPE;
    }

    static function getDisplayName(string $code): string
    {
        return match ($code) {
            self::SHORT_CODE_STRIPE => self::DISPLAY_NAME_STRIPE,
            default => "N/A"
        };
    }
}
