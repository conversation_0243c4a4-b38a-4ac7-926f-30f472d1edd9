<?php

namespace App\Services\Legacy\Payments\Gateways;

use App\Contracts\Legacy\Payments\PaymentGatewayAccountRepositoryContract;
use App\Models\Legacy\EloquentCompany;

abstract class BaseGatewayAccountRepository implements PaymentGatewayAccountRepositoryContract
{
    public function __construct(protected string $gateway, protected EloquentCompany $company) {}

    public function getGatewayId(): string
    {
        return $this->gateway;
    }

    abstract public function getAccountId(): ?string;

    abstract public function getDefaultPaymentMethodId(): ?string;

    abstract public function setDefaultPaymentMethodId(): ?string;

    abstract public function setAccountId(string $id): void;

    public function getCompanyId(): int
    {
        return $this->company->getKey();
    }

    public function getCompanyName(): string
    {
        return $this->company->companyname;
    }
}
