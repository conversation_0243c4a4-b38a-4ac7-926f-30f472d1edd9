<?php

namespace App\Services\Docusign;

use App\Enums\AuditLogType;
use App\Enums\ContractProvider;
use App\Enums\ContractType;
use App\Models\CompanyContract;
use App\Models\Contract;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Industry;
use App\Repositories\AuditLogRepository;
use App\Services\CloudStorage\GoogleCloudStorageService;
use App\Services\CompanyContractService;
use DocuSign\eSign\Api\EnvelopesApi;
use DocuSign\eSign\Client\ApiClient;
use DocuSign\eSign\Client\ApiException;
use DocuSign\eSign\Client\Auth\Account;
use DocuSign\eSign\Client\Auth\OAuth;
use DocuSign\eSign\Configuration;
use DocuSign\eSign\Model\CustomFields;
use DocuSign\eSign\Model\Envelope;
use DocuSign\eSign\Model\EnvelopeDefinition;
use DocuSign\eSign\Model\EnvelopeSummary;
use DocuSign\eSign\Model\PrefillTabs;
use DocuSign\eSign\Model\RecipientViewRequest;
use DocuSign\eSign\Model\Tabs;
use DocuSign\eSign\Model\TemplateRole;
use DocuSign\eSign\Model\Text;
use DocuSign\eSign\Model\TextCustomField;
use DocuSign\eSign\Model\UserInfo;
use DocuSign\eSign\Model\ViewUrl;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use SplFileObject;

class DocuSignService
{
    const string CLIENT_ROLE = 'signer';

    const string INITIAL_HOST_URL = 'https://demo.docusign.net/restapi';
    /** Integration or application key */
    private string $integrationKey;
    private string $privateKey;
    private string $userId;
    private string $accountId;
    private string $templateId;
    private string $envelopeId;

    protected CompanyUser $companyUser;
    protected AuditLogRepository $auditLogRepository;
    protected CompanyContractService $companyContractService;
    protected ApiClient $apiClient;
    protected OAuth $oauth;
    protected Configuration $configuration;
    protected EnvelopesApi $envelopesApi;
    protected TemplateRole $templateRole;

    /**
     * @param AuditLogRepository $auditLogRepository
     * @param CompanyContractService $companyContractService
     * @throws ApiException
     */
    public function __construct(
        AuditLogRepository      $auditLogRepository,
        CompanyContractService $companyContractService,
    ) {
        $this->companyContractService = $companyContractService;
        $this->auditLogRepository = $auditLogRepository;

        $this->userId = config('services.docusign.user_id');
        $this->integrationKey = config('services.docusign.integration_key');
        $this->privateKey = config('services.docusign.private_key');
        $this->accountId = config('services.docusign.account_id');

        $this->configuration = new Configuration();

        $this->configuration->setHost(self::INITIAL_HOST_URL);

        $this->oauth = new OAuth();

        $this->oauth->setOAuthBasePath(app()->isProduction() ? OAuth::$PRODUCTION_OAUTH_BASE_PATH : OAuth::$DEMO_OAUTH_BASE_PATH);

        $this->apiClient = new ApiClient($this->configuration, $this->oauth);

        [$authToken] = $this->apiClient->requestJWTUserToken(
            client_id: $this->integrationKey,
            user_id: $this->userId,
            rsa_private_key: $this->privateKey,
            scopes: "signature impersonation cors"
        );

        $this->configuration->setAccessToken($authToken->getAccessToken());

        /** @var Account $userAccountInfo */
        $userAccountInfo = $this->apiClient->getUserInfo($this->configuration->getAccessToken())[0]['accounts'][0];

        //todo test that this need rest api appended
        $this->configuration->setHost($userAccountInfo->getBaseUri() . '/restapi');

        $this->apiClient = new ApiClient($this->configuration, $this->oauth);

        $this->envelopesApi = new EnvelopesApi($this->apiClient);

        $this->templateRole = new TemplateRole();
    }

    /**
     * @param CompanyUser $companyUser
     * @param Contract $contract
     * @return string|null
     * @throws ApiException
     */
    public function getEmbeddedSignUrl(CompanyUser $companyUser, Contract $contract): ?string
    {
        $this->companyUser = $companyUser;

        $this->templateId = $contract->contract_provider_id;

        $envelopeSummary = $this->createEnvelopeForUser();

        $this->envelopeId = $envelopeSummary->getEnvelopeId();

        $this->companyContractService->createNewContract(
            company: $companyUser->company,
            companyUser: $companyUser,
            contractType: ContractType::FIXR,
            ip: 'N/A',
            contract: $contract,
            signatureId: $this->envelopeId,
        );

        $viewUrl = $this->createRecipientViewForUser($this->envelopeId);

        return $viewUrl->getUrl();
    }

    /**
     * Used to evaluate if the envelope is signed
     *
     * @param CompanyContract $companyContract
     * @return bool
     */
    public function hasUserSignedContract(CompanyContract $companyContract): bool
    {
        $envelope = $this->getEnvelope($companyContract->signature_id);

        return $envelope->getStatus() === 'completed';

    }

    protected function getEnvelope(string $envelopeId): Envelope
    {
        return $this->envelopesApi->getEnvelope($this->accountId, $envelopeId);
    }

    /**
     * @return EnvelopeSummary
     * @throws ApiException
     */
    protected function createEnvelopeForUser($embedded = true): EnvelopeSummary
    {
        $envelopDefinition = new EnvelopeDefinition();

        $envelopDefinition->setEmailSubject('Contract Signature Request');
        $envelopDefinition->setTemplateId($this->templateId);
        $envelopDefinition->setStatus('sent');
        $envelopDefinition->setTemplateRoles([$this->createTemplateRole($embedded)]);

        return $this->envelopesApi->createEnvelope($this->accountId, $envelopDefinition);
    }

    /**
     * @return TemplateRole
     * @throws ApiException
     */
    protected function createTemplateRole($embedded): TemplateRole
    {
        $this->templateRole->setEmail($this->companyUser->email);
        $this->templateRole->setName($this->companyUser->first_name . ' ' . $this->companyUser->last_name);
        $this->templateRole->setRoleName(self::CLIENT_ROLE);
        $this->templateRole->setRoutingOrder(1);
        $this->templateRole->setClientUserId($embedded ? $this->companyUser->id : null);
        $this->templateRole->setTabs($this->getCustomFields());

        return $this->templateRole;
    }

    protected function getCustomFields(): Tabs
    {
        $companyName = new Text([
            'tab_label' => "companyName", 'value' => $this->companyUser->company->name
        ]);

        $department = new Text([
            'tab_label' => "department", 'value' => $this->companyUser->department ?? ''
        ]);

        $vertical = new Text([
            'tab_label' => "vertical", 'value' => $this->companyUser->company?->industries()?->first()?->{Industry::FIELD_NAME} ?? 'Solar'
        ]);

        $entity = new Text([
            'tab_label' => "entity", 'value' => 'Solar Investments Inc'
        ]);

        return new Tabs([
            'text_tabs' => [$companyName, $department, $vertical, $entity]
        ]);
    }

    /**
     * @param string $envelopeId
     * @return ViewUrl
     * @throws ApiException
     */
    protected function createRecipientViewForUser(string $envelopeId): ViewUrl
    {
        $recipient_view_request = new RecipientViewRequest([
            'authentication_method' => 'none',
            'client_user_id' => $this->companyUser->id,
            'return_url' => config('app.dashboard.fixr_url') . '/terms-and-conditions',
            'user_name' => $this->companyUser->completeName(),
            'email' => $this->companyUser->email,
        ]);

        return $this->envelopesApi->createRecipientView(
            account_id: $this->accountId,
            envelope_id: $envelopeId,
            recipient_view_request: $recipient_view_request,
        );
    }

    /**
     * @param Request $request
     * @return void
     * @throws ApiException
     * @throws BindingResolutionException
     */
    public function handleCallbackEvent(Request $request): void
    {
        switch($request->get('event')) {
            case "envelope-completed":
                $companyContract = $this->getOrCreateCompanyContractFromEventCallBackRequest($request);
                $companyContract->agree();
                $this->auditLogRepository->createAuditLogForActorAndModel($companyContract, $companyContract->companyUser, AuditLogType::CONTRACT_SIGNED);
                $this->storeSignedContractPDF($companyContract);
                break;
            case "envelope-sent":
                $companyContract = $this->getOrCreateCompanyContractFromEventCallBackRequest($request);
                $this->auditLogRepository->createAuditLogForActorAndModel($companyContract, $companyContract->companyUser, AuditLogType::CONTRACT_CREATED);
                break;
            case "recipient-delivered":
                $companyContract = $this->getOrCreateCompanyContractFromEventCallBackRequest($request);
                $this->auditLogRepository->createAuditLogForActorAndModel($companyContract, $companyContract->companyUser, AuditLogType::CONTRACT_VIEWED);
                break;
            case 'template-modified':
                Contract::query()->updateOrCreate([
                    Contract::FIELD_CONTRACT_PROVIDER_ID => $request->get('data')['templateId'],
                    Contract::FIELD_DESCRIPTION => $request->get('data')['name'],
                ], [
                    Contract::FIELD_CONTRACT_PROVIDER => ContractProvider::DOCUSIGN,
                ]);
                break;
            case 'template-deleted':
                //todo we may want to guard against this
                //if not active delete
                //if active we have an issue.......
                break;
            default:
                break;
        }
    }

    /**
     * @param Request $request
     * @return CompanyContract
     */
    protected function getOrCreateCompanyContractFromEventCallBackRequest(Request $request): CompanyContract
    {
        $signatureRequestId = $request->get('data')['envelopeId'];

        return CompanyContract::query()
            ->where(CompanyContract::FIELD_SIGNATURE_ID, $signatureRequestId)
            ->firstOrFail();
    }

    /**
     * @param CompanyContract $companyContract
     * @return void
     * @throws ApiException
     * @throws BindingResolutionException
     */
    public function storeSignedContractPDF(CompanyContract $companyContract): void
    {
        //signature Id is envelopeId
        $file = $this->getSignatureRequestFilesAsPdf($companyContract->signature_id);

        Storage::put('contract.pdf', $file->fread($file->getSize()));

        $bucket = config('services.google.storage.buckets.contracts');
        $googleCloudStorageService = app()->make(GoogleCloudStorageService::class);
        $googleCloudStorageService->setCurrentBucket($bucket);

        $path = "/company-contracts/{$companyContract->id}/signature_request/{$companyContract->signature_id}.pdf";

        $googleCloudStorageService->upload($path, fopen(storage_path('app/contract.pdf'), 'r'));

        Storage::delete('/contract.pdf');

        $companyContract->file_url = $path;

        $companyContract->save();
    }

    /**
     * @param string $envelopeId
     * @return SplFileObject
     * @throws ApiException
     */
    protected function getSignatureRequestFilesAsPdf(string $envelopeId): SplFileObject
    {
        $listDocuments = $this->envelopesApi->listDocuments($this->accountId, $envelopeId);

        $document = $listDocuments->getEnvelopeDocuments()[0];

        try {
            return $this->envelopesApi->getDocument($this->accountId, $document->getDocumentId(), $envelopeId);
        } catch (ApiException $e) {
            throw new ApiException($e);
        }
    }

    /**
     * @param CompanyUser $companyUser
     * @param Contract $contract
     * @return bool
     * @throws ApiException
     */
    public function sendSignatureRequestWithTemplate(CompanyUser $companyUser, Contract $contract): bool
    {
        $this->companyUser = $companyUser;

        $this->templateId = $contract->contract_provider_id;

        $envelopeSummary = $this->createEnvelopeForUser(embedded: false);

        $this->envelopeId = $envelopeSummary->getEnvelopeId();

        $this->companyContractService->createNewContract(
            company: $companyUser->company,
            companyUser: $companyUser,
            contractType: ContractType::FIXR,
            ip: 'N/A',
            contract: $contract,
            signatureId: $this->envelopeId,
        );

        return true;
    }
}
