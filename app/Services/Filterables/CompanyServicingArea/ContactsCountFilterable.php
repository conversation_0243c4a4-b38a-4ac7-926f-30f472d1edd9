<?php

namespace App\Services\Filterables\CompanyServicingArea;

use App\Enums\Logical;
use App\Enums\Operator;
use App\Models\Legacy\EloquentCompany;
use App\Services\CompanyFilteringService;
use App\Services\Filterables\HorizontalDualOperatorWithDropdownOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class ContactsCountFilterable extends HorizontalDualOperatorWithDropdownOption
{
    public function __construct()
    {
        $this->model = EloquentCompany::class;
        $this->name = "Company Contacts";
        $this->id = 'company-servicing-area-contacts-count';
        $this->withRelations = null;

        $this->dropdownOptions = [
            [
                'id' => 'withPhone',
                'name' => 'With Phone',
            ],
            [
                'id' => 'withEmail',
                'name' => 'With Email',
            ],
            [
                'id' => 'withBoth',
                'name' => 'With Both',
            ],
            [
                'id' => 'withNeither',
                'name' => 'With Neither',
            ],
        ];

        $this->options = [
            'dropdown' => $this->dropdownOptions,
            'default' => 'withPhone',
        ];
    }

    /**
     * @param  Builder  $builder
     * @param  Operator  $firstOperator
     * @param  Operator|null  $secondOperator
     * @param  int  $firstInput
     * @param  int|null  $secondInput
     * @param  Logical|null  $logical
     * @param  string  $option
     * @return Builder
     * @throws Exception
     */
    protected function getQuery(
        Builder $builder,
        Operator $firstOperator,
        ?Operator $secondOperator,
        int $firstInput,
        ?int $secondInput,
        ?Logical $logical,
        string $option,
    ): Builder {
        $column = match ($option) {
            'withPhone' => CompanyFilteringService::CONTACTS_WITH_PHONE_COUNT,
            'withEmail' => CompanyFilteringService::CONTACTS_WITH_EMAIL_COUNT,
            'withBoth' => CompanyFilteringService::CONTACTS_WITH_BOTH_COUNT,
            'withNeither' => CompanyFilteringService::CONTACTS_WITH_NEITHER_COUNT,
            default => throw new Exception("Invalid option: $option"),
        };

        return $this->defaultQueryLogic($builder, $column, $firstOperator, $secondOperator, $firstInput, $secondInput, $logical);
    }
}
