<?php

namespace App\Services\Filterables\EmailMarketing;

use App\Models\Odin\Consumer;
use App\Services\Filterables\BaseFilterableService;
use App\Services\Filterables\Consumer\ConsumerClonedFilterable;
use App\Services\Filterables\Consumer\ConsumerContactRequestFilterable;
use App\Services\Filterables\Consumer\ConsumerDateFilterable;
use App\Services\Filterables\Consumer\ConsumerDeliveredFilterable;
use App\Services\Filterables\Consumer\ConsumerFilterableService;
use App\Services\Filterables\Consumer\ConsumerGoodToSellFilterable;
use App\Services\Filterables\Consumer\ConsumerHasAvailableBudgetFilterable;
use App\Services\Filterables\Consumer\ConsumerHasSecondaryServicesFilterable;
use App\Services\Filterables\Consumer\ConsumerIndustryFilterable;
use App\Services\Filterables\Consumer\ConsumerLegsSoldFilterable;
use App\Services\Filterables\Consumer\ConsumerLocationFilterable;
use App\Services\Filterables\Consumer\ConsumerMarketingAffiliateIdFilterable;
use App\Services\Filterables\Consumer\ConsumerMarketingCampaignIdFilterable;
use App\Services\Filterables\Consumer\ConsumerMarketingStrategyFilterable;
use App\Services\Filterables\Consumer\ConsumerOriginFilterable;
use App\Services\Filterables\Consumer\ConsumerOtherInterestsFilterable;
use App\Services\Filterables\Consumer\ConsumerProductTypeFilterable;
use App\Services\Filterables\Consumer\ConsumerServiceFilterable;
use App\Services\Filterables\Consumer\ConsumerStatusFilterable;
use App\Services\Filterables\Consumer\ConsumerTestProductFilterable;
use App\Services\Filterables\Consumer\ConsumerUnsoldWithBudgetFilterable;
use App\Services\Filterables\Consumer\ConsumerVerificationFilterable;
use Illuminate\Database\Eloquent\Builder;

class MarketingConsumerFilterableService extends ConsumerFilterableService
{
    const string FILTERABLE_CATEGORY = 'marketing-consumer-search';

    //excluding unnecessary filters from consumer search
    protected array $filters = [
        ConsumerStatusFilterable::class,
        ConsumerIndustryFilterable::class,
        ConsumerServiceFilterable::class,
        ConsumerProductTypeFilterable::class,
        ConsumerVerificationFilterable::class,
        ConsumerLocationFilterable::class,
        ConsumerOriginFilterable::class,
        ConsumerMarketingStrategyFilterable::class,
        ConsumerGoodToSellFilterable::class,
        ConsumerTestProductFilterable::class,
        ConsumerOtherInterestsFilterable::class,
        ConsumerLegsSoldFilterable::class,
        ConsumerMarketingAffiliateIdFilterable::class,
        ConsumerMarketingCampaignIdFilterable::class,
        ConsumerContactRequestFilterable::class,
        ConsumerUnsoldWithBudgetFilterable::class,
        ConsumerDeliveredFilterable::class,
        ConsumerHasAvailableBudgetFilterable::class,
        ConsumerClonedFilterable::class,
        ConsumerHasSecondaryServicesFilterable::class,
    ];
}
