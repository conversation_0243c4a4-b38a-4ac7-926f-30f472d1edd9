<?php

namespace App\Services\Filterables\LeadsReport;

use App\Models\Odin\Consumer;
use App\Services\Filterables\BaseFilterableService;
use Illuminate\Database\Eloquent\Builder;

class LeadsReportFilterableService extends BaseFilterableService
{
    const string FILTERABLE_CATEGORY = 'leads-report';

    protected array $filters = [
        LeadsReportIndustryFilterable::class,
        LeadsReportLocationFilterable::class,
        LeadsReportCampaignStatusFilterable::class,
        LeadsReportOriginFilterable::class,
        LeadsReportAdvertiserFilterable::class,
        LeadsReportPlatformFilterable::class,
        LeadsReportLeadTypeFilterable::class,
        LeadsReportCompanyUserRelationFilterable::class,
    ];

    protected string $baseModel = Consumer::class;

    /**
     * @param array $results
     * @param Builder|null $baseQuery
     * @return Builder
     */
    public function runQuery(array $results, ?Builder $baseQuery = null): Builder
    {
        $query = $baseQuery ?? Consumer::query();
        $this->runFilterQueries($query, $results);

        return $query;
    }
}
