<?php

namespace App\Services\Filterables\LeadsReport;

use App\Models\CompanyUserRelationship;
use App\Models\Odin\Consumer;
use App\Models\User;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class LeadsReportCompanyUserRelationFilterable extends MultiSelectFilterableOption
{
    const string ID = 'company-user-relation';

    protected array $options = [];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "Company User Relation";
        $this->id = self::ID;
        $this->withRelations = null;

        $this->options = User::query()
            ->join(CompanyUserRelationship::TABLE, User::TABLE.'.'.User::FIELD_ID, '=', CompanyUserRelationship::TABLE.'.'.CompanyUserRelationship::FIELD_USER_ID)
            ->whereNull(CompanyUserRelationship::TABLE.'.'.CompanyUserRelationship::FIELD_DELETED_AT)
            ->groupBy(User::TABLE.'.'.User::FIELD_ID)
            ->get([
                User::TABLE.'.'.User::FIELD_NAME,
                User::TABLE.'.'.User::FIELD_ID,
            ])
            ->mapWithKeys(fn(User $user) => [$user->name => $user->id])
            ->toArray();
    }

    /**
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $builder;
    }

}
