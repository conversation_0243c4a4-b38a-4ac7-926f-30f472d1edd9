<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Services\Filterables\MultiSelectWithChildrenFilterableOption;
use Illuminate\Database\Eloquent\Builder;

/**
 * Location Filterable with County/Zip as dependents
 * There are too many Zip Codes to supply all to front end at once, so Child Fitlerables are updated each query.
 */
class ConsumerLocationFilterable extends MultiSelectWithChildrenFilterableOption
{
    const CHILD_FILTERABLE_COUNTY_ID    = 'consumer-county';
    const CHILD_FILTERABLE_ZIP_CODE_ID  = 'consumer-zip-code';

    protected array $options = [];

    protected array $children = [];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "Location";
        $this->id = 'consumer-location';
        $this->withRelations = null;
        $this->filterPillDisplayLastChild = true;

        $this->options = Location::query()
            ->select(Location::STATE, Location::STATE_ABBREVIATION)
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->get()
            ->mapWithKeys(fn(Location $state) => [$state->state => $state->state_abbr])
            ->toArray();

        $this->children = [
            self::CHILD_FILTERABLE_COUNTY_ID    => new ConsumerCountyChildFilterableOption(),
            self::CHILD_FILTERABLE_ZIP_CODE_ID  => new ConsumerZipCodeChildFilterableOption(),
        ];
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        $value = $value ?? [];

        $stateValues = $value[$this->getId()] ?? [];
        $countyValues = $value[self::CHILD_FILTERABLE_COUNTY_ID] ?? [];
        $zipCodeValues = $value[self::CHILD_FILTERABLE_ZIP_CODE_ID] ?? [];

        $updatedCountyKeys = $this->children[self::CHILD_FILTERABLE_COUNTY_ID]->updateOptions($stateValues);
        $this->validateChildInputs($countyValues, $updatedCountyKeys);

        $updatedZipCodeKeys = $this->children[self::CHILD_FILTERABLE_ZIP_CODE_ID]->updateOptions($countyValues);
        $this->validateChildInputs($zipCodeValues, $updatedZipCodeKeys);

        $this->setUpdatedOptions(true);

        if($stateValues) {
            $builder->whereIn(Address::TABLE .'.'. Address::FIELD_STATE, $stateValues);
        }

        if ($zipCodeValues)
            $this->children[self::CHILD_FILTERABLE_ZIP_CODE_ID]->runQuery($builder, $zipCodeValues);
        else if ($countyValues)
            $this->children[self::CHILD_FILTERABLE_COUNTY_ID]->runQuery($builder, $countyValues);

        return $builder;
    }

    /**
     * Validate child options to handle user de-selecting parent regions
     * @param array $inputValues
     * @param array $validOptions
     * @return void
     */
    protected function validateChildInputs(array &$inputValues, array $validOptions): void
    {
        $validValues = array_values($validOptions);
        $inputValues = array_filter($inputValues, fn($value) => in_array($value, $validValues));
    }
}
