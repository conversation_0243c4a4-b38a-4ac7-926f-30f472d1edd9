<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class ConsumerDeliveredFilterable extends SelectFilterableOption
{
    protected array $options = [
        'Yes' => 1,
        'No' => 0,
    ];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "Delivered";
        $this->id = 'consumer-delivered';
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if ($value === null) return $builder;

        $value = (int)$value;

        return $builder->whereHas(Consumer::RELATION_CONSUMER_PRODUCT, function (Builder $query) use ($value) {
            $query->whereHas(ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT, function (Builder $query) use ($value) {
                $query->where(ProductAssignment::FIELD_DELIVERED, $value);
            });
        });

    }
}
