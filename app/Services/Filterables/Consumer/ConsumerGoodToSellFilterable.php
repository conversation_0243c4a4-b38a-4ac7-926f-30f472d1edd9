<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class ConsumerGoodToSellFilterable extends SelectFilterableOption
{

    protected array $options = [
        'Good To Sell Only'     => true
    ];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "Good To Sell";
        $this->id = 'consumer-good-to-sell';
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $builder->where(ConsumerProduct::FIELD_GOOD_TO_SELL, $value)
            : $builder;
    }

}
