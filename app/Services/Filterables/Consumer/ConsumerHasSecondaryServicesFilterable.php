<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class ConsumerHasSecondaryServicesFilterable extends SelectFilterableOption
{

    protected array $options = [
        'Has Secondary Services'     => true
    ];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "Secondary Services Only";
        $this->id = 'consumer-has-secondary-services';
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $builder->where(ConsumerProduct::FIELD_IS_SECONDARY_SERVICE, true)
            : $builder;
    }

}
