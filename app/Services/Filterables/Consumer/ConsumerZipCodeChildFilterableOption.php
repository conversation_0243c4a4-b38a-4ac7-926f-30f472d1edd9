<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class ConsumerZipCodeChildFilterableOption extends MultiSelectFilterableOption
{
    protected array $options = [];

    public function __construct()
    {
        $this->model         = Consumer::class;
        $this->name          = "Zip Code";
        $this->id            = ConsumerLocationFilterable::CHILD_FILTERABLE_ZIP_CODE_ID;
        $this->withRelations = null;

        $this->options = [];
    }

    public function updateOptions($filterByKeys): ?array
    {
        $newOptions = Location::query()
            ->select(Location::COUNTY_KEY, Location::ZIP_CODE)
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->whereIn(Location::COUNTY_KEY, $filterByKeys)
            ->get()
            ->reduce(function (array $output, Location $county) {
                $output[$county->zip_code] = $county->zip_code;
                return $output;
            }, []);
        $this->options = $newOptions;

        return $newOptions;
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $builder->whereIn(Address::FIELD_ZIP_CODE, $value)
            : $builder;
    }
}

