<?php

namespace App\Services\Filterables\Company;

use App\Models\Odin\Company;
use App\Repositories\Odin\ProductRepository;
use App\Services\Filterables\Base\NumericFilterable;
use App\Services\Filterables\MultiSelectWithChildrenFilterableOption;

class CompanyLeadRejectionPercentageFilterable extends MultiSelectWithChildrenFilterableOption
{
    use CompanyRejectionPercentageTrait;

    const MANUAL_REJECTION = 'company-lead-rejection-percentage-manual';

    const CRM_REJECTION = 'company-lead-rejection-percentage-crm';

    const OVERALL_REJECTION = 'company-lead-rejection-percentage-overall';

    protected bool $bypassParentCheckForActiveFilterDisplay = true;

    public function __construct(
        protected ProductRepository $productRepository,
    ) {
        $this->model = Company::class;
        $this->name = "Lead Rejections";
        $this->id = 'company-lead-rejection-percentage';
        $this->withRelations = null;
        $this->children = $this->loadChildren();
        $this->productId = $this->productRepository->getLeadProductId();
    }

    /**
     * @return array
     */
    private function loadChildren(): array
    {
        return [
            new NumericFilterable(self::MANUAL_REJECTION, 'Manual Rejection %', Company::class),
            new NumericFilterable(self::CRM_REJECTION, 'CRM Rejection %', Company::class),
            new NumericFilterable(self::OVERALL_REJECTION, 'Overall Rejection %', Company::class),
        ];
    }
}
