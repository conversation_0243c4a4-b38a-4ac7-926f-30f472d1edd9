<?php

namespace App\Services\Filterables\Company;

use App\Models\Odin\Company;
use App\Models\SuccessManager;
use App\Models\SuccessManagerClient;
use App\Models\User;
use App\Services\Filterables\MultiSelectFilterableOption;
use App\Services\SuccessManagerService;
use App\Transformers\Sales\SuccessManagerTransformer;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class CompanySuccessManagerFilterable extends MultiSelectFilterableOption
{
    public function __construct(
        SuccessManagerService $successManagerService,
        SuccessManagerTransformer $successManagerTransformer
    )
    {
        $this->model = Company::class;
        $this->name = "Success Manager";
        $this->id = 'company-success-manager';
        $this->withRelations = null;

        $successManagers = $successManagerService->getSuccessManagersWithUserJoin();

        $successManagers = $successManagers->sortBy(function ($value) {
            if (!!$value->{User::TABLE.'.'.User::FIELD_NAME})
                return $value->{User::TABLE.'.'.User::FIELD_NAME};
            else
                return $value->{SuccessManager::RELATION_USER}->{User::FIELD_NAME};
        });

        $this->options = $successManagerTransformer->transformAllIntoFilterableOptionsArray($successManagers);
    }

    /**
     * @param  Builder  $builder
     * @param  mixed  $value
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (empty($value)) {
            return $builder;
        }

        $builder->whereHas(Company::RELATION_SUCCESS_MANAGER_CLIENTS,
            function (Builder $query) use ($value) {
                $query->whereIn(SuccessManagerClient::FIELD_SUCCESS_MANAGER_ID, $value ?? []);

                $query->where(SuccessManagerClient::FIELD_STATUS, SuccessManagerClient::STATUS_ACTIVE);

            });

        return $builder;
    }
}
