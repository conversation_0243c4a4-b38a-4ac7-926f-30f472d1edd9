<?php

namespace App\Services\Filterables\Company;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyIsEnergySageCustomerFilterable extends SelectFilterableOption
{
    const string PROPERTY = 'is_energysage_customer';

    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Is Energy Sage Customer";
        $this->id = 'company-is-energy-sage-customer';
        $this->withRelations = null;

        $this->options = [
            'Yes' => 'true',
            'No' => 'false',
        ];
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (is_string($value) && !in_array($value, ['true', 'false'])) {
            return $builder;
        }

        $value = is_string($value) ? filter_var($value, FILTER_VALIDATE_BOOLEAN) : (is_bool($value) ? $value : null);

        if (!is_bool($value)) {
            return $builder;
        }

        $builder->whereHas(Company::RELATION_DATA, function ($has) use ($value) {
            if (!$value) {
                $has->whereNull(CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->".self::PROPERTY);

                $has->orWhere(
                    CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->".self::PROPERTY,
                    $value
                );
            } else {
                $has->where(
                    CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->".self::PROPERTY,
                    $value
                );
            }
        });

        return $builder;
    }
}
