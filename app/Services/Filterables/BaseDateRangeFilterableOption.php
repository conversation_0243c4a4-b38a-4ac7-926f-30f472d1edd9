<?php

namespace App\Services\Filterables;

use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;

abstract class BaseDateRangeFilterableOption extends DateRangeFilterableOption
{
    protected array $options = [];
    
    protected ?Carbon $minFromDate;
    protected ?Carbon $defaultFromDate;
    protected ?Carbon $maxFromDate;

    protected ?Carbon $minToDate;
    protected ?Carbon $defaultToDate;
    protected ?Carbon $maxToDate;
    
    protected string $tableAndColumn;

    public function __construct()
    {
        $this->name = 'Date Range';
        $this->clearable = false;
        $this->withRelations = null;

        $this->options = [
            self::DATE_RANGE_FROM  =>  [
                self::DATE_RANGE_MIN       => $this->minFromDate ?? null,
                self::DATE_RANGE_MAX       => $this->maxFromDate ?? now()->addHours(12),
                self::DATE_RANGE_DEFAULT   => $this->defaultFromDate ?? now()->subWeek(),
            ],
            self::DATE_RANGE_TO    => [
                self::DATE_RANGE_MIN       => $this->minToDate ?? $this->minFromDate ?? null,
                self::DATE_RANGE_MAX       => $this->maxToDate ?? now()->addHours(12),
                self::DATE_RANGE_DEFAULT   => $this->defaultToDate ?? now(),
            ],
            self::DATE_RANGE_PRESETS => [
                'Last Hour'             => [ self::DATE_RANGE_FROM => now()->subHour(), self::DATE_RANGE_TO => now() ],
                'Last 4 Hours'          => [ self::DATE_RANGE_FROM => now()->subHours(4), self::DATE_RANGE_TO => now() ],
                'Last 8 Hours'          => [ self::DATE_RANGE_FROM => now()->subHours(8), self::DATE_RANGE_TO => now() ],
                'Today Only'            => [ self::DATE_RANGE_FROM => now()->startOfDay(), self::DATE_RANGE_TO => now() ],
                'Yesterday Only'        => [ self::DATE_RANGE_FROM => now()->subDay()->startOfDay(), self::DATE_RANGE_TO => now()->startOfDay() ],
                'Last 24 Hours'         => [ self::DATE_RANGE_FROM => now()->subDay(), self::DATE_RANGE_TO => now()],
                'Last Week'             => [ self::DATE_RANGE_FROM => now()->subWeek(), self::DATE_RANGE_TO => now(), self::DATE_RANGE_DEFAULT => true],
                'Last Month'            => [ self::DATE_RANGE_FROM => now()->subMonth(), self::DATE_RANGE_TO => now() ],
                'This Calendar Month'   => [ self::DATE_RANGE_FROM => now()->startOfMonth(), self::DATE_RANGE_TO => now() ],
                'Last 3 Months'         => [ self::DATE_RANGE_FROM => now()->subMonths(3), self::DATE_RANGE_TO => now()],
                ...($this->minFromDate === null || $this->minFromDate >= now()->subYear() )
                    ? ['Last 12 Months' => [self::DATE_RANGE_FROM => now()->subYear(), self::DATE_RANGE_TO => now()]] 
                    : [],
            ],
        ];

        $this->setTableAndColumn();
    }

    /**
     * Must set the table and column to constrain by date range e.g. consumer_products.created_at
     * @return void
     */
    abstract protected function setTableAndColumn(): void;

    //TODO: move logic for frontend - backend date-time conversion to DateRangeFilterableOptions parent class
    //TODO: move logic for validating the min/max date values to DateRangeFilterableOptions parent class
    //TODO: permissions-based override for the 3-month limit on searching (will need to pass to Vue to inform DatePicker to remove limits)
    /**
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (empty($value[self::DATE_RANGE_PRESET])) {
            $startDate = $value[self::DATE_RANGE_FROM]
                ? new Carbon($value[self::DATE_RANGE_FROM])
                : $this->options[self::DATE_RANGE_FROM][self::DATE_RANGE_DEFAULT];
            $endDate = $value[self::DATE_RANGE_TO]
                ? new Carbon($value[self::DATE_RANGE_TO])
                : $this->options[self::DATE_RANGE_TO][self::DATE_RANGE_DEFAULT];

            // If the given value exceeds the datetime limit, update it to the min/max threshold.
            $startDate = !($startDate >= $this->options[self::DATE_RANGE_FROM][self::DATE_RANGE_MIN]
                && $startDate <= $this->options[self::DATE_RANGE_FROM][self::DATE_RANGE_MAX])
                ? $this->options[self::DATE_RANGE_FROM][self::DATE_RANGE_MIN]
                : $startDate;
            $endDate = !($endDate >= $this->options[self::DATE_RANGE_TO][self::DATE_RANGE_MIN]
                && $endDate <= $this->options[self::DATE_RANGE_TO][self::DATE_RANGE_MAX])
                ? $this->options[self::DATE_RANGE_TO][self::DATE_RANGE_MAX]
                : $endDate;

            if ($endDate && $startDate?->greaterThan($endDate)) {
                $startDate = $this->options[self::DATE_RANGE_FROM][self::DATE_RANGE_DEFAULT];
                $endDate = $this->options[self::DATE_RANGE_TO][self::DATE_RANGE_DEFAULT];
            }

            if ($startDate) {
                $builder->where($this->tableAndColumn, '>=', $startDate);
            }

            if ($endDate) {
                $builder->where($this->tableAndColumn, '<=', $endDate);
            }

            return $builder;
        }

        return match ($value[self::DATE_RANGE_PRESET]) {
            'Last Hour'             => $builder->where($this->tableAndColumn, '>=', now()->subHour()),
            'Last 4 Hours'          => $builder->where($this->tableAndColumn, '>=', now()->subHours(4)),
            'Last 8 Hours'          => $builder->where($this->tableAndColumn, '>=', now()->subHours(8)),
            'Today Only'            => $builder->where($this->tableAndColumn, '>=', now()->startOfDay()),
            'Yesterday Only'        => $builder->where($this->tableAndColumn, '>=', now()->subDay()->startOfDay())
                ->where($this->tableAndColumn, '<=', now()->startOfDay()),
            'Last 24 Hours'         => $builder->where($this->tableAndColumn, '>=', now()->subDay()),
            'Last Week'             => $builder->where($this->tableAndColumn, '>=', now()->subWeek()),
            'Last Month'            => $builder->where($this->tableAndColumn, '>=', now()->subMonth()),
            'This Calendar Month'   => $builder->where($this->tableAndColumn, '>=', now()->startOfMonth()),
            'Last 3 Months'         => $builder->where($this->tableAndColumn, '>=', now()->subMonths(3)),
            'Last 12 Months'        => $builder->where($this->tableAndColumn, '>=', now()->subYear()),
            default                 => $builder,
        };
    }
}
