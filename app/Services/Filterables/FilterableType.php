<?php

namespace App\Services\Filterables;

/**
 * Each FilterableType must have a corresponding Vue Renderer to handle display & input
 */
enum FilterableType: string
{
    case INPUT = 'input';
    case SELECT = 'select';
    case MULTI_SELECT = 'multi-select';
    case DATE_RANGE = 'date-range';
    case DUAL_OPERATOR = 'dual-operator';
    case DUAL_OPERATOR_WITH_TIME_FRAME = 'dual-operator-with-timeframe';
    case CHECKBOX_LIST_DUAL_OPERATOR = 'checkbox-list-dual-operator';
    case MULTI_SELECT_WITH_LOGICAL = 'multi-select-with-logical';
    case MULTI_SELECT_WITH_CHILDREN = 'multi-select-with-children';
    case HORIZONTAL_DUAL_OPERATOR_WITH_DROPDOWN = 'horizontal-dual-operator-with-dropdown';
    case HORIZONTAL_DUAL_OPERATOR = 'horizontal-dual-operator';
    case HORIZONTAL_IS_OR_IS_NOT_MULTI_SELECT = 'horizontal-is-or-is-not-multi-select';
}
