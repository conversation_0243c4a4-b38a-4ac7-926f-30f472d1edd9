<?php

namespace App\Services\Verification;

use App\Enums\CompanyUserVerificationMethod;
use App\Jobs\SendPhoneVerificationSMS;
use App\Models\Odin\CompanyUser;
use App\Models\SmsVerification;
use App\Services\Communication\TwilioCommunicationService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Events\Verified;
use Illuminate\Support\Facades\App;

class CompanyUserVerificationService
{
    public function __construct(protected TwilioCommunicationService $communicationService) {}

    /**
     * @param CompanyUser $user
     * @param CompanyUserVerificationMethod $method
     * @param bool $newUser
     * @return string|null|bool
     */
    public function sendVerificationNotification(CompanyUser $user, CompanyUserVerificationMethod $method, bool $newUser = false): null|string|bool
    {
        switch($method) {
            case CompanyUserVerificationMethod::EMAIL: {
                if ($user->{CompanyUser::FIELD_EMAIL}) {
                    if ($newUser) {
                        event(new Registered($user));
                    } else {
                        $user->sendEmailVerificationNotification();
                    }
                    return $method->value;
                }
                break;
            }
            case CompanyUserVerificationMethod::SMS:
                return $this->sendVerificationCode($user);
            default: {
                break;
            }
        }
        return null;
    }

    /**
     * Verify a CompanyUser's notification method
     *
     * @param CompanyUser $user
     * @param CompanyUserVerificationMethod $method
     * @param string|null $code
     *
     * @return bool
     */
    public function verifyCompanyUser(CompanyUser $user, CompanyUserVerificationMethod $method, ?string $code = null ): bool
    {
        $verified = false;
        //TODO: Check other verification methods once implemented
        $alreadyVerified = $user->hasVerifiedEmail();

        switch($method) {
            case 'email': {
                if (!$user->hasVerifiedEmail()) {
                    $verified = $user->markEmailAsVerified();
                    break;
                }
            }
            case CompanyUserVerificationMethod::SMS:
                return $this->verifyPhone($user, $code);
        }

        if ($verified && !$alreadyVerified) { event(new Verified($user)); }
        return $verified;
    }

    /**
     * @param CompanyUser $user
     *
     * @return bool
     */
    protected function sendVerificationCode(CompanyUser $user): bool
    {
        if (!$user->cell_phone) return false;

        if (App::isProduction()) {
            dispatch(new SendPhoneVerificationSMS($user));
            return true;
        }

        SmsVerification::query()->create([
            SmsVerification::REFERENCE => $user->reference,
            SmsVerification::CODE => '0000',
            SmsVerification::SENT_REFERENCE => 'sms-was-not-sent-in-development',
            SmsVerification::EXPIRES_AT => now()->addHour()
        ]);

        logger()->debug('Phone verification SMS was not sent in development environment');

        return true;
    }

    /**
     * @param CompanyUser $user
     * @param string|null $code
     *
     * @return bool
     */
    protected function verifyPhone(CompanyUser $user, ?string $code): bool
    {
        if (!$code || !$user->cell_phone) return false;

        /** @var SmsVerification $smsVerification */
        $smsVerification = SmsVerification::query()->where(SmsVerification::REFERENCE, $user->reference)->latest()->first();

        if (!$smsVerification) return false;
        if (now() > $smsVerification->expires_at) return false;
        if ($smsVerification->code !== $code) return false;

        $user->phone_verified_at = now();
        $user->save();

        return true;
    }
}
