<?php

namespace App\Services\Mailbox;

use App\DTO\Mail\Email;
use App\DTO\Mail\EmailLabel;
use App\DTO\Mail\ListEmailQueryDTO;
use App\DTO\Mail\EmailPart;
use App\Enums\ContactIdentification\SearchableFieldType;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Enums\Mailbox\EmailDirection;
use App\Jobs\DispatchPubSubEvent;
use App\Models\Mailbox\MailboxEmail;
use App\Models\Mailbox\MailboxEmailAttachment;
use App\Models\Mailbox\MailboxEmailLabel;
use App\Models\Mailbox\MailboxUserEmail;
use App\Models\Mailbox\MailboxUserLabel;
use App\Models\Mailbox\MailboxUserEmailListener;
use App\Models\Mailbox\MailboxUserToken;
use App\Models\User;
use App\Repositories\Mailbox\MailboxEmailRepository;
use App\Repositories\Mailbox\MailboxUserTokenRepository;
use App\Services\CloudStorage\GoogleCloudStorageService;
use App\Services\ContactIdentification\ContactIdentificationService;
use App\Services\Mailbox\Mail\MailProvider;
use App\Services\Mailbox\Mail\MailProviderFactory;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Str;
use JetBrains\PhpStorm\ArrayShape;

class SyncMailboxService
{
    protected MailProvider $mailProvider;

    /**
     * @param MailboxEmailRepository $mailboxEmailRepository
     * @param MailboxUserTokenRepository $mailboxUserTokenRepository
     * @param ContactIdentificationService $contactIdentificationService
     * @param GoogleCloudStorageService $googleCloudStorageService
     * @throws Exception
     */
    public function __construct(
        protected MailboxEmailRepository $mailboxEmailRepository,
        protected MailboxUserTokenRepository $mailboxUserTokenRepository,
        protected ContactIdentificationService $contactIdentificationService,
        protected GoogleCloudStorageService $googleCloudStorageService
    )
    {
        $this->mailProvider = MailProviderFactory::make();
    }

    /**
     * Get user messages
     * @param User $user
     * @param int $perPage
     * @param string|null $nextPageToken
     * @param ListEmailQueryDTO|null $query
     * @return array
     * @throws Exception
     */
    #[ArrayShape(['email_ids' => "array", 'next_page_token' => "string|null"])]
    public function getMessageIds(
        User $user,
        int $perPage,
        ?string $nextPageToken = null,
        ?ListEmailQueryDTO $query = new ListEmailQueryDTO()
    ): array
    {
        $mailboxUserToken = $this->getMailboxUserToken($user);

        [$emailIds, $nextPageToken] = $this->mailProvider->getEmailIds(
            mailboxUserToken: $mailboxUserToken,
            perPage         : $perPage,
            query           : $query,
            pageToken       : $nextPageToken
        );

        return [
            'email_ids'       => $emailIds,
            'next_page_token' => $nextPageToken,
        ];
    }

    /**
     * @param User $user
     * @return MailboxUserLabel[]
     * @throws Exception
     */
    public function syncLabels(User $user): array
    {
        $mailboxUserToken = $this->getMailboxUserToken($user);

        $emailLabels = $this->mailProvider->getUserCustomEmailLabels($mailboxUserToken);

        $result = [];

        foreach ($emailLabels as $emailLabel) {
            try {
                $result[] = $this->syncLabel($emailLabel, $user);
            } catch (Exception $e) {
                logger()->error('Error saving label ' . $e->getMessage());
            }
        }

        return $result;
    }

    /**
     * @param EmailLabel $emailLabel
     * @param User $user
     * @return MailboxUserLabel
     */
    public function syncLabel(EmailLabel $emailLabel, User $user): MailboxUserLabel
    {
        $label = MailboxUserLabel::query()->where(MailboxUserLabel::FIELD_EXTERNAL_ID, $emailLabel->getId())->first();

        if ($label) {
            $label->update([
                MailboxUserLabel::FIELD_NAME => $emailLabel->getName(),
                MailboxUserLabel::FIELD_SLUG => Str::slug($emailLabel->getName()),
            ]);
        } else {
            $label = new MailboxUserLabel();
            $label->fill([
                MailboxUserLabel::FIELD_EXTERNAL_ID => $emailLabel->getId(),
                MailboxUserLabel::FIELD_NAME        => $emailLabel->getName(),
                MailboxUserLabel::FIELD_SLUG        => Str::slug($emailLabel->getName()),
                MailboxUserLabel::FIELD_USER_ID     => $user->{User::FIELD_ID},
            ]);
            $label->save();
        }

        return $label;
    }

    /**
     * @param MailboxEmail $mailboxEmail
     * @param Email $userEmail
     * @param User $user
     * @return void
     */
    private function syncMailboxUserEmail(MailboxEmail $mailboxEmail, Email $userEmail, User $user): void
    {
        $found = MailboxUserEmail::query()
            ->where(MailboxUserEmail::FIELD_USER_ID, $user->{User::FIELD_ID})
            ->where(MailboxUserEmail::FIELD_EMAIL_ID, $mailboxEmail->{User::FIELD_ID})
            ->first();

        $common = [
            MailboxUserEmail::FIELD_IS_INBOX            => $userEmail->getFlags()->getIsInbox(),
            MailboxUserEmail::FIELD_IS_SENT             => $userEmail->getFlags()->getIsSent(),
            MailboxUserEmail::FIELD_IS_STARRED          => $userEmail->getFlags()->getIsStarred(),
            MailboxUserEmail::FIELD_IS_IMPORTANT        => $userEmail->getFlags()->getIsImportant(),
            MailboxUserEmail::FIELD_IS_READ             => $userEmail->getFlags()->getIsRead(),
            MailboxUserEmail::FIELD_IS_ARCHIVED         => $userEmail->getFlags()->getIsArchived(),
            MailboxUserEmail::FIELD_EXTERNAL_ID         => $userEmail->getId(),
            MailboxUserEmail::FIELD_EXTERNAL_THREAD_ID  => $userEmail->getThreadId(),
            MailboxUserEmail::FIELD_EXTERNAL_HISTORY_ID => $userEmail->getHistoryId(),
            MailboxUserEmail::FIELD_EXTERNAL_REFERENCES => $userEmail->getMeta()->getReferences(),
            MailboxUserEmail::FIELD_SENT_AT             => $userEmail->getMeta()->getUtcDate(),
            MailboxUserEmail::FIELD_DELETED_AT          => $userEmail->getFlags()->getIsTrash() ? now() : '',
        ];

        if (!$found) {
            MailboxUserEmail::query()->create(array_merge(
                [
                    MailboxUserEmail::FIELD_USER_ID  => $user->{User::FIELD_ID},
                    MailboxUserEmail::FIELD_EMAIL_ID => $mailboxEmail->{User::FIELD_ID},
                    MailboxUserEmail::FIELD_UUID     => Str::uuid()
                ], $common
            ));
        } else {
            $found->update($common);
        }
    }

    /**
     * Sync an user email by email dto or emailId
     * @param string|Email $email
     * @param User $user
     * @param bool $dispatchEmailReceivedEvent
     * @return MailboxEmail
     * @throws Exception
     */
    public function syncEmail(string|Email $email, User $user, bool $dispatchEmailReceivedEvent = false): MailboxEmail
    {
        if (gettype($email) === 'string') {
            $email = $this->getMessage($user, $email);
        }

        /** @var ?MailboxEmail $mailboxEmail */
        $mailboxEmail = MailboxEmail::query()
            ->where(MailboxEmail::FIELD_EXTERNAL_MESSAGE_ID, $email->getMeta()->getMessageId())
            ->first();

        $isNewEmail = false;

        if ($mailboxEmail) {
            $mailboxEmail->update([
                MailboxEmail::FIELD_EXTERNAL_MESSAGE_ID => $email->getMeta()->getMessageId(),
            ]);
        } else {
            $mailboxEmail = $this->createMailboxEmail($email, $user);
            $isNewEmail = true;
        }

        $this->syncMailboxUserEmail($mailboxEmail, $email, $user);
        $this->syncMailboxRecipients($mailboxEmail, $email);

        if ($isNewEmail && $dispatchEmailReceivedEvent) {
            DispatchPubSubEvent::dispatch(
                EventCategory::MAILBOX,
                EventName::MAILBOX_EMAIL_RECEIVED,
                [
                    'email_id' => $mailboxEmail->{MailboxEmail::FIELD_ID},
                    'user_id'  => $user->{User::FIELD_ID}
                ]
            );
        }

        return $mailboxEmail;
    }

    /**
     * Set up a listener for user's new emails
     * @param User $user
     * @return MailboxUserEmailListener
     * @throws Exception
     */
    public function setupUserNewEmailsListener(User $user): MailboxUserEmailListener
    {
        $mailboxUserToken = $this->getMailboxUserToken($user);

        $response = $this->mailProvider->setupEmailListener($mailboxUserToken, 'me');

        if (!$response->getExpiresAt()) {
            throw new Exception("Something went wrong setting up a listener for user $user->id");
        }

        return $this->saveMailboxUserEmailListener(
            user     : $user,
            expiresAt: $response->getExpiresAt()
        );
    }

    /**
     * @param User $user
     * @return void
     * @throws Exception
     */
    public function removeUserEmailsListener(User $user): void
    {
        $mailboxUserToken = $this->getMailboxUserToken($user);

        $this->mailProvider->removeEmailListener($mailboxUserToken, 'me');
    }

    /**
     * @param User $user
     * @param Carbon $expiresAt
     * @return MailboxUserEmailListener
     */
    public function saveMailboxUserEmailListener(
        User $user,
        Carbon $expiresAt
    ): MailboxUserEmailListener
    {
        $found = MailboxUserEmailListener::query()
            ->where(MailboxUserEmailListener::FIELD_USER_ID, $user->{User::FIELD_ID})
            ->first();

        if ($found) {
            $found->update([
                MailboxUserEmailListener::FIELD_EXPIRES_AT => $expiresAt
            ]);

            return $found;
        }

        $mailboxEmailListener = new MailboxUserEmailListener();
        $mailboxEmailListener->fill([
            MailboxUserEmailListener::FIELD_EXPIRES_AT => $expiresAt,
            MailboxUserEmailListener::FIELD_USER_ID    => $user->{User::FIELD_ID}
        ]);
        $mailboxEmailListener->save();

        return $mailboxEmailListener;
    }

    /**
     * @param Email $userEmail
     * @param User $user
     * @return MailboxEmail
     */
    public function createMailboxEmail(Email $userEmail, User $user): MailboxEmail
    {
        $userEmailMeta = $userEmail->getMeta();

        $allUserEmails = collect([
            $user->{User::FIELD_EMAIL},
            $user->{User::FIELD_EMAIL_ALIASES}
        ])->filter()->flatten();

        $direction = $allUserEmails->contains($userEmailMeta->getFrom())
            ? EmailDirection::OUTBOUND
            : EmailDirection::INBOUND;

        $identifiedContact = $this->contactIdentificationService->createIdentifiedContactAndDispatchJob(
            identifierValue: $userEmail->getMeta()->getFrom(),
            type           : SearchableFieldType::EMAIL
        );

        $mailboxEmail = new MailboxEmail();
        $mailboxEmail->fill([
            MailboxEmail::FIELD_FROM_USER_ID               => $direction === EmailDirection::OUTBOUND ? $user->{User::FIELD_ID} : null,
            MailboxEmail::FIELD_FROM_USER_EMAIL            => $direction === EmailDirection::OUTBOUND ? $userEmailMeta->getFrom() : null,
            MailboxEmail::FIELD_DIRECTION                  => $direction->value,
            MailboxEmail::FIELD_EXTERNAL_MESSAGE_ID        => $userEmailMeta->getMessageId(),
            MailboxEmail::FIELD_FROM_IDENTIFIED_CONTACT_ID => $identifiedContact->id,
            MailboxEmail::FIELD_CONTENT                    => $userEmail->getContent(),
            MailboxEmail::FIELD_SUBJECT                    => $userEmail->getMeta()->getSubject(),
            MailboxEmail::FIELD_SNIPPET                    => $userEmail->getSnippet(),
            MailboxEmail::FIELD_SENT_AT                    => $userEmail->getMeta()->getUtcDate(),
        ]);
        $mailboxEmail->save();

        return $mailboxEmail;
    }

    /**
     * @param string $messageId
     * @param EmailPart $part
     * @return EmailPart
     * @throws Exception
     */
    private function saveAttachmentsToCloud(string $messageId, EmailPart $part): EmailPart
    {
        $this->googleCloudStorageService->setCurrentBucket(config('services.google.storage.buckets.mailbox'));

        if (empty($part->getAttachmentId())) {
            return $part;
        }

        $stored = $this->googleCloudStorageService->upload('/emails/' . $messageId . '/' . $part->getFileName(), $part->getContent());
        $url = $stored->signedUrl(now()->addCentury()->toDateTime());
        $part->setUrl($url);

        return $part;
    }

    /**
     * @param MailboxEmail $mailboxEmail
     * @param Email $userEmail
     * @return bool
     * @throws Exception
     */
    public function syncMailboxEmailAttachments(MailboxEmail $mailboxEmail, Email $userEmail): bool
    {
        $collectionOfAttachments = collect($userEmail->getAttachments());

        $savedEmailAttachments = $mailboxEmail->{MailboxEmail::RELATION_ATTACHMENTS};

        $checkAttachmentHasBeenSaved = function (EmailPart $attachment) use ($savedEmailAttachments) {
            return $savedEmailAttachments
                ->first(fn(MailboxEmailAttachment $mailboxEmailAttachment) => $mailboxEmailAttachment->{MailboxEmailAttachment::FIELD_SIZE} === $attachment->getSize()
                    && $mailboxEmailAttachment->{MailboxEmailAttachment::FIELD_EXTERNAL_FILENAME} === $attachment->getFileName()
                );
        };

        foreach ($collectionOfAttachments as $item) {
            if (!$checkAttachmentHasBeenSaved($item)) {
                try {
                    $partInCloud = $this->saveAttachmentsToCloud($userEmail->getId(), $item);

                    MailboxEmailAttachment::query()->create([
                        MailboxEmailAttachment::FIELD_EMAIL_ID               => $mailboxEmail->{MailboxEmail::FIELD_ID},
                        MailboxEmailAttachment::FIELD_EXTERNAL_ID            => $partInCloud->getId(),
                        MailboxEmailAttachment::FIELD_EXTERNAL_ATTACHMENT_ID => $partInCloud->getAttachmentId(),
                        MailboxEmailAttachment::FIELD_EXTERNAL_FILENAME      => $partInCloud->getFileName(),
                        MailboxEmailAttachment::FIELD_EXTERNAL_TYPE          => $partInCloud->getMimeType(),
                        MailboxEmailAttachment::FIELD_URL                    => $partInCloud->getUrl(),
                        MailboxEmailAttachment::FIELD_SIZE                   => $partInCloud->getSize(),
                    ]);
                } catch (Exception $exception) {
                    logger()->error($exception);
                }
            }
        }

        return true;
    }

    /**
     * @param MailboxEmail $mailboxEmail
     * @param Email $userEmail
     * @param User $user
     * @return void
     */
    public function syncMailboxEmailLabels(MailboxEmail $mailboxEmail, Email $userEmail, User $user): void
    {
        $newLabelIds = MailboxUserLabel::query()
            ->whereIn(MailboxUserLabel::FIELD_EXTERNAL_ID, $userEmail->getLabelIds())
            ->where(MailboxUserLabel::FIELD_USER_ID, $user->{User::FIELD_ID})
            ->get()
            ->pluck(MailboxUserLabel::FIELD_ID);

        MailboxUserLabel::query()->whereNotIn(MailboxUserLabel::FIELD_ID, $newLabelIds)->delete(); // Remove roles not in the new list

        foreach ($newLabelIds as $labelId) {
            $data = [
                MailboxEmailLabel::FIELD_LABEL_ID => $labelId,
                MailboxEmailLabel::FIELD_EMAIL_ID => $mailboxEmail->{MailboxEmail::FIELD_ID},
            ];

            MailboxEmailLabel::query()->firstOrCreate($data, $data);
        }
    }

    /**
     * @param MailboxEmail $mailboxEmail
     * @param Email $userEmail
     * @return bool
     */
    public function syncMailboxRecipients(MailboxEmail $mailboxEmail, Email $userEmail): bool
    {
        $emailMeta = $userEmail->getMeta();

        $this->mailboxEmailRepository->createEmailRecipients(
            mailboxEmail: $mailboxEmail,
            to          : $emailMeta->getTo(),
            bcc         : $emailMeta->getBcc(),
            cc          : $emailMeta->getCc()
        );

        return true;
    }

    /**
     * Get one message
     * @param User $user
     * @param string $messageId
     * @return Email|null
     * @throws Exception
     */
    public function getMessage(User $user, string $messageId): ?Email
    {
        $token = $this->getMailboxUserToken($user);

        return $this->mailProvider->getEmail($token, $messageId);
    }

    /**
     * @param User $user
     * @return MailboxUserToken
     * @throws Exception
     */
    private function getMailboxUserToken(User $user): MailboxUserToken
    {
        $mailboxUserToken = $this->mailboxUserTokenRepository->getLatestUserEmailToken($user);

        if (!$mailboxUserToken) {
            throw new Exception('MailboxUserToken not found for user ' . $user->{User::FIELD_ID});
        }

        return $mailboxUserToken;
    }

    /**
     * @param User $user
     * @return void
     * @throws Exception
     */
    public function syncEmailSignature(User $user): void
    {
        $token = MailboxUserToken::query()
            ->where(MailboxUserToken::FIELD_USER_ID, $user->id)
            ->first();

        if (!$token) {
            throw new Exception("Mailbox token not found for user $user->id");
        }

        $primaryEmailSignature = $this->mailProvider->getPrimaryEmailSignature(
            mailboxUserToken: $token
        );

        if (!$primaryEmailSignature) {
            throw new Exception("User $user->id does not have a primary signature set");
        }

        if (empty($primaryEmailSignature->getSignature())) {
            throw new Exception("User signature is empty. User id $user->id");
        }

        $user->update([
           User::FIELD_EMAIL_SIGNATURE => $primaryEmailSignature->getSignature()
        ]);
    }
}
