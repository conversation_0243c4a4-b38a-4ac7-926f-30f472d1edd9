<?php

namespace App\Services\Sortable\Company;

use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyLeadRejectionPercentageOverallSortableOption extends BaseSortableOption
{
    use CompanyRejectionPercentageSortableTrait;

    protected string $id = 'overall-lead-rejection-percentage';

    public function appendToQuery(Builder $existingQuery, string $direction): void
    {
        $this->getAverageOverallLeadRejectionPercentageField($existingQuery);

        $existingQuery->orderBy('average_overall_lead_rejection_percentage', $direction);
    }
}
