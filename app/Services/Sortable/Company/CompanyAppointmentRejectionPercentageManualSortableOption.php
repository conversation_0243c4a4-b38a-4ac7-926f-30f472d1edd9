<?php

namespace App\Services\Sortable\Company;

use App\Models\ComputedRejectionStatistic;
use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyAppointmentRejectionPercentageManualSortableOption extends BaseSortableOption
{
    use CompanyRejectionPercentageSortableTrait;

    protected string $id = 'manual-appointment-rejection-percentage';

    public function appendToQuery(Builder $existingQuery, string $direction): void
    {
        $this->getAverageManualAppointmentRejectionPercentageField($existingQuery);

        $existingQuery->orderBy('average_manual_appointment_rejection_percentage', $direction);
    }
}
