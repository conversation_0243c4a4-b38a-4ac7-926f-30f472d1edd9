<?php

namespace App\Services\Sortable\Company;

use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyLifetimeRevenueSortableOption extends BaseSortableOption
{
    protected string $id = 'lifetime-revenue';

    public function appendToQuery(Builder $existingQuery, string $direction): void
    {
        $existingQuery->orderBy('lifetime_revenue', $direction);
    }

}
