<?php

namespace App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\ConsumerCommonGetter;

use App\Models\Odin\ConsumerProduct;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\BaseConsumerDataGetter;

class ConsumerAddressStreetNumberStreetNameGetter extends BaseConsumerDataGetter
{
    const ID = 'address_street_number_and_name';

    function getValue(ConsumerProduct $consumerProduct): string
    {
        return $consumerProduct->address->address_1;
    }
}
