<?php

namespace App\Services\Odin;

use App\Enums\CompanyMediaAssetType;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyMediaAsset;
use App\Services\Companies\CompanyProfileService;
use App\Services\FileUploadHelperService;
use Carbon\Carbon;
use Exception;
use Google\Cloud\Core\Exception\BadRequestException;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class MigrateAWSCompanyMediaService
{
    const LEGACY_MEDIA_ROOT = 'company_media';

    /**
     * @param CompanyProfileService $profileService
     */
    public function __construct(
        protected CompanyProfileService $profileService,
    ) {}

    /**
     * @param Collection $companies
     * @return void
     * @throws Exception
     */
    public function handle(Collection $companies): void
    {
        $companyMediaAssets = [];

        /** @var Company $company */
        foreach ($companies as $company) {
            $mediaLinks = $this->profileService->getObjectsFromBucket(
                path             : self::LEGACY_MEDIA_ROOT."/{$company->{Company::FIELD_LEGACY_ID}}",
                transformToFiles : true,
            );

            foreach ($mediaLinks as $mediaLink) {
                /** @var UploadedFile|null $file */
                $file = $this->prepareFileForUpload($mediaLink);

                if (!empty($file)) {
                    $fileNameToUpload = FileUploadHelperService::encryptFileName($file->getClientOriginalName(), true);

                    $companyMediaAssetBaseCriteria = [
                        CompanyMediaAsset::FIELD_COMPANY_ID => $company->{Company::FIELD_ID},
                        CompanyMediaAsset::FIELD_URL        => $this->prepareNewUrlForUpload($company, $fileNameToUpload),
                    ];

                    /** @var CompanyMediaAsset|null $alreadyAdded */
                    $alreadyAdded = CompanyMediaAsset::query()->where($companyMediaAssetBaseCriteria)->first();

                    if(!$alreadyAdded) {
                        try {
                            $url = $this->uploadFileToCloud($company, $file, $fileNameToUpload);

                            if (!is_null($url) && strlen(trim($url)) > 0) {
                                $now = Carbon::now()->toDateTimeString();

                                $companyMediaAssets[] = [
                                    ...$companyMediaAssetBaseCriteria,
                                    CompanyMediaAsset::FIELD_NAME       => $file->getClientOriginalName(),
                                    CompanyMediaAsset::FIELD_TYPE       => CompanyMediaAssetType::MEDIA->value,
                                    CompanyMediaAsset::FIELD_CREATED_AT => $now,
                                    CompanyMediaAsset::FIELD_UPDATED_AT => $now,
                                ];

                                if(count($companyMediaAssets) >= 1000) {
                                    DB::table(CompanyMediaAsset::TABLE)->insert($companyMediaAssets);

                                    $companyMediaAssets = [];
                                }
                            } else {
                                logger()->error("Skipping the legacy media: `{$mediaLink}` since it couldn't be uploaded to the cloud.");
                            }
                        } catch (Throwable $e) {
                            logger()->error("Something went wrong while uploading the legacy media: `{$mediaLink}`. Exception: " . $e->getMessage()."\n\n");
                        }
                    }
                } else {
                    logger()->error("The legacy link of a media is invalid: `{$mediaLink}`");
                }
            }
        }

        if(!empty($companyMediaAssets)) {
            DB::table(CompanyMediaAsset::TABLE)->insert($companyMediaAssets);
        }
    }

    /**
     * Handles preparing the cloud storage URL for the given file name against the requested company.
     *
     * @param Company $company
     * @param string  $fileName
     * @return string
     */
    protected function prepareNewUrlForUpload(Company $company, string $fileName): string
    {
        $path   = $this->profileService->getMediaUploadPath($company);
        $url    = config('services.google.storage.urls.company_logos_file_url');
        $bucket = config('services.google.storage.buckets.company_logos');

        return rtrim($url, '/') . "/{$bucket}/{$path}/{$fileName}";
    }

    /**
     * Handles processing the given link of a media and returns its `UploadedFile` instance.
     *
     * @param string $fileUrl
     * @return UploadedFile|null
     * @throws BadRequestException
     */
    protected function prepareFileForUpload(string $fileUrl): ?UploadedFile
    {
        if(!strlen(trim($fileUrl))) {
            return null;
        }

        $linkValues = explode('/', $fileUrl);
        $fileName   = end($linkValues);

        return FileUploadHelperService::createFromUrl(
            url            : $fileUrl,
            originalName   : $fileName,
            throwException : false,
        );
    }

    /**
     * Handles uploading the given file to the cloud.
     *
     * @param Company      $company
     * @param UploadedFile $file
     * @param string       $fileNameToUpload
     * @return string|null
     * @throws Exception
     */
    protected function uploadFileToCloud(Company $company, UploadedFile $file, string $fileNameToUpload): ?string
    {
        $path = $this->profileService->getMediaUploadPath($company);

        return $this->profileService->uploadInCloud($path, $file, $fileNameToUpload);
    }
}
