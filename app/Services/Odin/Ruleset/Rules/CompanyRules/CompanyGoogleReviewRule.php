<?php

namespace App\Services\Odin\Ruleset\Rules\CompanyRules;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyReview;
use App\Services\Odin\Ruleset\Rules\Rule;
use App\Services\Odin\Ruleset\Enums\ConditionType;
use App\Services\Odin\Ruleset\Enums\OperationType;
use App\Services\Odin\Ruleset\Interfaces\RulesetFilterRuleInterface;
use App\Services\Odin\Ruleset\Interfaces\RulesetRankingRuleInterface;
use App\Services\Odin\Ruleset\Traits\RulesetFilterRuleTrait;
use App\Services\Odin\Ruleset\Traits\RulesetRankingRuleTrait;
use Exception;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class CompanyGoogleReviewRule extends Rule implements RulesetRankingRuleInterface, RulesetFilterRuleInterface
{
    use RulesetRankingRuleTrait, RulesetFilterRuleTrait;

    public static string $id = 'google_reviews';
    protected string $label = 'Consumer Reviews';
    protected string $description = 'Based on internal consumer review data';
    protected array $availableOperationTypes = [
        OperationType::LESS_THAN,
        OperationType::LESS_THAN_OR_EQUAL_TO,
        OperationType::BETWEEN,
        OperationType::GREATER_THAN,
        OperationType::GREATER_THAN_OR_EQUAL_TO,
        OperationType::EQUAL_TO,
    ];

    protected array $availableVariables = [
        "zero_stars"    => "Zero stars",
        "one_star"      => "One star",
        "two_stars"     => "Two stars",
        "three_stars"   => "Three stars",
        "four_stars"    => "Four stars",
        "five_stars"    => "Five stars",
    ];

    CONST OVERALL_SCORE_FIELD = 'overall_score';
    CONST TOTAL_FIELD = 'total';

    CONST FIELD_ZERO_STARS = 'zero_stars';
    CONST FIELD_ONE_STAR = 'one_star';
    CONST FIELD_TWO_STARS = 'two_stars';
    CONST FIELD_THREE_STARS = 'three_stars';
    CONST FIELD_FOUR_STARS = 'four_stars';
    CONST FIELD_FIVE_STARS = 'five_stars';

    const GOOGLE_REVIEW_SCORES = [
        0  => self::FIELD_ZERO_STARS,
        1  => self::FIELD_ONE_STAR,
        2  => self::FIELD_TWO_STARS,
        3  => self::FIELD_THREE_STARS,
        4  => self::FIELD_FOUR_STARS,
        5  => self::FIELD_FIVE_STARS,
    ];


    public function getReference($entry): array
    {
        $groupedCompanyExternalReviews = $this->getCompanyExternalReviews($entry);

        return $this->consolidateCompanyReviews($groupedCompanyExternalReviews);
    }

    /**
     * Consolidate the total of reviews by the quantity of stars
     * @param array $groupedCompanyReviews
     * @return array
     */
    private function consolidateCompanyReviews(array $groupedCompanyReviews): array {
        $result = [];

        foreach (self::GOOGLE_REVIEW_SCORES as $id => $name) {
            // Filter greater or equal
            $sameScoreOrGreater = collect($groupedCompanyReviews)->filter(function ($item) use($id) {
                return $item[self::OVERALL_SCORE_FIELD] >= $id;
            });

            $total = 0;

            if ($sameScoreOrGreater->count() > 0) {
                // Sum all quantities and assign
                $aggregated = $sameScoreOrGreater->reduce(function (int $carry, array $item) {
                    return $carry + $item[self::TOTAL_FIELD];
                }, 0);

                $total = $aggregated;
            }

            $result[$name] = $total;
        }

        return $result;
    }

    /**
     * Get company external review scores grouped by its overall score
     * @param Company $entry
     * @return array
     */
    private function getCompanyExternalReviews(Company $entry): array
    {
        return CompanyReview::query()
            ->select(CompanyReview::TABLE . '.' . CompanyReview::FIELD_OVERALL_SCORE . ' as ' . self::OVERALL_SCORE_FIELD, DB::raw('count(*) as ' . self::TOTAL_FIELD))
            ->orderBy(CompanyReview::FIELD_OVERALL_SCORE, 'desc')
            ->groupBy(CompanyReview::FIELD_OVERALL_SCORE)
            ->where(CompanyReview::TABLE . '.' . CompanyReview::FIELD_COMPANY_ID, $entry->id)
            ->get()
            ->toArray();
    }


    private function getRateValueByKey(string $key){
        $found = collect(self::GOOGLE_REVIEW_SCORES)->filter(function ($item) use($key) {
            return value($item) === $key;
        });

        if (empty($found)) return 0;

        return $found->keys()[0];
    }

    /**
     * @param Builder $query
     * @return Builder
     * @throws Exception
     */
    public function getQuery(Builder $query): Builder
    {
        foreach ($this->data->conditions as $condition) {
            if ($this->getConditionType($condition) === ConditionType::CHECK->value) {
                foreach ($condition->operations as $operation) {

                    $operationRef = $operation->reference;
                    if (!$operationRef) continue;

                    $aggregateInValue = $this->getRateValueByKey($operation->comparisonFieldName);

                    switch ($operation->type->value) {
                        case OperationType::BETWEEN->value:
                            $query->where(function ($query) use ($aggregateInValue) {
                                $query
                                    ->selectRaw('COUNT(*)')
                                    ->from(CompanyReview::TABLE)
                                    ->whereRaw(CompanyReview::TABLE . '.' . CompanyReview::FIELD_COMPANY_ID . ' = ' . Company::TABLE . '.' . Company::FIELD_ID)
                                    ->where(CompanyReview::TABLE . '.' . CompanyReview::FIELD_OVERALL_SCORE, '>=', $aggregateInValue);
                            }, '>=', +$operationRef['start']);

                            $query->where(function ($query) use ($aggregateInValue) {
                                $query
                                    ->selectRaw('COUNT(*)')
                                    ->from(CompanyReview::TABLE)
                                    ->whereRaw(CompanyReview::TABLE . '.' . CompanyReview::FIELD_COMPANY_ID . ' = ' . Company::TABLE . '.' . Company::FIELD_ID)
                                    ->where(CompanyReview::TABLE . '.' . CompanyReview::FIELD_OVERALL_SCORE, '>=', $aggregateInValue);
                            }, '<=', +$operationRef['end']);
                            break;
                        default:
                            $query->where(function ($query) use ($aggregateInValue) {
                                $query
                                    ->selectRaw('COUNT(*)')
                                    ->from(CompanyReview::TABLE)
                                    ->whereRaw(CompanyReview::TABLE . '.' . CompanyReview::FIELD_COMPANY_ID . ' = ' . Company::TABLE . '.' . Company::FIELD_ID)
                                    ->where(CompanyReview::TABLE . '.' . CompanyReview::FIELD_OVERALL_SCORE, '>=', $aggregateInValue);
                            }, $operation->getSqlOperator(), +$operationRef['start']);
                    }
                }
            }
        }

        return $query;
    }
}
