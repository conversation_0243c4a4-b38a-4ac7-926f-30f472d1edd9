<?php

namespace App\Services\Odin;

use App\DataModels\Odin\ConfigurableFieldDataModel;
use App\Models\Odin\CompanyReviewTypeField;
use App\Models\Odin\CompanyUserField;
use App\Models\Odin\ConfigurableFieldType;
use App\Models\Odin\ConsumerFieldModuleVisibility;
use App\Models\Odin\GlobalCompanyField;
use App\Models\Odin\GlobalCompanyReviewField;
use App\Models\Odin\IndustryCompanyField;
use App\Models\Odin\IndustryCompanyReviewField;
use App\Models\Odin\IndustryConsumerField;
use App\Models\Odin\ServiceCompanyField;
use App\Models\Odin\ServiceCompanyReviewField;
use App\Models\Odin\ServiceConsumerField;
use App\Repositories\Odin\ConsumerConfigurableFieldCategoryRepository;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\Enums\ConsumerFieldSource;
use Exception;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class ConfigurableFieldsService
{
    const CATEGORY_GLOBAL = 'global';
    const CATEGORY_SERVICE = 'service';
    const CATEGORY_INDUSTRY = 'industry';
    const CATEGORY_GLOBAL_TYPE = 'global_type';

    const CATEGORIES = [
        self::CATEGORY_GLOBAL,
        self::CATEGORY_SERVICE,
        self::CATEGORY_INDUSTRY,
        self::CATEGORY_GLOBAL_TYPE
    ];

    const TYPE_COMPANY_REVIEWS = 'company_reviews';
    const TYPE_COMPANY = 'company';
    const TYPE_CONSUMER = 'consumer';
    const TYPE_COMPANY_USER = 'company_user';

    const TYPES = [
        self::TYPE_COMPANY_REVIEWS,
        self::TYPE_COMPANY,
        self::TYPE_CONSUMER,
        self::TYPE_COMPANY_USER
    ];

    const MODELS = [
        self::TYPE_COMPANY => [
            self::CATEGORY_GLOBAL => GlobalCompanyField::class,
            self::CATEGORY_INDUSTRY => IndustryCompanyField::class,
            self::CATEGORY_SERVICE => ServiceCompanyField::class
        ],
        self::TYPE_COMPANY_REVIEWS => [
            self::CATEGORY_GLOBAL => GlobalCompanyReviewField::class,
            self::CATEGORY_INDUSTRY => IndustryCompanyReviewField::class,
            self::CATEGORY_SERVICE => ServiceCompanyReviewField::class,
            self::CATEGORY_GLOBAL_TYPE => CompanyReviewTypeField::class
        ],
        self::TYPE_CONSUMER => [
            self::CATEGORY_INDUSTRY => IndustryConsumerField::class,
            self::CATEGORY_SERVICE => ServiceConsumerField::class
        ],
        self::TYPE_COMPANY_USER => [
            self::CATEGORY_GLOBAL => CompanyUserField::class,
        ]
    ];

    const COLUMN_PAYLOAD = 'payload';

    public function __construct(protected ConsumerConfigurableFieldCategoryRepository $consumerConfigurableFieldCategoryRepository)
    {

    }

    /**
     * @param string $type
     * @param string $category
     * @param int|null $categoryId
     * @return Collection
     * @throws Exception
     */
    public function getFields(string $type, string $category, ?int $categoryId = null): Collection
    {
        $model = $this->determineModel($type, $category);

        return $this->getBaseQuery($model, $category, $categoryId)->get();
    }

    /**
     * @param Collection $fields
     * @param string $type
     * @param string $category
     * @param int|null $categoryId
     * @return bool
     * @throws Exception
     */
    public function saveFields(Collection $fields, string $type, string $category, ?int $categoryId = null): bool
    {
        $model = $this->determineModel($type, $category);

        DB::transaction(function() use ($fields, $category, $categoryId, $model) {
            $existingIds = $this->getBaseQuery($model, $category, $categoryId)->pluck($model::FIELD_ID)->toArray();
            $incomingIds = $fields->pluck($model::FIELD_ID)->filter()->toArray();

            $deleteIds = array_diff($existingIds, $incomingIds);

            ConsumerFieldModuleVisibility::query()
                ->where(ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_TYPE, ConsumerFieldSource::CONFIGURABLE->value)
                ->where(ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_CATEGORY, $category)
                ->when($categoryId, function ($query) use($categoryId) {
                    $query->where(ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_CATEGORY_ID, $categoryId);
                })
                ->whereIn(ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_ID, $deleteIds)
                ->delete();

            $model->newQuery()->whereIn($model::FIELD_ID, $deleteIds)->delete();

            $fillableCols = array_diff(Schema::getColumnListing($model::TABLE), $model->getGuarded(), [$model::FIELD_ID]);

            foreach($fields as $field) {
                $fieldModel = $model->newQuery()->firstOrNew([$model::FIELD_ID => $field[$model::FIELD_ID]]);

                if(isset($field[self::COLUMN_PAYLOAD])) {
                    $field[self::COLUMN_PAYLOAD] = ConfigurableFieldDataModel::fromArray($field[self::COLUMN_PAYLOAD]);
                }

                foreach($fillableCols as $fillableCol) {
                    // Ensure that the field key value in database is kept on field update for already
                    if ($fillableCol === $model::FIELD_KEY && isset($fieldModel->id)) continue;

                    $fieldModel->{$fillableCol} = $field[$fillableCol];
                }

                if(!empty($field[$model::FIELD_ID])) {
                    $fieldModel->{$model::UPDATED_AT} = date('Y-m-d H:i:s');
                }
                else {
                    $fieldModel->{$model::CREATED_AT} = date('Y-m-d H:i:s');
                }

                $fieldModel->save();
            }
        });

        return true;
    }

    /**
     * @param string $category
     * @throws Exception
     */
    private function validateCategory(string $category): void
    {
        if(!in_array($category, self::CATEGORIES)) {
            throw new Exception("Invalid category");
        }
    }

    /**
     * @param string $type
     * @throws Exception
     */
    private function validateType(string $type): void
    {
        if(!in_array($type, self::TYPES)) {
            throw new Exception("Invalid type");
        }
    }

    /**
     * @param string $type
     * @param string $category
     * @return Model
     * @throws Exception
     */
    public function determineModel(string $type, string $category): Model
    {
        $this->validateType($type);
        $this->validateCategory($category);

        $modelName = self::MODELS[$type][$category];

        return new $modelName();
    }

    /**
     * @param Model $model
     * @param string $category
     * @param int|null $categoryId
     * @return Builder
     */
    private function getBaseQuery(Model $model, string $category, ?int $categoryId = null): Builder
    {
        if($category === self::CATEGORY_SERVICE && $categoryId) {
            return $model->newQuery()->where($model::FIELD_INDUSTRY_SERVICE_ID, $categoryId);
        }
        else if($category === self::CATEGORY_INDUSTRY && $categoryId) {
            return $model->newQuery()->where($model::FIELD_INDUSTRY_ID, $categoryId);
        }
        else if($category === self::CATEGORY_GLOBAL_TYPE) {
            return $model->newQuery()->where($model::FIELD_GLOBAL_TYPE_ID, $categoryId);
        }
        else {
            return $model->newQuery();
        }
    }

    /**
     * @param string $type
     * @param string $category
     * @return Collection
     * @throws Exception
     */
    public function getModelColumns(string $type, string $category): Collection
    {
        $model = $this->determineModel($type, $category);

        return collect(Schema::getColumnListing($model::TABLE));
    }

    /**
     * @return Collection
     */
    public function getFieldTypes(): Collection
    {
        return ConfigurableFieldType::all()->map(function(ConfigurableFieldType $configurableFieldType) {
            return [
                ConfigurableFieldType::FIELD_TYPE => $configurableFieldType->{ConfigurableFieldType::FIELD_TYPE},
                ConfigurableFieldType::FIELD_ID => $configurableFieldType->{ConfigurableFieldType::FIELD_ID},
                ConfigurableFieldType::FIELD_LABEL => $configurableFieldType->{ConfigurableFieldType::FIELD_LABEL}
            ];
        });
    }


    /**
     * Returns all consumer configurable field categories from database
     *
     * @return Collection
     */
    public function getAllConsumerConfigurableFieldCategories(): Collection
    {
        return $this->consumerConfigurableFieldCategoryRepository->getAll();
    }
}
