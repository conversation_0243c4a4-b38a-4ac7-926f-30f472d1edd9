<?php

namespace App\Services\Odin;


use App\Enums\Odin\ConsumerFieldType;
use App\Models\Odin\ConsumerCommonField;
use App\Models\Odin\ConsumerFieldModuleVisibility;
use App\Models\Odin\IndustryConsumerField;
use App\Models\Odin\ServiceConsumerField;
use App\Repositories\Odin\ConsumerCommonFieldRepository;
use App\Repositories\Odin\ConsumerFieldModuleVisibilityRepository;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\Enums\ConsumerFieldSource;

class ConsumerFieldModuleVisibilityAPIService
{
    public function __construct(
        protected ConsumerFieldModuleVisibilityRepository $consumerFieldModuleVisibilityRepository,
        protected ConsumerCommonFieldRepository $consumerCommonFieldRepository,
    )
    {

    }

    private function getComposedKey(
        string $category,
        int|string $categoryId,
        int $consumerFieldId,
        string $consumerFieldType,
        string $moduleType,
        string $featureType
    ): string
    {
        return "$category" .
            "$categoryId" .
            "$consumerFieldId" .
            "$consumerFieldType" .
            "$moduleType" .
            "$featureType";
    }

    public function getAll(ConsumerFieldType $category, int $categoryId): array
    {
        $commonConsumerFields = ConsumerCommonField::query()->get();

        $fieldsVisibility = ConsumerFieldModuleVisibility::query()
            ->where(ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_CATEGORY, $category->value)
            ->where(ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_CATEGORY_ID, $categoryId)
            ->get();

        $reference = $fieldsVisibility->reduce(function ($prev, $current) {
            $composedKey = $this->getComposedKey(
                $current->{ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_CATEGORY}->value,
                $current->{ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_CATEGORY_ID},
                $current->{ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_ID},
                $current->{ConsumerFieldModuleVisibility::FIELD_CONSUMER_FIELD_TYPE},
                $current->{ConsumerFieldModuleVisibility::FIELD_MODULE_TYPE}->value,
                $current->{ConsumerFieldModuleVisibility::FIELD_FEATURE_TYPE},
            );

            return array_merge($prev, [$composedKey => $current->{ConsumerFieldModuleVisibility::FIELD_IS_VISIBLE}]);
        }, []);

        $configurableFields = [];

        if ($category === ConsumerFieldType::INDUSTRY) {
            $configurableFields = IndustryConsumerField::query()
                ->where(IndustryConsumerField::FIELD_INDUSTRY_ID, $categoryId)
                ->get();
        } else if ($category === ConsumerFieldType::SERVICE) {
            $configurableFields = ServiceConsumerField::query()
                ->where(ServiceConsumerField::FIELD_INDUSTRY_SERVICE_ID, $categoryId)
                ->get();
        }

        $modules = \App\Enums\Odin\SystemModule::cases();

        $consumerFields = [
            ConsumerFieldSource::COMMON->value          => $commonConsumerFields,
            ConsumerFieldSource::CONFIGURABLE->value    => $configurableFields
        ];

        foreach ($modules as $module) {
            $features = $module->getFeatures();

            foreach ($features as $feature) {
                foreach ($consumerFields as $fieldCategory => $fields)  {
                    foreach ($fields as $field) {
                        $data = [
                            "consumer_field_category" => $category->value,
                            "consumer_field_category_id" => $categoryId,
                            "consumer_field_id" => $field->id,
                            "consumer_field_type" => $fieldCategory,
                            "module_type" => $module->value,
                            "feature_type" => $feature->value,
                        ];

                        $key = $this->getComposedKey(
                            $data["consumer_field_category"],
                            $data["consumer_field_category_id"],
                            $data["consumer_field_id"],
                            $data["consumer_field_type"],
                            $data["module_type"],
                            $data["feature_type"],
                        );

                        $result[] = [
                            ...$data,
                            "field" => $field,
                            "is_visible" => isset($reference[$key]) && !!$reference[$key]
                        ];
                    }
                }
            }
        }

        return $result;
    }

    public function saveMany(string $category, string $categoryId, array $fields): bool
    {
        $commonFields = collect($fields)
            ->filter(fn($f) => $f['consumer_field_type'] === ConsumerFieldSource::COMMON->value)
            ->pluck('field');

        $this->consumerCommonFieldRepository->updateMany($commonFields->toArray());

        return $this->consumerFieldModuleVisibilityRepository->saveMany($category, $categoryId, $fields);
    }
}
