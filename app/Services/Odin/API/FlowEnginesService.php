<?php

namespace App\Services\Odin\API;

use App\Enums\Company\CompanyAdminStatus;
use App\Enums\Company\CompanySystemStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\Odin\IndustryServiceSlug;
use App\Models\Legacy\CompanyRanking;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\Location;
use App\Models\Legacy\NonPurchasingCompanyLocation;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyService;
use App\Models\Odin\IndustryService;
use App\Models\TopCompanyByCounty;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\IndustryRepository;
use App\Services\DatabaseHelperService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use function intval;

class FlowEnginesService
{
    public function __construct(
        protected LocationRepository $locationRepository,
        protected IndustryRepository $industryRepository
    )
    {
    }

    protected array $selectColumns = [
        Company::TABLE .'.'. Company::FIELD_NAME,
        Company::TABLE .'.'. Company::FIELD_REFERENCE,
        Company::TABLE .'.'. Company::FIELD_ID,
        CompanyRanking::TABLE .'.'. CompanyRanking::REVIEW_COUNT,
        CompanyRanking::TABLE .'.'. CompanyRanking::BAYESIAN_ALL_TIME,
    ];

    /**
     * @param string $zipCode
     * @param int $serviceId
     * @return Collection
     */
    private function getCompanyIdsRecentlyPurchasedInZipCode(string $zipCode, int $serviceId): Collection
    {
        $county = $this->locationRepository->getCountyFromZipcode($zipCode);

        return TopCompanyByCounty::query()
            ->select([
                TopCompanyByCounty::TABLE .'.'. TopCompanyByCounty::FIELD_COMPANY_NAME . ' as name',
                TopCompanyByCounty::TABLE .'.'. TopCompanyByCounty::FIELD_COMPANY_REFERENCE . ' as reference',
                TopCompanyByCounty::TABLE .'.'. TopCompanyByCounty::FIELD_COMPANY_ID . ' as id',
                TopCompanyByCounty::TABLE .'.'. TopCompanyByCounty::FIELD_COMPANY_REVIEW_COUNT . ' as review_count',
                TopCompanyByCounty::TABLE .'.'. TopCompanyByCounty::FIELD_COMPANY_BAYESIAN_ALL_TIME . ' as bayesian_all_time',
            ])
            ->where(TopCompanyByCounty::FIELD_COUNTY_LOCATION_ID, $county->id)
            ->where(TopCompanyByCounty::FIELD_INDUSTRY_SERVICE_ID, $serviceId)
            ->get();
    }

    /**
     * @param string $zipCode
     * @param int $numberOfCompanies
     * @return array|\Illuminate\Database\Eloquent\Collection|Collection
     */
    public function getInstallersByZipCode(string $zipCode, int $numberOfCompanies = 4): \Illuminate\Database\Eloquent\Collection|array|Collection
    {
        $serviceId = IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, IndustryServiceSlug::SOLAR_INSTALLATION)
            ->first()
            ->{IndustryService::FIELD_ID};

        $companies = $this->getCompanyIdsRecentlyPurchasedInZipCode($zipCode, $serviceId);

        $remainingCompanyCount = $companies->count() < $numberOfCompanies ? $numberOfCompanies : 2; // We want to pull in 2 high ranking companies at a minimum

        $companies = $this->getTopInstallersByZipCode($zipCode, $serviceId, $companies, $remainingCompanyCount)->take($numberOfCompanies);

        return $companies->map(function ($company) {
            $rating = $company->bayesian_all_time;

            return [
                'reference'         => $company->reference,
                'review_count'      => ($company->review_count + 1) * 3,
                'name'              => $company->name,
                'reference '        => $company->reference,
                'bayesian_all_time' => $this->getBoostedRating($rating)
            ];
        });
    }

    /**
     * @param float|null $rating
     * @return float
     */
    private function getBoostedRating(?float $rating): float
    {
        $rating = max($rating, 1);

        while($rating < 3.5) {
            $rating = $rating + ((5 - $rating) / 2);
        }

        return $rating;
    }

    /**
     * Fetch top 4 installers from a zip code
     *
     * @param string $zipCode
     * @param int $serviceId
     * @param Collection $companies
     * @param int $numberOfCompanies
     * @return Collection
     */
    public function getTopInstallersByZipCode(string $zipCode, int $serviceId, Collection $companies, int $numberOfCompanies = 4): Collection
    {
        $excludedCompanyIds = $companies->pluck(Company::FIELD_ID)->toArray();

        $city = Location::query()
            ->where(Location::ZIP_CODE, $zipCode)
            ->first()
            ?->{Location::CITY_KEY};

        $results = collect();

        if($city) {
            $results = DB::table(Company::TABLE)
                ->select($this->selectColumns)
                ->distinct(Company::TABLE .'.'. Company::FIELD_ID)
                ->join(CompanyService::TABLE, CompanyService::TABLE .'.'. CompanyService::FIELD_COMPANY_ID, Company::TABLE .'.'. Company::FIELD_ID)
                ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, CompanyService::TABLE .'.'. CompanyService::FIELD_INDUSTRY_SERVICE_ID)
                ->join(DatabaseHelperService::readOnlyDatabase() .'.'. EloquentCompany::TABLE, EloquentCompany::TABLE .'.'. EloquentCompany::ID, Company::TABLE .'.'. Company::FIELD_LEGACY_ID)
                ->join(DatabaseHelperService::readOnlyDatabase() .'.'. CompanyRanking::TABLE, CompanyRanking::TABLE .'.'. CompanyRanking::COMPANY_ID, Company::TABLE .'.'. Company::FIELD_LEGACY_ID)
                ->join(DatabaseHelperService::readOnlyDatabase() .'.'. NonPurchasingCompanyLocation::TABLE, NonPurchasingCompanyLocation::TABLE .'.'. NonPurchasingCompanyLocation::FIELD_COMPANY_ID, Company::TABLE .'.'. Company::FIELD_LEGACY_ID)
                ->join(DatabaseHelperService::readOnlyDatabase() .'.'. Location::TABLE, Location::TABLE .'.'. Location::ID, NonPurchasingCompanyLocation::TABLE .'.'. NonPurchasingCompanyLocation::FIELD_LOCATION_ID)
                ->where(IndustryService::TABLE .'.'. IndustryService::FIELD_ID, $serviceId)
                ->where(Location::TABLE .'.'. Location::TYPE, Location::TYPE_CITY)
                ->where(Location::TABLE .'.'. Location::CITY_KEY, $city)
                ->whereIntegerNotInRaw(Company::TABLE .'.'. Company::FIELD_ID, $excludedCompanyIds)
                ->whereNotIn(Company::TABLE.'.'.Company::FIELD_ADMIN_STATUS, [
                    CompanyAdminStatus::ADMIN_LOCKED,
                    CompanyAdminStatus::ARCHIVED,
                    CompanyAdminStatus::COLLECTIONS,
                ])
                ->whereNotIn(Company::TABLE.'.'.Company::FIELD_SYSTEM_STATUS, [
                    CompanySystemStatus::SUSPENDED_PAYMENT
                ])
                ->orderBy(CompanyRanking::TABLE .'.'. CompanyRanking::BAYESIAN_ALL_TIME, 'DESC')
                ->limit($numberOfCompanies)
                ->get();
        }

        foreach ($results as $company) {
            if ($company)
                $companies->push($company);
        }

        if ($companies->count() < $numberOfCompanies) {
            $this->appendDefaultCompanies($companies, $numberOfCompanies);
        }

        return $companies;
    }

    /**
     * Append default companies if requested number of results is not returned
     * @param Collection $results
     * @param int $numberOfCompanies
     * @return void
     */
    private function appendDefaultCompanies(Collection &$results, int $numberOfCompanies): void
    {
        $defaultCompanyIds = config('services.flow_client.default_safe_call_companies') ?? '';
        $idArray = array_filter(preg_split("/\s*,\s*/", $defaultCompanyIds),
            fn(string $id) => !!intval($id));

        if (!count($idArray)) return;

        $fallbackCompanies = Company::query()
            ->join(DatabaseHelperService::readOnlyDatabase() .'.'. EloquentCompany::TABLE, EloquentCompany::TABLE .'.'. EloquentCompany::ID, Company::TABLE .'.'. Company::FIELD_LEGACY_ID)
            ->join(DatabaseHelperService::readOnlyDatabase() .'.'. CompanyRanking::TABLE, CompanyRanking::TABLE .'.'. CompanyRanking::COMPANY_ID, Company::TABLE .'.'. Company::FIELD_LEGACY_ID)
            ->select($this->selectColumns)
            ->findMany($idArray);

        foreach ($fallbackCompanies as $company) {
            if ($results->count() >= $numberOfCompanies) return;
            if ($company)
                $results->push($company);
        }
    }
}
