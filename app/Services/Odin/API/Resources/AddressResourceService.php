<?php

namespace App\Services\Odin\API\Resources;

use App\Enums\Odin\API\FieldType;
use App\Models\Odin\Address;
use App\Repositories\Odin\AddressRepository;
use App\Services\Odin\API\APIFieldModel;
use Illuminate\Support\Collection;

class AddressResourceService extends BaseResourceService
{
    /**
     * @param AddressRepository $repository
     */
    public function __construct(protected AddressRepository $repository) {}

    /**
     * @inheritDoc
     */
    public function create(Collection $data): bool
    {
        return $this->createAddressModel($data->filter(fn(APIFieldModel $datum) => $datum->type === FieldType::MODEL));
    }

    /**
     * @param Collection $data
     * @return bool
     */
    protected function createAddressModel(Collection $data): bool
    {
        $attributes = $this->transformFieldModelsToAttributes($data);

        /** @var Address|null $addressAlreadyAdded */
        $addressAlreadyAdded = $this->repository->findByLegacyId($attributes[Address::FIELD_LEGACY_ID]);
        if($addressAlreadyAdded) {
            return $this->updateAddressRecord($addressAlreadyAdded, $attributes);
        }

        return !!$this->repository->createAddressFromAttributes($attributes);
    }

    /**
     * @inheritDoc
     */
    public function update(mixed $primaryKey, Collection $data): bool
    {
        return $this->updateAddressRecord(
            $this->repository->findByIdOrFail($primaryKey),
            $this->filterAndTransformFieldModels($data, FieldType::MODEL)
        );
    }

    /**
     * @inheritDoc
     */
    public function delete(mixed $primaryKey): bool
    {
        $address  = $this->repository->findByIdOrFail($primaryKey);
        return $address->delete();
    }

    /**
     * @param Address $address
     * @param array $attributes
     * @return bool
     */
    protected function updateAddressRecord(Address $address, array $attributes): bool
    {
        return $this->repository->updateAddress($address, $attributes);
    }
}
