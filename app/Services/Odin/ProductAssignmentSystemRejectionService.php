<?php

namespace App\Services\Odin;

use App\Enums\RejectionReasons;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Services\CompanyUserService;
use Illuminate\Contracts\Container\BindingResolutionException;

class ProductAssignmentSystemRejectionService
{
    public function __construct(
        protected CompanyUserService $companyUserService,
        protected ProductAssignmentService $productAssignmentService
    )
    {

    }

    /**
     * @param Company $company
     * @param ProductAssignment $productAssignment
     * @param RejectionReasons $rejectionReason
     * @param string $rejectionReasonNote
     * @param bool $shouldAffectRejectionPercentage
     * @return void
     * @throws BindingResolutionException
     */
    public function rejectProductAssignment(
        Company $company,
        ProductAssignment $productAssignment,
        RejectionReasons $rejectionReason,
        string $rejectionReasonNote,
        bool $shouldAffectRejectionPercentage = true
    ): void
    {
        $companyUser = $this->companyUserService->findOrCreateSystemCompanyUser($company->id);

        if (!$shouldAffectRejectionPercentage) {
            $productAssignment->update([
                ProductAssignment::FIELD_AFFECT_REJECTION_PERCENTAGE => 0
            ]);
        }

        $this->productAssignmentService->rejectProductAssignment(
            productAssignment: $productAssignment,
            companyUserId    : $companyUser->id,
            rejectReason     : $rejectionReason->value . "|" . $rejectionReasonNote
        );
    }
}
