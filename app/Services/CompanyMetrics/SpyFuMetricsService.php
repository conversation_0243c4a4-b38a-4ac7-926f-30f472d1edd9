<?php

namespace App\Services\CompanyMetrics;

use App\Contracts\Services\CompanyMetricsServiceContract;
use App\DTO\PPCResponse;
use App\DTO\SpyFuPPCPayloadResponse;
use App\Enums\CompanyMetrics\CompanyMetricSources;
use App\Models\Odin\Company;
use Exception;
use DateTime;
use Illuminate\Support\Facades\Http;

class SpyFuMetricsService implements CompanyMetricsServiceContract
{
    private readonly string $apiKey;
    private readonly string $apiId;
    private readonly string $requestPath;
    const string REQUEST_PARAMETERS_COUNTRY_CODE            = "countryCode";
    const string REQUEST_PARAMETERS_DOMAIN                  = "domain";
    const string REQUEST_RESPONSE_AVERAGE_ORGANIC_RANK      = 'averageOrganicRank';
    const string REQUEST_RESPONSE_MONTHLY_PAID_CLICKS       = 'monthlyPaidClicks';
    const string REQUEST_RESPONSE_AVERAGE_AD_RANK           = 'averageAdRank';
    const string REQUEST_RESPONSE_TOTAL_ORGANIC_RESULTS     = 'totalOrganicResults';
    const string REQUEST_RESPONSE_MONTHLY_ORGANIC_VALUE     = 'monthlyOrganicValue';
    const string REQUEST_RESPONSE_TOTAL_ADS_PURCHASED       = 'totalAdsPurchased';
    const string REQUEST_RESPONSE_MONTHLY_ORGANIC_CLICKS    = 'monthlyOrganicClicks';
    const string REQUEST_RESPONSE_STRENGTH                  = 'strength';
    const string REQUEST_RESPONSE_TOTAL_INVERSE_RANK        = 'totalInverseRank';
    const string REQUEST_RESPONSE_RESULTS                   = 'results';
    const string REQUEST_RESPONSE_SEARCH_MONTH              = 'searchMonth';
    const string REQUEST_RESPONSE_SEARCH_YEAR               = 'searchYear';
    const string REQUEST_RESPONSE_MONTHLY_BUDGET            = 'monthlyBudget';
    const string REQUEST_RESPONSE_DOMAIN                    = 'domain';

    public function __construct() {
        $this->apiKey       = config('services.company_metrics.spy_fu.api_key')? : "";
        $this->apiId        = config('services.company_metrics.spy_fu.api_id')? : "";
        $this->requestPath  = config('services.company_metrics.spy_fu.base_url') . '/apis/domain_stats_api/v2/getLatestDomainStats';
    }

    /**
     * @inheritDoc
     */
    public function getServiceType(): CompanyMetricSources
    {
        return CompanyMetricSources::SPY_FU;
    }

    /**
     * @inheritDoc
     *
     * Returns a json response from the Similar Web API
     */
    public function getCompanyMetrics(Company $company, DateTime $startDate, DateTime $endDate): ?PPCResponse
    {
        $requestHeaders = $this->buildRequestHeaders();

        $requestParameters = $this->buildRequestParameters($company);

        try {
            $res = Http::withToken(base64_encode($this->apiId .':'. $this->apiKey), 'Basic')->get($this->requestPath, $requestParameters);
            if($res->ok()) {
                $contents = json_decode($res->body());

                if($contents->resultCount < 1){
                    throw new Exception("Result count 0 received from SpyFu API. Error Response Data: " . json_encode($contents));
                }
            }
            else {
                throw new Exception("Request failed, Status: " . $res->status() . " Error Response Data: " . json_encode($res->body()));
            }
        }
        catch(Exception $e) {
            logger()->error("Failed to retrieve SimilarWeb Company Metrics for Company: " . $company[Company::FIELD_NAME]);
            logger()->error("Exception: " . $e->getMessage());
            return null;
        }

        $response = json_decode($res->body(), true);

        $spyFuPPCResponsePayload = new SpyFuPPCPayloadResponse(
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_AVERAGE_ORGANIC_RANK],
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_MONTHLY_PAID_CLICKS],
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_AVERAGE_AD_RANK],
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_TOTAL_ORGANIC_RESULTS],
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_MONTHLY_ORGANIC_VALUE],
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_TOTAL_ADS_PURCHASED],
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_MONTHLY_ORGANIC_CLICKS],
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_STRENGTH],
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_TOTAL_INVERSE_RANK],
        );

        return new PPCResponse(
            month:          $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_SEARCH_MONTH],
            year:           $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_SEARCH_YEAR],
            monthlySpend:   $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_MONTHLY_BUDGET],
            targetDomain:   $response[self::REQUEST_RESPONSE_DOMAIN],
            request:        $this->requestPath . '?' . http_build_query($requestParameters) . '&' . http_build_query(['headers' => $requestHeaders]),
            payload:        $spyFuPPCResponsePayload
        );
    }

    /**
     * @return string[]
     */
    public function buildRequestHeaders(): array
    {
        return [
            'Authorization' => 'Basic '. base64_encode($this->apiId .':'. $this->apiKey)
        ];
    }

    /**
     * Builds array of parameters used in the API request
     * @param Company $company
     * @return array
     */
    private function buildRequestParameters(Company $company): array
    {
        return [
            self::REQUEST_PARAMETERS_COUNTRY_CODE   => "US",
            self::REQUEST_PARAMETERS_DOMAIN         => $company->{Company::FIELD_WEBSITE},
        ];
    }
}
