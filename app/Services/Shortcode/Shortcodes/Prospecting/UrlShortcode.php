<?php

namespace App\Services\Shortcode\Shortcodes\Prospecting;

use App\Models\Prospects\NewBuyerProspect;
use App\Services\Prospects\ProspectEmailService;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;

class UrlShortcode implements GenericShortcodeContract
{

    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return 'Url';
    }

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return 'url';
    }

    /**
     * @param NewBuyerProspect $input
     *
     * @return string
     */
    public function getValue(mixed $input): string
    {
        /** @var ProspectEmailService $service */
        $service = app(ProspectEmailService::class);
        [$siteName, $url, $calculatorUrl, $industry] = $service->getWebsiteDetails($input);

        return $url;
    }
}
