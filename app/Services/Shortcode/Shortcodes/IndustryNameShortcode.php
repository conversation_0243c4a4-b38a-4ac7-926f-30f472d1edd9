<?php

namespace App\Services\Shortcode\Shortcodes;

use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use InvalidArgumentException;

class IndustryNameShortcode implements GenericShortcodeContract
{
    public function getLabel(): string
    {
        return "Industry Name";
    }

    public function getKey(): string
    {
        return "industry_name";
    }

    /**
     * @param Industry|IndustryService|ConsumerProduct $input
     * @return string
     */
    public function getValue(mixed $input): string
    {
        $class = $input::class;

        return match ($class) {
            Industry::class        => $input->name,
            IndustryService::class => $input->industry->name,
            ConsumerProduct::class => $input->industryService->industry->name,
            default => throw new InvalidArgumentException("Shortcode getValue expected Industry, IndustryService or ConsumerProduct Modal, received: $class")
        };
    }
}