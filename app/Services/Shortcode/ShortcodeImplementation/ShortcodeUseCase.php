<?php

namespace App\Services\Shortcode\ShortcodeImplementation;

use App\Services\Shortcode\ShortcodeSets\ShortcodeSet;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

abstract class ShortcodeUseCase
{

    public function __construct()
    {
    }

    static function mock()
    {

    }

    /**
     * @return mixed
     */
    abstract public static function getShortcodeSets(): mixed;

    /**
     * TODO - Find a way to avoid repeating $outerKey.$key every time when retrieving all shortcodes with their custom prefix.
     * @param array $usedShortcodes
     * @return Collection
     */
    public function compile(array $usedShortcodes = []): Collection
    {
        if (empty($usedShortcodes)) {
            $usedShortcodes = collect($this::getShortcodesList())->pluck('value')->toArray();
        }

        $dataMapped = $this->getMappedData();

        try {
            $compiledShortcodes = $this::getShortcodeSets()->filter(function (ShortcodeSet $bundle, string $outerKey) use ($usedShortcodes, $dataMapped) {
                $keys = $bundle->getShortcodeKeys()->map(fn(string $key) => "$outerKey.$key")->toArray();

                return !empty(array_intersect($keys, $usedShortcodes));
            })->mapWithKeys(fn(ShortcodeSet $item, $outerKey) => collect($item->compile($dataMapped->get($outerKey)))
                ->mapWithKeys(fn($value, $key) => ["$outerKey.$key" => $value])->toArray());
            }
        catch (\Exception $e) {
            dd($e);
        }


        return $compiledShortcodes->filter(function ($value, $key) use ($usedShortcodes) {
            return in_array($key, $usedShortcodes);
        });
    }

    /**
     * @return Collection
     */
    public function getMappedData(): Collection
    {
        return collect();
    }

    /**
     * @return array
     */
    public static function getShortcodesList(): array
    {
        return static::getShortcodeSets()
            ->mapWithKeys(fn(ShortcodeSet $shortcodeSet, string $key) => $shortcodeSet->getShortcodeOptions()
                ->mapWithKeys(function (array $scOption) use ($key) {
                    $isCustomKey = !is_numeric($key);

                    $fullShortcodeKey = $isCustomKey
                        ? "{$key}.{$scOption['key']}"
                        : $scOption['key'];

                    $label = $isCustomKey ? Str::headline($key) . ' ' . $scOption['label'] : $scOption['label'];

                    return [
                        $fullShortcodeKey => [
                            'label' => $label,
                            'value' => $fullShortcodeKey
                        ]
                    ];
                }))->values()->toArray();
    }
}
