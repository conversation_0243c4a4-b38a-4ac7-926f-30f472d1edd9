<?php

namespace App\Services\Shortcode;

use App\Models\EmailTemplate;
use Illuminate\Support\Str;

class EmailTemplateShortcodeReplacerService
{
    public function __construct(
        protected ShortcodeReplacerService $replacerService
    )
    {
    }

    /**
     * @param EmailTemplate $template
     * @return array
     */
    public function getUsedShortcodes(EmailTemplate $template): array
    {
        $subjectShortcodes = $this->replacerService->getAllShortcodes($template->subject);
        $contentShortcodes = $this->replacerService->getAllShortcodes(Str::markdown($template->content));
        $headerShortcodes = $this->replacerService->getAllShortcodes($template->background?->header ?? '');
        $footerShortcodes = $this->replacerService->getAllShortcodes($template->background?->footer ?? '');

        return collect($subjectShortcodes)
            ->merge([
                ...$contentShortcodes,
                ...$headerShortcodes,
                ...$footerShortcodes
            ])->unique()->values()->toArray();
    }

}
