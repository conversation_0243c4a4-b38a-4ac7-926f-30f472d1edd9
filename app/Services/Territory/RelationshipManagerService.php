<?php

namespace App\Services\Territory;

use App\Enums\ActivityLog\ActivityLogDescription;
use App\Enums\ActivityLog\ActivityLogName;
use App\Models\Odin\Company;
use App\Models\Territory\RelationshipManager;
use App\Repositories\ActivityLog\ActivityLogRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Territory\RelationshipManagerRepository;
use Illuminate\Database\Eloquent\Builder;

class RelationshipManagerService
{

    public function __construct(
        protected RelationshipManagerRepository $relationshipManagerRepository,
        protected CompanyRepository             $companyRepository,
        protected ActivityLogRepository         $activityLogRepository,
    )
    {
    }

    /**
     * @param string|null $userName
     * @param string|null $companyName
     * @return Builder
     */
    public function listRelationshipManagers(
        ?string $userName = null,
        ?string $companyName = null
    ): Builder
    {
        return $this->relationshipManagerRepository->listRelationshipManagers(
            userName   : $userName,
            companyName: $companyName,
        );
    }

    /**
     * @param int $managerId
     * @return RelationshipManager|null
     */
    public function getRelationshipManager(
        int $managerId,
    ): ?RelationshipManager
    {
        return $this->relationshipManagerRepository->getRelationshipManager(
            id: $managerId,
        );
    }

    /**
     * @param int $userId
     * @return RelationshipManager
     */
    public function createRelationshipManager(int $userId): RelationshipManager
    {
        return $this->relationshipManagerRepository->createRelationshipManager(
            userId: $userId
        );
    }

    /**
     * @param RelationshipManager $relationshipManager
     * @return bool|null
     */
    public function deleteRelationshipManager(RelationshipManager $relationshipManager): ?bool
    {
        $companies = $relationshipManager->companies;

        foreach ($companies as $company) {
            $this->updateCompanyRelationshipManager(
                updateReason: ActivityLogDescription::USER_UPDATED,
                company     : $company,
            );
        }

        return $this->relationshipManagerRepository->deleteRelationshipManager($relationshipManager);
    }

    /**
     * @param int $companyId
     * @return RelationshipManager|null
     */
    public function getCompanyRelationshipManager(
        int $companyId,
    ): ?RelationshipManager
    {
        $company = $this->companyRepository->findOrFail($companyId);
        if ($company->{Company::FIELD_RELATIONSHIP_MANAGER_ID}) {
            return $this->relationshipManagerRepository->getRelationshipManager(
                id: $company->{Company::FIELD_RELATIONSHIP_MANAGER_ID},
            );
        }
        return null;
    }

    /**
     * @param ActivityLogDescription $updateReason
     * @param Company $company
     * @param int|null $relationshipManagerId
     * @return void
     */
    public function updateCompanyRelationshipManager(
        ActivityLogDescription $updateReason,
        Company                $company,
        ?int                   $relationshipManagerId = null,
    ): void
    {

        if ($company->relationship_manager_id === $relationshipManagerId) {
            return;
        }

        $this->activityLogRepository->createActivityLog(
            logName    : ActivityLogName::COMPANY_RELATIONSHIP_MANAGER_CHANGE->value,
            description: $updateReason->value,
            subjectType: $company::class,
            subjectId  : $company->id,
            properties : [
                'from_id' => $company->relationship_manager_id,
                'to_id'   => $relationshipManagerId,
            ],
        );

        $this->relationshipManagerRepository->assignRelationshipManagerToCompany(
            company              : $company,
            relationshipManagerId: $relationshipManagerId,
        );
    }
}
