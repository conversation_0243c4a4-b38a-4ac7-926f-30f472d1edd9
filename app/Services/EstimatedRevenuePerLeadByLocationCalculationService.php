<?php

namespace App\Services;

use App\Builders\Odin\CompanyCampaignBuilder;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\Odin\Industry;
use App\Enums\Odin\Product;
use App\Enums\Odin\PropertyType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CustomCampaignCountyFloorPrice;
use App\Models\Campaigns\CustomCampaignStateFloorPrice;
use App\Models\EstimatedRevenuePerLeadByLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Industry as IndustryModel;
use App\Models\Odin\Product as ProductModel;
use App\Models\Odin\ProductCountyBidPrice;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateBidPrice;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\Odin\ServiceProduct;
use App\Models\USZipCode;
use App\Strategies\CampaignBudget\FutureCampaignCalculateVerifiedBudgetStrategy;
use Exception;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class EstimatedRevenuePerLeadByLocationCalculationService
{
    private Collection $industryIdFilter;
    private Collection $serviceProductIdFilter;
    private array $industryToServiceProduct;
    private Collection $industryIdToSlug;

    private Collection $campaignToServiceId;

    // Use trio for max sale type id, won't use quads
    const int MAX_SALE_TYPE_ID = 3;

    // Only consider exclusive, duo, and trios
    const array SALE_TYPE_ID_LIMIT = [1,2,3];

    // Use regular quality tier pricing
    const int QUALITY_TIER_LIMIT = 1;

    // Residential pricing
    const int PROPERTY_TYPE_LIMIT = 1;

    const string CAMPAIGN_COMPANY_IDS = 'campaignCompanyIds';

    public function __construct(protected FutureCampaignCalculateVerifiedBudgetStrategy $budgetStrategy)
    {
        $this->industryIdFilter = collect();
        $this->serviceProductIdFilter = collect();
        $this->industryToServiceProduct = [];
        $this->industryIdToSlug = collect();
        $this->campaignToServiceId = collect();
    }

    /**
     * @return void
     * @throws Exception
     */
    public function calculateAndStoreEstimatedRevenuePerLeadByLocation(): void
    {
        DB::disableQueryLog();

        // Build industry filters and map campaign ids to service product ids
        $this->buildIndustryFilters();
        $this->getCampaignToServiceIdMap();
        $this->getTopIndustryServiceByIndustry();

        // Get available campaigns by zip code
        $zipCodeBudgets = $this->getZipCodeBudgets();

        // Get Campaign Bid Prices
        $countyCampaignBidPrices = $this->getCountyCampaignBidPrices();
        $stateCampaignBidPrices = $this->getStateCampaignBidPrices();

        // Get Campaign Custom Floor Prices
        $customCountyCampaignFloorPrices = $this->getCustomCountyFloorPrices();
        $customStateCampaignFloorPrices = $this->getCustomStateFloorPrices();

        // Getting floor prices by zip code for county and state
        $countyFloorPrices = $this->getCountyFloorPrices();
        $stateFloorPrices = $this->getStateFloorPrices();

        // Build the locations data - this is what we loop through to check each zip -> county -> state
        $counties = $this->getLocationData();

        // This stores the data that will be added to the estimated_revenue_per_lead_by_locations table
        $estimatedRevenuePerLeadData = [];

        // Use an id counter for model ids, instead of using truncate to reset we use delete inside transaction
        $idCounter = 1;

        // Loop through each county, then through each zip code within that county, calculate ERpL for each zip and weighted average them on population for county ERpL
        foreach ($counties as $countyLocationId => $zipCodes) {
            $county = $zipCodes->first();

            // ERPL for county is a weighted average of ERPL for each zip code (zip population is weight, done for each industry)
            $countyErplByIndustry = [];
            $countyNegativeZipsByIndustry = [];
            $countyCompaniesByIndustry = [];

            // Initialize to 0 for ERpL and no negative zips
            foreach ($this->industryIdFilter as $industryId) {
                $countyErplByIndustry[$industryId] = 0;
                $countyNegativeZipsByIndustry[$industryId] = [];
                $countyCompaniesByIndustry[$industryId] = [];
            }

            // Population data at the zip code level
            $countyPopulation = $zipCodes->sum(USZipCode::FIELD_POPULATION);

            // Loop through all zip codes in county
            foreach ($zipCodes as $zip) {
                // Get zip code and population
                $zipCode = $zip->{Location::ZIP_CODE};
                $zipCodePopulation = $zip->{USZipCode::FIELD_POPULATION};
                $zipCodeLocationId = $zip->{'zip_location_id'};
                $countyLocationId = $zip->{'county_location_id'};
                $stateLocationId = $zip->{'state_location_id'};

                // County and state floor prices grouped by location id -> service product id -> sale type for the current zip code
                $countyPrices = $countyFloorPrices[$countyLocationId] ?? [];
                $statePrices = $stateFloorPrices[$stateLocationId] ?? [];

                // Loop through each industry
                //  - If no budget, add zip to negative locations for that industry
                //  - If budget, calculate weighted ERPL (population weight) and add to county total for that industry
                foreach ($this->industryIdFilter as $industryId) {
                    $budgets = $zipCodeBudgets[$industryId][$zipCodeLocationId] ?? null;

                    if (!$budgets) {
                        // Add Zip Code to negative locations list for this industry
                        $countyNegativeZipsByIndustry[$industryId][] = $zipCode;
                        continue;
                    }

                    // Get Prices for each campaign
                    $campaigns = $budgets->pluck(self::CAMPAIGN_COMPANY_IDS)->first();

                    // Key value array with campaign id => company id
                    $campaignCompany = [];
                    foreach ($campaigns as $campaign) {
                        list($key, $value) = explode(':', $campaign);
                        $campaignCompany[$key] = $value;
                    }

                    // Build list of service product id => ['company_id' => company id, 'campaign_id' => campaign id]
                    $serviceProductsToCampaigns = [];
                    foreach ($campaignCompany as $campaignId => $companyId) {
                        $serviceProductId = $this->campaignToServiceId[$campaignId];
                        if (array_key_exists($serviceProductId, $serviceProductsToCampaigns)) {
                            $serviceProductsToCampaigns[$serviceProductId][$campaignId] = $companyId;
                        } else {
                            $serviceProductsToCampaigns[$serviceProductId] = [$campaignId => $companyId];
                        }
                    }

                    // Only use campaigns in the top service product
                    $campaignCompany = $serviceProductsToCampaigns[$this->industryToServiceProduct[$industryId]] ?? null;

                    // If the top service product is not available at this zip, use the service product with the most campaigns
                    if (!$campaignCompany) {
                        // Count campaigns for each service product
                        $countsByServiceProduct = array_map('count', $serviceProductsToCampaigns);
                        // Find the key with the maximum length
                        $maxServiceProduct = array_keys($serviceProductsToCampaigns, $serviceProductsToCampaigns[max(array_keys($countsByServiceProduct, max($countsByServiceProduct)))])[0];

                        // Use the service product with the most campaigns
                        $campaignCompany = $serviceProductsToCampaigns[$maxServiceProduct];
                    }


                    // Get array of unique company ids
                    $uniqueCompanies = array_keys(array_count_values(array_values($campaignCompany)));

                    // Count available companies at this zip code from unique company ids
                    $companyCount = count($uniqueCompanies);

                    // Add company ids to unique county company ids
                    $countyCompaniesByIndustry[$industryId] = array_unique(array_merge($countyCompaniesByIndustry[$industryId], $uniqueCompanies));

                    // Sale type ID will be equal to the available companies with a max of 3 for a trio
                    $saleTypeId = $companyCount;
                    if ($companyCount > self::MAX_SALE_TYPE_ID) {
                        $saleTypeId = self::MAX_SALE_TYPE_ID;
                    }

                    // Stores bid price for each campaign
                    $campaignPrices = [];

                    // Get campaign county bid prices
                    foreach (array_keys($campaignCompany) as $campaign) {
                        // 1: Maximum between county bid price and custom county floor price is the highest priority
                        $price = max($countyCampaignBidPrices[$countyLocationId][$campaign][$saleTypeId][0]->{ProductCountyBidPrice::FIELD_PRICE} ?? 0,
                            $customCountyCampaignFloorPrices[$countyLocationId][$campaign][$saleTypeId][0]->{CustomCampaignCountyFloorPrice::FIELD_PRICE} ?? 0);

                        // 2: Maximum between state bid price and custom state floor price is the next used if no county bids or county custom floor prices
                        if ($price === 0) {
                            $price = max($stateCampaignBidPrices[$stateLocationId][$campaign][$saleTypeId][0]->{ProductCountyBidPrice::FIELD_PRICE} ?? 0,
                                $customStateCampaignFloorPrices[$stateLocationId][$campaign][$saleTypeId][0]->{CustomCampaignStateFloorPrice::FIELD_PRICE} ?? 0);
                        }

                        // 3: Maximum between base county floor price and base state floor price is used if no bids or custom floor prices are found
                        if ($price === 0) {
                            $price = max($countyPrices[$this->campaignToServiceId[$campaign]][$saleTypeId][0]->{ProductCountyFloorPrice::FIELD_PRICE} ?? 0,
                                $statePrices[$this->campaignToServiceId[$campaign]][$saleTypeId][0]->{ProductCountyFloorPrice::FIELD_PRICE} ?? 0);
                        }

                        // If no price found anywhere: self-destruct
                        if ($price === 0) {
                            logger()->error("No price found for Campaign: $campaign, County: {$zip->{Location::COUNTY_KEY}}, Zip: $zipCode, Industry: $industryId, Sale Type: $saleTypeId");
                        }

                        // Store each campaign's price with the company id
                        $campaignPrices[$campaign] = [
                            'price'         => $price,
                            'company_id'    => $campaignCompany[$campaign],
                        ];
                    }

                    // Get the maximum price for each company in the zip code, this protects against multiple campaigns from the same company
                    $companyPrices = [];
                    foreach ($campaignPrices as $campaignPrice) {
                        if (array_key_exists($campaignPrice['company_id'], $companyPrices)) {
                            if ($campaignPrice['price'] > $companyPrices[$campaignPrice['company_id']]) {
                                $companyPrices[$campaignPrice['company_id']] = $campaignPrice['price'];
                            }
                        } else {
                            $companyPrices[$campaignPrice['company_id']] = $campaignPrice['price'];
                        }
                    }

                    // Sort prices in descending order
                    $prices = array_values($companyPrices);
                    sort($prices);
                    $prices = array_reverse($prices);

                    // Take sum of sale type id for highest bids
                    $price = array_sum(array_slice($prices, 0, $saleTypeId));

                    // Calculate ERPL for Zip Code by price * sale type (legs sold)
                    $estimatedRevenuePerLead = $price;

                    $estimatedRevenuePerLeadData[] = [
                        EstimatedRevenuePerLeadByLocation::FIELD_ID => $idCounter, // Can only do this because we are clearing the full table below
                        EstimatedRevenuePerLeadByLocation::FIELD_LOCATION_ID => $zipCodeLocationId,
                        EstimatedRevenuePerLeadByLocation::FIELD_ESTIMATED_REVENUE => $estimatedRevenuePerLead,
                        EstimatedRevenuePerLeadByLocation::FIELD_INDUSTRY_ID => $industryId,
                        EstimatedRevenuePerLeadByLocation::FIELD_COUNTY_FIPS => $county->{USZipCode::FIELD_COUNTY_FIPS},
                        EstimatedRevenuePerLeadByLocation::FIELD_NEGATIVE_ZIP_CODES => null,
                        EstimatedRevenuePerLeadByLocation::FIELD_AVAILABLE_COMPANIES => $companyCount,
                        EstimatedRevenuePerLeadByLocation::FIELD_INDUSTRY_TYPE => $this->industryIdToSlug[$industryId],
                        EstimatedRevenuePerLeadByLocation::CREATED_AT => now(),
                    ];
                    $idCounter++;

                    if ($countyPopulation !== 0) {
                        // Add zip code ERPL to county ERPL by weight of zip population / county population
                        $countyErplByIndustry[$industryId] += $estimatedRevenuePerLead * ($zipCodePopulation / $countyPopulation);
                    } else {
                        // If no population data for county, use the average of all zip codes associated
                        // This only applies to two small counties that don't hold the majority of any zip code (manassas-park-city, williamsburg-city)
                        $countyErplByIndustry[$industryId] += $estimatedRevenuePerLead * (1.0 / $zipCodes->count());
                    }
                }
            }

            // Round county ERPLs to two decimal places
            foreach ($this->industryIdFilter as $industryId) {
                $countyErplByIndustry[$industryId] = round($countyErplByIndustry[$industryId], 2);
            }

            foreach ($this->industryIdFilter as $industryId) {
                $estimatedRevenuePerLead = $countyErplByIndustry[$industryId];
                $negativeZipLocations = $countyNegativeZipsByIndustry[$industryId];
                $companyCount = count($countyCompaniesByIndustry[$industryId]);
                $estimatedRevenuePerLeadData[] = [
                    EstimatedRevenuePerLeadByLocation::FIELD_ID => $idCounter, // Can only do this because we are clearing the full table below
                    EstimatedRevenuePerLeadByLocation::FIELD_LOCATION_ID => $county->{'county_location_id'},
                    EstimatedRevenuePerLeadByLocation::FIELD_ESTIMATED_REVENUE => $estimatedRevenuePerLead,
                    EstimatedRevenuePerLeadByLocation::FIELD_INDUSTRY_ID => $industryId,
                    EstimatedRevenuePerLeadByLocation::FIELD_COUNTY_FIPS => $county->{USZipCode::FIELD_COUNTY_FIPS},
                    EstimatedRevenuePerLeadByLocation::FIELD_NEGATIVE_ZIP_CODES => json_encode($negativeZipLocations),
                    EstimatedRevenuePerLeadByLocation::FIELD_AVAILABLE_COMPANIES => $companyCount,
                    EstimatedRevenuePerLeadByLocation::FIELD_INDUSTRY_TYPE => $this->industryIdToSlug[$industryId],
                    EstimatedRevenuePerLeadByLocation::CREATED_AT => now(),
                ];
                $idCounter++;
            }
        }

        // Clear table and add new data
        DB::transaction(function() use ($estimatedRevenuePerLeadData) {
            // Clear table, using delete instead of truncate to maintain within transaction
            EstimatedRevenuePerLeadByLocation::query()->delete();

            foreach (array_chunk($estimatedRevenuePerLeadData, 500) as $chunk) {
                EstimatedRevenuePerLeadByLocation::insert($chunk);
            }
        });

        DB::enableQueryLog();
    }

    /**
     * Mapping active campaign IDs to service product IDs for county and state floor price retrieval
     * @return void
     */
    public function getCampaignToServiceIdMap(): void
    {
        $this->campaignToServiceId = CompanyCampaign::query()
            ->join(Company::TABLE, Company::TABLE.'.'.Company::FIELD_ID, '=', CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_COMPANY_ID)
            ->join(ServiceProduct::TABLE, fn (JoinClause $join) =>
            $join->on(ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_PRODUCT_ID, '=', CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_PRODUCT_ID)
                ->on(ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, '=', CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_SERVICE_ID)
            )
            ->where(Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS->value)
            ->where(CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_STATUS, CampaignStatus::ACTIVE->value)
            ->get([
                CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ID,
                ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_ID.' as service_product_id',
            ])->mapWithKeys(function($campaign) {
                return [$campaign[CompanyCampaign::FIELD_ID] => $campaign['service_product_id']];
            });
    }

    /**
     * Get top industry services for each industry in the past three months
     * @return void
     */
    public function getTopIndustryServiceByIndustry(): void
    {
        $industryServiceCounts = ConsumerProduct::query()
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_ID, '=', ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->join(IndustryService::TABLE, IndustryService::TABLE.'.'.IndustryService::FIELD_ID, '=', ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
            ->where(ConsumerProduct::TABLE.'.'.ConsumerProduct::CREATED_AT, '>', now()->subMonths(3)->format('Y-m-d'))
            ->groupBy(IndustryService::TABLE.'.'.IndustryService::FIELD_INDUSTRY_ID, ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_ID)
            ->orderBy('lead_count', 'desc')
            ->get([
                IndustryService::TABLE.'.'.IndustryService::FIELD_INDUSTRY_ID,
                ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_ID.' as service_product_id',
                DB::raw('COUNT(*) as lead_count'),
            ])->groupBy(IndustryService::FIELD_INDUSTRY_ID);

        foreach ($industryServiceCounts as $industryServiceCount) {
            $maxValue = $industryServiceCount->max('lead_count');
            $maxEntry = $industryServiceCount->firstWhere('lead_count', $maxValue);
            $this->industryToServiceProduct[$maxEntry->{IndustryService::FIELD_INDUSTRY_ID}] = $maxEntry->{'service_product_id'};
        }
    }

    /**
     * This function returns a list of the industry service ids with active future campaigns
     * @return Collection
     */
    public function getIndustryServiceLimit(): Collection
    {
        return CompanyCampaign::query()
            ->join(Company::TABLE, Company::TABLE.'.'.Company::FIELD_ID, '=', CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_COMPANY_ID)
            ->where(Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS->value)
            ->where(CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_STATUS, CampaignStatus::ACTIVE->value)
            ->get([DB::raw("DISTINCT ".CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_SERVICE_ID)])
            ->pluck(CompanyCampaign::FIELD_SERVICE_ID);
    }

    /**
     * We are filtering here to limit the amount of pricing entries we are looking at
     * @return void
     */
    public function buildIndustryFilters(): void
    {
        $serviceIdFilter = $this->getIndustryServiceLimit();

        // Get industry, service, and service product ids for limits.
        // Without these limits there are 2 million floor price entries for every possible combination the human brain could imagine
        $industryServices = IndustryService::query()
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, IndustryService::TABLE.'.'.IndustryService::FIELD_ID)
            ->join(IndustryModel::TABLE, IndustryModel::TABLE.'.'.IndustryModel::FIELD_ID, '=', IndustryService::TABLE.'.'.IndustryService::FIELD_INDUSTRY_ID)
            ->join(ProductModel::TABLE, ProductModel::TABLE.'.'.ProductModel::FIELD_ID, '=', ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_PRODUCT_ID)
            ->select(
                DB::raw(IndustryService::TABLE.'.'.IndustryService::FIELD_ID.' as industry_service_id'),
                DB::raw(IndustryService::TABLE.'.'.IndustryService::FIELD_INDUSTRY_ID.' as industry_id'),
                DB::raw(IndustryModel::TABLE.'.'.IndustryModel::FIELD_SLUG.' as industry_slug'),
                DB::raw(ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_ID.' as service_product_id'),
            )
            ->whereIn(IndustryService::TABLE.'.'.IndustryService::FIELD_ID, $serviceIdFilter)
            ->where(ProductModel::TABLE.'.'.ProductModel::FIELD_NAME, Product::LEAD->value)
            ->get();

        $this->serviceProductIdFilter = $industryServices->pluck('service_product_id')->unique()->values();
        $this->industryIdFilter = $industryServices->pluck('industry_id')->unique()->values();
        $this->industryIdToSlug = $industryServices->mapWithKeys(function($industry) {
            return [$industry['industry_id'] => $industry['industry_slug']];
        });
    }

    /**
     * Returns a hash mapped collection of active campaigns by zip code
     * Access via [industry id][zip code location id] => available budget entry with campaigns listed in 'campaign_ids'
     * @return Collection
     */
    public function getZipCodeBudgets(): Collection
    {
        $zipCodeBudgets = collect();

        foreach ($this->industryIdToSlug->toArray() as $industry) {
            $availableCampaigns = app()->make(CompanyCampaignBuilder::class)
                ->forIndustries([Industry::fromSlug($industry)])
                ->forProducts([Product::LEAD])
                ->forStatuses([CampaignStatus::ACTIVE])
                ->excludeLowBids(false)
                ->forCompanyConsolidatedStatuses([CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS])
                ->forPropertyTypes([PropertyType::RESIDENTIAL])
                ->setExclusionOfAdAutomationDisabledCampaigns(true)
                ->getQuery()
                ->get();

            $budgetsByLocation = $this->budgetStrategy->calculateBudgetAvailable($availableCampaigns);
            $zipCodeBudgets = $zipCodeBudgets->merge($budgetsByLocation);
        }

        // Group by on collection for hash map access by [industry id][zip code location id]
        return $zipCodeBudgets->groupBy(['industryId', 'locationId']);
    }

    /**
     * Returns a collection of floor prices by county location id, service product id, and sale type id
     * @return Collection
     */
    public function getCountyFloorPrices(): Collection
    {
        return DB::table(DatabaseHelperService::readOnlyDatabase().'.'.Location::TABLE)
            ->leftJoin(DatabaseHelperService::database().'.'.ProductCountyFloorPrice::TABLE, fn(JoinClause $join) =>
            $join->on(ProductCountyFloorPrice::TABLE.'.'.ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID, '=', Location::TABLE.'.'.Location::ID)
                ->whereIn(ProductCountyFloorPrice::TABLE.'.'.ProductCountyFloorPrice::FIELD_SALE_TYPE_ID, self::SALE_TYPE_ID_LIMIT)
                ->where(ProductCountyFloorPrice::TABLE.'.'.ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID, self::PROPERTY_TYPE_LIMIT)
                ->where(ProductCountyFloorPrice::TABLE.'.'.ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID, self::QUALITY_TIER_LIMIT)
                ->whereIn(ProductCountyFloorPrice::TABLE.'.'.ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, $this->serviceProductIdFilter)
            )
            ->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_COUNTY)
            ->get([
                Location::TABLE.'.'.Location::ID.' as county_location_id',
                Location::TABLE.'.'.Location::COUNTY_KEY,
                Location::TABLE.'.'.Location::STATE_ABBREVIATION,
                ProductCountyFloorPrice::TABLE.'.'.ProductCountyFloorPrice::FIELD_PRICE,
                ProductCountyFloorPrice::TABLE.'.'.ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID,
                ProductCountyFloorPrice::TABLE.'.'.ProductCountyFloorPrice::FIELD_SALE_TYPE_ID,
            ])
            // Group by on collection for hash map access by [location id][service product id][sale type id]
            ->groupBy(['county_location_id', ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, ProductStateFloorPrice::FIELD_SALE_TYPE_ID]);
    }

    /**
     * Returns a collection of floor prices by state location id, service product id, and sale type id
     * @return Collection
     */
    public function getStateFloorPrices(): Collection
    {
        return DB::table(DatabaseHelperService::readOnlyDatabase().'.'.Location::TABLE)
            ->join(DatabaseHelperService::database().'.'.ProductStateFloorPrice::TABLE, fn(JoinClause $join) =>
            $join->on(ProductStateFloorPrice::TABLE.'.'.ProductStateFloorPrice::FIELD_STATE_LOCATION_ID, '=', Location::TABLE.'.'.Location::ID)
                ->where(ProductStateFloorPrice::TABLE.'.'.ProductStateFloorPrice::FIELD_QUALITY_TIER_ID, self::QUALITY_TIER_LIMIT)
                ->whereIn(ProductStateFloorPrice::TABLE.'.'.ProductStateFloorPrice::FIELD_SALE_TYPE_ID, self::SALE_TYPE_ID_LIMIT)
                ->where(ProductStateFloorPrice::TABLE.'.'.ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID, self::PROPERTY_TYPE_LIMIT)
                ->whereIn(ProductStateFloorPrice::TABLE.'.'.ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, $this->serviceProductIdFilter)
            )
            ->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_STATE)
            ->get([
                Location::TABLE.'.'.Location::ID.' as state_location_id',
                Location::TABLE.'.'.Location::STATE_ABBREVIATION,
                ProductStateFloorPrice::TABLE.'.'.ProductStateFloorPrice::FIELD_PRICE,
                ProductStateFloorPrice::TABLE.'.'.ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID,
                ProductStateFloorPrice::TABLE.'.'.ProductStateFloorPrice::FIELD_SALE_TYPE_ID,
            ])
            // Group by on collection for hash map access by [location id][service product id][sale type id]
            ->groupBy(['state_location_id', ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, ProductStateFloorPrice::FIELD_SALE_TYPE_ID]);
    }

    /**
     * Returns a list of bid prices by county location, campaign id, and sale type id
     * @return Collection
     */
    public function getCountyCampaignBidPrices(): Collection
    {
        return Company::query()
            ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_COMPANY_ID, '=', Company::TABLE.'.'.Company::FIELD_ID)
            ->join(ProductCountyBidPrice::TABLE, fn (JoinClause $join) =>
            $join->on(ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_PRODUCT_CAMPAIGN_ID, '=', CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ID)
                ->where(ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_QUALITY_TIER_ID, self::QUALITY_TIER_LIMIT)
                ->whereIn(ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_SALE_TYPE_ID, self::SALE_TYPE_ID_LIMIT)
                ->where(ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID, self::PROPERTY_TYPE_LIMIT)
            )
            ->where(Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS)
            ->where(CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_STATUS, CampaignStatus::ACTIVE->value)
            ->get([
                Company::TABLE.'.'.Company::FIELD_ID.' as company_id',
                CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ID.' as campaign_id',
                ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_PRICE,
                ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID,
                ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID,
                ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_SALE_TYPE_ID,
            ])
            ->groupBy([ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID, 'campaign_id', ProductCountyBidPrice::FIELD_SALE_TYPE_ID]);
    }

    /**
     * Returns a list of state bid prices for campaigns by state location id, campaign id, and sale type
     * @return Collection
     */
    public function getStateCampaignBidPrices(): Collection
    {
        return Company::query()
            ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_COMPANY_ID, '=', Company::TABLE.'.'.Company::FIELD_ID)
            ->join(ProductStateBidPrice::TABLE, fn (JoinClause $join) =>
            $join->on(ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_PRODUCT_CAMPAIGN_ID, '=', CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ID)
                ->where(ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_QUALITY_TIER_ID, self::QUALITY_TIER_LIMIT)
                ->whereIn(ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_SALE_TYPE_ID, self::SALE_TYPE_ID_LIMIT)
                ->where(ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID, self::PROPERTY_TYPE_LIMIT)
            )
            ->where(Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS)
            ->where(CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_STATUS, CampaignStatus::ACTIVE->value)
            ->get([
                Company::TABLE.'.'.Company::FIELD_ID.' as company_id',
                CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ID.' as campaign_id',
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_PRICE,
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_STATE_LOCATION_ID,
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_SERVICE_PRODUCT_ID,
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_SALE_TYPE_ID,
            ])
            ->groupBy([ProductStateBidPrice::FIELD_STATE_LOCATION_ID, 'campaign_id', ProductStateBidPrice::FIELD_SALE_TYPE_ID]);
    }

    /**
     * Returns a list of custom floor prices by county location id, campaign id, and sale type id
     * @return Collection
     */
    public function getCustomCountyFloorPrices(): Collection
    {
        return CustomCampaignCountyFloorPrice::query()
            ->where(CustomCampaignCountyFloorPrice::TABLE.'.'.CustomCampaignCountyFloorPrice::FIELD_QUALITY_TIER_ID, self::QUALITY_TIER_LIMIT)
            ->whereIn(CustomCampaignCountyFloorPrice::TABLE.'.'.CustomCampaignCountyFloorPrice::FIELD_SALE_TYPE_ID, self::SALE_TYPE_ID_LIMIT)
            ->where(CustomCampaignCountyFloorPrice::TABLE.'.'.CustomCampaignCountyFloorPrice::FIELD_PROPERTY_TYPE_ID, self::PROPERTY_TYPE_LIMIT)
            ->get([
                CustomCampaignCountyFloorPrice::FIELD_COUNTY_LOCATION_ID,
                CustomCampaignCountyFloorPrice::FIELD_COMPANY_CAMPAIGN_ID,
                CustomCampaignCountyFloorPrice::FIELD_SALE_TYPE_ID,
                CustomCampaignCountyFloorPrice::FIELD_PRICE,
            ])->groupBy([CustomCampaignCountyFloorPrice::FIELD_COUNTY_LOCATION_ID, CustomCampaignCountyFloorPrice::FIELD_COMPANY_CAMPAIGN_ID, CustomCampaignCountyFloorPrice::FIELD_SALE_TYPE_ID]);
    }

    /**
     * Returns a list of custom floor prices by state location id, campaign id, and sale type id
     * @return Collection
     */
    public function getCustomStateFloorPrices(): Collection
    {
        return CustomCampaignStateFloorPrice::query()
            ->where(CustomCampaignStateFloorPrice::TABLE.'.'.CustomCampaignStateFloorPrice::FIELD_QUALITY_TIER_ID, self::QUALITY_TIER_LIMIT)
            ->whereIn(CustomCampaignStateFloorPrice::TABLE.'.'.CustomCampaignStateFloorPrice::FIELD_SALE_TYPE_ID, self::SALE_TYPE_ID_LIMIT)
            ->where(CustomCampaignStateFloorPrice::TABLE.'.'.CustomCampaignStateFloorPrice::FIELD_PROPERTY_TYPE_ID, self::PROPERTY_TYPE_LIMIT)
            ->get([
                CustomCampaignStateFloorPrice::FIELD_STATE_LOCATION_ID,
                CustomCampaignStateFloorPrice::FIELD_COMPANY_CAMPAIGN_ID,
                CustomCampaignStateFloorPrice::FIELD_SALE_TYPE_ID,
                CustomCampaignStateFloorPrice::FIELD_PRICE,
            ])
            ->groupBy([CustomCampaignStateFloorPrice::FIELD_STATE_LOCATION_ID, CustomCampaignStateFloorPrice::FIELD_COMPANY_CAMPAIGN_ID, CustomCampaignStateFloorPrice::FIELD_SALE_TYPE_ID]);
    }

    /**
     * Returns zip codes -> counties -> states, this is looped through to calculated ERpL for each industry in each county
     * @return Collection
     */
    public function getLocationData(): Collection
    {
        return DB::table(DatabaseHelperService::readOnlyDatabase().'.'.Location::TABLE)
            ->leftJoin(USZipCode::TABLE, fn(JoinClause $join) =>
            $join->on(USZipCode::TABLE.'.'.USZipCode::FIELD_ZIP_CODE, '=', Location::TABLE.'.'.Location::ZIP_CODE)
                ->where(USZipCode::TABLE.'.'.USZipCode::FIELD_CITY_TYPE, USZipCode::DEFAULT_CITY_TYPE)
            )
            ->leftJoin(DatabaseHelperService::readOnlyDatabase() .'.'. Location::TABLE .' as county_locations', fn(JoinClause $join) =>
            $join->on('county_locations.' . Location::COUNTY_KEY, '=', Location::TABLE .'.'. Location::COUNTY_KEY)
                ->on('county_locations.' . Location::STATE_ABBREVIATION, '=', Location::TABLE .'.'. Location::STATE_ABBREVIATION)
                ->where('county_locations.' . Location::TYPE, Location::TYPE_COUNTY)
            )
            ->leftJoin(DatabaseHelperService::readOnlyDatabase() .'.'. Location::TABLE .' as state_locations', fn(JoinClause $join) =>
            $join->on('state_locations.' . Location::STATE_ABBREVIATION, '=', Location::TABLE .'.'. Location::STATE_ABBREVIATION)
                ->where('state_locations.' . Location::TYPE, Location::TYPE_STATE)
            )
            ->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_ZIP_CODE)
            ->get([
                Location::TABLE.'.'.Location::ID.' as zip_location_id',
                Location::TABLE.'.'.Location::ZIP_CODE,
                'county_locations.'.Location::ID.' as county_location_id',
                'county_locations.'.Location::COUNTY_KEY,
                'county_locations.'.Location::STATE_ABBREVIATION,
                'state_locations.'.Location::ID.' as state_location_id',
                USZipCode::TABLE.'.'.USZipCode::FIELD_POPULATION,
                USZipCode::TABLE.'.'.USZipCode::FIELD_COUNTY_FIPS,
            ])
            ->groupBy('county_location_id');
    }
}
