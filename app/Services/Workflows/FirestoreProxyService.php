<?php

namespace App\Services\Workflows;

use Exception;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class FirestoreProxyService
{
    protected string $baseUrl;
    protected string $secret;
    public function __construct()
    {
        $this->baseUrl = config('services.google.firestore.flow_proxy_url') ?: '';
        $this->secret = config('services.google.firestore.flow_proxy_secret') ?: '';
    }

    /**
     * @return PendingRequest
     */
    protected function getClient(): PendingRequest
    {
        return Http::withToken($this->secret)->throw(function($res, $e) {
            logger()->error($e);
        });
    }

    /**
     * @param string $route
     * @return string
     */
    protected function joinPaths(string $route): string
    {
        return trim($this->baseUrl, "/") . "/" . trim($route, "/");
    }

    /**
     * @param string $route
     * @param array|null $params
     * @return Response
     */
    public function get(string $route, ?array $params = []): Response
    {
        return $this->getClient()->get($this->joinPaths($route), $params);
    }

    /**
     * @param string $route
     * @param array|null $body
     * @return Response
     */
    public function put(string $route, ?array $body = []): Response
    {
        return $this->getClient()->put($this->joinPaths($route), $body);
    }

    /**
     * @param string $route
     * @param array|null $body
     * @return Response
     */
    public function patch(string $route, ?array $body = []): Response
    {
        return $this->getClient()->patch($this->joinPaths($route), $body);
    }

    /**
     * @param string $route
     * @param array|null $body
     * @return Response
     */
    public function post(string $route, ?array $body = []): Response
    {
        return $this->getClient()->post($this->joinPaths($route), $body);
    }

    /**
     * @param string $route
     * @return Response
     */
    public function delete(string $route): Response
    {
        return $this->getClient()->delete($this->joinPaths($route));
    }
}
