<?php

namespace App\Services\Workflows;

use App\Abstracts\Workflows\Action;
use App\Enums\RunningWorkflowStatus;
use App\Factories\Workflows\RunningWorkflowFactory;
use App\Jobs\Workflows\RunWorkflowPipeline;
use App\Models\Workflow;
use App\Workflows\WorkflowPayload;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Pipeline\Pipeline;

class WorkflowProcessingService
{
    use DispatchesJobs;

    protected array $visitors;

    public function __construct($visitors)
    {
        $this->visitors = $visitors;
    }

    /**
     * Processes an action through the pipeline of visitors, and returns the workflow.
     *
     * @param Action $action
     * @return WorkflowPayload
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function process(Action $action): WorkflowPayload
    {
        /** @var Pipeline $pipeline */
        $pipeline = app()->make(Pipeline::class);

        $pipeline->send($action)
                 ->through($this->visitors)
                 ->via('handle')
                 ->then(function ($action) {
                     $action->handle();
                 });

        return $action->getPayload();
    }

    /**
     * Dispatches a new running workflow.
     *
     * @param int $id
     * @param WorkflowPayload $payload
     * @return void
     */
    public function dispatchNewRunningWorkflow(int $id, WorkflowPayload $payload): void
    {
        $workflow = Workflow::find($id);

        if($workflow) {
            $runningWorkflow = RunningWorkflowFactory::create(
                $id,
                $payload,
                RunningWorkflowStatus::INITIAL,
                $workflow->entry_action_id
            );

            $this->dispatch(new RunWorkflowPipeline($runningWorkflow));
        }
    }
}
