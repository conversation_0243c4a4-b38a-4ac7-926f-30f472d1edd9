<?php

namespace App\Http\Middleware\CompanyRegistration;

use App\Models\Odin\Company;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Validation\UnauthorizedException;

class HasRegistrationReference
{
    const REQUEST_COMPANY_REFERENCE     = 'company_reference';

    /**
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $companyReference = $request->get(self::REQUEST_COMPANY_REFERENCE);
        if ($companyReference && Company::query()->where(Company::FIELD_REFERENCE, $companyReference)->first()) {
            return $next($request);
        }
        else throw new UnauthorizedException('Unauthorized request, or invalid Company.');
    }


}
