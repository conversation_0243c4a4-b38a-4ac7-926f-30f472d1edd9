<?php

namespace App\Http\Middleware;

use App\Models\Legacy\EloquentUser;
use Closure;
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\UnauthorizedException;

class CheckClientDashboardCompany
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure(Request): (Response|RedirectResponse) $next
     * @return Response|RedirectResponse
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $companyId = (int) request()->route('legacyCompanyId');
        if(empty($companyId)) {
            throw new Exception("Missing company ID");
        }

        if(app(EloquentUser::class)->{EloquentUser::COMPANY_ID} !== $companyId) {
            throw new UnauthorizedException("User is not assigned to this company");
        }

        return $next($request);
    }
}
