<?php

namespace App\Http\Resources\ContactIdentification;

use App\Enums\ContactIdentification\ContactType;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\ContactIdentification\PossibleContact;
use Illuminate\Http\Client\Request;

/**
 * @mixin PossibleContact
 */
class PossibleContactResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @throws \Exception
     */
    public function toArray($request): array
    {
        return [
            'id'                    => $this->id,
            'model_relation_type'   => ContactType::fromModelClass($this->relation_type),
            'model_relation_id'     => $this->relation_id,
            'contact'               => $this->identifiable ? IdentifiedContactResourceFactory::make($this->identifiable) : null,
            'identified_contact_id' => $this->identified_contact_id
        ];
    }
}
