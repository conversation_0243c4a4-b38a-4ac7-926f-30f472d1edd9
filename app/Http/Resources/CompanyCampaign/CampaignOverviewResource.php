<?php

namespace App\Http\Resources\CompanyCampaign;

use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use Carbon\CarbonInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Number;

/**
 * @mixin array
 */
class CampaignOverviewResource extends BaseJsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'campaign_id'          => $this['campaign_id'],
            'campaign_reference'   => $this['campaign_reference'],
            'budget_display_name'  => $this['budget_display_name'],
            'budget_id'            => $this['budget_id'],
            'campaign_name'        => $this['campaign_name'],
            'budget_type'          => BudgetType::tryFrom($this['budget_type'])->getDisplayName(),
            'campaign_status'      => $this['campaign_status'],
            'spend_today'          => Number::currency($this['spend_today']),
            'average_daily_spend'  => Number::currency($this['average_daily_spend']),
            'budget_usage_today'   => round($this['budget_usage_today'] * 100, 2) . '%',
            'maximum_budget_usage' => round($this['maximum_budget_usage'], 2) . '%',
            'paused_at'            => $this->getPausedAtString(),
        ];
    }

    /**
     * @return string|null
     */
    public function getPausedAtString(): ?string
    {
        if ($this['campaign_status'] === CampaignStatus::ACTIVE->value) {
            return null;
        } else {
            return "(". CarbonHelper::parse($this['campaign_paused_at'])->diffForHumans(['syntax' => CarbonInterface::DIFF_RELATIVE_TO_NOW]) . ")";
        }
    }

}