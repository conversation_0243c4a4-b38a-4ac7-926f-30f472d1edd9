<?php

namespace App\Http\Resources\Billing;

use App\Enums\Billing\InvoiceItemTypes;
use App\Models\Odin\ProductAssignment;
use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
class InvoiceItemAddedResource extends AuthorableEventResource
{
    const string EVENT_PROPERTY_NOTES         = "notes";
    const string EVENT_PROPERTY_QUANTITY      = "quantity";
    const string EVENT_PROPERTY_UNIT_PRICE    = "unitPrice";
    const string EVENT_PROPERTY_BILLABLE_ID   = "billableId";
    const string EVENT_PROPERTY_DESCRIPTION   = "description";
    const string EVENT_PROPERTY_INVOICE_UUID  = "invoiceUuid";
    const string EVENT_PROPERTY_BILLABLE_TYPE = "billableType";
    const string ITEM_DETAILS                 = 'item_details';

    /**
     * Transform the resource into an array.
     *
     * @return array
     */
    public function getEventData(): array
    {
        $itemDetails = [
            self::EVENT_PROPERTY_QUANTITY      => $this->event_properties[self::EVENT_PROPERTY_QUANTITY],
            self::EVENT_PROPERTY_UNIT_PRICE    => $this->event_properties[self::EVENT_PROPERTY_UNIT_PRICE],
            self::EVENT_PROPERTY_BILLABLE_ID   => $this->event_properties[self::EVENT_PROPERTY_BILLABLE_ID],
            self::EVENT_PROPERTY_BILLABLE_TYPE => $this->event_properties[self::EVENT_PROPERTY_BILLABLE_TYPE],
            self::EVENT_PROPERTY_DESCRIPTION   => $this->event_properties[self::EVENT_PROPERTY_DESCRIPTION],
            ...$this->getInvoiceItemDetailsByType(
                $this->event_properties[self::EVENT_PROPERTY_BILLABLE_TYPE],
                $this->event_properties[self::EVENT_PROPERTY_BILLABLE_ID]
            )
        ];

        return [
            self::EVENT_PROPERTY_NOTES        => $this->event_properties[self::EVENT_PROPERTY_NOTES],
            self::ITEM_DETAILS                => $itemDetails,
            self::EVENT_PROPERTY_INVOICE_UUID => $this->event_properties[self::EVENT_PROPERTY_INVOICE_UUID],
        ];
    }

    /**
     * @param string $type
     * @param int|null $id
     * @return array
     */
    protected function getInvoiceItemDetailsByType(string $type, ?int $id = null): array
    {
        // TODO - Create classes ? Return from factory ?
        return match ($type) {
            InvoiceItemTypes::PRODUCT->value => $this->getProductDetails($id),
            default                          => []
        };
    }

    /**
     * TODO - Create classes ? Return from factory ?
     * @param int $id
     * @return array
     */
    protected function getProductDetails(int $id): array
    {
        $productAssignment = ProductAssignment::query()->find($id);

        if (!$productAssignment) return [];

        return [
            'consumer_product_id' => $productAssignment->{ProductAssignment::FIELD_CONSUMER_PRODUCT_ID},
        ];
    }
}
