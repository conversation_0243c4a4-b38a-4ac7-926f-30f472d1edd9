<?php

namespace App\Http\Resources\Billing;

use App\Enums\Billing\InvoiceStates;
use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
class InvoiceChargeRequestFailedResource extends AuthorableEventResource
{
    const string EVENT_PROPERTY_INVOICE_UUID = "invoiceUuid";

    /**
     * Transform the resource into an array.
     *
     * @return array
     */
    public function getEventData(): array
    {

        return [
            self::EVENT_PROPERTY_INVOICE_UUID => $this->event_properties[self::EVENT_PROPERTY_INVOICE_UUID],
        ];
    }
}
