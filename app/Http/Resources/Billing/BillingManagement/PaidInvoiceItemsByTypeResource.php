<?php

namespace App\Http\Resources\Billing\BillingManagement;

use App\Enums\Billing\InvoiceItemTypes;
use App\Http\Resources\Odin\BaseJsonResource;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Collection;

/**
 * @mixin array
 */
class PaidInvoiceItemsByTypeResource extends BaseJsonResource
{
    const string VALUE = 'value';
    const string TYPE  = 'type';
    const string NAME  = 'name';
    const string TOTAL = 'total';
    const string DATA  = 'data';

    public function __construct($resource)
    {
        parent::__construct($resource);
    }

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        /** @var Collection $data */
        $data = $this->resource;

        $formatted = [];
        $total     = 0;

        foreach ($data as $type) {
            $total += $type['total'];

            $formatted[] = [
                self::VALUE => $type['total'] / 100,
                self::TYPE  => $type['type'],
                self::NAME  => InvoiceItemTypes::tryFrom($type['type'])->getTitle()
            ];
        }
        return [
            self::TOTAL => $total / 100,
            self::DATA  => $formatted
        ];
    }
}
