<?php

namespace App\Http\Resources\Companies\Sales;

use App\Models\User;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;
use App\Models\SuccessManager;

/**
 * Class SuccessManagerResource
 * @mixin SuccessManager
 */
class SuccessManagerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        return
            [
                "id"                            => $this->{SuccessManager::FIELD_ID},
                "type"                          => SuccessManager::TYPE_NAMES[$this->{SuccessManager::FIELD_TYPE}],
                "name"                          => $this->{SuccessManager::RELATION_USER}?->{User::FIELD_NAME} ?? 'User unavailable',
                "user_id"                       => $this->{SuccessManager::FIELD_USER_ID},
                "include_in_sales_round_robin"  => $this->{SuccessManager::FIELD_INCLUDE_IN_SALES_ROUND_ROBIN} ? "Yes" : "No"
            ];
    }
}
