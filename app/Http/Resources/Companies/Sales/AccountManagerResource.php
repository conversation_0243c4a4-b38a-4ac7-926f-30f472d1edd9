<?php

namespace App\Http\Resources\Companies\Sales;

use App\Models\User;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;
use App\Models\AccountManager;

/**
 * Class AccountManagerResource
 * @mixin User
 */
class AccountManagerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        return [
            "id"                            => $this->{User::FIELD_ID},
            "type"                          => null,
            "name"                          => $this->{User::FIELD_NAME},
            "user_id"                       => $this->{User::FIELD_ID},
            "include_in_sales_round_robin"  => null,
        ];
    }
}
