<?php

namespace App\Http\Resources\Companies;

use App\Console\Commands\CompanyMergeTool\CompanyMergeService;
use App\Models\CompanyMergeRecord;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\ProductAssignment;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin CompanyMergeRecord
 */
class CompanyMergeRecordResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        return [
            'id'                  => $this->id,
            'source_company_name' => $this->getCleanName(),
            'source_company_id'   => $this->source_company_id,
            'undo_payload'        => $this->undo_payload,
            'queued_undo_summary' => $this->getQueuedUndoSummary(),
            'status'              => $this->getStatus(),
            'created_at'          => $this->created_at,
            'completed_at'        => $this->completed_at,
        ];
    }

    /**
     * @return string
     */
    private function getCleanName(): string
    {
        return preg_replace("/\(merge[^)]*\)/", '', $this->sourceCompany->name);
    }

    /**
     * @return string
     */
    private function getStatus(): string
    {
        if (!$this->completed_at) {
            return $this->created_at < now()->subMinutes(2)
                ? 'error'
                : 'in_progress';
        }
        else {
            return $this->undo_payload === null && $this->queued_undo_payload !== null
                ? 'undo_in_progress'
                : 'completed';
        }
    }

    /**
     * @return array
     */
    private function getQueuedUndoSummary(): array
    {
        $queuedUndoPayload = $this->queued_undo_payload;
        $productAssignments = count($queuedUndoPayload[CompanyMergeService::DATABASE_ADMIN2] ?? []);
        $quoteCompanies = count($queuedUndoPayload[CompanyMergeService::DATABASE_LEGACY] ?? []);

        return [
            CompanyMergeService::DATABASE_ADMIN2 => [
                ProductAssignment::TABLE    => [
                    ['total' => "$productAssignments will be returned to company_id $this->source_company_id"],
                ],
            ],
            CompanyMergeService::DATABASE_LEGACY => [
                EloquentQuoteCompany::TABLE => [
                    ['total' => "$quoteCompanies will be returned to company_id $this->source_company_id"],
                ],
            ],
        ];
    }
}
