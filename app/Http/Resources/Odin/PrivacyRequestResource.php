<?php

namespace App\Http\Resources\Odin;

use App\Models\PrivacyRequest;
use App\Models\User;
use Exception;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use JsonSerializable;

/**
 * @mixin PrivacyRequest
 */
class PrivacyRequestResource extends BaseJsonResource
{
    const string ID                = 'id';
    const string UUID              = 'uuid';
    const string STATUS            = 'status';
    const string APPROVED_BY       = 'approved_by';
    const string SOURCE            = 'source';
    const string SCAN_RESPONSE     = 'scan_response';
    const string PAYLOAD           = 'payload';
    const string CREATED_AT        = 'created_at';
    const string UPDATED_AT        = 'updated_at';
    const string DELETED_AT        = 'deleted_at';
    const string TITLE             = 'title';
    const string NAME              = 'name';
    const string FIRST_NAME        = 'first_name';
    const string LAST_NAME         = 'last_name';
    const string EMAIL             = 'email';
    const string PHONE             = 'phone';
    const string DESCRIPTION       = 'description';
    const string ADDRESS           = 'address';
    const string TOTALS            = 'totals';
    const string RECORDS_TOTALS    = 'records_totals';
    const string REDACTION_RECORDS = 'redaction_records';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     * @throws Exception
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        $statusDetails = [
            self::TITLE => $this->status->getTitle(),
            self::ID    => $this->status->value
        ];

        if ($this->approved_by_id !== null) {
            $userDetails = [
                self::NAME => $this->user->{User::FIELD_NAME},
                self::ID   => $this->approved_by_id
            ];
        } else {
            $userDetails = null;
        }

        $totals = [];

        if ($this->scan_response && is_array($this->scan_response)) {
            foreach ($this->scan_response as $url => $data) {
                $totalRecords = 0;
                foreach ($data as $item) {
                    if (isset($item['records']) && is_array($item['records'])) {
                        $totalRecords += count($item['records']);
                    }
                }
                $totals[$url] = $totalRecords;
            }
        } else {
            $totals = null;
        }

        return [
            self::ID                => $this->id,
            self::UUID              => $this->uuid,
            self::STATUS            => $statusDetails,
            self::APPROVED_BY       => $userDetails,
            self::SOURCE            => $this->source,
            self::SCAN_RESPONSE     => $this->scan_response,
            self::FIRST_NAME        => isset($this->payload[self::FIRST_NAME]) ? $this->payload[self::FIRST_NAME]: null,
            self::LAST_NAME         => isset($this->payload[self::LAST_NAME]) ? $this->payload[self::LAST_NAME]: null,
            self::EMAIL             => isset($this->payload[self::EMAIL]) ? $this->payload[self::EMAIL]: null,
            self::PHONE             => isset($this->payload[self::PHONE]) ? $this->payload[self::PHONE] : null,
            self::DESCRIPTION       => isset($this->payload[self::DESCRIPTION]) ? $this->payload[self::DESCRIPTION] : null,
            self::ADDRESS           => isset($this->payload[self::ADDRESS]) ? $this->payload[self::ADDRESS] : null,
            self::CREATED_AT        => Carbon::parse($this->created_at)->format('Y-m-d'),
            self::UPDATED_AT        => Carbon::parse($this->updated_at)->format('Y-m-d'),
            self::DELETED_AT        => $this->deleted_at !== null ?
                Carbon::parse($this->deleted_at)->format('Y-m-d') :
                null,
            self::TOTALS            => $totals,
            self::REDACTION_RECORDS => PrivacyRequestRedactionRecordsResource::collection($this->records),
        ];
    }
}
