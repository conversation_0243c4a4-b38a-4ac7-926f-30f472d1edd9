<?php

namespace App\Http\Resources\Odin;

use App\Models\Phone;
use Illuminate\Http\Client\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BasicPhoneResource extends JsonResource
{
    const FIELD_ID      = 'id';
    const FIELD_PHONE   = 'phone';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            self::FIELD_ID      => $this->{Phone::FIELD_ID},
            self::FIELD_PHONE   => $this->{Phone::FIELD_PHONE},
        ];
    }
}
