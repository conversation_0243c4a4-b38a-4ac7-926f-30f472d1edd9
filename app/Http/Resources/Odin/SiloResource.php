<?php

namespace App\Http\Resources\Odin;

use App\Models\Odin\Silo;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin Silo
 */
class SiloResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        $website = $this->website;
        $industry = $this->industry;
        $locations = $this->locationSiloPages;

        return [
            'id'                => $this->id,
            'name'              => $this->name,
            'root_path'         => $this->root_path,
            'collection_handle' => $this->collection_handle,
            'is_active'         => $this->is_active,
            'website'           => [
                'id'    => $website->id,
                'name'  => $website->name,
            ],
            'industry'          => [
                'id'    => $industry->id,
                'name'  => $industry->name,
            ],
            'flow_id'           => $this->flow_id,
            'revision_id'       => $this->revision_id,
            'location_silos' => LocationSiloPageResource::collection($locations)
        ];
    }
}
