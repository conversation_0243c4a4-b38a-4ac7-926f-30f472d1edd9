<?php

namespace App\Http\Resources\Odin;

use App\Models\Odin\WebsiteApiKeyOrigin;
use Illuminate\Http\Resources\Json\JsonResource;

class WebsiteApiKeyOriginResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request): array|\JsonSerializable|\Illuminate\Contracts\Support\Arrayable
    {
        return [
            WebsiteApiKeyOrigin::FIELD_ID => $this->{WebsiteApiKeyOrigin::FIELD_ID},
            WebsiteApiKeyOrigin::FIELD_ORIGIN => $this->{WebsiteApiKeyOrigin::FIELD_ORIGIN},
        ];
    }
}
