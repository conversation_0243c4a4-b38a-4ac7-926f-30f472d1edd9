<?php

namespace App\Http\Resources\Odin;

use App\Models\Odin\ProductCampaign;
use Illuminate\Http\Client\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductCampaignFieldOptionsResource extends JsonResource
{
    const FIELD_ID      = 'id';
    const FIELD_NAME    = 'name';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            self::FIELD_ID      => $this->{ProductCampaign::FIELD_ID},
            self::FIELD_NAME    => $this->{ProductCampaign::FIELD_NAME},
        ];
    }
}
