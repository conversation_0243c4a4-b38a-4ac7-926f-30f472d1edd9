<?php

namespace App\Http\Resources\MarketingCampaign;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\MarketingLog;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Str;

/**
 * @mixin MarketingLog
 */
class ListMarketingLogResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id'          => $this->{MarketingLog::FIELD_ID},
            'level'       => $this->{MarketingLog::FIELD_LEVEL},
            'message'     => $this->{MarketingLog::FIELD_MESSAGE},
            'namespace'   => Str::headline($this->{MarketingLog::FIELD_NAMESPACE}->value),
            'created_at'  => CarbonHelper::parse($this->{MarketingLog::FIELD_CREATED_AT})->toFormat(),
            'relations'   => ListMarketingLogRelationResource::collection($this->{MarketingLog::RELATION_RELATIONS})
        ];
    }
}
