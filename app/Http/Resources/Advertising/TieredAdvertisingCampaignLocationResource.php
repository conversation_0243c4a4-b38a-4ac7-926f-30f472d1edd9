<?php

namespace App\Http\Resources\Advertising;

use App\Models\Legacy\Location;
use App\Models\TieredAdvertisingCounty;
use App\Models\User;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * Class TieredAdvertisingCampaignLocationResource
 * @mixin TieredAdvertisingCounty
 */
class TieredAdvertisingCampaignLocationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        return [
            "county"                => $this->{Location::COUNTY},
            "stateAbbr"             => $this->{Location::STATE_ABBREVIATION},
            "state"                 => $this->{Location::STATE},
            "tcpa"                  => $this->{TieredAdvertisingCounty::FIELD_TCPA_BID},
            "negativeZips"          => $this->{TieredAdvertisingCounty::FIELD_NEGATIVE_ZIP_CODES},
            "data"                  => $this->{TieredAdvertisingCounty::FIELD_DATA},
        ];
    }
}
