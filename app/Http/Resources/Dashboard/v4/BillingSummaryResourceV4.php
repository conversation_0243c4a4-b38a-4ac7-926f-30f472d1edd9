<?php

namespace App\Http\Resources\Dashboard\v4;

use App\Http\Resources\Billing\CompanyCreditBalancesResource;
use App\Models\Billing\Invoice;
use App\Models\Odin\ProductAssignment;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use JsonSerializable;

/**
 * @mixin ProductAssignment
 */
class BillingSummaryResourceV4 extends JsonResource
{
    const string NAME               = 'name';
    const string NUMBER_OF_LEADS    = 'number_of_leads';
    const string TOTAL_PRICE        = 'total_price';
    const string FIELD_CREDIT_DATA  = 'credit_data';
    const string FIELD_PRODUCT_DATA = 'product_data';
    const string CREDITS            = 'credits';
    const string TYPES              = 'types';
    const string AVAILABLE_CREDIT   = 'available_credit';
    const string CAMPAIGNS          = 'campaigns';

    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        $creditData         = $this->resource->get(self::FIELD_CREDIT_DATA, collect());
        $productAssignments = $this->resource->get(self::FIELD_PRODUCT_DATA, collect());

        return [
            self::CREDITS   => [
                self::TYPES            => CompanyCreditBalancesResource::collection($creditData),
                self::AVAILABLE_CREDIT => $creditData->sum('balance') / 100
            ],
            self::CAMPAIGNS => $this->formatToCampaignSummary($productAssignments),
        ];
    }

    public function formatToCampaignSummary(Collection $productAssignments)
    {

        $formatted = [];

        foreach ($productAssignments as $productAssignment) {
            $campaignName = $productAssignment->budget?->budgetContainer?->companyCampaign?->name
                ?? $productAssignment->campaign?->name
                ?? 'Unknown Campaign';

            if (!isset($formatted[$campaignName])) {
                $formatted[$campaignName] = [
                    self::NAME            => $campaignName,
                    self::NUMBER_OF_LEADS => 0,
                    self::TOTAL_PRICE     => 0.00,
                ];
            }

            $formatted[$campaignName][self::NUMBER_OF_LEADS]++;
            $formatted[$campaignName][self::TOTAL_PRICE] += $productAssignment->cost;
        }

        return collect($formatted)->values();
    }

}
