<?php

namespace App\Http\Resources\Dashboard\LegacyTransformers;

class NotificationSettingLegacyTransformer
{
    const FIELD_ID              = 'id';
    const FIELD_LEGACY_USER_ID  = 'legacy_id';
    const FIELD_NAME            = 'name';
    const FIELD_EMAIL           = 'email';
    const FIELD_CELL_PHONE      = 'cell_phone';

    const FIELD_ALERT_TYPE      = 'type';
    const FIELD_ALERT_METHOD    = 'method';

    const RELATION_ALERTS       = 'alerts';

    const ALERT_TYPE_REVIEWS    = 'reviews';
    const ALERT_TYPE_INVOICES   = 'invoices';
    const ALERT_TYPE_OUTBID     = 'outbid';

    const ALERT_METHOD_SMS      = 'sms';
    const ALERT_METHOD_EMAIL    = 'email';

    /**
     * @param array $legacyNotificationData
     * @return array
     */
    public function transformGetRequest(array $legacyNotificationData): array
    {
        return $this->convertCompanyUsersGet($legacyNotificationData);
    }

    private function convertCompanyUsersGet(array $companyUsers): array
    {
        return collect($companyUsers)->map(function(array $user) {
            return [
                self::FIELD_LEGACY_USER_ID  => $user['contactid'] ?? 0,
                self::FIELD_NAME            => $user['name'] ?? '',
                self::FIELD_EMAIL           => $user['email'] ?? '',
                self::FIELD_CELL_PHONE      => $user->mobile ?? '',
                self::RELATION_ALERTS       => $this->convertAlertsGet($user['alerts'])
            ];
        })->toArray();
    }

    private function convertAlertsGet(array $alerts): array
    {
        return collect($alerts)->map(function(array $alert) {
            return [
                self::FIELD_ALERT_TYPE      => $alert['type'] ?? null,
                self::FIELD_ALERT_METHOD    => $alert['method'] ?? null,
            ];
        })->toArray();
    }

    /**
     * @param array $legacyNotificationData
     * @return array
     */
    public function transformPutRequest(array $legacyNotificationData): array
    {
        return $this->convertCompanyUserPut($legacyNotificationData);
    }

    private function convertCompanyUserPut(array $companyUsers): array
    {
        return collect($companyUsers)->map(function(array $user) {
            return [
                self::FIELD_ID              => $user[self::FIELD_LEGACY_USER_ID] ?? null,
                self::ALERT_TYPE_INVOICES   => $user[self::RELATION_ALERTS][self::ALERT_TYPE_INVOICES],
                self::ALERT_TYPE_REVIEWS    => $user[self::RELATION_ALERTS][self::ALERT_TYPE_REVIEWS],
                self::ALERT_TYPE_OUTBID     => $user[self::RELATION_ALERTS][self::ALERT_TYPE_OUTBID],
            ];
        })->toArray();
    }

}
