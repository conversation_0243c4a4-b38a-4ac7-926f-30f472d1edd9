<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class CrmProviderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|Arrayable|JsonSerializable
    {
        $systemFields = $this['crm_fields'][0] ?? null;
        $fields = $this['crm_fields'][1] ?? null;

        return [
            'id'            => $this['id'],
            'name'          => $this['display_name'],
            'key'           => $this['name'],
            'status'        => $this['status'],
            'system_fields' => $systemFields ? $this->transformFields($systemFields) : [],
            'fields'        => $fields ? $this->transformFields($fields) : [],
        ];
    }

    /**
     * @param array $fields
     * @return array
     */
    private function transformFields(array $fields): array
    {
        $fieldArray = $fields['crm_fields'] ?? null;
        if ($fieldArray) {
            return array_map(function($field) {
                return [
                    'id'        => $field['crm_integration_field_id'],
                    'name'      => $field['display_name'],
                    'key'       => $field['name'],
                    'mandatory' => $field['mandatory'],
                    'status'    => $field['status'],
                    'default'   => $field['default_field_value']
                ];
            }, $fieldArray);
        }
        else return [];
    }

}
