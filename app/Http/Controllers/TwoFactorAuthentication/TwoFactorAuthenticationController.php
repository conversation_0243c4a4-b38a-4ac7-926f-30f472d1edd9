<?php

namespace App\Http\Controllers\TwoFactorAuthentication;

use App\Http\Controllers\Controller;
use App\Http\Middleware\Verify2FAMiddleware;
use App\Models\User;
use App\Services\TwoFactorAuthentication\TwoFactorAuthenticationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

class TwoFactorAuthenticationController extends Controller
{
    const REQUEST_CODE = 'code';

    /**
     * Shows the setup page to handle setting up 2FA.
     *
     * @return Response
     */
    public function setup(TwoFactorAuthenticationService $service): Response|RedirectResponse
    {
        /** @var User $user */
        $user = Auth::user();

        if ($user->verified_2fa)
            return response()->redirectTo('/');

        return response()->view('2fa.setup', ['qrCode' => $service->get2FAQRCode($user)]);
    }

    /**
     * Handles setting the verified 2fa flag on a user if the code is valid.
     *
     * @param TwoFactorAuthenticationService $service
     * @return JsonResponse
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function verifySetup(TwoFactorAuthenticationService $service): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $user->verified_2fa = $service->verifyCode($user, request()->get(self::REQUEST_CODE, ''));
        $user->save();

        if ($user->verified_2fa)
            request()->session()->put(Verify2FAMiddleware::SESSION_KEY, true);

        return response()->json([
            'data' => [
                'valid' => $user->verified_2fa
            ]
        ]);
    }

    /**
     * Shows the verify page to handle showing 2FA.
     *
     * @return Response
     */
    public function show(): Response
    {
        return response()->view('2fa.verify');
    }

    /**
     * Handles verifying.
     *
     * @return JsonResponse
     */
    public function verify(TwoFactorAuthenticationService $service): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $valid = $service->verifyCode($user, request()->get(self::REQUEST_CODE, ''));
        if ($valid)
            request()->session()->put(Verify2FAMiddleware::SESSION_KEY, true);

        return response()->json([
            'data' => [
                'valid' => $valid
            ],
        ]);
    }
}
