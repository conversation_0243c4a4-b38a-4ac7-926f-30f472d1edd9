<?php

namespace App\Http\Controllers;

use App\Models\UserFilterPreset;
use App\Models\User;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;

class TaskController extends Controller
{
    public function index(): View
    {
        return view('tasks');
    }


    /**
     * Renders task queue page binding users' filter preset
     *
     * @return View
     */
    public function tasksQueue(): View
    {
        /** @var User $user */
        $user = Auth::user();

        /** @var UserFilterPreset $filterPreset */
        $filterPreset = $user->filterPreset['data'] ?? json_encode(new \stdClass());

        return view('tasks', ['user' => $user, 'filterPreset' => $filterPreset]);
    }
}
