<?php

namespace App\Http\Controllers\Odin\ResourceAPI;

use App\Models\Odin\Address;
use App\Repositories\Odin\AddressRepository;
use App\Repositories\Odin\CompanyLocationRepository;
use Illuminate\Validation\Rule;
use App\Services\Odin\API\OdinAPIService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CompanyLocationController extends BaseAPIController
{
    const REQUEST_ADDRESS           = 'address';
    const REQUEST_ADDRESS_LEGACY_ID = 'legacy_address_id';
    const REQUEST_COMPANY_LOCATION  = 'company_location';
    const REQUEST_ODIN_ADDRESS_ID   = 'odin_address_id';

    public function __construct(
        Request $request,
        OdinAPIService $service,
        protected AddressRepository $addressRepository,
        protected CompanyLocationRepository $companyLocationRepository,
    )
    {
        parent::__construct($request, $service);
    }

    public function updateCompanyLocation($addressOnly = false, $odinId = null): JsonResponse
    {
        $this->validate(
            $this->request,
            [
                self::REQUEST_ADDRESS           => 'required|array',
                self::REQUEST_ADDRESS_LEGACY_ID => [ Rule::requiredIf(!$odinId), 'int' ],
                self::REQUEST_COMPANY_LOCATION  => [ Rule::requiredIf(!$addressOnly), 'array'],
            ]
        );

        $addressLegacyId = $this->request->get(self::REQUEST_ADDRESS_LEGACY_ID);
        $addressData = $this->request->get(self::REQUEST_ADDRESS);
        $address = $odinId
            ? $this->addressRepository->findByIdOrFail($odinId)
            : $this->addressRepository->findByLegacyId($addressLegacyId);
        $companyLocationData = $this->request->get(self::REQUEST_COMPANY_LOCATION);

        $locationSuccess = $this->service->update($address->{Address::FIELD_ID}, $addressData);
        if ($addressOnly) {
            return $this->formatResponse([ "status" => $locationSuccess ]);
        }

        return $this->formatResponse([
            "status" => $this->service->create($companyLocationData) && $locationSuccess
        ]);
    }

    /**
     * Assign a legacy ID to a newly created Address
     * @return JsonResponse
     */
    public function assignLegacyIdToNewAddress(): JsonResponse
    {
        $odinId = $this->request->get(self::REQUEST_ODIN_ADDRESS_ID);
        return $this->updateCompanyLocation(true, $odinId);
    }

    public function assignLegacyIdToNewAddressFromImporter()
    {
        $this->validate(
            $this->request,
            [
                self::REQUEST_ADDRESS_LEGACY_ID  => 'required|int',
                self::REQUEST_ODIN_ADDRESS_ID  => 'required|int',
            ]
        );

        $legacyAddressId = $this->request->get(self::REQUEST_ADDRESS_LEGACY_ID);
        $odinAddressId = $this->request->get(self::REQUEST_ODIN_ADDRESS_ID);

        $address = $this->addressRepository->findByIdOrFail($odinAddressId);

        $address->{Address::FIELD_LEGACY_ID} = $legacyAddressId;
        $address->save();
    }

}
