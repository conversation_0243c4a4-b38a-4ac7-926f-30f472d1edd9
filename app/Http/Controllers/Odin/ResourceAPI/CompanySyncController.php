<?php

namespace App\Http\Controllers\Odin\ResourceAPI;

use App\Models\Odin\Company;
use App\Repositories\Odin\CompanyRepository;
use App\Services\Odin\API\OdinAPIService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class CompanySyncController extends BaseAPIController
{
    const REQUEST_LEGACY_ID            = 'legacy_id';
    const REQUEST_COMPANY_REFERENCE    = 'company_reference';

    /**
     * @param Request $request
     * @param OdinAPIService $service
     * @param CompanyRepository $companyRepository
     */
    public function __construct(
        Request                     $request,
        OdinAPIService              $service,
        protected CompanyRepository $companyRepository,
    )
    {
        parent::__construct($request, $service);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function syncLegacyId(): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_LEGACY_ID         => 'required|numeric',
            self::REQUEST_COMPANY_REFERENCE => 'required|string',
        ]);
        $company = $this->companyRepository->findByLegacyReferenceOrFail($this->request->get(self::REQUEST_COMPANY_REFERENCE));
        $success = $company->update([ Company::FIELD_LEGACY_ID => $this->request->get(self::REQUEST_LEGACY_ID )]);

        return $this->formatResponse([
            'status'    => $success
        ]);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getCompanyId(): JsonResponse
    {
        $this->validate($this->request, [
            self::REQUEST_LEGACY_ID => 'required|integer',
        ]);
        $company = $this->companyRepository->findByLegacyIdOrFail($this->request->get(self::REQUEST_LEGACY_ID));
        return $this->formatResponse([
            'status' => true,
            'id' => $company->{Company::FIELD_ID},
            'reference' => $company->{Company::FIELD_REFERENCE}
        ]);
    }
}
