<?php

namespace App\Http\Controllers\Odin\ResourceAPI;

use App\Http\Resources\Odin\PhoneOptionResource;
use App\Repositories\LeadProcessing\LeadCommunicationRepository;
use App\Services\Odin\API\OdinAPIService;
use Illuminate\Http\Request;

class PhoneController extends BaseAPIController
{
    public function __construct(
        Request $request,
        OdinAPIService $service,
        protected LeadCommunicationRepository $communicationRepository
    )
    {
        parent::__construct($request, $service);
    }

    function getAvailablePhones(): \Illuminate\Http\JsonResponse
    {
        $availableNumbers = $this->communicationRepository->getAvailableNumbers();

        return $this->formatResponse([
            'status' => true,
            'phones' => PhoneOptionResource::collection($availableNumbers)
        ]);
    }
}
