<?php

namespace App\Http\Controllers\Prospects;

use App\Models\User;

class ProspectingUsersController extends ProspectingConfigController
{
    public function index()
    {
        return response()->json([
            'data' => User::with('roles')
                ->role($this->roles)
                ->get()
                ->map(fn ($user) => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'roles' => $user->roles
                        ->filter(fn ($role) => collect($this->roles)->contains($role->name))
                        ->map(fn ($role) => [
                            'id' => $role->id,
                            'name' => str($role->name)->headline(),
                        ])
                        ->values(),
                ]),
        ]);
    }
}
