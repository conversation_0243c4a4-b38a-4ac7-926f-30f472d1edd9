<?php

namespace App\Http\Controllers\API;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\StoreGlobalConfigurationRequest;
use App\Models\GlobalConfiguration;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Services\GlobalConfigurationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class GlobalConfigurationsManagementController extends APIController
{
    const CONFIGURATIONS = 'configurations';

    public function __construct(
        Request                           $request,
        JsonAPIResponseFactory            $apiResponseFactory,
        public GlobalConfigurationRepository $globalConfigurationRepository
    ){
        parent::__construct($request, $apiResponseFactory);
    }

    public function getConfigurations(Request $request): JsonResponse
    {
        $configKey = $request->input('config_key');

        $configurations = GlobalConfiguration::query()
            ->when($configKey, fn ($query) => $query->where(GlobalConfiguration::FIELD_CONFIGURATION_KEY, $configKey))
            ->get()
            ->map(function ($configuration) {
            return GlobalConfigurationService::prepareConfigurationResponse($configuration);
        });

        return $this->formatResponse([
            self::CONFIGURATIONS => $configurations
        ]);
    }

    /**
     * @param StoreGlobalConfigurationRequest $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function addConfiguration(StoreGlobalConfigurationRequest $request): JsonResponse
    {
        $configuration = $this->globalConfigurationRepository->createConfiguration(
            $request->get(GlobalConfiguration::FIELD_CONFIGURATION_KEY),
            $request->get(GlobalConfiguration::FIELD_CONFIGURATION_PAYLOAD),
            Auth::user());

        return $this->formatResponse([
            'configuration' => $configuration
        ]);
    }

    /**
     * @param GlobalConfiguration $configuration,
     * @param StoreGlobalConfigurationRequest $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updateConfiguration(GlobalConfiguration $configuration, StoreGlobalConfigurationRequest $request): JsonResponse
    {
        $this->globalConfigurationRepository->updateConfiguration(
            $configuration,
            $request->get(GlobalConfiguration::FIELD_CONFIGURATION_KEY),
            $request->get(GlobalConfiguration::FIELD_CONFIGURATION_PAYLOAD),
            Auth::user());

        return $this->formatResponse([
            'configuration' => $configuration
        ]);
    }

    public function deleteConfiguration($configurationId): JsonResponse
    {
        $this->globalConfigurationRepository->deleteConfiguration(
            $configurationId,
            Auth::user());

        return $this->formatResponse();
    }
}
