<?php

namespace App\Http\Controllers\API\Appointments;

use App\Enums\Odin\AppointmentCancellationReason;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\CancelAppointmentRequest;
use App\Repositories\Odin\CompanyRepository;
use App\Services\Odin\Appointments\AppointmentService;
use App\Services\Odin\Appointments\MultiIndustryAppointmentService;
use App\Services\Odin\Campaigns\CompanyCampaignAppointmentService;
use App\Transformers\API\AppointmentAPITransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Override methods as required for CompanyCampaigns
 * TODO: Once Solar is migrated, merge into one Controller and remove anything redundant
 */
class CompanyCampaignAppointmentsController extends AppointmentController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        CompanyRepository $companyRepository,
        AppointmentService $appointmentService,
        AppointmentAPITransformer $transformer,
        MultiIndustryAppointmentService $multiIndustryAppointmentService
    )
    {
        parent::__construct($request, $apiResponseFactory, $companyRepository, $appointmentService, $transformer, $multiIndustryAppointmentService);
    }

    /**
     * @param CancelAppointmentRequest $cancelAppointmentRequest
     * @return JsonResponse
     */
    public function cancelConsumerAppointment(CancelAppointmentRequest $cancelAppointmentRequest): JsonResponse
    {
        $data = $cancelAppointmentRequest->safe()->only([
            self::REQUEST_APPOINTMENT_KEY,
            self::REQUEST_APPOINTMENT_CODE,
            self::REQUEST_CANCELLATION_REASON,
            self::REQUEST_CANCELLATION_NOTE,
            self::REQUEST_RESCHEDULED_DATE
        ]);

        $cancellationReason = gettype($data[self::REQUEST_CANCELLATION_REASON]) === 'string'
            ? AppointmentCancellationReason::from($data[self::REQUEST_CANCELLATION_REASON])
            : $data[self::REQUEST_CANCELLATION_REASON];

        $appointmentDelivery = $this->appointmentService->getAppointmentDeliveryByKeyAndCode(
            $data[self::REQUEST_APPOINTMENT_KEY],
            $data[self::REQUEST_APPOINTMENT_CODE]
        );
        $companyCampaign = $appointmentDelivery->companyCampaign ?? null;

        if ($companyCampaign) {
            /** @var CompanyCampaignAppointmentService $appointmentService */
            $appointmentService = app(CompanyCampaignAppointmentService::class);
            $status = $appointmentService->cancelOrRescheduleAppointmentForConsumer(
                appointmentDelivery: $appointmentDelivery,
                cancellationReason: $cancellationReason,
                cancellationNote: $data[self::REQUEST_CANCELLATION_NOTE],
                rescheduledDateTimestamp: $data[self::REQUEST_RESCHEDULED_DATE]
            );

            return $this->formatResponse([
                self::REQUEST_STATUS => $status,
            ]);
        }
        else {
            return parent::cancelConsumerAppointment($cancelAppointmentRequest);
        }
    }
}
