<?php

namespace App\Http\Controllers\API\Users;

use App\Enums\SupportedTimezones;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\StoreUserFilterPresetRequest;
use App\Http\Requests\StoreUserSearchFilterRequest;
use App\Http\Requests\UserSearchFilterRequest;
use App\Models\User;
use App\Models\UserCallForwarding;
use App\Models\UserSearchFilters;
use App\Repositories\UserRepository;
use App\Repositories\UserSearchFilterRepository;
use App\Services\Odin\UserService;
use App\Services\UserFilterPresetService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class UserSettingsController extends UserBaseAPIController
{

    const STATUS   = 'status';
    const NUMBER   = 'number';
    const TIMEZONE = 'timezone';
    const USER     = 'user';


    /**
     * @param Request                $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     */
    public function __construct(
        Request                  $request,
        JsonAPIResponseFactory   $apiResponseFactory,
        UserService $userService,
        protected UserRepository $repository,
        protected UserFilterPresetService  $userFilterPresetService,
        protected UserSearchFilterRepository $userSearchFilterRepository
    ) {
        parent::__construct($request, $apiResponseFactory, $repository, $userService);
    }

    /**
     * @param User $user
     * @return JsonResponse
     */
    public function getCallForwardingData(User $user): JsonResponse
    {
        $callForwarding = UserCallForwarding::firstOrCreate(
            [UserCallForwarding::FIELD_USER_ID => $user->id]
        );
        return $this->formatResponse([$callForwarding]);
    }

    /**
     * @param User $user
     * @return JsonResponse
     */
    public function updateCallForwardingStatus(User $user): JsonResponse
    {
        $status = $this->request->get(self::STATUS, false);

        $callForwarding = UserCallForwarding::updateOrCreate(
            [UserCallForwarding::FIELD_USER_ID => $user->id],
            [UserCallForwarding::FIELD_STATUS => $status]
        );
        return $this->formatResponse([$callForwarding]);
    }

    /**
     * @param User $user
     * @return JsonResponse
     */
    public function updateCallForwardingNumber(User $user): JsonResponse
    {
        $number = $this->request->get(self::NUMBER, false);
        $callForwarding = UserCallForwarding::updateOrCreate(
            [UserCallForwarding::FIELD_USER_ID => $user->id],
            [UserCallForwarding::FIELD_FORWARD_TO_NUMBER => $number]
        );
        return $this->formatResponse([$callForwarding]);
    }

    /**
     * @param string|null $number
     * @return bool
     */
    function validatePhoneNumber(?string $number): bool
    {
        if (
            !$number ||
            (strlen($number) === 10 && !str_starts_with($number, '1'))
        ) {
            return true;
        }
        return false;
    }

    /**
     * Returns the user timezone.
     *
     * @param User $user
     * @return JsonResponse
     */
    public function getTimezone(User $user): JsonResponse
    {
        return $this->formatResponse(["timezone" => $user->timezone]);
    }

    /**
     * Updates a users timezone.
     *
     * @param User $user
     * @return JsonResponse
     */
    public function updateTimezone(User $user): JsonResponse
    {
        $timezone = $this->request->get(self::TIMEZONE, SupportedTimezones::EASTERN->value);

        $user->timezone = SupportedTimezones::tryFrom($timezone) ?: $user->timezone;
        $user->save();

        return $this->formatResponse(["timezone" => $user->timezone]);
    }

    /**
     * Stores a users filter preset.
     *
     * @param StoreUserFilterPresetRequest $request
     * @param User $user
     * @return void
     */
    public function storeUserFilterPresets(StoreUserFilterPresetRequest $request, User $user): void
    {
       $validated = $request->validated();

       $this->userFilterPresetService->createOrUpdateFilterPreset($validated['filterPreset'], $user->id);
    }

    /**
     * @param User $user
     * @return JsonResponse
     */
    public function getUserLinks(User $user): JsonResponse
    {
        return $this->formatResponse([
            self::STATUS    => !!$user,
            self::USER      => [
                User::FIELD_MEETING_URL => $user->{User::FIELD_MEETING_URL} ?? '',
            ],
        ]);
    }

    /**
     * @param User $user
     * @return JsonResponse
     */
    public function updateUserLinks(User $user): JsonResponse
    {
        $validated = $this->request->validate([
            User::FIELD_MEETING_URL => 'string|max:64|nullable',
        ]);

        return $this->formatResponse([
            self::STATUS    => $user->update([
                User::FIELD_MEETING_URL => $validated[User::FIELD_MEETING_URL],
            ]),
        ]);
    }

    /**
     * @param StoreUserSearchFilterRequest $request
     * @param User $user
     * @return JsonResponse
     */
    public function storeUserSearchFilters(StoreUserSearchFilterRequest $request, User $user): JsonResponse
    {
        $validated = $request->validated();

        return $this->formatResponse([
            self::STATUS => !!$this->userSearchFilterRepository->createOrUpdate(
                $user->{User::FIELD_ID},
                $validated[StoreUserSearchFilterRequest::REQUEST_FILTER_CATEGORY],
                $validated[StoreUserSearchFilterRequest::REQUEST_FILTER_DATA],
            ),
        ]);
    }


    /**
     * @param UserSearchFilterRequest $request
     * @param User $user
     * @return JsonResponse
     */
    public function getUserSearchFilters(UserSearchFilterRequest $request, User $user): JsonResponse
    {
        $validated = $request->validated();

        return $this->formatResponse([
            'filterSet' => $this->userSearchFilterRepository->find(
                $user->{User::FIELD_ID},
                $validated[UserSearchFilterRequest::REQUEST_FILTER_CATEGORY]
            )
        ]);
    }

    /*
    * @param User $user
    * @return JsonResponse
    */
    public function updateUserProfile(User $user): JsonResponse
    {
        $validated = $this->request->validate([
            User::FIELD_NAME => 'required|string',
        ]);

        return $this->formatResponse([
            self::STATUS    => $user->update([
                User::FIELD_NAME => $validated[User::FIELD_NAME],
            ]),
        ]);
    }
}
