<?php

namespace App\Http\Controllers\API;

use App\Factories\JsonAPIResponseFactory;
use App\Services\Legacy\MissedRevenueService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MissedRevenueByLocationController extends APIController
{
    const ZIP_CODE = 'zip_code';

    /** @var MissedRevenueService $missedRevenueService */
    protected MissedRevenueService $missedRevenueService;

    public function __construct(Request $request, JsonAPIResponseFactory $apiResponseFactory, MissedRevenueService $missedRevenueService)
    {
        parent::__construct($request, $apiResponseFactory);

        $this->missedRevenueService = $missedRevenueService;
    }

    /**
     * @return JsonResponse
     */
    public function getTopCountiesInIndustry(): JsonResponse
    {
        $industry = $this->request->get('industry');
        $daysAgo  = intval($this->request->get('period_in_days', 7));

        $locations = $this->missedRevenueService->getData(
            $industry,
            Carbon::now()->subDays($daysAgo)->startOfDay(),
            Carbon::now()->startOfDay(),
        );

        return $this->formatResponse([
            'locations' => $locations
        ]);
    }
}
