<?php

namespace App\Http\Controllers\API\OutreachCadence;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Models\Cadence\CadenceEmailHeaderFooter;
use App\Models\Cadence\CadenceEmailTemplate;
use App\Models\Cadence\CadenceSmsTemplate;
use App\Models\Cadence\CadenceTaskTemplate;
use App\Models\User;
use App\Repositories\OutreachCadence\CadenceTemplateRepository;
use App\Services\EmailTemplates\EmailTemplateImageService;
use App\Services\OutreachCadence\TemplateResolutionService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ReflectionException;

class CadenceTemplateController extends APIController
{

    const IMAGE_UPLOAD_BASE_PATH = 'cadence_email_template_images';
    const REQUEST_IMAGE_NAME     = 'image_name';
    const REQUEST_IMAGE_DATA_URL = 'image_data_url';
    const REQUEST_TEMPLATE_TYPE  = 'template_type';
    const REQUEST_TEMPLATE_ID    = 'template_id';
    const REQUEST_SUBJECT        = 'subject';
    const REQUEST_BODY           = 'body';

    public function __construct(
        protected Request                   $request,
        protected JsonAPIResponseFactory    $apiResponseFactory,
        protected CadenceTemplateRepository $templateRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @return JsonResponse
     * @throws Exception
     */
    public function cloneEmailTemplate(): JsonResponse
    {
        $validated  = $this->request->validate([self::REQUEST_TEMPLATE_ID => 'required|numeric']);
        $templateId = $validated[self::REQUEST_TEMPLATE_ID];
        /** @var User $user */
        $user = Auth::user();
        $this->templateRepository->cloneEmailTemplate($templateId, $user);
        return $this->getEmailTemplates();
    }

    /**
     * @return JsonResponse
     */
    public function cloneSmsTemplate(): JsonResponse
    {
        $validated  = $this->request->validate([self::REQUEST_TEMPLATE_ID => 'required|numeric']);
        $templateId = $validated[self::REQUEST_TEMPLATE_ID];
        /** @var User $user */
        $user = Auth::user();
        $this->templateRepository->cloneSmsTemplate($templateId, $user);
        return $this->getSmsTemplates();
    }

    /**
     * @return JsonResponse
     */
    public function cloneTaskTemplate(): JsonResponse
    {
        $validated  = $this->request->validate([self::REQUEST_TEMPLATE_ID => 'required|numeric']);
        $templateId = $validated[self::REQUEST_TEMPLATE_ID];
        /** @var User $user */
        $user = Auth::user();
        $this->templateRepository->cloneTaskTemplate($templateId, $user);
        return $this->getTaskTemplates();
    }

    /**
     * @param TemplateResolutionService $templateService
     * @return JsonResponse
     * @throws ReflectionException
     */
    public function getEmailPreview(TemplateResolutionService $templateService): JsonResponse
    {
        $validated = $this->request->validate([
            self::REQUEST_SUBJECT     => 'string',
            self::REQUEST_BODY        => 'string',
            self::REQUEST_TEMPLATE_ID => 'numeric'
        ]);

        /** @var User $user */
        $user = Auth::user();
        return $this->formatResponse($templateService->getEmailPreview(
            $user,
            $validated[self::REQUEST_TEMPLATE_ID],
            $validated[self::REQUEST_SUBJECT],
            $validated[self::REQUEST_BODY]
        ));
    }

    /**
     * @param EmailTemplateImageService $service
     * @return JsonResponse
     * @throws Exception
     */
    public function saveImage(EmailTemplateImageService $service): JsonResponse
    {
        $validated = $this->request->validate([
            self::REQUEST_IMAGE_NAME     => ['required', 'string', 'regex:/^' . EmailTemplateImageService::IMAGE_NAME_REGEX . "$/"],
            self::REQUEST_IMAGE_DATA_URL => ['required', 'string', 'regex:' . EmailTemplateImageService::IMAGE_DATA_URL_REGEX],
            self::REQUEST_TEMPLATE_TYPE  => 'required|string',
            self::REQUEST_TEMPLATE_ID    => 'numeric|nullable',
        ]);

        $storageObject = $service->uploadImageDataUrlToCloudStorage(
            ($validated[self::REQUEST_TEMPLATE_ID] ?? Auth::user()->id),
            $validated[self::REQUEST_IMAGE_NAME],
            $validated[self::REQUEST_IMAGE_DATA_URL],
            self::IMAGE_UPLOAD_BASE_PATH . '/' . $validated[self::REQUEST_TEMPLATE_TYPE]
        );

        return $this->formatResponse([
            "status"   => $storageObject->exists(),
            "filename" => $validated[self::REQUEST_IMAGE_NAME]
        ]);
    }

    /**
     * @throws Exception
     */
    public function getUserHeaderFooter(): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        return $this->formatResponse($this->templateRepository->getUserHeaderAndFooter($user->id));
    }

    /**
     * @return JsonResponse
     */
    public function updateHeader(): JsonResponse
    {
        $validated = $this->request->validate([
            CadenceEmailHeaderFooter::FIELD_CONTENT => 'string|nullable',
        ]);
        /** @var User $user */
        $user = Auth::user();
        $this->templateRepository->updateUserHeader($user->id, $validated[CadenceEmailHeaderFooter::FIELD_CONTENT] ?? '');
        return $this->formatResponse();
    }

    /**
     * @return JsonResponse
     */
    public function updateFooter(): JsonResponse
    {
        $validated = $this->request->validate([
            CadenceEmailHeaderFooter::FIELD_CONTENT => 'string|nullable',
        ]);
        /** @var User $user */
        $user = Auth::user();
        $this->templateRepository->updateUserFooter($user->id, $validated[CadenceEmailHeaderFooter::FIELD_CONTENT] ?? '');
        return $this->formatResponse();
    }

    /**
     * @return JsonResponse
     * @throws Exception
     */
    public function getAllTemplates(): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        return $this->formatResponse($this->templateRepository->getAllTemplatesArray($user, true, true));
    }

    /**
     * @return JsonResponse
     * @throws Exception
     */
    public function getEmailTemplates(): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        return $this->formatResponse($this->templateRepository->getEmailTemplates($user)->toArray());
    }

    /**
     * @return JsonResponse
     */
    public function getSmsTemplates(): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        return $this->formatResponse($this->templateRepository->getSmsTemplates($user)->toArray());
    }

    /**
     * @return JsonResponse
     */
    public function getTaskTemplates(): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();
        return $this->formatResponse($this->templateRepository->getTaskTemplates($user)->toArray());
    }


    /**
     * @return JsonResponse
     */
    public function createEmailTemplate(): JsonResponse
    {
        $validated = $this->request->validate([
            CadenceEmailTemplate::FIELD_NAME    => 'required|string',
            CadenceEmailTemplate::FIELD_SUBJECT => 'required|string',
            CadenceEmailTemplate::FIELD_BODY    => 'required|string',
            CadenceEmailTemplate::FIELD_GLOBAL  => 'boolean',
        ]);
        /** @var User $user */
        $user = Auth::user();
        $this->templateRepository->createEmailTemplate($validated, $user);
        return $this->getEmailTemplates();
    }

    /**
     * @return JsonResponse
     */
    public function createSmsTemplate(): JsonResponse
    {
        $validated = $this->request->validate([
            CadenceSmsTemplate::FIELD_NAME   => 'required|string',
            CadenceSmsTemplate::FIELD_BODY   => 'required|string',
            CadenceSmsTemplate::FIELD_GLOBAL => 'boolean',
        ]);
        /** @var User $user */
        $user = Auth::user();
        $this->templateRepository->createSmsTemplate($validated, $user);
        return $this->getSmsTemplates();
    }

    /**
     * @return JsonResponse
     */
    public function createTaskTemplate(): JsonResponse
    {
        $validated = $this->request->validate([
            CadenceTaskTemplate::FIELD_NAME      => 'required|string',
            CadenceTaskTemplate::FIELD_NOTES     => 'required|string',
            CadenceTaskTemplate::FIELD_GLOBAL    => 'boolean',
            CadenceTaskTemplate::FIELD_TASK_NAME => 'required|string'
        ]);
        /** @var User $user */
        $user = Auth::user();
        $this->templateRepository->createTaskTemplate($validated, $user);
        return $this->getTaskTemplates();
    }

    /**
     * @param int $templateId
     * @return JsonResponse
     * @throws Exception
     */
    public function updateEmailTemplate(int $templateId): JsonResponse
    {
        $validated = $this->request->validate([
            CadenceEmailTemplate::FIELD_NAME    => 'string',
            CadenceEmailTemplate::FIELD_SUBJECT => 'string',
            CadenceEmailTemplate::FIELD_BODY    => 'string',
            CadenceEmailTemplate::FIELD_GLOBAL  => 'boolean',
        ]);
        $this->templateRepository->updateEmailTemplate($templateId, $validated);
        return $this->getEmailTemplates();
    }

    /**
     * @param int $templateId
     * @return JsonResponse
     */
    public function updateSmsTemplate(int $templateId): JsonResponse
    {
        $validated = $this->request->validate([
            CadenceSmsTemplate::FIELD_NAME   => 'string',
            CadenceSmsTemplate::FIELD_BODY   => 'string',
            CadenceSmsTemplate::FIELD_GLOBAL => 'boolean',
        ]);
        $this->templateRepository->updateSmsTemplate($templateId, $validated);
        return $this->getSmsTemplates();
    }

    /**
     * @param int $templateId
     * @return JsonResponse
     */
    public function updateTaskTemplate(int $templateId): JsonResponse
    {
        $validated = $this->request->validate([
            CadenceTaskTemplate::FIELD_NAME      => 'string',
            CadenceTaskTemplate::FIELD_NOTES     => 'string',
            CadenceTaskTemplate::FIELD_GLOBAL    => 'boolean',
            CadenceTaskTemplate::FIELD_TASK_NAME => 'string'
        ]);
        $this->templateRepository->updateTaskTemplate($templateId, $validated);
        return $this->getTaskTemplates();
    }


    /**
     * @param int $templateId
     * @return JsonResponse
     * @throws Exception
     */
    public function deleteEmailTemplate(int $templateId): JsonResponse
    {
        $this->templateRepository->deleteEmailTemplate($templateId);
        return $this->getEmailTemplates();
    }

    /**
     * @param int $templateId
     * @return JsonResponse
     */
    public function deleteSmsTemplate(int $templateId): JsonResponse
    {
        $this->templateRepository->deleteSmsTemplate($templateId);
        return $this->getSmsTemplates();
    }

    /**
     * @param int $templateId
     * @return JsonResponse
     */
    public function deleteTaskTemplate(int $templateId): JsonResponse
    {
        $this->templateRepository->deleteTaskTemplate($templateId);
        return $this->getTaskTemplates();
    }

}
