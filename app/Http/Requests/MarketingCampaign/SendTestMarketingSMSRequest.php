<?php

namespace App\Http\Requests\MarketingCampaign;

use App\Enums\PermissionType;
use App\Models\EmailTemplate;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class SendTestMarketingSMSRequest extends FormRequest
{
    const string TO_PHONE = 'to_phone';
    const string MESSAGE  = 'message';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::MARKETING_CAMPAIGNS_CREATE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::TO_PHONE => ['required'],
            self::MESSAGE  => ['required'],
        ];
    }
}

