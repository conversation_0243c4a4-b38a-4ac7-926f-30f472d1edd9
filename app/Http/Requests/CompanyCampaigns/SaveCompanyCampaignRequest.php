<?php

namespace App\Http\Requests\CompanyCampaigns;

use App\Campaigns\Modules\Bidding\ProductBiddingModule;
use App\Campaigns\Modules\Budget\BaseBudgetContainerModule;
use App\Campaigns\Modules\DeliveryModule;
use App\Campaigns\Modules\LocationModule;
use App\Enums\Campaigns\CampaignStatus;
use App\Http\Controllers\DashboardAPI\V4\CompanyCampaignController;
use App\Models\Campaigns\CompanyCampaign;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class SaveCompanyCampaignRequest extends FormRequest
{
    const REQUEST_PROPERTY_TYPES = 'property_types';
    const REQUEST_PRODUCT        = 'product';
    const REQUEST_SERVICE        = 'service';
    const REQUEST_INDUSTRY       = 'industry';

    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $isUpdate = $this->isMethod(Request::METHOD_PATCH);

        return [
            CompanyCampaign::FIELD_STATUS              => ['required', new Enum(CampaignStatus::class)],
            CompanyCampaign::FIELD_NAME                => ['required', 'string'],
            CompanyCampaign::FIELD_REFERENCE           => [Rule::requiredIf(fn() => $isUpdate)],
            CompanyCampaign::FIELD_ZIP_CODE_TARGETED   => ['boolean', 'nullable', Rule::excludeIf(fn() => $isUpdate)],
            CompanyCampaign::FIELD_TYPE                => ['numeric', 'nullable'],
            self::REQUEST_PRODUCT                      => ['string', Rule::requiredIf(fn() => !$isUpdate), Rule::excludeIf(fn() => $isUpdate)],
            self::REQUEST_SERVICE                      => ['string', Rule::requiredIf(fn() => !$isUpdate), Rule::excludeIf(fn() => $isUpdate)],
            self::REQUEST_INDUSTRY                     => ['string', 'nullable'],
            self::REQUEST_PROPERTY_TYPES               => ['array'],
            CompanyCampaignController::REQUEST_PAYLOAD => ['required', 'array'],
            ...$this->getPayloadRules(),
        ];
    }

    /**
     * @return array
     */
    protected function getPayloadRules(): array
    {
        $rules = collect([
            LocationModule::PAYLOAD_PARENT_KEY            => ['required', 'array'],
            BaseBudgetContainerModule::PAYLOAD_PARENT_KEY => ['required', 'array'],
            ProductBiddingModule::PAYLOAD_PARENT_KEY      => ['required', 'array'],
            DeliveryModule::PAYLOAD_PARENT_KEY            => ['required', 'array'],
        ]);

        return $this->isMethod(Request::METHOD_POST)
            ? $rules->reduce(fn($output, $value, $key) =>
                [...$output, CompanyCampaignController::REQUEST_PAYLOAD .'.'. $key => $value], [])
            : [];
    }

    /**
     * @return array
     */
    public function messages(): array
    {
        $customMessages = collect([
            LocationModule::PAYLOAD_PARENT_KEY,
            BaseBudgetContainerModule::PAYLOAD_PARENT_KEY,
            ProductBiddingModule::PAYLOAD_PARENT_KEY,
            DeliveryModule::PAYLOAD_PARENT_KEY,
        ]);

        return $customMessages->reduce(fn($output, $value) =>
            [...$output, CompanyCampaignController::REQUEST_PAYLOAD .'.'. $value => "The " . ucwords($value) . " slide must be completed."], []);
    }
}
