<?php

namespace App\Http\Requests;

use App\Http\Controllers\API\BundleManagement\BundleSearchController;
use App\Models\Bundle;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SearchBundlesRequest extends FormRequest
{
    const REQUEST_NAME = Bundle::FIELD_NAME;
    const REQUEST_TITLE = Bundle::FIELD_TITLE;
    const REQUEST_DESCRIPTION = Bundle::FIELD_DESCRIPTION;
    const REQUEST_INDUSTRY_ID = Bundle::FIELD_INDUSTRY_ID;
    const REQUEST_COST_FROM = 'cost_from';
    const REQUEST_COST_TO = 'cost_to';
    const REQUEST_CREDIT_FROM = 'credit_from';
    const REQUEST_CREDIT_TO = 'credit_to';
    const REQUEST_ACTIVE = 'active';
    const REQUEST_PER_PAGE = 'per_page';
    const REQUEST_PAGE = 'page';
    const REQUEST_SORT_COL = 'sort_col';
    const REQUEST_SORT_DIR = 'sort_dir';
    const SORTABLE_COLUMNS = [
        self::REQUEST_NAME,
        Bundle::FIELD_COST,
        Bundle::FIELD_CREDIT,
        Bundle::FIELD_ACTIVATED_AT,
        self::REQUEST_INDUSTRY_ID
    ];

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->can(['view-bundles']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::REQUEST_NAME => ['present', 'string', 'nullable'],
            self::REQUEST_COST_FROM => ['sometimes', 'numeric', 'nullable'],
            self::REQUEST_COST_TO => ['sometimes', 'numeric', 'nullable'],
            self::REQUEST_CREDIT_FROM => ['sometimes', 'numeric', 'nullable'],
            self::REQUEST_CREDIT_TO => ['sometimes', 'numeric', 'nullable'],
            self::REQUEST_ACTIVE => ['sometimes', 'boolean', 'nullable'],
            self::REQUEST_INDUSTRY_ID => ['sometimes', 'numeric', 'nullable'],
            self::REQUEST_PER_PAGE => ['present', 'integer', Rule::in(25, 50, 100)],
            self::REQUEST_PAGE => ['present', 'integer'],
            self::REQUEST_SORT_COL => ['present', 'string', Rule::in(self::SORTABLE_COLUMNS)],
            self::REQUEST_SORT_DIR => ['present', 'string', Rule::in(["asc", "desc"])],
        ];
    }
}
