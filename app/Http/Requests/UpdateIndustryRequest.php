<?php

namespace App\Http\Requests;

use App\Models\Odin\Industry;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateIndustryRequest extends FormRequest
{
    const PERMISSION_NAME                  = 'industry-management';

    const REQUEST_CONFIGURATION            = 'configuration';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(self::PERMISSION_NAME);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            Industry::FIELD_NAME             => ["required", "string", "max:255"],
            Industry::FIELD_COLOR_DARK       => ["nullable", "string", "max:20"],
            Industry::FIELD_COLOR_LIGHT      => ["nullable", "string", "max:20"],
            self::REQUEST_CONFIGURATION      => ['array', 'nullable'],
        ];
    }
}
