<?php

namespace App\Http\Requests;

use App\Http\Controllers\API\CompaniesController;
use Illuminate\Foundation\Http\FormRequest;

class GetCompanyCampaignsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            CompaniesController::REQUEST_STATUS => ["integer", "nullable"],
            CompaniesController::REQUEST_PAGE => ["integer", "nullable"],
            CompaniesController::REQUEST_PER_PAGE => ["integer", 'nullable'],
        ];
    }

}
