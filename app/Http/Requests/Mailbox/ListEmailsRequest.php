<?php

namespace App\Http\Requests\Mailbox;

use App\Enums\PermissionType;
use App\Models\User;
use App\Rules\MailboxTabs;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ListEmailsRequest extends FormRequest
{
    const FIELD_TAB        = 'tab';
    const FIELD_QUERY      = 'query';
    const FIELD_UUID       = 'uuid';
    const FIELD_COMPANY_ID = 'company_id';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::MAILBOX_LIST_EMAILS->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_TAB          => ['sometimes' , new MailboxTabs()],
            self::FIELD_QUERY        => 'sometimes',
            self::FIELD_UUID         => 'sometimes|uuid',
            self::FIELD_COMPANY_ID   => 'sometimes|integer',
        ];
    }
}

