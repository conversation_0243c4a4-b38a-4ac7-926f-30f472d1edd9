<?php

namespace App\Http\Requests\Dashboard;

use App\Enums\Odin\QualityTier;
use App\Models\Odin\ProductCampaignBudget;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreCampaignRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'id'                                 => ['uuid', 'nullable', Rule::requiredIf(request()->isMethod('patch'))],
            'name'                               => ['string', 'max:64', 'required'],
            'status'                             => ['boolean', 'nullable'],
            'daily_limit_type'                   => ['string', 'nullable'],
            'max_daily_spend'                    => [Rule::requiredIf(fn() => request()->get('daily_limit_type') === 'spend'), 'nullable', 'numeric'],
            'max_daily_leads'                    => [Rule::requiredIf(fn() => request()->get('daily_limit_type') === 'leads'), 'nullable', 'numeric'],
            'property_types'                     => ['array', 'required'],
            'contact_deliveries'                 => ['array', Rule::requiredIf(fn() => !request()->get('crm_deliveries')), 'nullable'],
            'crm_deliveries'                     => ['array', Rule::requiredIf(fn() => !request()->get('contact_deliveries')), 'nullable'],
            'optional_leads_types'               => ['array', 'nullable'],
            'zip_codes'                          => ['array', 'required'],
            'allow_non_budget_premium_leads'     => ['boolean', 'nullable'],
            'schedules'                          => ['array'],
            'appointment_budgets'                => ['nullable', 'array'],
            'appointment_budgets.*.quality_tier' => ['required', 'in:' . implode(',', [QualityTier::IN_HOME->value, QualityTier::ONLINE->value])],
            'appointment_budgets.*.budget_type'  => ['required', 'in:' . implode(',', [ProductCampaignBudget::VALUE_TYPE_NO_LIMIT, ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_APPOINTMENTS, ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_SPEND])],
            'appointment_budgets.*.budget_value' => ['required', 'numeric'],
            'appointment_budgets.*.category'     => ['required', 'string'],
            'appointment_budgets.*.status'       => ['required', 'boolean'],
        ];
    }
}
