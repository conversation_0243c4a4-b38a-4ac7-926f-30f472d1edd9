<?php

namespace App\Http\Requests\Dashboard;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreCrmDeliveryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name'                              => 'required|string|max:128',
            'lead_campaign_id'                  => 'uuid|nullable',
            'crm'                               => 'required|array',
            'crm.id'                            => 'required|numeric',
            'enabled'                           => 'required|boolean',
            'integration_fields'                => 'required|array',
            'integration_fields.fields'         => 'required|array',
            'integration_fields.system_fields'  => 'required|array',
            'integration_fields.custom_fields'  => 'nullable|array',
        ];
    }
}
