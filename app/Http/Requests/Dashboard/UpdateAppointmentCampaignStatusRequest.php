<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Controllers\API\ClientDashboard\CampaignController;
use App\Models\Legacy\EloquentUser;
use Illuminate\Foundation\Http\FormRequest;

class UpdateAppointmentCampaignStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return app(EloquentUser::class)->active();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            CampaignController::REQUEST_STATUS => ['required', 'boolean'],
            CampaignController::REQUEST_REACTIVATE_AT_TIMESTAMP => ['present', 'integer', 'min:0']
        ];
    }
}
