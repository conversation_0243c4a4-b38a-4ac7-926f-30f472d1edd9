<?php

namespace App\Http\Requests\Dashboard;

use Illuminate\Foundation\Http\FormRequest;

class SearchInvoicesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return void
     */
    public function prepareForValidation(): void
    {
        $this->merge([
            'date_start'    => $this->date_start ?  $this->date_start/1000 : null,
            'date_end'      => $this->date_end ?  $this->date_end/1000 : null,
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'status'        => 'string|nullable',
            'date_start'    => 'numeric|nullable',
            'date_end'      => 'numeric|nullable',
            'invoice_id'    => 'numeric|nullable',
        ];
    }
}
