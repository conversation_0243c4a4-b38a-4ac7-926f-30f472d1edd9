<?php

namespace App\Http\Requests\Dashboard;

use App\Enums\Odin\BudgetCategory;
use App\Enums\Odin\QualityTier;
use App\Http\Controllers\API\ClientDashboard\CampaignController;
use App\Models\Legacy\EloquentUser;
use App\Models\Odin\ProductCampaignBudget;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class UpdateAppointmentsCampaignBudgetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return app(EloquentUser::class)->active();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            CampaignController::REQUEST_BUDGETS => ['array:'.implode(',', [QualityTier::ONLINE->value, QualityTier::IN_HOME->value])],
            CampaignController::REQUEST_BUDGETS.'.*.'.CampaignController::BUDGETS_CATEGORY => ['required', new Enum(BudgetCategory::class)],
            CampaignController::REQUEST_BUDGETS.'.*.'.CampaignController::BUDGETS_STATUS => ['required', 'boolean'],
            CampaignController::REQUEST_BUDGETS.'.*.'.CampaignController::BUDGETS_BUDGET_TYPE => [
                'required',
                Rule::in([
                    ProductCampaignBudget::VALUE_TYPE_NO_LIMIT,
                    ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_APPOINTMENTS,
                    ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_SPEND
                ])
            ],
            CampaignController::REQUEST_BUDGETS.'.*.'.CampaignController::BUDGETS_BUDGET_VALUE => ['required', 'numeric', 'gte:0'],
            CampaignController::REQUEST_BUDGETS.'.*.'.CampaignController::BUDGETS_TIER => ['required', new Enum(QualityTier::class)],
        ];
    }
}
