<?php

namespace App\Http\Requests\Rulesets;

use App\Enums\PermissionType;
use App\Enums\RulesetType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class GetAllRulesetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::RULESET_MANAGEMENT->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'type'    => "string|" . Rule::in(array_column(RulesetType::cases(), 'value'))
        ];
    }
}
