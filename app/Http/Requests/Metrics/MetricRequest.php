<?php

namespace App\Http\Requests\Metrics;

use App\Enums\Metrics\MetricRange;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class MetricRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'range' => [
                'sometimes',
                'required',
                Rule::enum(MetricRange::class),
            ],
        ];
    }
}
