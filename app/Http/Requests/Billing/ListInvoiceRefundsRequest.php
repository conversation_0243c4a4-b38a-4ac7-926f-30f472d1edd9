<?php

namespace App\Http\Requests\Billing;

use App\Enums\InvoiceRefundStatus;
use App\Enums\PermissionType;
use App\Models\Billing\Invoice;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ListInvoiceRefundsRequest extends FormRequest
{
    const string FIELD_PAGE          = 'page';
    const string FIELD_PER_PAGE      = 'per_page';
    const string FIELD_INVOICE_ID    = 'invoice_id';
    const string FIELD_MINIMUM       = 'minimum';
    const string FIELD_MAXIMUM       = 'maximum';
    const string FIELD_REFUND_STATUS = 'refund_status';


    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_VIEW_INVOICE->value);
    }

    public function prepareForValidation(): void
    {
        if ($this->amount) {
            $amount = json_decode($this->amount, true);

            $array = [];

            if (isset($amount['min'])) {
                $array[self::FIELD_MINIMUM] = $amount['min'];
            }
            if (isset($amount['max'])) {
                $array[self::FIELD_MAXIMUM] = $amount['max'];
            }

            $this->merge($array);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        $paginationRules = [
            self::FIELD_PAGE     => 'nullable|numeric|min:1',
            self::FIELD_PER_PAGE => 'nullable|numeric|min:1|max:100',
        ];

        return [
            ...$paginationRules,
            self::FIELD_INVOICE_ID           => ['sometimes', 'exists:' . Invoice::TABLE . ',' . Invoice::FIELD_ID],
            self::FIELD_MINIMUM              => ['sometimes', 'numeric'],
            self::FIELD_MAXIMUM              => ['sometimes', 'numeric'],
            self::FIELD_REFUND_STATUS        => ['sometimes', 'array'],
            self::FIELD_REFUND_STATUS . '.*' => [Rule::enum(InvoiceRefundStatus::class)],
        ];
    }
}
