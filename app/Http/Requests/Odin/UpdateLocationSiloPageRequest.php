<?php

namespace App\Http\Requests\Odin;

use App\Http\Controllers\SiloManagementController;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateLocationSiloPageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(SiloManagementController::PERMISSION_UPDATE);
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'id'                => [Rule::requiredIf(fn() => !$this->get('silo_id') && !$this->get('location_id'))],
            'location_id'       => [Rule::requiredIf(fn() => !$this->get('id'))],
            'silo_id'           => [Rule::requiredIf(fn() => !$this->get('id'))],
            'entry_slug'        => 'string|nullable',
            'relative_path'     => 'string|nullable',
            'is_active'         => 'boolean|nullable',
        ];
    }
}
