<?php

namespace App\Http\Requests\Odin;

use App\Enums\Odin\ConsumerFieldType;
use App\Enums\Odin\SystemModule;
use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SaveConsumerFieldVisibilityRequest extends FormRequest
{
    const FIELD_FIELDS = 'fields';

    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->can(PermissionType::INDUSTRY_MANAGEMENT->value);
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_FIELDS                                          => ['required', 'array'],
            self::FIELD_FIELDS . '.*.' . 'consumer_field_category'      => ['required', Rule::in(ConsumerFieldType::getValues())],
            self::FIELD_FIELDS . '.*.' . 'consumer_field_category_id'   => ['required'],
            self::FIELD_FIELDS . '.*.' . 'consumer_field_id'            => ['sometimes'],
            self::FIELD_FIELDS . '.*.' . 'consumer_field_type'          => ['required', 'string'],
            self::FIELD_FIELDS . '.*.' . 'module_type'                  => ['required', Rule::in(SystemModule::getValues())],
            self::FIELD_FIELDS . '.*.' . 'feature_type'                 => ['required', 'string'],
            self::FIELD_FIELDS . '.*.' . 'is_visible'                   => ['required', 'boolean'],

            self::FIELD_FIELDS . '.*.' . 'field'                        => ['required', 'array'],
            self::FIELD_FIELDS . '.*.' . 'field' . '.' . 'id'           => ['required', 'integer'],
            self::FIELD_FIELDS . '.*.' . 'field' . '.' . 'name'         => ['required', 'string'],
        ];
    }
}
