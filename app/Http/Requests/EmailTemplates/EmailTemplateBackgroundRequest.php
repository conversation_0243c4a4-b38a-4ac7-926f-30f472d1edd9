<?php

namespace App\Http\Requests\EmailTemplates;

use App\Enums\PermissionType;
use App\Models\EmailTemplateBackground;
use App\Models\Odin\Industry;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class EmailTemplateBackgroundRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->can(PermissionType::EMAIL_TEMPLATE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            EmailTemplateBackground::FIELD_NAME => ['string', 'required'],
            EmailTemplateBackground::FIELD_HEADER => ['string', 'present', 'nullable'],
            EmailTemplateBackground::FIELD_FOOTER => ['string', 'present', 'nullable'],
            EmailTemplateBackground::FIELD_PERSONAL => ['boolean', 'required'],
            EmailTemplateBackground::FIELD_INDUSTRY_ID => [
                'integer',
                'nullable',
                Rule::exists(Industry::TABLE, Industry::FIELD_ID),
            ],
        ];
    }
}
