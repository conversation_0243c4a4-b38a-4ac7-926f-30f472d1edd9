<?php

namespace App\Projectors;

use App\Enums\Billing\Disputes\InvoiceDisputeStatus;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\InvoiceTransactionScenario;
use App\Enums\Billing\InvoiceStates;
use App\Enums\Billing\InvoiceTransactionType;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeDisputeClosed;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeDisputeCreated;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceDispute;
use App\Models\Billing\InvoiceTransaction;
use App\Repositories\Billing\InvoiceDispute\InvoiceDisputeRepository;
use App\Repositories\Billing\InvoiceTransactionRepository;
use App\Services\Billing\InvoiceService;
use App\Services\Billing\InvoiceTransactions\InvoiceTransactionService;
use Illuminate\Support\Str;
use Spatie\EventSourcing\EventHandlers\Projectors\Projector;

class InvoiceDisputeProjector extends Projector
{
    public function __construct(
        protected InvoiceTransactionRepository $invoiceTransactionRepository,
        protected InvoiceDisputeRepository $invoiceDisputeRepository,
        protected InvoiceTransactionService $invoiceTransactionService,
        protected InvoiceService $invoiceService
    )
    {
    }

    /**
     * @param InvoiceChargeDisputeCreated $event
     * @return void
     */
    public function onInvoiceChargeDisputeCreated(InvoiceChargeDisputeCreated $event): void
    {
        $invoiceChargeTransaction = $this->invoiceTransactionRepository->getInvoiceTransactionByExternalId($event->chargeId);

        /** @var Invoice $invoice */
        $invoice = $invoiceChargeTransaction->{InvoiceTransaction::RELATION_INVOICE};

        $this->invoiceDisputeRepository->updateOrCreate(
            invoiceId          : $invoice->{Invoice::FIELD_ID},
            externalId         : $event->externalTransactionId,
            transactionChargeId: $invoiceChargeTransaction->{InvoiceTransaction::FIELD_ID},
            externalChargeId   : $event->chargeId,
            reason             : $event->reason,
            status             : $event->status,
            amount             : $event->amount,
            currency           : $event->currency,
            source             : $event->source,
        );

        $this->invoiceService->updateInvoiceStatus(
            invoice   : $invoice,
            newStatus : InvoiceStates::CHARGEBACK->value,
            authorType: InvoiceEventAuthorTypes::SYSTEM->value
        );
    }

    /**
     * Should we also listen to updates?
     * @param InvoiceChargeDisputeClosed $event
     * @return void
     */
    public function onInvoiceChargeDisputeClosed(InvoiceChargeDisputeClosed $event): void
    {
        $invoiceChargeTransaction = $this->invoiceTransactionRepository->getInvoiceTransactionByExternalId($event->chargeId);

        /** @var Invoice $invoice */
        $invoice = $invoiceChargeTransaction->{InvoiceTransaction::RELATION_INVOICE};

        $dispute = $this->invoiceDisputeRepository->updateOrCreate(
            invoiceId          : $invoice->{Invoice::FIELD_ID},
            externalId         : $event->externalTransactionId,
            transactionChargeId: $invoiceChargeTransaction->{InvoiceTransaction::FIELD_ID},
            externalChargeId   : $event->chargeId,
            reason             : $event->reason,
            status             : $event->status,
            amount             : $event->amount,
            currency           : $event->currency,
            source             : $event->source,
        );

        if (in_array($dispute->{InvoiceDispute::FIELD_STATUS}, [
            InvoiceDisputeStatus::WON,
            InvoiceDisputeStatus::LOST,
        ])) {
            $scenario = InvoiceTransactionScenario::tryFrom($dispute->{InvoiceDispute::FIELD_STATUS}->value);

            $this->invoiceTransactionService->createInvoiceTransaction(
                uuid             : Str::uuid(),
                invoiceUuid      : $invoice->{Invoice::FIELD_UUID},
                externalReference: $event->externalTransactionId,
                amount           : $event->amount,
                currency         : $event->currency,
                type             : InvoiceTransactionType::DISPUTE->value,
                origin           : $event->source,
                payload          : [ // event->toArray() ?
                    "amount"   => $event->amount,
                    "currency" => $event->currency,
                    "source"   => $event->source,
                ],
                scenario         : $scenario->value
            );
        }
    }
}
