<?php

namespace App\DTO\Graphs;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class BaseGraph implements DTOContract
{
    const string FIELD_LABELS   = 'labels';
    const string FIELD_DATASETS = 'datasets';

    /**
     * @param string[]|null $labels
     * @param array|BaseGraphDataset[]|null $datasets
     */
    public function __construct(
        protected ?array $labels = [],
        protected ?array $datasets = [],
    )
    {
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_LABELS   => $this->labels,
            self::FIELD_DATASETS => $this->datasets
        ];
    }

    /**
     * @param array $array
     * @return self
     */
    public static function fromArray(array $array): self
    {
        return new self(
            Arr::get($array, self::FIELD_LABELS),
            Arr::get($array, self::FIELD_DATASETS)
        );
    }

    public function getLabels(): ?array
    {
        return $this->labels;
    }

    public function getDatasets(): ?array
    {
        return $this->datasets;
    }
}