<?php

namespace App\DTO\Billing\Credit;

use App\DTO\DTOContract;
use Carbon\Carbon;
use Illuminate\Support\Arr;

class ConcreteCompanyCredit implements DTOContract
{
    const string FIELD_ID                      = 'id';
    const string FIELD_COMPANY_ID              = 'company_id';
    const string FIELD_ATOMIC_INITIAL_AMOUNT   = 'atomic_initial_amount';
    const string FIELD_ATOMIC_REMAINING_AMOUNT = 'atomic_remaining_amount';
    const string FIELD_CREDIT_TYPE             = 'credit_type';
    const string FIELD_DATE_APPLIED            = 'date_applied';
    const string FIELD_SOURCE                  = 'source';
    const string FIELD_NOTES                   = 'notes';

    public function __construct(
        protected int $id,
        protected int $companyId,
        protected int $atomicInitialAmount,
        protected int $atomicRemainingAmount,
        protected string $creditType,
        protected Carbon $dateApplied,
        protected string $source,
        protected ?string $notes = null,
    )
    {

    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     * @return int
     */
    public function getAtomicInitialAmount(): int
    {
        return $this->atomicInitialAmount;
    }

    /**
     * @return int
     */
    public function getAtomicInitialAmountInDollars(): int
    {
        return $this->atomicInitialAmount / 100;
    }

    /**
     * @return int
     */
    public function getAtomicRemainingAmount(): int
    {
        return $this->atomicRemainingAmount;
    }

    /**
     * @return int
     */
    public function getRemainingAmountInDollars(): int
    {
        return $this->atomicRemainingAmount / 100;
    }

    /**
     * @param int $atomicAmount
     * @return void
     */
    public function decrementAtomicRemainingAmount(int $atomicAmount): void
    {
        $this->atomicRemainingAmount -= $atomicAmount;
    }

    /**
     * @return string
     */
    public function getCreditType(): string
    {
        return $this->creditType;
    }

    /**
     * @return Carbon
     */
    public function getDateApplied(): Carbon
    {
        return $this->dateApplied->copy();
    }

    /**
     * @return string
     */
    public function getSource(): string
    {
        return $this->source;
    }

    /**
     * @return string|null
     */
    public function getNotes(): ?string
    {
        return $this->notes;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_ID                      => $this->id,
            self::FIELD_COMPANY_ID              => $this->companyId,
            self::FIELD_ATOMIC_INITIAL_AMOUNT   => $this->atomicInitialAmount,
            self::FIELD_ATOMIC_REMAINING_AMOUNT => $this->atomicRemainingAmount,
            self::FIELD_CREDIT_TYPE             => $this->creditType,
            self::FIELD_DATE_APPLIED            => $this->dateApplied,
            self::FIELD_SOURCE                  => $this->source,
            self::FIELD_NOTES                   => $this->notes,
        ];
    }

    /**
     * @param array $array
     * @return ConcreteCompanyCredit
     */
    public static function fromArray(array $array): self
    {
        return new self(
            id                   : Arr::get($array, self::FIELD_ID),
            companyId            : Arr::get($array, self::FIELD_COMPANY_ID),
            atomicInitialAmount  : Arr::get($array, self::FIELD_ATOMIC_INITIAL_AMOUNT),
            atomicRemainingAmount: Arr::get($array, self::FIELD_ATOMIC_REMAINING_AMOUNT),
            creditType           : Arr::get($array, self::FIELD_CREDIT_TYPE),
            dateApplied          : Arr::get($array, self::FIELD_DATE_APPLIED),
            source               : Arr::get($array, self::FIELD_SOURCE),
            notes                : Arr::get($array, self::FIELD_NOTES),
        );
    }
}
