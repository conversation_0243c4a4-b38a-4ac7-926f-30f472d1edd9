<?php

namespace App\DTO\Mail;


use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class SendEmailParam implements DTOContract
{
    const FIELD_TO                      = 'to';
    const FIELD_FROM                    = 'from';
    const FIELD_SUBJECT                 = 'subject';
    const FIELD_CONTENT                 = 'content';
    const FIELD_BCC                     = 'bcc';
    const FIELD_CC                      = 'cc';
    const FIELD_THREAD_ID               = 'threadId';
    const FIELD_REFERENCES              = 'references';
    const FIELD_REPLY_TO_MESSAGE_ID     = 'replyToMessageId';

    public function __construct(
        protected ?array  $to = [],
        protected ?string $from = '',
        protected ?string $subject = '',
        protected ?string $content = '',
        protected ?array  $bcc = [],
        protected ?array  $cc = [],
        protected ?string $threadId = null,
        protected ?string $references = null,
        protected ?string $replyToMessageId = null,
    )
    {

    }

    /**
     * @return string|null
     */
    public function getFrom(): ?string
    {
        return $this->from;
    }

    /**
     * @param string|null $from
     */
    public function setFrom(?string $from): void
    {
        $this->from = $from;
    }

    /**
     * @return string|null
     */
    public function getSubject(): ?string
    {
        return $this->subject;
    }

    /**
     * @param string|null $subject
     */
    public function setSubject(?string $subject): void
    {
        $this->subject = $subject;
    }

    /**
     * @return string|null
     */
    public function getContent(): ?string
    {
        return $this->content;
    }

    /**
     * @param string|null $content
     */
    public function setContent(?string $content): void
    {
        $this->content = $content;
    }

    /**
     * @return array|null
     */
    public function getBcc(): ?array
    {
        return $this->bcc;
    }

    /**
     * @param array|null $bcc
     */
    public function setBcc(?array $bcc): void
    {
        $this->bcc = $bcc;
    }

    /**
     * @return array|null
     */
    public function getCc(): ?array
    {
        return $this->cc;
    }

    /**
     * @param array|null $cc
     */
    public function setCc(?array $cc): void
    {
        $this->cc = $cc;
    }

    /**
     * @return array|null
     */
    public function getTo(): ?array
    {
        return $this->to;
    }

    /**
     * @param array|null $to
     */
    public function setTo(?array $to): void
    {
        $this->to = $to;
    }

    public function toArray(): array
    {
        return [
            self::FIELD_TO          => $this->to,
            self::FIELD_FROM        => $this->from,
            self::FIELD_SUBJECT     => $this->subject,
            self::FIELD_CONTENT     => $this->content,
            self::FIELD_BCC         => $this->bcc,
            self::FIELD_CC          => $this->cc,
            self::FIELD_THREAD_ID   => $this->threadId,
            self::FIELD_REFERENCES  => $this->references,
            self::FIELD_REPLY_TO_MESSAGE_ID  => $this->replyToMessageId,
        ];
    }

    public static function fromArray(array $array): SendEmailParam
    {
        return new SendEmailParam(
            Arr::get($array, self::FIELD_TO),
            Arr::get($array, self::FIELD_FROM),
            Arr::get($array, self::FIELD_SUBJECT),
            Arr::get($array, self::FIELD_CONTENT),
            Arr::get($array, self::FIELD_BCC),
            Arr::get($array, self::FIELD_CC),
            Arr::get($array, self::FIELD_THREAD_ID),
            Arr::get($array, self::FIELD_REFERENCES),
            Arr::get($array, self::FIELD_REPLY_TO_MESSAGE_ID),
        );
    }

    /**
     * @return string|null
     */
    public function getReferences(): ?string
    {
        return $this->references;
    }

    /**
     * @param string|null $references
     */
    public function setReferences(?string $references): void
    {
        $this->references = $references;
    }

    /**
     * @return string|null
     */
    public function getThreadId(): ?string
    {
        return $this->threadId;
    }

    /**
     * @param string|null $threadId
     */
    public function setThreadId(?string $threadId): void
    {
        $this->threadId = $threadId;
    }

    /**
     * @return string|null
     */
    public function getReplyToMessageId(): ?string
    {
        return $this->replyToMessageId;
    }

    /**
     * @param string|null $replyToMessageId
     */
    public function setReplyToMessageId(?string $replyToMessageId): void
    {
        $this->replyToMessageId = $replyToMessageId;
    }
}
