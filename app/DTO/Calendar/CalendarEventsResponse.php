<?php

namespace App\DTO\Calendar;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class CalendarEventsResponse implements DTOContract
{
    const string FIELD_NEXT_SYNC_TOKEN = 'next_sync_token';
    const string FIELD_NEXT_PAGE_TOKEN = 'next_page_token';
    const string FIELD_TIME_ZONE       = 'time_zone';
    const string FIELD_SUMMARY         = 'summary';
    const string FIELD_EVENTS          = 'events';

    /**
     * @param string|null $nextSyncToken
     * @param string|null $nextPageToken
     * @param string|null $timeZone
     * @param string|null $summary
     * @param Collection $events
     */
    public function __construct(
        protected ?string $nextSyncToken = null,
        protected ?string $nextPageToken = null,
        protected ?string $timeZone = null,
        protected ?string $summary = null,
        protected Collection $events = new Collection()
    )
    {

    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_NEXT_SYNC_TOKEN => $this->nextSyncToken,
            self::FIELD_NEXT_PAGE_TOKEN => $this->nextPageToken,
            self::FIELD_TIME_ZONE       => $this->timeZone,
            self::FIELD_SUMMARY         => $this->summary,
            self::FIELD_EVENTS          => $this->events,
        ];
    }

    /**
     * @param array $array
     * @return DTOContract
     * @throws \Exception
     */
    public static function fromArray(array $array): DTOContract
    {
        return new self(
            nextSyncToken: Arr::get($array, self::FIELD_NEXT_SYNC_TOKEN),
            nextPageToken: Arr::get($array, self::FIELD_NEXT_PAGE_TOKEN),
            timeZone     : Arr::get($array, self::FIELD_TIME_ZONE),
            summary      : Arr::get($array, self::FIELD_SUMMARY),
            events       : collect(Arr::get($array, self::FIELD_EVENTS, []))->map(fn($item) => CalendarEventDTO::fromArray($item)),
        );
    }


    /**
     * @return string|null
     */
    public function getNextSyncToken(): ?string
    {
        return $this->nextSyncToken;
    }

    /**
     * @return string|null
     */
    public function getNextPageToken(): ?string
    {
        return $this->nextPageToken;
    }

    /**
     * @return string|null
     */
    public function getTimeZone(): ?string
    {
        return $this->timeZone;
    }

    /**
     * @return string|null
     */
    public function getSummary(): ?string
    {
        return $this->summary;
    }

    /**
     * @return Collection<CalendarEventDTO>
     */
    public function getEvents(): Collection
    {
        return $this->events;
    }
}
