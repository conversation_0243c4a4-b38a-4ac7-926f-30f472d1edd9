<?php

namespace App\DTO;

class SpyFuPPCPayloadResponse implements BasePPCPayloadResponse {
    const string FIELD_AVERAGE_ORGANIC_RANK     = 'averageOrganicRank';
    const string FIELD_MONTHLY_PAID_CLICKS      = 'monthlyPaidClicks';
    const string FIELD_AVERAGE_AD_RANK          = 'averageAdRank';
    const string FIELD_TOTAL_ORGANIC_RESULTS    = 'totalOrganicResults';
    const string FIELD_MONTHLY_ORGANIC_VALUE    = 'monthlyOrganicValue';
    const string FIELD_TOTAL_ADS_PURCHASED      = 'totalAdsPurchased';
    const string FIELD_MONTHLY_ORGANIC_CLICKS   = 'monthlyOrganicClicks';
    const string FIELD_STRENGTH                 = 'strength';
    const string FIELD_TOTAL_INVERSE_RANK       = 'totalInverseRank';

    protected float $averageOrganicRank;
    protected float $monthlyPaidClicks;
    protected float $averageAdRank;
    protected float $totalOrganicResults;
    protected float $monthlyOrganicValue;
    protected float $totalAdsPurchased;
    protected float $monthlyOrganicClicks;
    protected int $strength;
    protected int $totalInverseRank;

    /**
     * @param float $averageOrganicRank
     * @param float $monthlyPaidClicks
     * @param float $averageAdRank
     * @param float $totalOrganicResults
     * @param float $monthlyOrganicValue
     * @param float $totalAdsPurchased
     * @param float $monthlyOrganicClicks
     * @param int $strength
     * @param int $totalInverseRank
     */
    public function __construct(
        float $averageOrganicRank   = 0,
        float $monthlyPaidClicks    = 0,
        float $averageAdRank        = 0,
        float $totalOrganicResults  = 0,
        float $monthlyOrganicValue  = 0,
        float $totalAdsPurchased    = 0,
        float $monthlyOrganicClicks = 0,
        int $strength               = 0,
        int $totalInverseRank       = 0,
    )
    {
        $this->averageOrganicRank   = $averageOrganicRank;
        $this->monthlyPaidClicks    = $monthlyPaidClicks;
        $this->averageAdRank        = $averageAdRank;
        $this->totalOrganicResults  = $totalOrganicResults;
        $this->monthlyOrganicValue  = $monthlyOrganicValue;
        $this->totalAdsPurchased    = $totalAdsPurchased;
        $this->monthlyOrganicClicks = $monthlyOrganicClicks;
        $this->strength             = $strength;
        $this->totalInverseRank     = $totalInverseRank;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_AVERAGE_ORGANIC_RANK    => $this->averageOrganicRank,
            self::FIELD_MONTHLY_PAID_CLICKS     => $this->monthlyPaidClicks,
            self::FIELD_AVERAGE_AD_RANK         => $this->averageAdRank,
            self::FIELD_TOTAL_ORGANIC_RESULTS   => $this->totalOrganicResults,
            self::FIELD_MONTHLY_ORGANIC_VALUE   => $this->monthlyOrganicValue,
            self::FIELD_TOTAL_ADS_PURCHASED     => $this->totalAdsPurchased,
            self::FIELD_MONTHLY_ORGANIC_CLICKS  => $this->monthlyOrganicClicks,
            self::FIELD_STRENGTH                => $this->strength,
            self::FIELD_TOTAL_INVERSE_RANK      => $this->totalInverseRank
        ];
    }

}
