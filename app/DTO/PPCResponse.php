<?php

namespace App\DTO;

use App\Enums\CompanyMetrics\CompanyMetricSources;
use App\Models\CompanyMetric;
use Illuminate\Contracts\Support\Arrayable;

class PPCResponse implements Arrayable {

    const string FIELD_MONTH            = 'month';
    const string FIELD_YEAR             = 'year';
    const string FIELD_MONTHLY_SPEND    = 'monthlySpend';
    const string FIELD_TARGET_DOMAIN    = 'targetDomain';
    const string FIELD_REQUEST          = 'request';
    const string FIELD_PAYLOAD          = 'payload';

    /**
     * @param int|null $month
     * @param int|null $year
     * @param float|null $monthlySpend
     * @param string|null $targetDomain
     * @param string|null $request
     * @param BasePPCPayloadResponse|null $payload
     */
    public function __construct(
        protected ?int                    $month            = null,
        protected ?int                    $year             = null,
        protected ?float                  $monthlySpend     = null,
        protected ?string                 $targetDomain     = null,
        protected ?string                 $request          = null,
        protected ?BasePPCPayloadResponse $payload          = null,
    )
    {}

    /**
     * @param CompanyMetric $source
     * @return PPCResponse
     */
    public static function fromCompanyMetric(CompanyMetric $source) : PPCResponse
    {
        return new self(
            $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_MONTH],
            $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_YEAR],
            $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_MONTHLY_SPEND],
            $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_TARGET_DOMAIN],
            $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_REQUEST],
            self::getBasePPC($source)
        );
    }

    /**
     * @param CompanyMetric $source
     * @return ?BasePPCPayloadResponse
     */
    public static function getBasePPC(CompanyMetric $source) : ?BasePPCPayloadResponse
    {
        if ($source[CompanyMetric::FIELD_SOURCE] === CompanyMetricSources::SIMILAR_WEB->value) {
            // Create SimilarWeb PPC DTO
            return new SimilarWebPPCPayloadResponse(
                $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_PAYLOAD][SimilarWebPPCPayloadResponse::FIELD_GRANULARITY],
                $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_PAYLOAD][SimilarWebPPCPayloadResponse::FIELD_MAIN_DOMAIN_ONLY],
                $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_PAYLOAD][SimilarWebPPCPayloadResponse::FIELD_FORMAT],
                $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_PAYLOAD][SimilarWebPPCPayloadResponse::FIELD_START_DATE],
                $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_PAYLOAD][SimilarWebPPCPayloadResponse::FIELD_END_DATE],
                $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_PAYLOAD][SimilarWebPPCPayloadResponse::FIELD_COUNTRY],
                $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_PAYLOAD][SimilarWebPPCPayloadResponse::FIELD_STATUS],
                $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_PAYLOAD][SimilarWebPPCPayloadResponse::FIELD_LAST_UPDATED],
            );
        } else if ($source[CompanyMetric::FIELD_SOURCE] === CompanyMetricSources::SPY_FU->value) {
            // Create SpyFu PPC DTO
            return new SpyFuPPCPayloadResponse(
                $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_PAYLOAD][SpyFuPPCPayloadResponse::FIELD_AVERAGE_ORGANIC_RANK],
                $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_PAYLOAD][SpyFuPPCPayloadResponse::FIELD_MONTHLY_PAID_CLICKS],
                $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_PAYLOAD][SpyFuPPCPayloadResponse::FIELD_AVERAGE_AD_RANK],
                $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_PAYLOAD][SpyFuPPCPayloadResponse::FIELD_TOTAL_ORGANIC_RESULTS],
                $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_PAYLOAD][SpyFuPPCPayloadResponse::FIELD_MONTHLY_ORGANIC_VALUE],
                $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_PAYLOAD][SpyFuPPCPayloadResponse::FIELD_TOTAL_ADS_PURCHASED],
                $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_PAYLOAD][SpyFuPPCPayloadResponse::FIELD_MONTHLY_ORGANIC_CLICKS],
                $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_PAYLOAD][SpyFuPPCPayloadResponse::FIELD_STRENGTH],
                $source[CompanyMetric::FIELD_REQUEST_RESPONSE][self::FIELD_PAYLOAD][SpyFuPPCPayloadResponse::FIELD_TOTAL_INVERSE_RANK],
            );
        } else {
            return null;
        }
    }

    /**
     * @return string|null
     */
    public function getRequest(): ?string
    {
        return $this->request;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_MONTH           => $this->month,
            self::FIELD_YEAR            => $this->year,
            self::FIELD_MONTHLY_SPEND   => $this->monthlySpend,
            self::FIELD_TARGET_DOMAIN   => $this->targetDomain,
            self::FIELD_REQUEST         => $this->request,
            self::FIELD_PAYLOAD         => $this->payload->toArray(),
        ];
    }

    /**
     * @return string|null
     */
    public function getMonth(): ?string
    {
        return $this->month;
    }

    /**
     * @return string|null
     */
    public function getYear(): ?string
    {
        return $this->year;
    }

    /**
     * @return string|null
     */
    public function getMonthlySpend(): ?string
    {
        return $this->monthlySpend;
    }

    /**
     * @return string|null
     */
    public function getTargetDomain(): ?string
    {
        return $this->targetDomain;
    }

    /**
     * @return BasePPCPayloadResponse|null
     */
    public function getPayload(): ?BasePPCPayloadResponse
    {
        return $this->payload;
    }
}
