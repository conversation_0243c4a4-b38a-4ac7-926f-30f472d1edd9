#!/bin/bash
echo APP_NAME=${APP_NAME} | tee -a /app/.env && \
echo APP_ENV=${APP_ENV} | tee -a /app/.env && \
echo MIX_APP_ENV=${APP_ENV} | tee -a /app/.env && \
echo APP_KEY=${APP_KEY} | tee -a /app/.env && \
echo APP_DEBUG=${APP_DEBUG} | tee -a /app/.env && \
echo APP_URL=${APP_URL} | tee -a /app/.env && \
echo DB_CONNECTION=${DB_CONNECTION} | tee -a /app/.env && \
echo DB_HOST=${DB_HOST} | tee -a /app/.env && \
echo DB_PORT=${DB_PORT} | tee -a /app/.env && \
echo DB_DATABASE=${DB_DATABASE} | tee -a /app/.env && \
echo DB_USERNAME=${DB_USERNAME} | tee -a /app/.env && \
echo DB_PASSWORD=${DB_PASSWORD} | tee -a /app/.env && \
echo REPLICA_DB_CONNECTION=${REPLICA_DB_CONNECTION} | tee -a /app/.env && \
echo REPLICA_DB_HOST=${REPLICA_DB_HOST} | tee -a /app/.env && \
echo REPLICA_TWO_DB_HOST=${REPLICA_TWO_DB_HOST} | tee -a /app/.env && \
echo REPLICA_DB_PORT=${REPLICA_DB_PORT} | tee -a /app/.env && \
echo REPLICA_DB_DATABASE=${REPLICA_DB_DATABASE} | tee -a /app/.env && \
echo REPLICA_DB_USERNAME=${REPLICA_DB_USERNAME} | tee -a /app/.env && \
echo REPLICA_DB_PASSWORD=${REPLICA_DB_PASSWORD} | tee -a /app/.env && \
echo READONLY_DB_CONNECTION=${READONLY_DB_CONNECTION} | tee -a /app/.env && \
echo READONLY_DB_HOST=${READONLY_DB_HOST} | tee -a /app/.env && \
echo READONLY_DB_PORT=${READONLY_DB_PORT} | tee -a /app/.env && \
echo READONLY_DB_DATABASE=${READONLY_DB_DATABASE} | tee -a /app/.env && \
echo READONLY_DB_USERNAME=${READONLY_DB_USERNAME} | tee -a /app/.env && \
echo READONLY_DB_PASSWORD=${READONLY_DB_PASSWORD} | tee -a /app/.env && \
echo QUEUE_CONNECTION=${QUEUE_CONNECTION} | tee -a /app/.env && \
echo REDIS_CLIENT=${REDIS_CLIENT} | tee -a /app/.env && \
echo REDIS_HOST=${REDIS_HOST} | tee -a /app/.env && \
echo REDIS_QUEUE_HOST=${REDIS_QUEUE_HOST} | tee -a /app/.env && \
echo REDIS_QUEUE_PORT=${REDIS_QUEUE_PORT} | tee -a /app/.env && \
echo SESSION_DRIVER=${SESSION_DRIVER} | tee -a /app/.env && \
echo PROJECT_ID=${PROJECT_ID} | tee -a /app/.env && \
echo DEPLOYMENT_NAME=${DEPLOYMENT_NAME} | tee -a /app/.env && \
echo CLUSTER_NAME=${CLUSTER_NAME} | tee -a /app/.env && \
echo KEY_FILE=${KEY_FILE} | tee -a /app/.env && \
echo DEFAULT_ZONE=${DEFAULT_ZONE} | tee -a /app/.env && \
echo SENTRY_DSN=${SENTRY_DSN} | tee -a /app/.env && \
echo MIX_SENTRY_DSN=${SENTRY_DSN} | tee -a /app/.env && \
echo SENTRY_TRACE_QUEUE_ENABLED=${SENTRY_TRACE_QUEUE_ENABLED} | tee -a /app/.env && \
echo SENTRY_SEND_DEFAULT_PII=${SENTRY_SEND_DEFAULT_PII} | tee -a /app/.env && \
echo SENTRY_TRACES_SAMPLE_RATE=${SENTRY_TRACES_SAMPLE_RATE} | tee -a /app/.env && \
echo LEAD_VERIFICATION_DRIVER=${LEAD_VERIFICATION_DRIVER} | tee -a /app/.env && \
echo WHITEPAGES_IDENTITY_VERIFICATION_KEY=${WHITEPAGES_IDENTITY_VERIFICATION_KEY} | tee -a /app/.env && \
echo WHITEPAGES_IDENTITY_VERIFICATION_URL=${WHITEPAGES_IDENTITY_VERIFICATION_URL} | tee -a /app/.env && \
echo TWILIO_ML_SID=${TWILIO_ML_SID} | tee -a /app/.env && \
echo TWILIO_SID=${TWILIO_SID} | tee -a /app/.env && \
echo TWILIO_TOKEN=${TWILIO_TOKEN} | tee -a /app/.env && \
echo TWILIO_REVIEW_REQUESTS=${TWILIO_REVIEW_REQUESTS} | tee -a /app/.env && \
echo COMMUNICATION_DRIVER=${COMMUNICATION_DRIVER} | tee -a /app/.env && \
echo PUSHER_APP_ID=${PUSHER_APP_ID} | tee -a /app/.env && \
echo PUSHER_APP_KEY=${PUSHER_APP_KEY} | tee -a /app/.env && \
echo PUSHER_APP_SECRET=${PUSHER_APP_SECRET} | tee -a /app/.env && \
echo PUSHER_APP_CLUSTER=${PUSHER_APP_CLUSTER} | tee -a /app/.env && \
echo BROADCAST_DRIVER=${BROADCAST_DRIVER} | tee -a /app/.env && \
echo CACHE_DRIVER=${CACHE_DRIVER} | tee -a /app/.env && \
echo LEAD_PROCESSING_API_DRIVER=${LEAD_PROCESSING_API_DRIVER} | tee -a /app/.env && \
echo USER_MANAGEMENT_API_DRIVER=${USER_MANAGEMENT_API_DRIVER} | tee -a /app/.env && \
echo ADMIN_INTEGRATION_CLIENT_SECRET=${ADMIN_INTEGRATION_CLIENT_SECRET} | tee -a /app/.env && \
echo ADMIN_INTEGRATION_BASE_URL=${ADMIN_INTEGRATION_BASE_URL} | tee -a /app/.env && \
echo MAIL_MAILER=${MAIL_MAILER} | tee -a /app/.env && \
echo MAIL_HOST=${MAIL_HOST} | tee -a /app/.env && \
echo MAIL_PORT=${MAIL_PORT} | tee -a /app/.env && \
echo MAIL_USERNAME=${MAIL_USERNAME} | tee -a /app/.env && \
echo MAIL_PASSWORD=${MAIL_PASSWORD} | tee -a /app/.env && \
echo MAIL_USERNAME_SECONDARY=${MAIL_USERNAME_SECONDARY} | tee -a /app/.env && \
echo MAIL_PASSWORD_SECONDARY=${MAIL_PASSWORD_SECONDARY} | tee -a /app/.env && \
echo MAIL_USERNAME_TERTIARY=${MAIL_USERNAME_TERTIARY} | tee -a /app/.env && \
echo MAIL_PASSWORD_TERTIARY=${MAIL_PASSWORD_TERTIARY} | tee -a /app/.env && \
echo MAIL_FROM_ADDRESS=${MAIL_FROM_ADDRESS} | tee -a /app/.env && \
echo MAIL_FROM_NAME=${MAIL_FROM_NAME} | tee -a /app/.env && \
echo SOLAR_MAIL_FROM_NAME=${SOLAR_MAIL_FROM_NAME} | tee -a /app/.env && \
echo ROOFING_MAIL_FROM_NAME=${ROOFING_MAIL_FROM_NAME} | tee -a /app/.env && \
echo SOLAR_MAIL_FROM_ADDRESS=${SOLAR_MAIL_FROM_ADDRESS} | tee -a /app/.env && \
echo ROOFING_MAIL_FROM_ADDRESS=${ROOFING_MAIL_FROM_ADDRESS} | tee -a /app/.env && \
echo LEGACY_ADMIN_AUTH_TOKEN=${LEGACY_ADMIN_AUTH_TOKEN} | tee -a /app/.env && \
echo GOOGLE_STATIC_MAPS_API_KEY=${GOOGLE_STATIC_MAPS_API_KEY} | tee -a /app/.env && \
echo GOOGLE_MAPS_GEOCODING_API_KEY=${GOOGLE_MAPS_GEOCODING_API_KEY} | tee -a /app/.env && \
echo GOOGLE_CLOUD_KEY_FILE=${GOOGLE_CLOUD_KEY_FILE} | tee -a /app/.env && \
echo GOOGLE_STORAGE_INVOICES_BUCKET=${GOOGLE_STORAGE_INVOICES_BUCKET} | tee -a /app/.env && \
echo PAYMENT_GATEWAY_DRIVER=${PAYMENT_GATEWAY_DRIVER} | tee -a /app/.env && \
echo STRIPE_WEBHOOK_SIGNING_SECRET=${STRIPE_WEBHOOK_SIGNING_SECRET} | tee -a /app/.env && \
echo OUTGOING_COMMUNICATION_TEST_MODE=${OUTGOING_COMMUNICATION_TEST_MODE} | tee -a /app/.env && \
echo GOOGLE_LOGGING_PROJECT_ID=${GOOGLE_LOGGING_PROJECT_ID} | tee -a /app/.env && \
echo STACKDRIVER_ENABLED=${STACKDRIVER_ENABLED} | tee -a /app/.env && \
echo GOOGLE_PUBSUB_PROJECT_ID=${GOOGLE_PUBSUB_PROJECT_ID} | tee -a /app/.env && \
echo GOOGLE_PUBSUB_TOPIC=${GOOGLE_PUBSUB_TOPIC} | tee -a /app/.env && \
echo GOOGLE_STORAGE_PROJECT_ID=${GOOGLE_STORAGE_PROJECT_ID} | tee -a /app/.env && \
echo GOOGLE_STORAGE_EMAIL_TEMPLATE_IMAGES_BUCKET=${GOOGLE_STORAGE_EMAIL_TEMPLATE_IMAGES_BUCKET} | tee -a /app/.env && \
echo MIX_LEGACY_ADMIN_BASE_URL=${MIX_LEGACY_ADMIN_BASE_URL} | tee -a /app/.env && \
echo WWW_SOLARREVIEWS_DOMAIN=${WWW_SOLARREVIEWS_DOMAIN} | tee -a /app/.env && \
echo CLIENT_JWT_SIGNING_KEY=${CLIENT_JWT_SIGNING_KEY} | tee -a /app/.env && \
echo ADVERTISING_DRIVER=${ADVERTISING_DRIVER} | tee -a /app/.env && \
echo GOOGLE_ADS_DEVELOPER_TOKEN=${GOOGLE_ADS_DEVELOPER_TOKEN} | tee -a /app/.env && \
echo GOOGLE_ADS_CLIENT_ID=${GOOGLE_ADS_CLIENT_ID} | tee -a /app/.env && \
echo GOOGLE_ADS_CLIENT_SECRET=${GOOGLE_ADS_CLIENT_SECRET} | tee -a /app/.env && \
echo GOOGLE_ADS_REFRESH_TOKEN=${GOOGLE_ADS_REFRESH_TOKEN} | tee -a /app/.env && \
echo GOOGLE_ADS_GEOTARGETS_CSV_URL=${GOOGLE_ADS_GEOTARGETS_CSV_URL} | tee -a /app/.env && \
echo GOOGLE_MAPS_PLACES_API_URI=${GOOGLE_MAPS_PLACES_API_URI} | tee -a /app/.env && \
echo ADDRESS_IDENTIFICATION_DRIVER=${ADDRESS_IDENTIFICATION_DRIVER} | tee -a /app/.env && \
echo GOOGLE_ADS_IMPERSONATED_EMAIL=${GOOGLE_ADS_IMPERSONATED_EMAIL} | tee -a /app/.env && \
echo GOOGLE_ADS_AUTH_DRIVER=${GOOGLE_ADS_AUTH_DRIVER} | tee -a /app/.env && \
echo IP_QUALITY_SCORE_BASE_URL=${IP_QUALITY_SCORE_BASE_URL} | tee -a /app/.env && \
echo IP_QUALITY_SCORE_API_KEY=${IP_QUALITY_SCORE_API_KEY} | tee -a /app/.env && \
echo DATABASE_CONNECTION_RETRY_AFTER=${DATABASE_CONNECTION_RETRY_AFTER} | tee -a /app/.env && \
echo CONSUMER_PRODUCT_VERIFICATION_DRIVER=${CONSUMER_PRODUCT_VERIFICATION_DRIVER} | tee -a /app/.env && \
echo TINY_MCE_API_KEY=${TINY_MCE_API_KEY} | tee -a /app/.env && \
echo TIME_MCE_STORAGE_DISK=${TIME_MCE_STORAGE_DISK} | tee -a /app/.env && \
echo GOOGLE_STORAGE_TINYMCE_FILES_BUCKET=${GOOGLE_STORAGE_TINYMCE_FILES_BUCKET} | tee -a /app/.env && \
echo GOOGLE_STORAGE_TINYMCE_FILE_URL=${GOOGLE_STORAGE_TINYMCE_FILE_URL} | tee -a /app/.env && \
echo IP_QUALITY_SCORE_DRIVER=${IP_QUALITY_SCORE_DRIVER} | tee -a /app/.env && \
echo MICROSOFT_ADS_CLIENT_ID=${MICROSOFT_ADS_CLIENT_ID} | tee -a /app/.env && \
echo MICROSOFT_ADS_OAUTH_REDIRECT_URI=${MICROSOFT_ADS_OAUTH_REDIRECT_URI} | tee -a /app/.env && \
echo MICROSOFT_ADS_CUSTOMER_ID=${MICROSOFT_ADS_CUSTOMER_ID} | tee -a /app/.env && \
echo MICROSOFT_ADS_OFFLINE_CONVERSION_GOAL_NAME=${MICROSOFT_ADS_OFFLINE_CONVERSION_GOAL_NAME} | tee -a /app/.env && \
echo MICROSOFT_ADS_DEVELOPER_TOKEN=${MICROSOFT_ADS_DEVELOPER_TOKEN} | tee -a /app/.env && \
echo GOOGLE_ADS_CONVERSION_ACTION_NAME=${GOOGLE_ADS_CONVERSION_ACTION_NAME} | tee -a /app/.env && \
echo MICROSOFT_ADS_CLIENT_SECRET=${MICROSOFT_ADS_CLIENT_SECRET} | tee -a /app/.env && \
echo HUNTER_PRIORITY_CALCULATION_UNSOLD_DAYS=${HUNTER_PRIORITY_CALCULATION_UNSOLD_DAYS} | tee -a /app/.env && \
echo ADS_NOTIFICATION_EMAILS=${ADS_NOTIFICATION_EMAILS} | tee -a /app/.env && \
echo PING_POST_NOTIFICATION_EMAILS=${PING_POST_NOTIFICATION_EMAILS} | tee -a /app/.env && \
echo FIXR_DOMAIN=${FIXR_DOMAIN} | tee -a /app/.env && \
echo AUTH_TOKEN_SINGING_KEY=${AUTH_TOKEN_SINGING_KEY} | tee -a /app/.env && \
echo TWILIO_FROM_PHONE_NUMBER=${TWILIO_FROM_PHONE_NUMBER} | tee -a /app/.env && \
echo TWILIO_CONSUMER_VERIFICATION_FROM_PHONE=${TWILIO_CONSUMER_VERIFICATION_FROM_PHONE} | tee -a /app/.env && \
echo TWILIO_RECYCLED_LEADS_FROM_PHONE=${TWILIO_RECYCLED_LEADS_FROM_PHONE} | tee -a /app/.env && \
echo META_ADS_APP_ID=${META_ADS_APP_ID} | tee -a /app/.env && \
echo META_ADS_APP_SECRET=${META_ADS_APP_SECRET} | tee -a /app/.env && \
echo META_ADS_BUSINESS_ID=${META_ADS_BUSINESS_ID} | tee -a /app/.env && \
echo META_ADS_ADMIN_SYSTEM_USER_TOKEN=${META_ADS_ADMIN_SYSTEM_USER_TOKEN} | tee -a /app/.env && \
echo COMPANY_DISCOVERY_GOOGLE_PLACES_API_KEY=${COMPANY_DISCOVERY_GOOGLE_PLACES_API_KEY} | tee -a /app/.env && \
echo STRIPE_LIVE=${STRIPE_LIVE} | tee -a /app/.env && \
echo STRIPE_API_VERSION=${STRIPE_API_VERSION} | tee -a /app/.env && \
echo STRIPE_API_KEY_SECRET_TEST=${STRIPE_API_KEY_SECRET_TEST} | tee -a /app/.env && \
echo STRIPE_API_KEY_SECRET_LIVE=${STRIPE_API_KEY_SECRET_LIVE} | tee -a /app/.env && \
echo STRIPE_API_KEY_PUBLISHABLE_TEST=${STRIPE_API_KEY_PUBLISHABLE_TEST} | tee -a /app/.env && \
echo STRIPE_API_KEY_PUBLISHABLE_LIVE=${STRIPE_API_KEY_PUBLISHABLE_LIVE} | tee -a /app/.env && \
echo LOG_SLACK_WEBHOOK_URL=${LOG_SLACK_WEBHOOK_URL} | tee -a /app/.env && \
echo DASHBOARD_CLIENT_API_JWT_SIGNING_KEY=${DASHBOARD_CLIENT_API_JWT_SIGNING_KEY} | tee -a /app/.env && \
echo DASHBOARD_CLIENT_API_JWT_EXPIRE_IN=${DASHBOARD_CLIENT_API_JWT_EXPIRE_IN} | tee -a /app/.env && \
echo LEAD_MINIMUM_ELECTRIC_SPEND=${LEAD_MINIMUM_ELECTRIC_SPEND} | tee -a /app/.env && \
echo LEAD_REJECTION_PERCENTAGE_THRESHOLD=${LEAD_REJECTION_PERCENTAGE_THRESHOLD} | tee -a /app/.env && \
echo LEAD_CRM_REJECTION_PERCENTAGE_THRESHOLD=${LEAD_CRM_REJECTION_PERCENTAGE_THRESHOLD} | tee -a /app/.env && \
echo APPT_MINIMUM_ELECTRIC_SPEND=${APPT_MINIMUM_ELECTRIC_SPEND} | tee -a /app/.env && \
echo APPT_REJECTION_PERCENTAGE_THRESHOLD=${APPT_REJECTION_PERCENTAGE_THRESHOLD} | tee -a /app/.env && \
echo APPT_CRM_REJECTION_PERCENTAGE_THRESHOLD=${APPT_CRM_REJECTION_PERCENTAGE_THRESHOLD} | tee -a /app/.env && \
echo MAX_CHARGE_ATTEMPTS=${MAX_CHARGE_ATTEMPTS} | tee -a /app/.env && \
echo APPT_OFFERING_AVAILABLE_DURATION_MIN=${APPT_OFFERING_AVAILABLE_DURATION_MIN} | tee -a /app/.env && \
echo APPTS_SOLARREVIEWS_DOMAIN=${APPTS_SOLARREVIEWS_DOMAIN} | tee -a /app/.env && \
echo APPT_MAX_OFFERING_ATTEMPTS=${APPT_MAX_OFFERING_ATTEMPTS} | tee -a /app/.env && \
echo APPT_REJECTION_WINDOW_DURATION_HOURS=${APPT_REJECTION_WINDOW_DURATION_HOURS} | tee -a /app/.env && \
echo BRS_DRIVER=${BRS_DRIVER} | tee -a /app/.env && \
echo APPT_CONSUMER_ADVANCE_NOTICE_HOURS=${APPT_CONSUMER_ADVANCE_NOTICE_HOURS} | tee -a /app/.env && \
echo PRODUCT_PRICING_DRIVER=${PRODUCT_PRICING_DRIVER} | tee -a /app/.env && \
echo LEAD_REJECTION_WINDOW_DURATION_HOURS=${LEAD_REJECTION_WINDOW_DURATION_HOURS} | tee -a /app/.env && \
echo APPT_LOG_BRS_QUERIES=${APPT_LOG_BRS_QUERIES} | tee -a /app/.env && \
echo LOG_JOB_EVENTS=${LOG_JOB_EVENTS} | tee -a /app/.env && \
echo GOOGLE_FIRESTORE_PROJECT_ID=${GOOGLE_FIRESTORE_PROJECT_ID} | tee -a /app/.env && \
echo GOOGLE_FIRESTORE_KEY_FILE=${GOOGLE_FIRESTORE_KEY_FILE} | tee -a /app/.env && \
echo GOOGLE_FIRESTORE_COLLECTION_PREFIX=${GOOGLE_FIRESTORE_COLLECTION_PREFIX} | tee -a /app/.env && \
echo FLOW_BUILDER_IMAGE_BUCKET=${FLOW_BUILDER_IMAGE_BUCKET} | tee -a /app/.env && \
echo FLOW_BUILDER_CDN_URL=${FLOW_BUILDER_CDN_URL} | tee -a /app/.env && \
echo FLOW_BUILDER_URL=${FLOW_BUILDER_URL} | tee -a /app/.env && \
echo FLOW_CLIENT_URL=${FLOW_CLIENT_URL} | tee -a /app/.env && \
echo SCHEDULING_BASE_API_URL=${SCHEDULING_BASE_API_URL} | tee -a /app/.env && \
echo SCHEDULING_CLIENT_SECRET=${SCHEDULING_CLIENT_SECRET} | tee -a /app/.env && \
echo ALLOCATE_DIRECT_LEADS_WITHOUT_TIMEZONE_OPEN_DELAY=${ALLOCATE_DIRECT_LEADS_WITHOUT_TIMEZONE_OPEN_DELAY} | tee -a /app/.env && \
echo DATABASE_QUEUE_AFTER_COMMIT=${DATABASE_QUEUE_AFTER_COMMIT} | tee -a /app/.env && \
echo APPT_NEXT_DELIVERY_ATTEMPT_BUFFER_MINUTES=${APPT_NEXT_DELIVERY_ATTEMPT_BUFFER_MINUTES} | tee -a /app/.env && \
echo APPT_MAX_DELIVERY_ATTEMPTS=${APPT_MAX_DELIVERY_ATTEMPTS} | tee -a /app/.env && \
echo URL_SIGNER_SIGNATURE_KEY=${URL_SIGNER_SIGNATURE_KEY} | tee -a /app/.env && \
echo OPPORTUNITY_NOTIFICATIONS_CRON=${OPPORTUNITY_NOTIFICATIONS_CRON} | tee -a /app/.env && \
echo GOOGLE_ADS_LOGIN_CUSTOMER_ID=${GOOGLE_ADS_LOGIN_CUSTOMER_ID} | tee -a /app/.env && \
echo GOOGLE_DEBUG_MONITORING_LOGS=${GOOGLE_DEBUG_MONITORING_LOGS} | tee -a /app/.env && \
echo GOOGLE_ADS_LOGIN_CUSTOMER_ID=${GOOGLE_ADS_LOGIN_CUSTOMER_ID} | tee -a /app/.env && \
echo APPT_SELL_APPT_AS_LEAD_BUFFER_AFTER_MIN=${APPT_SELL_APPT_AS_LEAD_BUFFER_AFTER_MIN} | tee -a /app/.env && \
echo FLOW_PROXY_URL=${FLOW_PROXY_URL} | tee -a /app/.env && \
echo FLOW_PROXY_SECRET=${FLOW_PROXY_SECRET} | tee -a /app/.env && \
echo FILTERS_API_DRIVER=${FILTERS_API_DRIVER} | tee -a /app/.env && \
echo RUN_SCHEDULED_ADS_JOBS=${RUN_SCHEDULED_ADS_JOBS} | tee -a /app/.env && \
echo MAILSLURP_API_KEY=${MAILSLURP_API_KEY} | tee -a /app/.env && \
echo SOLAR_REVIEWS_FRONTEND_DOMAIN=${SOLAR_REVIEWS_FRONTEND_DOMAIN} | tee -a /app/.env && \
echo GOOGLE_GMAIL_CLIENT_ID=${GOOGLE_GMAIL_CLIENT_ID} | tee -a /app/.env && \
echo GOOGLE_STORAGE_MAILBOX_BUCKET=${GOOGLE_STORAGE_MAILBOX_BUCKET} | tee -a /app/.env && \
echo GOOGLE_STORAGE_MAILBOX_CDN_URL=${GOOGLE_STORAGE_MAILBOX_CDN_URL} | tee -a /app/.env && \
echo GOOGLE_GMAIL_CLIENT_SECRET=${GOOGLE_GMAIL_CLIENT_SECRET} | tee -a /app/.env && \
echo GOOGLE_GMAIL_EMAIL_LISTENER_TOPIC=${GOOGLE_GMAIL_EMAIL_LISTENER_TOPIC} | tee -a /app/.env && \
echo MAILBOX_MAIL_PROVIDER=${MAILBOX_MAIL_PROVIDER} | tee -a /app/.env && \
echo MAILBOX_API_DRIVER=${MAILBOX_API_DRIVER} | tee -a /app/.env && \
echo FLOW_BUILDER_DEFAULT_SAFE_CALL_IDS=${FLOW_BUILDER_DEFAULT_SAFE_CALL_IDS} | tee -a /app/.env && \
echo FLOW_BUILDER_DEFAULT_SAFE_CALL_IDS=${FLOW_BUILDER_DEFAULT_SAFE_CALL_IDS} | tee -a /app/.env && \
echo ARE_UTILITY_FILTERS_ACTIVE=${ARE_UTILITY_FILTERS_ACTIVE} | tee -a /app/.env && \
echo COMPANY_SEARCH_FILTER_PURCHASING_FROM_COMPETITOR_QUERY_STRATEGY=${COMPANY_SEARCH_FILTER_PURCHASING_FROM_COMPETITOR_QUERY_STRATEGY} | tee -a /app/.env && \
echo DELIVERY_FAILURE_NOTIFICATION_EMAILS=${DELIVERY_FAILURE_NOTIFICATION_EMAILS} | tee -a /app/.env && \
echo DROPBOX_API_KEY=${DROPBOX_API_KEY} | tee -a /app/.env && \
echo MIX_DROPBOX_ACCOUNT_ID=${MIX_DROPBOX_ACCOUNT_ID} | tee -a /app/.env && \
echo GOOGLE_STORAGE_CONTRACT_BUCKET=${GOOGLE_STORAGE_CONTRACT_BUCKET} | tee -a /app/.env && \
echo GOOGLE_STORAGE_CONTRACT_URL=${GOOGLE_STORAGE_CONTRACT_URL} | tee -a /app/.env && \
echo TWILIO_VERIFY_SERVICE_SID=${TWILIO_VERIFY_SERVICE_SID} | tee -a /app/.env && \
echo COMPANY_METRICS_DRIVER=${COMPANY_METRICS_DRIVER} | tee -a /app/.env && \
echo SIMILAR_WEB_API_BASE_URL=${SIMILAR_WEB_API_BASE_URL} | tee -a /app/.env && \
echo SIMILAR_WEB_API_KEY=${SIMILAR_WEB_API_KEY} | tee -a /app/.env && \
echo SPY_FU_API_BASE_URL=${SPY_FU_API_BASE_URL} | tee -a /app/.env && \
echo SPY_FU_API_KEY=${SPY_FU_API_KEY} | tee -a /app/.env && \
echo SPY_FU_API_ID=${SPY_FU_API_ID} | tee -a /app/.env && \
echo DELIVERY_FAILURE_NOTIFICATION_EMAILS=${DELIVERY_FAILURE_NOTIFICATION_EMAILS} | tee -a /app/.env && \
echo LARGE_ACCOUNT_REVENUE_THRESHOLD=${LARGE_ACCOUNT_REVENUE_THRESHOLD} | tee -a /app/.env && \
echo STAGING_TESTING_PUBSUB_TOPIC=${STAGING_TESTING_PUBSUB_TOPIC} | tee -a /app/.env && \
echo FUTURE_ALLOCATION_TESTING_INDUSTRY_ID=${FUTURE_ALLOCATION_TESTING_INDUSTRY_ID} | tee -a /app/.env && \
echo OMIT_LOW_NEVER_EXCEED_BUDGET=${OMIT_LOW_NEVER_EXCEED_BUDGET} | tee -a /app/.env && \
echo META_ADS_AUTOMATION_SYSTEM_USER_NAME=${META_ADS_AUTOMATION_SYSTEM_USER_NAME} | tee -a /app/.env && \
echo META_WADE_ADS_APP_ID=${META_WADE_ADS_APP_ID} | tee -a /app/.env && \
echo META_WADE_ADS_APP_SECRET=${META_WADE_ADS_APP_SECRET} | tee -a /app/.env && \
echo META_WADE_ADS_BUSINESS_ID=${META_WADE_ADS_BUSINESS_ID} | tee -a /app/.env && \
echo META_WADE_ADS_ADMIN_SYSTEM_USER_TOKEN=${META_WADE_ADS_ADMIN_SYSTEM_USER_TOKEN} | tee -a /app/.env && \
echo META_WADE_ADS_PIXEL_ID=${META_WADE_ADS_PIXEL_ID} | tee -a /app/.env && \
echo META_GABE_ADS_AD_COST_APP_ID=${META_GABE_ADS_AD_COST_APP_ID} | tee -a /app/.env && \
echo META_GABE_ADS_AD_COST_APP_SECRET=${META_GABE_ADS_AD_COST_APP_SECRET} | tee -a /app/.env && \
echo META_GABE_ADS_AD_COST_TOKEN=${META_GABE_ADS_AD_COST_TOKEN} | tee -a /app/.env && \
echo META_WADE_ADS_AD_COST_APP_ID=${META_WADE_ADS_AD_COST_APP_ID} | tee -a /app/.env && \
echo META_WADE_ADS_AD_COST_APP_SECRET=${META_WADE_ADS_AD_COST_APP_SECRET} | tee -a /app/.env && \
echo META_WADE_ADS_AD_COST_TOKEN=${META_WADE_ADS_AD_COST_TOKEN} | tee -a /app/.env && \
echo WATCHDOG_TWO_SERVER_URL=${WATCHDOG_TWO_SERVER_URL} | tee -a /app/.env && \
echo WATCHDOG_TWO_PERSONAL_ACCESS_TOKEN=${WATCHDOG_TWO_PERSONAL_ACCESS_TOKEN} | tee -a /app/.env && \
echo PRIVACY_ACCESS_TOKEN=${PRIVACY_ACCESS_TOKEN} | tee -a /app/.env && \
echo SOLAR_ESTIMATE_DOMAIN=${SOLAR_ESTIMATE_DOMAIN} | tee -a /app/.env && \
echo AFFILIATES_PORTAL_API_TOKEN=${AFFILIATES_PORTAL_API_TOKEN} | tee -a /app/.env && \
echo AFFILIATES_PORTAL_API_URL=${AFFILIATES_PORTAL_API_URL} | tee -a /app/.env && \
echo FLOW_ENGINE_URL=${FLOW_ENGINE_URL} | tee -a /app/.env && \
echo EMAIL_MARKETING_API_KEY=${EMAIL_MARKETING_API_KEY} | tee -a /app/.env && \
echo EMAIL_MARKETING_SERVER=${EMAIL_MARKETING_SERVER} | tee -a /app/.env && \
echo EMAIL_MARKETING_DRIVER=${EMAIL_MARKETING_DRIVER} | tee -a /app/.env && \
echo DIRECT_LEAD_ENABLED_INDUSTRY_IDS=${DIRECT_LEAD_ENABLED_INDUSTRY_IDS} | tee -a /app/.env && \
echo CONTRACT_PROVIDER_INTEGRATION_KEY=${CONTRACT_PROVIDER_INTEGRATION_KEY} | tee -a /app/.env && \
echo CONTRACT_PROVIDER_USER_ID=${CONTRACT_PROVIDER_USER_ID} | tee -a /app/.env && \
echo CONTRACT_PROVIDER_ACCOUNT_ID=${CONTRACT_PROVIDER_ACCOUNT_ID} | tee -a /app/.env && \
echo CONTRACT_PROVIDER_PRIVATE_KEY=${CONTRACT_PROVIDER_PRIVATE_KEY} | tee -a /app/.env && \
echo EMAIL_MARKETING_SOCKET_LABS_SERVER=${EMAIL_MARKETING_SOCKET_LABS_SERVER} | tee -a /app/.env && \
echo EMAIL_MARKETING_SOCKET_LABS_API_KEY=${EMAIL_MARKETING_SOCKET_LABS_API_KEY} | tee -a /app/.env && \
echo SALES_INTEL_API_KEY=${SALES_INTEL_API_KEY} | tee -a /app/.env \
echo SLACK_ADMIN_SYSTEM_BOT_AUTH_TOKEN=${SLACK_ADMIN_SYSTEM_BOT_AUTH_TOKEN} | tee -a /app/.env && \
echo SLACK_COMPANY_REGISTRATION_BOT_AUTH_TOKEN=${SLACK_COMPANY_REGISTRATION_BOT_AUTH_TOKEN} | tee -a /app/.env && \
echo SLACK_REGISTRATION_NOTIFICATION_CHANNEL_ID=${SLACK_REGISTRATION_NOTIFICATION_CHANNEL_ID} | tee -a /app/.env && \
echo GOOGLE_STORAGE_BASE_URL=${GOOGLE_STORAGE_BASE_URL} | tee -a /app/.env && \
echo REVIEW_ATTACHMENTS_BUCKET=${REVIEW_ATTACHMENTS_BUCKET} | tee -a /app/.env && \
echo EMAIL_MARKETING_SOCKET_LABS_WEBHOOK_SECRET=${EMAIL_MARKETING_SOCKET_LABS_WEBHOOK_SECRET} | tee -a /app/.env
echo REVIEW_ATTACHMENTS_BUCKET=${REVIEW_ATTACHMENTS_BUCKET} | tee -a /app/.env && \
echo DEEPGRAM_API_KEY=${DEEPGRAM_API_KEY} | tee -a /app/.env && \
echo EMAIL_MARKETING_SOCKET_LABS_API_KEY_SECONDARY=${EMAIL_MARKETING_SOCKET_LABS_API_KEY_SECONDARY} | tee -a /app/.env && \
echo EMAIL_MARKETING_SOCKET_LABS_SERVER_SECONDARY=${EMAIL_MARKETING_SOCKET_LABS_SERVER_SECONDARY} | tee -a /app/.env
