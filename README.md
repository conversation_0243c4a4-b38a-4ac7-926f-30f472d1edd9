# SolarReviews Admin  
Project for SolarReviews' Admin 2.0.

# Setting up
### How to get leads into your queue
- To create leads run the following command from your project root
  - ./artisan create:multi-industry-lead --leads=10 --service=8 --zip=90080 --state=CA
  - It will generate 10 leads in total
- Make sure the jobs are running
  - check the jobs table in database
  - Run the queues ./artisan queue:work 
  - To run the specific queue: ./artisan queue:work --queue=appointment_allocation_queue 
  - To run multiple queues: ./artisan queue:work --queue=myJobQueue, myJobQueue1, myJobQueue2,..myJobQueue7
- Inorder to see the leads
  - Add the user to the processor list for the specific service that your lead has been created for
  - This can be done in lead-processing-management page in Admin 2.0
  - Go to lead-processing page, you should now have list of leads for processing
- If nothing comes up,
  - double check that your jobs are running as expected
  - check for the queue restrictions queries in associated repository
  
   
### Running tests locally
1. Copy the `.env.testing.example` file to `env.testing`:

   ``
   cp .env.testing.example .env.testing
   ``
2. Use the variables in the `.env.testing` file on LastPass, ask your manager to share this with you if you don't have it.
3. Create the local databases `test_admin_solarreviews` and `test_solarreviews`
   
    ``
    mysql -u root -p -e "CREATE DATABASE test_admin_solarreviews; CREATE DATABASE test_solarreviews;
    ``
4. Run the tests from your project root:

    ``phpunit``

